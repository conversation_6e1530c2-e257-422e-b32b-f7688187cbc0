#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股分红数据查询脚本

功能：
1. 查询单只股票的分红历史
2. 查询指定时间范围的分红数据
3. 统计分红数据
4. 导出分红数据到CSV

使用示例：
1. 查询腾讯分红: python query_hk_dividend_data.py --stock 00700
2. 查询2023年分红: python query_hk_dividend_data.py --year 2023
3. 导出所有数据: python query_hk_dividend_data.py --export all_dividends.csv

作者：AI Assistant
创建时间：2025年
"""

import sqlite3
import pandas as pd
import argparse
import sys
from datetime import datetime, timedelta

class HKDividendDataQuery:
    """港股分红数据查询器"""
    
    def __init__(self, db_name: str = "hk_dividend_data.db"):
        self.db_name = db_name
        
        # 检查数据库是否存在
        try:
            conn = sqlite3.connect(self.db_name)
            conn.close()
        except Exception as e:
            print(f"❌ 无法连接数据库 {self.db_name}: {e}")
            sys.exit(1)
    
    def query_stock_dividends(self, stock_code: str) -> pd.DataFrame:
        """查询单只股票的分红历史"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            query = '''
                SELECT 
                    stock_code,
                    stock_name,
                    ex_dividend_date,
                    dividend_amount,
                    dividend_type,
                    plan_description,
                    year,
                    data_source,
                    announcement_date,
                    payment_date
                FROM dividend_data 
                WHERE stock_code = ?
                ORDER BY ex_dividend_date DESC
            '''
            
            df = pd.read_sql_query(query, conn, params=(stock_code,))
            conn.close()
            
            return df
            
        except Exception as e:
            print(f"❌ 查询股票分红数据失败: {e}")
            return pd.DataFrame()
    
    def query_dividends_by_year(self, year: int) -> pd.DataFrame:
        """查询指定年份的分红数据"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            query = '''
                SELECT 
                    stock_code,
                    stock_name,
                    ex_dividend_date,
                    dividend_amount,
                    dividend_type,
                    plan_description,
                    data_source
                FROM dividend_data 
                WHERE year = ?
                ORDER BY ex_dividend_date DESC, dividend_amount DESC
            '''
            
            df = pd.read_sql_query(query, conn, params=(year,))
            conn.close()
            
            return df
            
        except Exception as e:
            print(f"❌ 查询年份分红数据失败: {e}")
            return pd.DataFrame()
    
    def query_dividends_by_date_range(self, start_date: str, end_date: str) -> pd.DataFrame:
        """查询指定日期范围的分红数据"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            query = '''
                SELECT 
                    stock_code,
                    stock_name,
                    ex_dividend_date,
                    dividend_amount,
                    dividend_type,
                    plan_description,
                    data_source
                FROM dividend_data 
                WHERE ex_dividend_date BETWEEN ? AND ?
                ORDER BY ex_dividend_date DESC, dividend_amount DESC
            '''
            
            df = pd.read_sql_query(query, conn, params=(start_date, end_date))
            conn.close()
            
            return df
            
        except Exception as e:
            print(f"❌ 查询日期范围分红数据失败: {e}")
            return pd.DataFrame()
    
    def get_top_dividend_stocks(self, limit: int = 20, year: int = None) -> pd.DataFrame:
        """获取分红最高的股票"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            if year:
                query = '''
                    SELECT 
                        stock_code,
                        stock_name,
                        SUM(dividend_amount) as total_dividend,
                        COUNT(*) as dividend_count,
                        AVG(dividend_amount) as avg_dividend,
                        MAX(dividend_amount) as max_dividend
                    FROM dividend_data 
                    WHERE year = ? AND dividend_amount > 0
                    GROUP BY stock_code, stock_name
                    ORDER BY total_dividend DESC
                    LIMIT ?
                '''
                params = (year, limit)
            else:
                query = '''
                    SELECT 
                        stock_code,
                        stock_name,
                        SUM(dividend_amount) as total_dividend,
                        COUNT(*) as dividend_count,
                        AVG(dividend_amount) as avg_dividend,
                        MAX(dividend_amount) as max_dividend
                    FROM dividend_data 
                    WHERE dividend_amount > 0
                    GROUP BY stock_code, stock_name
                    ORDER BY total_dividend DESC
                    LIMIT ?
                '''
                params = (limit,)
            
            df = pd.read_sql_query(query, conn, params=params)
            conn.close()
            
            return df
            
        except Exception as e:
            print(f"❌ 查询高分红股票失败: {e}")
            return pd.DataFrame()
    
    def get_dividend_statistics(self) -> dict:
        """获取分红数据统计"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            stats = {}
            
            # 基本统计
            cursor.execute('SELECT COUNT(*) FROM dividend_data')
            stats['total_records'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM dividend_data')
            stats['stocks_count'] = cursor.fetchone()[0]
            
            cursor.execute('SELECT MIN(ex_dividend_date), MAX(ex_dividend_date) FROM dividend_data WHERE ex_dividend_date IS NOT NULL')
            date_range = cursor.fetchone()
            stats['date_range'] = f"{date_range[0]} 至 {date_range[1]}" if date_range[0] else "N/A"
            
            # 分红金额统计
            cursor.execute('SELECT AVG(dividend_amount), MIN(dividend_amount), MAX(dividend_amount) FROM dividend_data WHERE dividend_amount > 0')
            amount_stats = cursor.fetchone()
            if amount_stats[0]:
                stats['avg_dividend'] = round(amount_stats[0], 4)
                stats['min_dividend'] = amount_stats[1]
                stats['max_dividend'] = amount_stats[2]
            
            # 按年份统计
            cursor.execute('SELECT year, COUNT(*) FROM dividend_data WHERE year IS NOT NULL GROUP BY year ORDER BY year DESC')
            stats['yearly_counts'] = dict(cursor.fetchall())
            
            # 按数据源统计
            cursor.execute('SELECT data_source, COUNT(*) FROM dividend_data GROUP BY data_source')
            stats['source_counts'] = dict(cursor.fetchall())
            
            conn.close()
            return stats
            
        except Exception as e:
            print(f"❌ 获取统计数据失败: {e}")
            return {}
    
    def export_all_data(self, output_file: str):
        """导出所有分红数据到CSV"""
        try:
            conn = sqlite3.connect(self.db_name)
            
            query = '''
                SELECT 
                    stock_code,
                    stock_name,
                    announcement_date,
                    ex_dividend_date,
                    record_date,
                    payment_date,
                    dividend_amount,
                    dividend_type,
                    plan_description,
                    year,
                    currency,
                    data_source,
                    created_at
                FROM dividend_data 
                ORDER BY stock_code, ex_dividend_date DESC
            '''
            
            df = pd.read_sql_query(query, conn)
            conn.close()
            
            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            print(f"✅ 成功导出 {len(df)} 条记录到 {output_file}")
            
        except Exception as e:
            print(f"❌ 导出数据失败: {e}")

def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='港股分红数据查询器')
    
    # 查询选项
    group = parser.add_mutually_exclusive_group()
    group.add_argument('--stock', type=str, help='查询单只股票代码 (如 00700)')
    group.add_argument('--year', type=int, help='查询指定年份的分红')
    group.add_argument('--top', type=int, help='查询分红最高的N只股票')
    group.add_argument('--stats', action='store_true', help='显示分红统计')
    group.add_argument('--export', type=str, help='导出所有数据到CSV文件')
    
    # 其他参数
    parser.add_argument('--start', type=str, help='开始日期 (YYYY-MM-DD)')
    parser.add_argument('--end', type=str, help='结束日期 (YYYY-MM-DD)')
    parser.add_argument('--db', type=str, default='hk_dividend_data.db', help='数据库文件路径')
    
    args = parser.parse_args()
    
    # 如果没有提供任何参数，显示统计信息
    if not any([args.stock, args.year, args.top, args.stats, args.export, args.start]):
        args.stats = True
    
    query_tool = HKDividendDataQuery(args.db)
    
    try:
        if args.stock:
            # 查询单只股票
            stock_code = args.stock.zfill(5)
            print(f"🔍 查询股票 {stock_code} 的分红历史")
            print("=" * 60)
            
            df = query_tool.query_stock_dividends(stock_code)
            if not df.empty:
                print(f"📊 找到 {len(df)} 条分红记录:")
                print(df.to_string(index=False))
            else:
                print("❌ 未找到分红记录")
        
        elif args.year:
            # 查询指定年份
            print(f"🔍 查询 {args.year} 年的分红数据")
            print("=" * 60)
            
            df = query_tool.query_dividends_by_year(args.year)
            if not df.empty:
                print(f"📊 找到 {len(df)} 条分红记录:")
                print(df.head(20).to_string(index=False))
                if len(df) > 20:
                    print(f"\n... 还有 {len(df) - 20} 条记录")
            else:
                print("❌ 未找到分红记录")
        
        elif args.top:
            # 查询高分红股票
            year = args.year if hasattr(args, 'year') else None
            print(f"🔍 查询分红最高的 {args.top} 只股票")
            if year:
                print(f"   限定年份: {year}")
            print("=" * 60)
            
            df = query_tool.get_top_dividend_stocks(args.top, year)
            if not df.empty:
                print(df.to_string(index=False))
            else:
                print("❌ 未找到数据")
        
        elif args.stats:
            # 显示统计
            print("📊 分红数据统计")
            print("=" * 60)
            
            stats = query_tool.get_dividend_statistics()
            if stats:
                print(f"总记录数: {stats.get('total_records', 0):,}")
                print(f"股票数量: {stats.get('stocks_count', 0)}")
                print(f"日期范围: {stats.get('date_range', 'N/A')}")
                
                if 'avg_dividend' in stats:
                    print(f"\n分红金额统计:")
                    print(f"  平均分红: {stats['avg_dividend']:.4f} 港元")
                    print(f"  最小分红: {stats['min_dividend']:.4f} 港元")
                    print(f"  最大分红: {stats['max_dividend']:.4f} 港元")
                
                yearly_counts = stats.get('yearly_counts', {})
                if yearly_counts:
                    print(f"\n按年份统计:")
                    for year, count in list(yearly_counts.items())[:10]:  # 显示最近10年
                        print(f"  {year}: {count:,} 条")
                
                source_counts = stats.get('source_counts', {})
                if source_counts:
                    print(f"\n按数据源统计:")
                    for source, count in source_counts.items():
                        print(f"  {source}: {count:,} 条")
            else:
                print("❌ 无法获取统计数据")
        
        elif args.export:
            # 导出数据
            print(f"📤 导出所有分红数据到 {args.export}")
            print("=" * 60)
            
            query_tool.export_all_data(args.export)
        
        elif args.start and args.end:
            # 查询日期范围
            print(f"🔍 查询 {args.start} 至 {args.end} 的分红数据")
            print("=" * 60)
            
            df = query_tool.query_dividends_by_date_range(args.start, args.end)
            if not df.empty:
                print(f"📊 找到 {len(df)} 条分红记录:")
                print(df.head(20).to_string(index=False))
                if len(df) > 20:
                    print(f"\n... 还有 {len(df) - 20} 条记录")
            else:
                print("❌ 未找到分红记录")
    
    except Exception as e:
        print(f"❌ 查询过程出错: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
