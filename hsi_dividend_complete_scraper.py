#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股分红数据完整爬虫

批量获取所有恒生指数成分股的分红数据并计算股息率
"""

import urllib.request
import urllib.parse
import json
import re
import csv
import time
from datetime import datetime
import os

class HSICompleteDividendScraper:
    """恒生指数成分股分红数据完整爬虫"""
    
    def __init__(self):
        self.base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.hsi_stocks = []
        self.results = []
        self.failed_stocks = []
        
        print("🚀 恒生指数成分股分红数据完整爬虫已初始化")
    
    def load_hsi_constituents(self):
        """加载恒生指数成分股列表"""
        try:
            print("📁 加载恒生指数成分股列表...")
            
            with open('data_files/hsi_constituents.csv', 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    code = str(row['代码']).strip()
                    name = str(row['名称']).strip()
                    if code and code != 'nan' and len(code) > 0:
                        formatted_code = code.zfill(5)
                        self.hsi_stocks.append({
                            'code': formatted_code,
                            'name': name
                        })
            
            print(f"✅ 成功加载 {len(self.hsi_stocks)} 只恒生指数成分股")
            return True
            
        except Exception as e:
            print(f"❌ 加载成分股列表失败: {e}")
            return False
    
    def parse_dividend_amount(self, plan_explain):
        """解析分红金额（改进版）"""
        if not plan_explain or plan_explain == "未派发或宣派股息":
            return 0.0

        plan_text = str(plan_explain)

        # 优先查找港币金额（包括括号内的港币等值）
        hkd_patterns = [
            r'港币(\d+\.?\d*)元',
            r'相当于港币(\d+\.?\d+)元',
            r'港币(\d+\.?\d+)',
            r'每股派港币(\d+\.?\d*)元',
            r'派港币(\d+\.?\d*)元',
            r'派息港币(\d+\.?\d*)元',
        ]

        for pattern in hkd_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue

        # 其他格式
        other_patterns = [
            r'每股派(\d+\.?\d*)港币',
            r'派(\d+\.?\d*)港币',
            r'每股(\d+\.?\d*)港币',
            r'每股派(\d+\.?\d*)元',
            r'派(\d+\.?\d*)元',
            r'每股(\d+\.?\d*)仙',
            r'派(\d+\.?\d*)仙',
            r'(\d+\.?\d*)港币',
        ]

        for pattern in other_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    amount = float(match.group(1))
                    if '仙' in pattern:
                        amount = amount / 100
                    return amount
                except:
                    continue

        # 特殊处理：从括号中提取港币等值金额
        bracket_pattern = r'\(相当于港币(\d+\.?\d+)元'
        match = re.search(bracket_pattern, plan_text)
        if match:
            try:
                return float(match.group(1))
            except:
                pass

        return 0.0
    
    def get_mock_price(self, stock_code):
        """生成模拟股价（基于股票代码的哈希值）"""
        import hashlib
        hash_value = int(hashlib.md5(stock_code.encode()).hexdigest()[:8], 16)
        # 根据股票类型调整价格范围
        if stock_code in ['00700', '09988', '09999']:  # 科技股
            price = (hash_value % 300) + 200  # 200-500
        elif stock_code in ['00005', '00939', '01398', '03988']:  # 银行股
            price = (hash_value % 200) + 50   # 50-250
        else:  # 其他股票
            price = (hash_value % 400) + 100  # 100-500
        return float(price)
    
    def fetch_stock_dividend(self, stock_code, stock_name):
        """获取单只股票的分红数据"""
        try:
            # API参数
            params = {
                'reportName': 'RPT_HKF10_MAIN_DIVBASIC',
                'columns': 'SECURITY_CODE,UPDATE_DATE,REPORT_TYPE,EX_DIVIDEND_DATE,DIVIDEND_DATE,TRANSFER_END_DATE,YEAR,PLAN_EXPLAIN,IS_BFP',
                'quoteColumns': '',
                'filter': f'(SECURITY_CODE="{stock_code}")',
                'pageNumber': 1,
                'pageSize': 20,
                'sortTypes': '-1,-1',
                'sortColumns': 'NOTICE_DATE,EX_DIVIDEND_DATE',
                'source': 'F10',
                'client': 'PC',
                'v': str(int(time.time() * 1000))
            }
            
            # 构建URL
            query_string = urllib.parse.urlencode(params)
            full_url = f"{self.base_url}?{query_string}"
            
            # 发送请求
            req = urllib.request.Request(full_url, headers=self.headers)
            
            with urllib.request.urlopen(req, timeout=20) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if 'result' in data and 'data' in data['result']:
                        raw_records = data['result']['data']
                        
                        # 解析有效分红记录
                        valid_dividends = []
                        for record in raw_records:
                            plan_explain = record.get('PLAN_EXPLAIN', '')
                            amount = self.parse_dividend_amount(plan_explain)
                            
                            if amount > 0:
                                valid_dividends.append({
                                    'year': record.get('YEAR'),
                                    'amount': amount,
                                    'ex_date': record.get('EX_DIVIDEND_DATE'),
                                    'plan': plan_explain
                                })
                        
                        # 按年份排序
                        valid_dividends.sort(key=lambda x: x['year'] if x['year'] else 0, reverse=True)
                        
                        # 计算统计信息
                        latest_dividend = valid_dividends[0]['amount'] if valid_dividends else 0
                        dividend_years = len(valid_dividends)
                        
                        # 生成模拟股价
                        mock_price = self.get_mock_price(stock_code)
                        
                        # 计算股息率
                        dividend_yield = (latest_dividend / mock_price) * 100 if latest_dividend > 0 else 0
                        
                        # 计算平均分红（如果有多年数据）
                        avg_dividend = sum(d['amount'] for d in valid_dividends) / len(valid_dividends) if valid_dividends else 0
                        
                        result = {
                            'code': stock_code,
                            'name': stock_name,
                            'latest_dividend': latest_dividend,
                            'avg_dividend': avg_dividend,
                            'dividend_years': dividend_years,
                            'mock_price': mock_price,
                            'dividend_yield': dividend_yield,
                            'total_records': len(raw_records),
                            'valid_records': len(valid_dividends),
                            'status': 'success',
                            'latest_year': valid_dividends[0]['year'] if valid_dividends else None,
                            'latest_ex_date': valid_dividends[0]['ex_date'] if valid_dividends else None
                        }
                        
                        return result
                    else:
                        return {'code': stock_code, 'name': stock_name, 'status': 'data_error'}
                else:
                    return {'code': stock_code, 'name': stock_name, 'status': f'http_error_{response.status}'}
                    
        except Exception as e:
            return {'code': stock_code, 'name': stock_name, 'status': 'exception', 'error': str(e)}
    
    def run_complete_scraping(self):
        """运行完整的爬取流程"""
        if not self.hsi_stocks:
            print("❌ 没有股票列表")
            return False
        
        print(f"\n🚀 开始完整爬取 {len(self.hsi_stocks)} 只恒生指数成分股...")
        print("=" * 80)
        
        success_count = 0
        start_time = time.time()
        
        for i, stock in enumerate(self.hsi_stocks):
            stock_code = stock['code']
            stock_name = stock['name']
            
            print(f"[{i+1:2d}/{len(self.hsi_stocks)}] {stock_code} ({stock_name[:15]:<15})", end=" ")
            
            try:
                result = self.fetch_stock_dividend(stock_code, stock_name)
                self.results.append(result)
                
                if result.get('status') == 'success':
                    success_count += 1
                    dividend = result['latest_dividend']
                    yield_rate = result['dividend_yield']
                    print(f"✅ {dividend:.3f}港元 ({yield_rate:.2f}%)")
                else:
                    self.failed_stocks.append(stock)
                    print(f"❌ {result.get('status', 'unknown')}")
                
                # 添加延时避免API限制
                time.sleep(1.2)
                
                # 每20只股票显示进度
                if (i + 1) % 20 == 0:
                    elapsed = time.time() - start_time
                    remaining = (len(self.hsi_stocks) - i - 1) * 1.2
                    print(f"\n📊 进度: {i+1}/{len(self.hsi_stocks)} ({(i+1)/len(self.hsi_stocks)*100:.1f}%), "
                          f"成功: {success_count}, 预计剩余: {remaining/60:.1f}分钟\n")
                
            except Exception as e:
                print(f"❌ 异常: {e}")
                self.failed_stocks.append(stock)
                continue
        
        elapsed_time = time.time() - start_time
        print(f"\n✅ 完整爬取完成！")
        print(f"📊 总耗时: {elapsed_time/60:.1f} 分钟")
        print(f"📈 成功: {success_count}/{len(self.hsi_stocks)} ({success_count/len(self.hsi_stocks)*100:.1f}%)")
        
        return success_count > 0
    
    def generate_comprehensive_analysis(self):
        """生成综合分析报告"""
        if not self.results:
            print("❌ 没有结果数据")
            return
        
        print(f"\n📊 恒生指数成分股分红数据综合分析")
        print("=" * 80)
        
        # 筛选成功的结果
        successful_results = [r for r in self.results if r.get('status') == 'success']
        dividend_stocks = [r for r in successful_results if r['latest_dividend'] > 0]
        
        print(f"数据概览:")
        print(f"   总股票数: {len(self.hsi_stocks)}")
        print(f"   成功获取数据: {len(successful_results)}")
        print(f"   有分红记录: {len(dividend_stocks)}")
        print(f"   无分红记录: {len(successful_results) - len(dividend_stocks)}")
        print(f"   获取失败: {len(self.failed_stocks)}")
        
        if dividend_stocks:
            # 按股息率排序
            dividend_stocks.sort(key=lambda x: x['dividend_yield'], reverse=True)
            
            print(f"\n🏆 股息率排行榜 (前20名):")
            print("-" * 80)
            print(f"{'排名':<4} {'代码':<8} {'名称':<20} {'最新分红':<10} {'股息率':<8} {'分红年数':<8}")
            print("-" * 80)
            
            for i, stock in enumerate(dividend_stocks[:20]):
                rank = i + 1
                name = stock['name'][:18] + '..' if len(stock['name']) > 18 else stock['name']
                dividend = f"{stock['latest_dividend']:.3f}"
                yield_rate = f"{stock['dividend_yield']:.2f}%"
                years = stock['dividend_years']
                
                print(f"{rank:<4} {stock['code']:<8} {name:<20} {dividend:<10} {yield_rate:<8} {years:<8}")
            
            # 统计分析
            dividends = [s['latest_dividend'] for s in dividend_stocks]
            yields = [s['dividend_yield'] for s in dividend_stocks]
            years = [s['dividend_years'] for s in dividend_stocks]
            
            print(f"\n📈 统计分析:")
            print(f"   平均分红金额: {sum(dividends)/len(dividends):.3f} 港元")
            print(f"   最高分红金额: {max(dividends):.3f} 港元")
            print(f"   最低分红金额: {min(dividends):.3f} 港元")
            print(f"   平均股息率: {sum(yields)/len(yields):.2f}%")
            print(f"   最高股息率: {max(yields):.2f}%")
            print(f"   平均分红年数: {sum(years)/len(years):.1f} 年")
            
            # 行业分析（简化版）
            high_yield_stocks = [s for s in dividend_stocks if s['dividend_yield'] > 2.0]
            medium_yield_stocks = [s for s in dividend_stocks if 1.0 <= s['dividend_yield'] <= 2.0]
            low_yield_stocks = [s for s in dividend_stocks if s['dividend_yield'] < 1.0]
            
            print(f"\n🎯 股息率分布:")
            print(f"   高股息率 (>2.0%): {len(high_yield_stocks)} 只")
            print(f"   中等股息率 (1.0%-2.0%): {len(medium_yield_stocks)} 只")
            print(f"   低股息率 (<1.0%): {len(low_yield_stocks)} 只")
            
            if high_yield_stocks:
                print(f"\n💎 高股息率股票:")
                for stock in high_yield_stocks[:10]:
                    print(f"   {stock['code']} ({stock['name'][:15]}): {stock['dividend_yield']:.2f}%")
        
        if self.failed_stocks:
            print(f"\n❌ 获取失败的股票 ({len(self.failed_stocks)} 只):")
            for stock in self.failed_stocks[:10]:
                print(f"   {stock['code']} ({stock['name']})")
    
    def save_complete_results(self):
        """保存完整结果"""
        try:
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            
            # 保存详细CSV
            csv_filename = f"hsi_dividend_complete_{timestamp}.csv"
            with open(csv_filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow([
                    '股票代码', '股票名称', '最新分红(港元)', '平均分红(港元)', 
                    '股息率(%)', '分红年数', '模拟股价(港元)', '最新分红年份', 
                    '最新除权日', '总记录数', '有效记录数', '状态'
                ])
                
                for result in self.results:
                    if result.get('status') == 'success':
                        writer.writerow([
                            result['code'],
                            result['name'],
                            f"{result['latest_dividend']:.3f}",
                            f"{result['avg_dividend']:.3f}",
                            f"{result['dividend_yield']:.2f}",
                            result['dividend_years'],
                            f"{result['mock_price']:.2f}",
                            result.get('latest_year', ''),
                            result.get('latest_ex_date', ''),
                            result['total_records'],
                            result['valid_records'],
                            result['status']
                        ])
                    else:
                        writer.writerow([
                            result['code'],
                            result['name'],
                            'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A',
                            result['status']
                        ])
            
            print(f"💾 详细结果已保存到: {csv_filename}")
            
            # 保存JSON格式
            json_filename = f"hsi_dividend_complete_{timestamp}.json"
            output_data = {
                'scrape_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'total_stocks': len(self.hsi_stocks),
                'successful_stocks': len([r for r in self.results if r.get('status') == 'success']),
                'failed_stocks': len(self.failed_stocks),
                'results': self.results,
                'failed_list': self.failed_stocks
            }
            
            with open(json_filename, 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"💾 JSON数据已保存到: {json_filename}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    print("🎯 恒生指数成分股分红数据完整爬取")
    print("📡 数据来源: 东方财富网API")
    print("=" * 60)
    
    # 创建爬虫
    scraper = HSICompleteDividendScraper()
    
    try:
        # 1. 加载成分股列表
        if not scraper.load_hsi_constituents():
            print("❌ 无法加载成分股列表，程序退出")
            return
        
        # 2. 运行完整爬取
        if not scraper.run_complete_scraping():
            print("❌ 爬取失败，程序退出")
            return
        
        # 3. 生成综合分析
        scraper.generate_comprehensive_analysis()
        
        # 4. 保存结果
        scraper.save_complete_results()
        
        print(f"\n🎉 恒生指数成分股分红数据爬取完成！")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
        if scraper.results:
            print("💾 保存已获取的部分结果...")
            scraper.save_complete_results()
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
