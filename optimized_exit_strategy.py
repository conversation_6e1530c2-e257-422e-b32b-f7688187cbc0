#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化的去趋势策略 - 智能卖出机制
设计多层次的动态卖出策略，而非固定持有期
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.filters.hp_filter import hpfilter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedExitStrategy:
    """优化的卖出策略分析器"""

    def __init__(self):
        self.data = None
        self.processed_data = None
        self.strategy_results = {}

    def load_data(self):
        """加载数据"""
        try:
            self.data = pd.read_csv('data/china_bond_index_full_history.csv')
            self.data['日期'] = pd.to_datetime(self.data['日期'])
            self.data.set_index('日期', inplace=True)

            print(f"✅ 成功加载数据: {len(self.data)} 个数据点")
            return True
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def prepare_data(self):
        """准备策略数据"""
        print(f"\n🔄 准备策略数据...")

        price = self.data['中债综合全价指数']

        # HP滤波去趋势
        cycle, trend = hpfilter(price.dropna(), lamb=7000000)

        # 创建结果DataFrame
        results_df = pd.DataFrame(index=price.index)
        results_df['价格'] = price
        results_df['趋势'] = trend.reindex(price.index)
        results_df['去趋势'] = cycle.reindex(price.index)

        # 计算技术指标
        results_df['滚动均值'] = results_df['去趋势'].rolling(252).mean()
        results_df['滚动标准差'] = results_df['去趋势'].rolling(252).std()
        results_df['Z_Score'] = (results_df['去趋势'] - results_df['滚动均值']) / results_df['滚动标准差']
        results_df['百分位数'] = results_df['去趋势'].rolling(252).rank(pct=True)

        # 计算移动平均线
        results_df['MA20'] = price.rolling(20).mean()
        results_df['MA60'] = price.rolling(60).mean()

        # 计算价格相对位置
        results_df['价格百分位'] = price.rolling(252).rank(pct=True)

        # 计算收益率
        results_df['日收益率'] = price.pct_change()
        results_df['累积收益5日'] = (1 + results_df['日收益率']).rolling(5).apply(lambda x: x.prod()) - 1
        results_df['累积收益20日'] = (1 + results_df['日收益率']).rolling(20).apply(lambda x: x.prod()) - 1

        self.processed_data = results_df
        print(f"✅ 策略数据准备完成")
        return True

    def create_dynamic_strategy(self, strategy_name="动态策略"):
        """创建动态买卖策略"""
        print(f"\n📊 创建{strategy_name}...")

        data = self.processed_data.copy()

        # 初始化
        position = pd.Series(0.0, index=data.index)  # 持仓比例 (0-1)
        buy_signals = pd.Series(False, index=data.index)
        sell_signals = pd.Series(False, index=data.index)

        # 买入信号 - 多层次买入
        buy_conditions = {
            '极端低点': data['Z_Score'] <= -2.0,
            '低点': data['Z_Score'] <= -1.5,
            '较低点': data['Z_Score'] <= -1.0,
            '百分位极低': data['百分位数'] <= 0.05,
            '百分位低': data['百分位数'] <= 0.10
        }

        # 卖出信号 - 多层次卖出
        sell_conditions = {
            '极端高点': data['Z_Score'] >= 2.0,
            '高点': data['Z_Score'] >= 1.5,
            '较高点': data['Z_Score'] >= 1.0,
            '百分位极高': data['百分位数'] >= 0.95,
            '百分位高': data['百分位数'] >= 0.90,
            '价格高位': data['价格百分位'] >= 0.85,
            '短期获利': data['累积收益5日'] >= 0.015,  # 5日收益超过1.5%
            '中期获利': data['累积收益20日'] >= 0.03   # 20日收益超过3%
        }

        # 动态仓位管理
        for i, date in enumerate(data.index):
            if i == 0:
                continue

            current_pos = position.iloc[i-1]

            # 买入逻辑 - 分层加仓
            if buy_conditions['极端低点'].iloc[i]:
                new_pos = min(1.0, current_pos + 0.4)  # 极端低点大幅加仓
                buy_signals.iloc[i] = True
            elif buy_conditions['低点'].iloc[i]:
                new_pos = min(1.0, current_pos + 0.3)  # 低点中等加仓
                buy_signals.iloc[i] = True
            elif buy_conditions['较低点'].iloc[i] and current_pos < 0.3:
                new_pos = min(0.5, current_pos + 0.2)  # 较低点小幅加仓
                buy_signals.iloc[i] = True
            elif buy_conditions['百分位极低'].iloc[i]:
                new_pos = min(1.0, current_pos + 0.3)
                buy_signals.iloc[i] = True
            elif buy_conditions['百分位低'].iloc[i] and current_pos < 0.5:
                new_pos = min(0.7, current_pos + 0.2)
                buy_signals.iloc[i] = True
            else:
                new_pos = current_pos

            # 卖出逻辑 - 分层减仓
            if sell_conditions['极端高点'].iloc[i]:
                new_pos = max(0.0, current_pos - 0.5)  # 极端高点大幅减仓
                sell_signals.iloc[i] = True
            elif sell_conditions['高点'].iloc[i]:
                new_pos = max(0.0, current_pos - 0.3)  # 高点中等减仓
                sell_signals.iloc[i] = True
            elif sell_conditions['较高点'].iloc[i] and current_pos > 0.7:
                new_pos = max(0.5, current_pos - 0.2)  # 较高点小幅减仓
                sell_signals.iloc[i] = True
            elif sell_conditions['百分位极高'].iloc[i]:
                new_pos = max(0.0, current_pos - 0.4)
                sell_signals.iloc[i] = True
            elif sell_conditions['百分位高'].iloc[i] and current_pos > 0.6:
                new_pos = max(0.3, current_pos - 0.3)
                sell_signals.iloc[i] = True
            elif sell_conditions['价格高位'].iloc[i] and current_pos > 0.8:
                new_pos = max(0.5, current_pos - 0.3)
                sell_signals.iloc[i] = True
            elif sell_conditions['短期获利'].iloc[i] and current_pos > 0.5:
                new_pos = max(0.3, current_pos - 0.2)  # 短期获利了结
                sell_signals.iloc[i] = True
            elif sell_conditions['中期获利'].iloc[i] and current_pos > 0.7:
                new_pos = max(0.4, current_pos - 0.3)  # 中期获利了结
                sell_signals.iloc[i] = True

            position.iloc[i] = new_pos

        return {
            'position': position,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals,
            'buy_conditions': buy_conditions,
            'sell_conditions': sell_conditions
        }

    def calculate_strategy_performance(self, strategy_data, strategy_name="动态策略"):
        """计算策略表现"""
        print(f"\n📈 计算{strategy_name}表现...")

        price = self.processed_data['价格']
        position = strategy_data['position']

        # 计算每日收益
        daily_price_returns = price.pct_change().fillna(0)

        # 策略收益 = 仓位 × 价格收益 + (1-仓位) × 现金收益(假设0)
        strategy_daily_returns = position.shift(1) * daily_price_returns
        strategy_daily_returns = strategy_daily_returns.fillna(0)

        # 累积收益
        strategy_cumulative = (1 + strategy_daily_returns).cumprod()
        buy_hold_cumulative = price / price.iloc[0]

        # 计算表现指标
        total_return = strategy_cumulative.iloc[-1] - 1
        annual_return = (strategy_cumulative.iloc[-1] ** (252 / len(strategy_cumulative))) - 1

        # 买入持有基准
        buy_hold_total = buy_hold_cumulative.iloc[-1] - 1
        buy_hold_annual = (buy_hold_cumulative.iloc[-1] ** (252 / len(buy_hold_cumulative))) - 1

        # 波动率
        strategy_volatility = strategy_daily_returns.std() * np.sqrt(252)

        # 最大回撤
        rolling_max = strategy_cumulative.expanding().max()
        drawdown = (strategy_cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()

        # 夏普比率
        sharpe_ratio = annual_return / strategy_volatility if strategy_volatility > 0 else 0

        # 交易统计
        buy_count = strategy_data['buy_signals'].sum()
        sell_count = strategy_data['sell_signals'].sum()
        avg_position = position.mean()

        # 计算持仓时间分布
        position_changes = position.diff().abs()
        active_days = (position_changes > 0.01).sum()  # 仓位变化超过1%的天数

        metrics = {
            '总收益率': total_return,
            '年化收益率': annual_return,
            '年化波动率': strategy_volatility,
            '夏普比率': sharpe_ratio,
            '最大回撤': max_drawdown,
            '买入持有年化收益': buy_hold_annual,
            '超额收益': annual_return - buy_hold_annual,
            '平均仓位': avg_position,
            '买入次数': buy_count,
            '卖出次数': sell_count,
            '调仓次数': active_days,
            '累积收益序列': strategy_cumulative,
            '回撤序列': drawdown,
            '仓位序列': position
        }

        self.strategy_results[strategy_name] = metrics

        print(f"✅ {strategy_name}表现计算完成")
        return metrics

    def create_comparison_strategies(self):
        """创建多个策略进行对比"""
        print(f"\n🔄 创建多个策略进行对比...")

        strategies = {}

        # 策略1: 原始固定持有期策略
        strategies['固定持有60天'] = self.create_fixed_hold_strategy(60)

        # 策略2: 优化的动态策略
        strategies['动态策略'] = self.create_dynamic_strategy()

        # 策略3: 保守动态策略
        strategies['保守动态策略'] = self.create_conservative_dynamic_strategy()

        # 策略4: 积极动态策略
        strategies['积极动态策略'] = self.create_aggressive_dynamic_strategy()

        return strategies

    def create_fixed_hold_strategy(self, hold_days=60):
        """创建固定持有期策略（用于对比）"""
        data = self.processed_data.copy()

        position = pd.Series(0.0, index=data.index)
        buy_signals = pd.Series(False, index=data.index)
        sell_signals = pd.Series(False, index=data.index)

        # 简单的买入信号
        buy_condition = data['Z_Score'] <= -1.5

        current_position = 0
        hold_until = None

        for i, date in enumerate(data.index):
            if hold_until is not None and date >= hold_until:
                current_position = 0
                hold_until = None
                sell_signals.iloc[i] = True

            if buy_condition.iloc[i] and current_position == 0:
                current_position = 1
                buy_signals.iloc[i] = True

                # 计算卖出日期
                try:
                    sell_date_idx = i + hold_days
                    if sell_date_idx < len(data.index):
                        hold_until = data.index[sell_date_idx]
                except:
                    hold_until = None
                    current_position = 0

            position.iloc[i] = current_position

        return {
            'position': position,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }

    def create_conservative_dynamic_strategy(self):
        """创建保守的动态策略"""
        data = self.processed_data.copy()

        position = pd.Series(0.0, index=data.index)
        buy_signals = pd.Series(False, index=data.index)
        sell_signals = pd.Series(False, index=data.index)

        for i, date in enumerate(data.index):
            if i == 0:
                continue

            current_pos = position.iloc[i-1]

            # 保守买入：只在极端低点买入
            if data['Z_Score'].iloc[i] <= -2.0:
                new_pos = min(0.8, current_pos + 0.4)
                buy_signals.iloc[i] = True
            elif data['Z_Score'].iloc[i] <= -1.5 and current_pos < 0.3:
                new_pos = min(0.5, current_pos + 0.2)
                buy_signals.iloc[i] = True
            else:
                new_pos = current_pos

            # 保守卖出：在高点或获利时卖出
            if data['Z_Score'].iloc[i] >= 1.5:
                new_pos = max(0.0, current_pos - 0.5)
                sell_signals.iloc[i] = True
            elif data['累积收益20日'].iloc[i] >= 0.025 and current_pos > 0.5:
                new_pos = max(0.2, current_pos - 0.3)
                sell_signals.iloc[i] = True

            position.iloc[i] = new_pos

        return {
            'position': position,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }

    def create_aggressive_dynamic_strategy(self):
        """创建积极的动态策略"""
        data = self.processed_data.copy()

        position = pd.Series(0.0, index=data.index)
        buy_signals = pd.Series(False, index=data.index)
        sell_signals = pd.Series(False, index=data.index)

        for i, date in enumerate(data.index):
            if i == 0:
                continue

            current_pos = position.iloc[i-1]

            # 积极买入：在多个条件下买入
            if data['Z_Score'].iloc[i] <= -1.0:
                new_pos = min(1.0, current_pos + 0.3)
                buy_signals.iloc[i] = True
            elif data['百分位数'].iloc[i] <= 0.15:
                new_pos = min(1.0, current_pos + 0.2)
                buy_signals.iloc[i] = True
            else:
                new_pos = current_pos

            # 积极卖出：在多个条件下卖出
            if data['Z_Score'].iloc[i] >= 1.0:
                new_pos = max(0.0, current_pos - 0.4)
                sell_signals.iloc[i] = True
            elif data['百分位数'].iloc[i] >= 0.85:
                new_pos = max(0.0, current_pos - 0.3)
                sell_signals.iloc[i] = True
            elif data['累积收益5日'].iloc[i] >= 0.01 and current_pos > 0.7:
                new_pos = max(0.4, current_pos - 0.3)
                sell_signals.iloc[i] = True

            position.iloc[i] = new_pos

        return {
            'position': position,
            'buy_signals': buy_signals,
            'sell_signals': sell_signals
        }

    def run_comprehensive_analysis(self):
        """运行全面的策略对比分析"""
        print(f"\n🚀 开始全面的策略对比分析...")

        # 创建所有策略
        strategies = self.create_comparison_strategies()

        # 计算每个策略的表现
        for strategy_name, strategy_data in strategies.items():
            self.calculate_strategy_performance(strategy_data, strategy_name)

        # 打印对比结果
        self.print_strategy_comparison()

        # 创建可视化
        # self.create_comprehensive_visualization(strategies)  # 暂时注释掉

        return strategies

    def print_strategy_comparison(self):
        """打印策略对比结果"""
        print(f"\n📊 策略对比结果总结")
        print("="*100)

        # 创建对比表
        comparison_data = []
        for strategy_name, metrics in self.strategy_results.items():
            comparison_data.append({
                '策略': strategy_name,
                '年化收益': f"{metrics['年化收益率']:.2%}",
                '最大回撤': f"{metrics['最大回撤']:.2%}",
                '夏普比率': f"{metrics['夏普比率']:.2f}",
                '超额收益': f"{metrics['超额收益']:.2%}",
                '平均仓位': f"{metrics['平均仓位']:.1%}",
                '调仓次数': f"{metrics['调仓次数']}次"
            })

        # 打印表格
        if comparison_data:
            df = pd.DataFrame(comparison_data)
            print(df.to_string(index=False))

        # 买入持有基准
        if self.strategy_results:
            first_strategy = list(self.strategy_results.keys())[0]
            buy_hold_return = self.strategy_results[first_strategy]['买入持有年化收益']
            print(f"\n📈 买入持有基准: {buy_hold_return:.2%}")

        # 找出最佳策略
        print(f"\n🏆 最佳策略分析:")

        best_return = max(self.strategy_results.items(), key=lambda x: x[1]['年化收益率'])
        best_sharpe = max(self.strategy_results.items(), key=lambda x: x[1]['夏普比率'])
        best_drawdown = min(self.strategy_results.items(), key=lambda x: x[1]['最大回撤'])

        print(f"   最高收益: {best_return[0]} ({best_return[1]['年化收益率']:.2%})")
        print(f"   最高夏普: {best_sharpe[0]} ({best_sharpe[1]['夏普比率']:.2f})")
        print(f"   最低回撤: {best_drawdown[0]} ({best_drawdown[1]['最大回撤']:.2%})")

def main():
    """主函数"""
    print("🚀 优化卖出策略分析器启动")
    print("="*80)

    # 创建分析器实例
    analyzer = OptimizedExitStrategy()

    # 加载数据
    if not analyzer.load_data():
        return None

    # 准备数据
    if not analyzer.prepare_data():
        return None

    # 运行全面分析
    strategies = analyzer.run_comprehensive_analysis()

    print(f"\n✅ 优化卖出策略分析完成！")
    print("="*80)

    return analyzer

if __name__ == "__main__":
    analyzer = main()