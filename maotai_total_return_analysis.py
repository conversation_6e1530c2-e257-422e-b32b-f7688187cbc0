#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
茅台过去5年总回报分析（包含分红）

该脚本计算贵州茅台(600519)过去5年的总回报，包括：
1. 股价涨幅
2. 分红收益
3. 复合年化收益率
4. 与基准指数对比
"""

import os
import sys
import pandas as pd
import numpy as np
import akshare as ak
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import logging

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class MaotaiTotalReturnAnalyzer:
    def __init__(self):
        self.stock_code = "600519"
        self.stock_name = "贵州茅台"
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=5*365 + 30)  # 5年多一点，确保有足够数据
        
        self.stock_data = None
        self.dividend_data = None
        self.benchmark_data = None
        
    def get_stock_price_data(self):
        """获取茅台股价数据"""
        logger.info("获取茅台股价数据...")
        
        try:
            # 获取前复权股价数据
            start_str = self.start_date.strftime('%Y%m%d')
            end_str = self.end_date.strftime('%Y%m%d')
            
            df = ak.stock_zh_a_hist(
                symbol=self.stock_code, 
                period="daily", 
                start_date=start_str, 
                end_date=end_str, 
                adjust="qfq"  # 前复权
            )
            
            if df is not None and not df.empty:
                df['日期'] = pd.to_datetime(df['日期'])
                df.set_index('日期', inplace=True)
                df = df.sort_index()
                
                # 重命名列
                df = df.rename(columns={
                    '收盘': 'close',
                    '开盘': 'open', 
                    '最高': 'high',
                    '最低': 'low',
                    '成交量': 'volume',
                    '成交额': 'amount',
                    '振幅': 'amplitude',
                    '涨跌幅': 'pct_change',
                    '涨跌额': 'change',
                    '换手率': 'turnover'
                })
                
                self.stock_data = df
                logger.info(f"成功获取茅台股价数据: {len(df)} 条记录")
                logger.info(f"数据期间: {df.index[0].strftime('%Y-%m-%d')} 至 {df.index[-1].strftime('%Y-%m-%d')}")
                return True
            else:
                logger.error("未获取到茅台股价数据")
                return False
                
        except Exception as e:
            logger.error(f"获取茅台股价数据失败: {str(e)}")
            return False
    
    def get_dividend_data(self):
        """获取茅台分红数据"""
        logger.info("获取茅台分红数据...")
        
        try:
            # 使用akshare获取分红数据
            df = ak.stock_fhps_detail_em(symbol=self.stock_code)
            
            if df is not None and not df.empty:
                # 处理分红数据
                df = df.copy()
                
                # 转换日期列
                if '除权除息日' in df.columns:
                    df['除权除息日'] = pd.to_datetime(df['除权除息日'], errors='coerce')
                    df = df.dropna(subset=['除权除息日'])
                    df.set_index('除权除息日', inplace=True)
                    df = df.sort_index()
                    
                    # 筛选过去5年的分红数据
                    five_years_ago = self.end_date - timedelta(days=5*365)
                    df = df[df.index >= five_years_ago]
                    
                    self.dividend_data = df
                    logger.info(f"成功获取茅台分红数据: {len(df)} 条记录")
                    
                    if len(df) > 0:
                        logger.info("过去5年分红记录:")
                        for idx, row in df.iterrows():
                            cash_dividend = row.get('现金分红-现金分红比例', 0)
                            logger.info(f"  {idx.strftime('%Y-%m-%d')}: 每股分红 {cash_dividend} 元")
                    
                    return True
                else:
                    logger.warning("分红数据中未找到除权除息日列")
                    self.dividend_data = pd.DataFrame()
                    return True
            else:
                logger.warning("未获取到分红数据")
                self.dividend_data = pd.DataFrame()
                return True
                
        except Exception as e:
            logger.error(f"获取分红数据失败: {str(e)}")
            self.dividend_data = pd.DataFrame()
            return True  # 即使分红数据获取失败，也继续分析
    
    def get_benchmark_data(self):
        """获取基准指数数据（沪深300）"""
        logger.info("获取沪深300指数数据作为基准...")
        
        try:
            # 获取沪深300指数数据
            start_str = self.start_date.strftime('%Y%m%d')
            end_str = self.end_date.strftime('%Y%m%d')
            
            df = ak.stock_zh_index_daily(symbol="sh000300")
            
            if df is not None and not df.empty:
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                df = df.sort_index()
                
                # 筛选时间范围
                df = df[df.index >= self.start_date]
                df = df[df.index <= self.end_date]
                
                self.benchmark_data = df
                logger.info(f"成功获取沪深300指数数据: {len(df)} 条记录")
                return True
            else:
                logger.warning("未获取到沪深300指数数据")
                return False
                
        except Exception as e:
            logger.error(f"获取基准指数数据失败: {str(e)}")
            return False
    
    def calculate_total_return(self):
        """计算总回报"""
        logger.info("计算茅台过去5年总回报...")
        
        if self.stock_data is None or self.stock_data.empty:
            logger.error("没有股价数据，无法计算回报")
            return None
        
        # 确定5年前的日期
        five_years_ago = self.end_date - timedelta(days=5*365)
        
        # 找到最接近5年前的交易日
        stock_data_5y = self.stock_data[self.stock_data.index >= five_years_ago]
        
        if stock_data_5y.empty:
            logger.error("没有足够的历史数据计算5年回报")
            return None
        
        start_price = stock_data_5y['close'].iloc[0]
        end_price = stock_data_5y['close'].iloc[-1]
        start_date_actual = stock_data_5y.index[0]
        end_date_actual = stock_data_5y.index[-1]
        
        logger.info(f"计算期间: {start_date_actual.strftime('%Y-%m-%d')} 至 {end_date_actual.strftime('%Y-%m-%d')}")
        logger.info(f"起始价格: {start_price:.2f} 元")
        logger.info(f"结束价格: {end_price:.2f} 元")
        
        # 计算股价涨幅
        price_return = (end_price - start_price) / start_price
        
        # 计算分红收益
        dividend_return = 0
        total_dividends = 0
        
        if self.dividend_data is not None and not self.dividend_data.empty:
            # 筛选计算期间内的分红
            period_dividends = self.dividend_data[
                (self.dividend_data.index >= start_date_actual) & 
                (self.dividend_data.index <= end_date_actual)
            ]
            
            for idx, row in period_dividends.iterrows():
                cash_dividend = row.get('现金分红-现金分红比例', 0)
                if pd.notna(cash_dividend) and cash_dividend > 0:
                    total_dividends += cash_dividend
            
            # 分红收益率 = 总分红 / 起始价格
            dividend_return = total_dividends / start_price
        
        # 总回报 = 股价回报 + 分红回报
        total_return = price_return + dividend_return
        
        # 计算年化收益率
        years = (end_date_actual - start_date_actual).days / 365.25
        annual_return = (1 + total_return) ** (1/years) - 1
        
        results = {
            'start_date': start_date_actual,
            'end_date': end_date_actual,
            'start_price': start_price,
            'end_price': end_price,
            'price_return': price_return,
            'total_dividends': total_dividends,
            'dividend_return': dividend_return,
            'total_return': total_return,
            'annual_return': annual_return,
            'years': years
        }
        
        return results
    
    def calculate_benchmark_return(self):
        """计算基准指数回报"""
        if self.benchmark_data is None or self.benchmark_data.empty:
            return None
        
        five_years_ago = self.end_date - timedelta(days=5*365)
        benchmark_5y = self.benchmark_data[self.benchmark_data.index >= five_years_ago]
        
        if benchmark_5y.empty:
            return None
        
        start_value = benchmark_5y['close'].iloc[0]
        end_value = benchmark_5y['close'].iloc[-1]
        
        benchmark_return = (end_value - start_value) / start_value
        years = (benchmark_5y.index[-1] - benchmark_5y.index[0]).days / 365.25
        benchmark_annual = (1 + benchmark_return) ** (1/years) - 1
        
        return {
            'total_return': benchmark_return,
            'annual_return': benchmark_annual
        }
    
    def print_results(self, results, benchmark_results=None):
        """打印分析结果"""
        print("\n" + "="*60)
        print(f"🍷 {self.stock_name}({self.stock_code}) 过去5年总回报分析")
        print("="*60)
        
        print(f"\n📅 分析期间:")
        print(f"   起始日期: {results['start_date'].strftime('%Y年%m月%d日')}")
        print(f"   结束日期: {results['end_date'].strftime('%Y年%m月%d日')}")
        print(f"   投资年限: {results['years']:.2f} 年")
        
        print(f"\n💰 价格变化:")
        print(f"   起始价格: ¥{results['start_price']:,.2f}")
        print(f"   结束价格: ¥{results['end_price']:,.2f}")
        print(f"   股价涨幅: {results['price_return']:.2%}")
        
        print(f"\n🎁 分红收益:")
        print(f"   累计分红: ¥{results['total_dividends']:.2f}/股")
        print(f"   分红收益率: {results['dividend_return']:.2%}")
        
        print(f"\n📊 总回报:")
        print(f"   总收益率: {results['total_return']:.2%}")
        print(f"   年化收益率: {results['annual_return']:.2%}")
        
        if benchmark_results:
            print(f"\n📈 与沪深300对比:")
            print(f"   沪深300总收益率: {benchmark_results['total_return']:.2%}")
            print(f"   沪深300年化收益率: {benchmark_results['annual_return']:.2%}")
            excess_return = results['annual_return'] - benchmark_results['annual_return']
            print(f"   超额年化收益: {excess_return:.2%}")
        
        print(f"\n💡 投资收益示例:")
        initial_investment = 100000  # 10万元
        final_value = initial_investment * (1 + results['total_return'])
        profit = final_value - initial_investment
        print(f"   如果5年前投资 ¥{initial_investment:,}")
        print(f"   现在价值: ¥{final_value:,.0f}")
        print(f"   总盈利: ¥{profit:,.0f}")
        
        print("="*60)
    
    def run_analysis(self):
        """运行完整分析"""
        logger.info("开始茅台总回报分析...")
        
        # 获取数据
        if not self.get_stock_price_data():
            return False
        
        self.get_dividend_data()
        self.get_benchmark_data()
        
        # 计算回报
        results = self.calculate_total_return()
        if results is None:
            logger.error("计算总回报失败")
            return False
        
        benchmark_results = self.calculate_benchmark_return()
        
        # 打印结果
        self.print_results(results, benchmark_results)
        
        return True

def main():
    analyzer = MaotaiTotalReturnAnalyzer()
    success = analyzer.run_analysis()
    
    if success:
        logger.info("分析完成!")
    else:
        logger.error("分析失败!")

if __name__ == "__main__":
    main()
