#!/usr/bin/env python3
"""
恒生指数成分股每日股息率计算器 - 单独文件输出版本
为每只股票单独生成股息率数据文件
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from hsi_daily_dividend_yield_calculator import HSIDailyDividendYieldCalculator
import time

class HSIIndividualStockDividendYieldCalculator:
    """
    恒生指数成分股股息率计算器 - 单独文件输出版本
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841", 
                 cache_dir: str = "cache", 
                 output_dir: str = "hsi_individual_dividend_yields"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.output_dir = output_dir
        self.calculator = HSIDailyDividendYieldCalculator(dividend_data_dir, cache_dir)
        self.results = {}
        self.failed_stocks = []
        self.summary_stats = []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def get_stock_list(self):
        """获取所有股票列表"""
        print("📁 获取股票列表...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        stock_list = []
        
        for file in dividend_files:
            stock_code = file.split('_')[0]
            stock_name = file.split('_')[1].replace('.json', '')
            stock_list.append({
                'code': stock_code,
                'name': stock_name,
                'file': file
            })
        
        # 按股票代码排序
        stock_list.sort(key=lambda x: x['code'])
        
        print(f"✅ 找到 {len(stock_list)} 只股票")
        return stock_list
    
    def save_individual_stock_data(self, stock_code, stock_name, yield_series, stats):
        """保存单只股票的数据到独立文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建详细数据文件
        filename = f"{stock_code}_{stock_name}_dividend_yield_{timestamp}.csv"
        filepath = os.path.join(self.output_dir, filename)
        
        # 获取价格数据用于完整分析
        price_data = self.calculator.get_stock_price_data(stock_code)
        
        if price_data is not None:
            # 创建完整的分析DataFrame
            analysis_df = pd.DataFrame({
                '日期': yield_series.index,
                '股价(港元)': price_data['close'].reindex(yield_series.index),
                '开盘价(港元)': price_data['open'].reindex(yield_series.index),
                '最高价(港元)': price_data['high'].reindex(yield_series.index),
                '最低价(港元)': price_data['low'].reindex(yield_series.index),
                '成交量': price_data['volume'].reindex(yield_series.index),
                '股息率(%)': yield_series.values,
                '滚动12月分红总额(港元)': [self.calculator.calculate_rolling_12m_dividends(stock_code, date) 
                                    for date in yield_series.index]
            })
        else:
            # 如果没有价格数据，只保存股息率
            analysis_df = pd.DataFrame({
                '日期': yield_series.index,
                '股息率(%)': yield_series.values,
                '滚动12月分红总额(港元)': [self.calculator.calculate_rolling_12m_dividends(stock_code, date) 
                                    for date in yield_series.index]
            })
        
        # 保存到CSV文件
        analysis_df.to_csv(filepath, index=False, encoding='utf-8-sig')
        
        # 创建股票信息摘要文件
        summary_filename = f"{stock_code}_{stock_name}_summary_{timestamp}.txt"
        summary_filepath = os.path.join(self.output_dir, summary_filename)
        
        with open(summary_filepath, 'w', encoding='utf-8') as f:
            f.write(f"股票代码: {stock_code}\n")
            f.write(f"股票名称: {stock_name}\n")
            f.write(f"数据期间: {stats['data_start']} 至 {stats['data_end']}\n")
            f.write(f"总交易日数: {stats['total_days']}\n")
            f.write(f"有分红天数: {stats['dividend_days']}\n")
            f.write(f"数据覆盖率: {stats['coverage_rate']:.1f}%\n")
            f.write(f"平均股息率: {stats['avg_yield']:.4f}%\n")
            f.write(f"最大股息率: {stats['max_yield']:.4f}%\n")
            f.write(f"最小股息率: {stats['min_yield']:.4f}%\n")
            f.write(f"标准差: {stats['std_yield']:.4f}%\n")
            f.write(f"最新股息率: {stats['latest_yield']:.4f}%\n")
            f.write(f"\n分红历史:\n")
            
            # 添加分红历史信息
            if stock_code in self.calculator.dividend_data:
                dividend_history = self.calculator.dividend_data[stock_code]['dividend_history']
                for div in dividend_history:
                    f.write(f"  {div['year']}年: {div['ex_date'].strftime('%Y-%m-%d')} 除权, 分红 {div['amount']} 港元\n")
        
        print(f"   💾 已保存: {filename}")
        print(f"   📄 摘要文件: {summary_filename}")
        
        return filepath, summary_filepath
    
    def calculate_single_stock(self, stock_info):
        """计算单只股票的股息率并保存到独立文件"""
        stock_code = stock_info['code']
        stock_name = stock_info['name']
        
        try:
            print(f"📈 计算 {stock_code} ({stock_name})...")
            
            # 使用已有的计算器
            yield_series = self.calculator.calculate_daily_dividend_yield(stock_code)
            
            if yield_series is not None and len(yield_series) > 0:
                # 计算基本统计信息
                valid_yields = yield_series.dropna()
                non_zero_yields = valid_yields[valid_yields > 0]
                
                stats = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'total_days': len(valid_yields),
                    'dividend_days': len(non_zero_yields),
                    'coverage_rate': len(non_zero_yields) / len(valid_yields) * 100 if len(valid_yields) > 0 else 0,
                    'avg_yield': non_zero_yields.mean() if len(non_zero_yields) > 0 else 0,
                    'max_yield': non_zero_yields.max() if len(non_zero_yields) > 0 else 0,
                    'min_yield': non_zero_yields.min() if len(non_zero_yields) > 0 else 0,
                    'std_yield': non_zero_yields.std() if len(non_zero_yields) > 0 else 0,
                    'latest_yield': valid_yields.iloc[-1] if len(valid_yields) > 0 else 0,
                    'data_start': valid_yields.index.min() if len(valid_yields) > 0 else None,
                    'data_end': valid_yields.index.max() if len(valid_yields) > 0 else None
                }
                
                # 保存到独立文件
                data_file, summary_file = self.save_individual_stock_data(
                    stock_code, stock_name, yield_series, stats
                )
                
                # 记录结果
                self.results[stock_code] = {
                    'yield_series': yield_series,
                    'stats': stats,
                    'data_file': data_file,
                    'summary_file': summary_file
                }
                
                self.summary_stats.append(stats)
                
                print(f"   ✅ 成功: 平均股息率 {stats['avg_yield']:.4f}%, 覆盖率 {stats['coverage_rate']:.1f}%")
                return True
                
            else:
                print(f"   ❌ 无有效数据")
                self.failed_stocks.append({
                    'code': stock_code,
                    'name': stock_name,
                    'reason': '无有效股息率数据'
                })
                return False
                
        except Exception as e:
            print(f"   ❌ 计算失败: {e}")
            self.failed_stocks.append({
                'code': stock_code,
                'name': stock_name,
                'reason': str(e)
            })
            return False
    
    def calculate_all_stocks(self):
        """计算所有股票的股息率并保存到独立文件"""
        print("🚀 开始计算所有恒生指数成分股股息率 - 独立文件输出")
        print("=" * 60)
        
        # 加载分红数据
        self.calculator.load_dividend_data()
        
        # 获取股票列表
        stock_list = self.get_stock_list()
        
        print(f"\n🔄 开始批量计算...")
        print(f"📁 输出目录: {self.output_dir}")
        start_time = datetime.now()
        
        successful_count = 0
        total_count = len(stock_list)
        
        for i, stock_info in enumerate(stock_list, 1):
            print(f"\n[{i}/{total_count}] ", end="")
            
            if self.calculate_single_stock(stock_info):
                successful_count += 1
            
            # 每10只股票显示一次进度
            if i % 10 == 0:
                elapsed = datetime.now() - start_time
                avg_time = elapsed.total_seconds() / i
                remaining = (total_count - i) * avg_time
                print(f"\n📊 进度: {i}/{total_count} ({i/total_count*100:.1f}%), "
                      f"成功: {successful_count}, "
                      f"预计剩余: {remaining/60:.1f}分钟")
        
        end_time = datetime.now()
        total_time = end_time - start_time
        
        print(f"\n🎉 计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {successful_count}/{total_count} ({successful_count/total_count*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")
        print(f"   输出文件数: {successful_count * 2}")  # 每只股票2个文件
        
        return self.results
    
    def create_master_summary(self):
        """创建总体摘要文件"""
        if not self.summary_stats:
            print("❌ 没有统计数据可汇总")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 创建统计摘要CSV
        stats_df = pd.DataFrame(self.summary_stats)
        stats_df = stats_df.sort_values('avg_yield', ascending=False)
        stats_file = os.path.join(self.output_dir, f"hsi_all_stocks_summary_{timestamp}.csv")
        stats_df.to_csv(stats_file, index=False, encoding='utf-8-sig')
        
        # 创建文件清单
        file_list_path = os.path.join(self.output_dir, f"file_list_{timestamp}.txt")
        with open(file_list_path, 'w', encoding='utf-8') as f:
            f.write("恒生指数成分股股息率数据文件清单\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"总股票数: {len(self.results)}\n")
            f.write(f"总文件数: {len(self.results) * 2}\n\n")
            
            f.write("股票列表:\n")
            for stock_code, data in self.results.items():
                stats = data['stats']
                f.write(f"{stock_code} ({stats['stock_name']}): 平均股息率 {stats['avg_yield']:.4f}%\n")
            
            f.write(f"\n详细文件:\n")
            for stock_code, data in self.results.items():
                f.write(f"数据文件: {os.path.basename(data['data_file'])}\n")
                f.write(f"摘要文件: {os.path.basename(data['summary_file'])}\n")
                f.write("-" * 30 + "\n")
        
        print(f"📊 总体摘要已保存:")
        print(f"   统计文件: {stats_file}")
        print(f"   文件清单: {file_list_path}")
        
        return stats_file, file_list_path

def main():
    """主函数"""
    print("🚀 恒生指数成分股股息率计算器 - 独立文件输出版本")
    print("=" * 60)
    
    # 创建计算器
    calculator = HSIIndividualStockDividendYieldCalculator(
        dividend_data_dir="hsi_dividend_data_20250603_173841",
        output_dir="hsi_individual_dividend_yields"
    )
    
    try:
        # 计算所有股票
        results = calculator.calculate_all_stocks()
        
        if results:
            # 创建总体摘要
            summary_files = calculator.create_master_summary()
            
            # 显示汇总信息
            print(f"\n📈 计算结果汇总:")
            print(f"   成功处理股票数: {len(results)}")
            print(f"   输出目录: {calculator.output_dir}")
            print(f"   每只股票包含: 数据文件(.csv) + 摘要文件(.txt)")
            
            # 显示前10只高股息率股票
            if calculator.summary_stats:
                avg_yields = [s['avg_yield'] for s in calculator.summary_stats if s['avg_yield'] > 0]
                if avg_yields:
                    print(f"   股息率范围: {min(avg_yields):.4f}% - {max(avg_yields):.4f}%")
                    
                    print(f"\n🏆 股息率最高的前10只股票:")
                    sorted_stats = sorted(calculator.summary_stats, key=lambda x: x['avg_yield'], reverse=True)
                    for i, stats in enumerate(sorted_stats[:10], 1):
                        if stats['avg_yield'] > 0:
                            print(f"   {i:2d}. {stats['stock_code']} ({stats['stock_name']}): {stats['avg_yield']:.4f}%")
            
            print(f"\n✅ 所有文件已生成完成!")
        else:
            print("❌ 没有成功计算任何股票")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
