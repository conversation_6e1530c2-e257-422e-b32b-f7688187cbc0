#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股反转因子回测分析
基于历史价格数据计算反转因子进行因子有效性验证
"""

import os
import json
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIReversalFactorBacktest:
    """恒生指数反转因子回测器"""

    def __init__(self, cache_dir: str = "hsi_cache_5y", rebalance_freq: str = 'M',
                 enable_short: bool = False, n_groups: int = 5):
        """
        初始化回测器

        Args:
            cache_dir: 价格数据缓存目录
            rebalance_freq: 再平衡频率 ('M'=月度, 'Q'=季度, 'H'=半年, 'Y'=年度)
            enable_short: 是否允许做空
            n_groups: 分组数量
        """
        self.cache_dir = cache_dir
        self.stock_data = {}
        self.reversal_data = {}
        self.backtest_results = {}
        self.hsi_constituents = []

        # 回测参数
        self.rebalance_freq = rebalance_freq
        self.enable_short = enable_short
        self.n_groups = n_groups
        self.lookback_days = 252   # 1年回看期
        
        # 根据再平衡频率调整前瞻期
        if rebalance_freq == 'M':
            self.forward_days = 21     # 1个月前瞻期
        elif rebalance_freq == 'Q':
            self.forward_days = 63     # 3个月前瞻期
        elif rebalance_freq == 'H':
            self.forward_days = 126    # 6个月前瞻期
        else:  # 'Y'
            self.forward_days = 252    # 1年前瞻期
        
        freq_names = {'M': '月度', 'Q': '季度', 'H': '半年度', 'Y': '年度'}
        print("🚀 恒生指数反转因子回测器已初始化")
        print(f"📁 缓存目录: {cache_dir}")
        print(f"🔄 再平衡频率: {freq_names.get(rebalance_freq, rebalance_freq)}")
        print(f"📊 分组数量: {n_groups}")
        print(f"🔄 做空策略: {'启用' if enable_short else '禁用'}")
    
    def load_hsi_constituents(self) -> bool:
        """加载恒生指数成分股列表"""
        try:
            csv_file = 'data_files/hsi_constituents.csv'
            print(f"📁 从 {csv_file} 加载恒生指数成分股...")
            
            df = pd.read_csv(csv_file, dtype={'代码': str})
            
            # 格式化股票代码
            hsi_stocks = []
            for _, row in df.iterrows():
                code = str(row['代码']).strip()
                name = str(row['名称']).strip()
                if code and code != 'nan':
                    code_formatted = str(code).zfill(5)  # 补齐到5位数
                    hsi_stocks.append({
                        'code': code_formatted,
                        'name': name
                    })
            
            self.hsi_constituents = hsi_stocks
            print(f"✅ 已加载 {len(hsi_stocks)} 只恒生指数成分股")
            return True
            
        except Exception as e:
            print(f"❌ 加载成分股列表失败: {e}")
            return False

    def load_stock_data(self) -> bool:
        """从缓存加载所有股票的价格数据"""
        try:
            print("📊 从缓存加载股票价格数据...")

            if not self.hsi_constituents:
                print("❌ 请先加载恒生指数成分股列表")
                return False

            loaded_count = 0
            for stock_info in self.hsi_constituents:
                stock_code = stock_info['code']
                stock_name = stock_info['name']
                
                # 尝试加载缓存数据
                cache_file = os.path.join(self.cache_dir, f"{stock_code}_5y.pkl")
                
                if os.path.exists(cache_file):
                    try:
                        with open(cache_file, 'rb') as f:
                            price_data = pickle.load(f)
                        
                        # 处理不同的数据格式
                        if isinstance(price_data, pd.Series) and len(price_data) > 100:
                            # 确保索引是datetime类型
                            if not isinstance(price_data.index, pd.DatetimeIndex):
                                price_data.index = pd.to_datetime(price_data.index)
                            
                            self.stock_data[stock_code] = {
                                'name': stock_name,
                                'price': price_data.dropna()
                            }
                            loaded_count += 1
                            
                    except Exception as e:
                        print(f"⚠️  加载 {stock_code} 缓存失败: {e}")
                        continue
            
            print(f"✅ 成功加载 {loaded_count} 只股票的价格数据")
            
            if loaded_count == 0:
                print("❌ 未找到有效的价格数据")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 加载股票数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def calculate_reversal_factors(self) -> bool:
        """计算反转因子"""
        try:
            print("🔄 计算反转因子...")
            
            if not self.stock_data:
                print("❌ 请先加载股票数据")
                return False
            
            reversal_data = {}
            
            for stock_code, stock_info in self.stock_data.items():
                try:
                    prices = stock_info['price']
                    
                    if len(prices) < 500:  # 至少需要500个交易日的数据（约2年）
                        continue
                    
                    # 计算收益率
                    returns = prices.pct_change()
                    
                    # 1. 短期反转因子（负的短期收益率）
                    reversal_5d = -prices.pct_change(5)     # 负的5日收益率
                    reversal_10d = -prices.pct_change(10)   # 负的10日收益率
                    reversal_20d = -prices.pct_change(20)   # 负的1个月收益率
                    reversal_60d = -prices.pct_change(60)   # 负的3个月收益率
                    
                    # 2. 长期反转因子
                    reversal_120d = -prices.pct_change(120) # 负的6个月收益率
                    reversal_252d = -prices.pct_change(252) # 负的12个月收益率
                    
                    # 3. 波动率调整反转因子
                    volatility_20d = returns.rolling(20).std()
                    volatility_60d = returns.rolling(60).std()
                    vol_adj_reversal_20d = reversal_20d / volatility_20d
                    vol_adj_reversal_60d = reversal_60d / volatility_60d
                    
                    # 4. 移动平均反转因子
                    ma_20 = prices.rolling(window=20).mean()
                    ma_60 = prices.rolling(window=60).mean()
                    ma_reversal_20d = -(prices - ma_20) / ma_20  # 负的偏离度
                    ma_reversal_60d = -(prices - ma_60) / ma_60
                    
                    # 5. 复合反转因子（多周期加权平均）
                    # 权重: 短期30%, 中期40%, 长期30%
                    composite_reversal = (
                        0.3 * reversal_20d +
                        0.4 * reversal_60d +
                        0.3 * reversal_120d
                    )
                    
                    # 6. 改进反转因子（基于最近表现差的股票）
                    # 结合短期和中期反转
                    improved_reversal = (
                        0.4 * reversal_20d +
                        0.6 * reversal_60d
                    )
                    
                    # 7. 超跌反转因子（基于连续下跌）
                    # 计算最近N日的累计收益率，取负值
                    cum_return_30d = prices.pct_change(30)
                    oversold_reversal = -cum_return_30d  # 跌得越多，反转信号越强
                    
                    # 创建反转数据DataFrame
                    reversal_df = pd.DataFrame({
                        'reversal_5d': reversal_5d,
                        'reversal_10d': reversal_10d,
                        'reversal_20d': reversal_20d,
                        'reversal_60d': reversal_60d,
                        'reversal_120d': reversal_120d,
                        'reversal_252d': reversal_252d,
                        'vol_adj_reversal_20d': vol_adj_reversal_20d,
                        'vol_adj_reversal_60d': vol_adj_reversal_60d,
                        'ma_reversal_20d': ma_reversal_20d,
                        'ma_reversal_60d': ma_reversal_60d,
                        'composite_reversal': composite_reversal,
                        'improved_reversal': improved_reversal,
                        'oversold_reversal': oversold_reversal
                    }, index=prices.index)
                    
                    reversal_data[stock_code] = reversal_df
                    
                except Exception as e:
                    print(f"⚠️  计算 {stock_code} 反转因子失败: {e}")
                    continue
            
            self.reversal_data = reversal_data
            print(f"✅ 成功计算 {len(reversal_data)} 只股票的反转因子")
            
            if len(reversal_data) == 0:
                print("❌ 未能计算任何反转因子")
                return False
            
            return True

        except Exception as e:
            print(f"❌ 计算反转因子失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def calculate_factor_scores(self, date: datetime, factor_name: str = 'improved_reversal') -> pd.Series:
        """计算指定日期的反转因子得分"""
        scores = {}

        for stock_code in self.reversal_data.keys():
            if stock_code in self.reversal_data:
                reversal_df = self.reversal_data[stock_code]

                # 找到指定日期之前的最新数据
                available_dates = reversal_df.index[reversal_df.index <= date]
                if len(available_dates) > 0:
                    latest_date = available_dates[-1]

                    # 获取反转因子得分
                    if factor_name in reversal_df.columns:
                        reversal_score = reversal_df.loc[latest_date, factor_name]

                        # 确保反转得分有效
                        if pd.notna(reversal_score) and not np.isinf(reversal_score):
                            scores[stock_code] = reversal_score

        return pd.Series(scores)

    def create_portfolios(self, factor_scores: pd.Series) -> Dict[str, Dict]:
        """基于反转因子得分创建投资组合"""
        if len(factor_scores) == 0:
            return {}

        # 按因子得分排序（降序，高反转信号在前）
        sorted_scores = factor_scores.sort_values(ascending=False)

        portfolios = {}

        # 分组
        n_stocks_per_group = len(sorted_scores) // self.n_groups

        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(sorted_scores)
            else:
                end_idx = (i + 1) * n_stocks_per_group

            group_stocks = sorted_scores.iloc[start_idx:end_idx].index.tolist()

            # 为每个组合设置权重和方向
            portfolios[f'Group_{i+1}'] = {
                'stocks': group_stocks,
                'weights': {stock: 1.0/len(group_stocks) for stock in group_stocks},
                'direction': 'long'
            }

        # 添加特殊组合
        total_stocks = len(sorted_scores)

        # 高反转组合（前20%）- 做多
        top_20_count = max(1, int(total_stocks * 0.2))
        top_stocks = sorted_scores.head(top_20_count).index.tolist()
        portfolios['高反转组合'] = {
            'stocks': top_stocks,
            'weights': {stock: 1.0/len(top_stocks) for stock in top_stocks},
            'direction': 'long'
        }

        # 低反转组合（后20%）
        bottom_20_count = max(1, int(total_stocks * 0.2))
        bottom_stocks = sorted_scores.tail(bottom_20_count).index.tolist()
        portfolios['低反转组合'] = {
            'stocks': bottom_stocks,
            'weights': {stock: 1.0/len(bottom_stocks) for stock in bottom_stocks},
            'direction': 'long'
        }

        # 全市场组合（所有股票等权重做多）
        all_stocks = sorted_scores.index.tolist()
        portfolios['全市场组合'] = {
            'stocks': all_stocks,
            'weights': {stock: 1.0/len(all_stocks) for stock in all_stocks},
            'direction': 'long'
        }

        return portfolios

    def calculate_stock_return(self, stock_code: str, start_date: datetime, end_date: datetime) -> float:
        """计算股票在指定期间的收益率"""
        if stock_code not in self.stock_data:
            return 0.0

        price_series = self.stock_data[stock_code]['price']

        # 获取期间数据
        period_data = price_series[(price_series.index >= start_date) & (price_series.index <= end_date)]

        if len(period_data) < 2:
            return 0.0

        # 获取起始和结束价格
        start_price = period_data.iloc[0]
        end_price = period_data.iloc[-1]

        if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
            return 0.0

        # 计算收益率
        return (end_price - start_price) / start_price

    def calculate_portfolio_returns(self, portfolios: Dict[str, Dict],
                                  start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """计算投资组合在指定期间的收益率"""
        portfolio_returns = {}

        for portfolio_name, portfolio_info in portfolios.items():
            stocks = portfolio_info['stocks']
            weights = portfolio_info['weights']

            if not stocks:
                continue

            portfolio_return = 0.0
            valid_stocks = 0

            for stock_code in stocks:
                if stock_code in self.stock_data and stock_code in weights:
                    # 计算股票收益率
                    stock_return = self.calculate_stock_return(
                        stock_code, start_date, end_date
                    )

                    if stock_return != 0.0:  # 有效收益率
                        weight = weights[stock_code]
                        portfolio_return += weight * stock_return
                        valid_stocks += 1

            # 保存组合收益率
            if valid_stocks > 0:
                portfolio_returns[portfolio_name] = portfolio_return
            else:
                portfolio_returns[portfolio_name] = 0.0

        return portfolio_returns

    def get_rebalance_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取再平衡日期"""
        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)

            # 根据再平衡频率确定下一个日期
            if self.rebalance_freq == 'M':  # 月度
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1, day=1)
            elif self.rebalance_freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_month = 6
                elif current_date.month <= 6:
                    next_month = 9
                elif current_date.month <= 9:
                    next_month = 12
                else:
                    next_month = 3
                    current_date = current_date.replace(year=current_date.year + 1)

                if next_month <= 12:
                    current_date = current_date.replace(month=next_month, day=1)
            elif self.rebalance_freq == 'H':  # 半年度
                if current_date.month <= 6:
                    current_date = current_date.replace(month=12, day=1)
                else:
                    current_date = current_date.replace(year=current_date.year + 1, month=6, day=1)
            else:  # 年度
                current_date = current_date.replace(year=current_date.year + 1)

        return dates

    def run_backtest(self, factor_name: str = 'improved_reversal') -> bool:
        """运行反转因子回测"""
        try:
            print(f"🔄 开始反转因子回测（因子: {factor_name}）...")

            # 确定回测期间
            all_dates = set()
            for stock_info in self.stock_data.values():
                all_dates.update(stock_info['price'].index)

            if not all_dates:
                print("❌ 没有可用的日期数据")
                return False

            start_date = min(all_dates) + timedelta(days=self.lookback_days)
            end_date = max(all_dates) - timedelta(days=self.forward_days)

            print(f"📅 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

            # 获取再平衡日期
            rebalance_dates = self.get_rebalance_dates(start_date, end_date)
            print(f"🔄 再平衡次数: {len(rebalance_dates)}")

            # 存储所有组合的收益率
            all_portfolio_returns = defaultdict(list)
            all_factor_scores = []

            for i, rebalance_date in enumerate(rebalance_dates):
                print(f"🔄 再平衡 {i+1}/{len(rebalance_dates)}: {rebalance_date.strftime('%Y-%m-%d')}")

                # 计算因子得分
                factor_scores = self.calculate_factor_scores(rebalance_date, factor_name)
                if factor_scores.empty:
                    print(f"   ⚠️  {rebalance_date.strftime('%Y-%m-%d')} 因子得分为空，跳过")
                    continue

                all_factor_scores.append(factor_scores)

                # 创建投资组合
                portfolios = self.create_portfolios(factor_scores)

                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    hold_end = rebalance_dates[i + 1]
                else:
                    hold_end = end_date

                # 计算期间收益
                period_returns = self.calculate_portfolio_returns(
                    portfolios, rebalance_date, hold_end
                )

                # 累积收益
                for portfolio_name, returns in period_returns.items():
                    all_portfolio_returns[portfolio_name].append(returns)

                # 显示组合信息（仅第一次）
                if i == 0:
                    print("📊 投资组合构成:")
                    for portfolio_name, portfolio_info in portfolios.items():
                        if portfolio_name.startswith('Group_'):
                            stocks = portfolio_info['stocks']
                            avg_reversal = factor_scores[stocks].mean() if stocks else 0
                            print(f"   {portfolio_name}: {len(stocks)}只股票, 平均反转信号: {avg_reversal:.4f}, 策略: 做多")

            # 保存回测结果
            self.backtest_results = {
                'portfolio_returns': dict(all_portfolio_returns),
                'factor_scores': all_factor_scores,
                'rebalance_dates': rebalance_dates,
                'start_date': start_date,
                'end_date': end_date,
                'factor_name': factor_name
            }

            print("✅ 回测完成")
            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def analyze_factor_performance(self) -> Dict:
        """分析因子表现"""
        if not self.backtest_results:
            print("❌ 请先运行回测")
            return {}

        print("📊 分析因子表现...")

        portfolio_returns = self.backtest_results['portfolio_returns']
        analysis_results = {}

        # 计算各组合的统计指标
        for portfolio_name, returns in portfolio_returns.items():
            if not returns or len(returns) == 0:
                continue

            returns_array = np.array(returns)

            # 基本统计
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array) if len(returns_array) > 1 else 0
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0

            # 累积收益
            cumulative_return = np.prod(1 + returns_array) - 1

            # 年化收益率
            if self.rebalance_freq == 'M':
                periods_per_year = 12
            elif self.rebalance_freq == 'Q':
                periods_per_year = 4
            elif self.rebalance_freq == 'H':
                periods_per_year = 2
            else:  # 'Y'
                periods_per_year = 1

            annual_return = (1 + mean_return) ** periods_per_year - 1

            # 最大回撤
            if len(returns_array) > 1:
                cumulative_returns = np.cumprod(1 + returns_array)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max
                max_drawdown = np.min(drawdowns)
            else:
                max_drawdown = 0

            # 胜率
            win_rate = np.sum(returns_array > 0) / len(returns_array)

            analysis_results[portfolio_name] = {
                'mean_return': mean_return,
                'std_return': std_return,
                'sharpe_ratio': sharpe_ratio,
                'cumulative_return': cumulative_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_periods': len(returns)
            }

        return analysis_results

    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数（IC）"""
        if not self.backtest_results:
            return {}

        print("📈 计算信息系数...")

        factor_scores = self.backtest_results['factor_scores']
        ic_values = []

        for i, scores in enumerate(factor_scores):
            if i >= len(factor_scores) - 1:
                break

            # 获取下期收益率
            next_period_returns = {}
            rebalance_date = self.backtest_results['rebalance_dates'][i]
            next_date = self.backtest_results['rebalance_dates'][i + 1] if i + 1 < len(self.backtest_results['rebalance_dates']) else self.backtest_results['end_date']

            for stock_code in scores.index:
                if stock_code in self.stock_data:
                    # 计算股票收益率
                    stock_return = self.calculate_stock_return(
                        stock_code, rebalance_date, next_date
                    )

                    if stock_return != 0.0:
                        next_period_returns[stock_code] = stock_return

            # 计算IC
            if len(next_period_returns) >= 5:  # 至少需要5只股票
                factor_values = []
                return_values = []

                for stock_code in scores.index:
                    if stock_code in next_period_returns:
                        factor_values.append(scores[stock_code])
                        return_values.append(next_period_returns[stock_code])

                if len(factor_values) >= 5:
                    ic = np.corrcoef(factor_values, return_values)[0, 1]
                    if not np.isnan(ic):
                        ic_values.append(ic)

        if ic_values:
            return {
                'ic_mean': np.mean(ic_values),
                'ic_std': np.std(ic_values),
                'ic_ir': np.mean(ic_values) / np.std(ic_values) if np.std(ic_values) > 0 else 0,
                'ic_values': ic_values,
                'ic_positive_rate': np.sum(np.array(ic_values) > 0) / len(ic_values)
            }
        else:
            return {}

    def generate_report(self, analysis_results: Dict, ic_results: Dict):
        """生成分析报告"""
        print("\n" + "="*80)
        print("恒生指数成分股反转因子回测分析报告")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: {self.cache_dir}")
        print(f"分析股票数: {len(self.stock_data)}")
        factor_name = self.backtest_results.get('factor_name', 'improved_reversal')
        print(f"投资策略: 基于{factor_name}反转因子的纯多头策略")
        print(f"分组数量: {self.n_groups}")
        print()

        # 回测基本信息
        if self.backtest_results:
            print("回测基本信息")
            print("-"*40)
            print(f"回测期间: {self.backtest_results['start_date'].strftime('%Y-%m-%d')} 至 {self.backtest_results['end_date'].strftime('%Y-%m-%d')}")
            print(f"再平衡频率: {self.rebalance_freq}")
            print(f"再平衡次数: {len(self.backtest_results['rebalance_dates'])}")
            print()

        # 组合表现分析
        if analysis_results:
            print("投资组合表现分析")
            print("-"*90)
            print(f"{'组合名称':<15} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'最大回撤':<8} {'胜率':<8}")
            print("-"*90)

            # 按预期顺序显示组合
            portfolio_order = [f'Group_{i+1}' for i in range(self.n_groups)]
            portfolio_order.extend(['高反转组合', '低反转组合', '全市场组合'])

            for portfolio_name in portfolio_order:
                if portfolio_name in analysis_results:
                    perf = analysis_results[portfolio_name]
                    print(f"{portfolio_name:<15} {perf['annual_return']*100:<10.2f}% "
                          f"{perf['std_return']*100:<8.2f}% {perf['sharpe_ratio']:<8.2f} "
                          f"{abs(perf['max_drawdown'])*100:<8.2f}% {perf['win_rate']*100:<8.1f}%")
            print()

        # 反转因子有效性分析
        print("反转因子有效性分析")
        print("-"*40)

        if '高反转组合' in analysis_results and '低反转组合' in analysis_results:
            high_reversal = analysis_results['高反转组合']
            low_reversal = analysis_results['低反转组合']

            print(f"高反转组合年化收益: {high_reversal['annual_return']*100:.2f}%")
            print(f"低反转组合年化收益: {low_reversal['annual_return']*100:.2f}%")
            print(f"收益差: {(high_reversal['annual_return'] - low_reversal['annual_return'])*100:.2f}%")

            if high_reversal['annual_return'] > low_reversal['annual_return']:
                print("✅ 反转因子表现正向：高反转信号股票收益更高")
            else:
                print("❌ 反转因子表现负向：低反转信号股票收益更高")
            print()

        # IC分析
        if ic_results:
            print("信息系数（IC）分析")
            print("-"*40)
            print(f"平均IC: {ic_results['ic_mean']:.4f}")
            print(f"IC标准差: {ic_results['ic_std']:.4f}")
            print(f"IC信息比率: {ic_results['ic_ir']:.4f}")
            print(f"IC正值比例: {ic_results['ic_positive_rate']*100:.1f}%")

            if ic_results['ic_mean'] > 0.02:
                print("✅ IC表现良好：因子预测能力较强")
            elif ic_results['ic_mean'] > 0:
                print("⚠️ IC表现一般：因子预测能力有限")
            else:
                print("❌ IC表现较差：因子预测能力不足")
            print()

    def run_validation(self, factor_name: str = 'improved_reversal') -> bool:
        """运行完整的因子验证流程"""
        try:
            # 1. 加载成分股列表
            if not self.load_hsi_constituents():
                return False

            # 2. 加载数据
            if not self.load_stock_data():
                return False

            # 3. 计算反转因子
            if not self.calculate_reversal_factors():
                return False

            # 4. 运行回测
            if not self.run_backtest(factor_name):
                return False

            # 5. 分析表现
            analysis_results = self.analyze_factor_performance()
            if not analysis_results:
                print("❌ 分析结果为空")
                return False

            # 6. 计算IC
            ic_results = self.calculate_information_coefficient()

            # 7. 生成报告
            self.generate_report(analysis_results, ic_results)

            return True

        except Exception as e:
            print(f"❌ 验证过程失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def generate_visualizations(self):
        """生成可视化分析图表"""
        if not self.backtest_results:
            print("❌ 请先运行回测")
            return

        try:
            import matplotlib.pyplot as plt
            import matplotlib.dates as mdates
            from datetime import datetime

            # 设置中文字体和图表样式
            import matplotlib
            import matplotlib.font_manager as fm

            # 尝试找到可用的中文字体
            chinese_fonts = []
            for font in fm.fontManager.ttflist:
                if any(name in font.name.lower() for name in ['arial unicode', 'heiti', 'simhei', 'microsoft yahei', 'pingfang', 'hiragino']):
                    chinese_fonts.append(font.name)

            if chinese_fonts:
                matplotlib.rcParams['font.sans-serif'] = chinese_fonts + ['DejaVu Sans', 'sans-serif']
                print(f"✅ 使用中文字体: {chinese_fonts[0]}")
            else:
                # 如果没有找到中文字体，使用默认设置
                matplotlib.rcParams['font.sans-serif'] = ['DejaVu Sans', 'sans-serif']
                print("⚠️  未找到中文字体，使用默认字体")

            matplotlib.rcParams['axes.unicode_minus'] = False
            matplotlib.rcParams['font.size'] = 10
            plt.style.use('default')

            # 获取数据
            portfolio_returns = self.backtest_results['portfolio_returns']
            rebalance_dates = self.backtest_results['rebalance_dates']
            factor_name = self.backtest_results.get('factor_name', 'improved_reversal')

            # 创建图表
            fig = plt.figure(figsize=(20, 16))

            # 1. 累积收益率曲线
            ax1 = plt.subplot(3, 2, 1)
            self._plot_cumulative_returns(ax1, portfolio_returns, rebalance_dates)

            # 2. 各组合年化收益率对比
            ax2 = plt.subplot(3, 2, 2)
            self._plot_annual_returns(ax2, portfolio_returns)

            # 3. 风险收益散点图
            ax3 = plt.subplot(3, 2, 3)
            self._plot_risk_return_scatter(ax3, portfolio_returns)

            # 4. IC时间序列
            ax4 = plt.subplot(3, 2, 4)
            self._plot_ic_timeseries(ax4)

            # 5. 月度胜率分析
            ax5 = plt.subplot(3, 2, 5)
            self._plot_monthly_win_rate(ax5, portfolio_returns, rebalance_dates)

            # 6. 回撤分析
            ax6 = plt.subplot(3, 2, 6)
            self._plot_drawdown_analysis(ax6, portfolio_returns, rebalance_dates)

            # 设置总标题
            fig.suptitle(f'HSI Reversal Factor ({factor_name}) Backtest Analysis',
                        fontsize=16, fontweight='bold', y=0.98)

            # 调整布局
            plt.tight_layout(rect=[0, 0.02, 1, 0.96])

            # 保存图表
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f'hsi_reversal_factor_analysis_{timestamp}.png'
            plt.savefig(filename, dpi=300, bbox_inches='tight',
                       facecolor='white', edgecolor='none')

            print(f"✅ 图表已保存: {filename}")

            # 显示图表
            plt.show()

        except Exception as e:
            print(f"❌ 生成图表失败: {e}")
            import traceback
            traceback.print_exc()

    def _plot_cumulative_returns(self, ax, portfolio_returns, rebalance_dates):
        """绘制累积收益率曲线"""
        import matplotlib.dates as mdates
        # 计算累积收益率
        for portfolio_name, returns in portfolio_returns.items():
            if portfolio_name.startswith('Group_') or portfolio_name in ['高反转组合', '低反转组合', '全市场组合']:
                cumulative_returns = [1.0]
                for ret in returns:
                    cumulative_returns.append(cumulative_returns[-1] * (1 + ret))

                # 创建日期序列（累积收益率比收益率多一个初始值1.0）
                dates = [rebalance_dates[0]] + rebalance_dates[:len(returns)]

                # 设置线条样式
                if portfolio_name == '高反转组合':
                    ax.plot(dates, cumulative_returns, linewidth=3, label='High Reversal', color='red')
                elif portfolio_name == '低反转组合':
                    ax.plot(dates, cumulative_returns, linewidth=3, label='Low Reversal', color='blue')
                elif portfolio_name == '全市场组合':
                    ax.plot(dates, cumulative_returns, linewidth=2, label='Market',
                           color='black', linestyle='--')
                else:
                    ax.plot(dates, cumulative_returns, linewidth=1, label=portfolio_name, alpha=0.7)

        ax.set_title('Cumulative Returns', fontsize=12, fontweight='bold')
        ax.set_xlabel('Date')
        ax.set_ylabel('Cumulative Return')
        ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax.grid(True, alpha=0.3)

        # 格式化x轴日期
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.YearLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    def _plot_annual_returns(self, ax, portfolio_returns):
        """绘制年化收益率对比"""
        # 计算年化收益率
        annual_returns = {}
        for portfolio_name, returns in portfolio_returns.items():
            if returns:
                mean_return = np.mean(returns)
                annual_return = (1 + mean_return) ** 12 - 1  # 月度调仓
                annual_returns[portfolio_name] = annual_return * 100

        # 按组合类型分类
        group_returns = {}
        special_returns = {}

        for name, ret in annual_returns.items():
            if name.startswith('Group_'):
                group_returns[name] = ret
            elif name in ['高反转组合', '低反转组合', '全市场组合']:
                special_returns[name] = ret

        # 绘制分组收益率
        if group_returns:
            groups = list(group_returns.keys())
            values = list(group_returns.values())
            colors = plt.cm.RdYlBu_r(np.linspace(0.2, 0.8, len(groups)))

            bars = ax.bar(groups, values, color=colors, alpha=0.8)

            # 添加数值标签
            for bar, value in zip(bars, values):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 0.5 if height >= 0 else height - 1,
                       f'{value:.1f}%', ha='center', va='bottom' if height >= 0 else 'top')

        ax.set_title('Annual Returns Comparison', fontsize=12, fontweight='bold')
        ax.set_xlabel('Portfolio')
        ax.set_ylabel('Annual Return (%)')
        ax.grid(True, alpha=0.3, axis='y')
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)

        # 旋转x轴标签
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    def _plot_risk_return_scatter(self, ax, portfolio_returns):
        """绘制风险收益散点图"""
        returns_data = []
        volatility_data = []
        names = []
        colors = []

        for portfolio_name, returns in portfolio_returns.items():
            if returns and len(returns) > 1:
                mean_return = np.mean(returns) * 12 * 100  # 年化收益率(%)
                volatility = np.std(returns) * np.sqrt(12) * 100  # 年化波动率(%)

                returns_data.append(mean_return)
                volatility_data.append(volatility)
                names.append(portfolio_name)

                # 设置颜色
                if portfolio_name == '高反转组合':
                    colors.append('red')
                elif portfolio_name == '低反转组合':
                    colors.append('blue')
                elif portfolio_name == '全市场组合':
                    colors.append('black')
                else:
                    colors.append('gray')

        # 绘制散点图
        scatter = ax.scatter(volatility_data, returns_data, c=colors, s=100, alpha=0.7)

        # 添加标签
        for i, name in enumerate(names):
            if name == '高反转组合':
                ax.annotate('High Reversal', (volatility_data[i], returns_data[i]),
                           xytext=(5, 5), textcoords='offset points', fontsize=9)
            elif name == '低反转组合':
                ax.annotate('Low Reversal', (volatility_data[i], returns_data[i]),
                           xytext=(5, 5), textcoords='offset points', fontsize=9)
            elif name == '全市场组合':
                ax.annotate('Market', (volatility_data[i], returns_data[i]),
                           xytext=(5, 5), textcoords='offset points', fontsize=9)

        ax.set_title('Risk-Return Scatter Plot', fontsize=12, fontweight='bold')
        ax.set_xlabel('Annual Volatility (%)')
        ax.set_ylabel('Annual Return (%)')
        ax.grid(True, alpha=0.3)
        ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        ax.axvline(x=0, color='black', linestyle='-', alpha=0.5)

    def _plot_ic_timeseries(self, ax):
        """绘制IC时间序列"""
        import matplotlib.dates as mdates
        ic_results = self.calculate_information_coefficient()

        if ic_results and 'ic_values' in ic_results:
            ic_values = ic_results['ic_values']
            rebalance_dates = self.backtest_results['rebalance_dates'][:len(ic_values)]

            # 绘制IC时间序列
            ax.plot(rebalance_dates, ic_values, linewidth=1, alpha=0.7, color='blue')
            ax.axhline(y=0, color='black', linestyle='-', alpha=0.5)
            ax.axhline(y=ic_results['ic_mean'], color='red', linestyle='--',
                      label=f"平均IC: {ic_results['ic_mean']:.4f}")

            # 添加正负区域填充
            ax.fill_between(rebalance_dates, 0, ic_values,
                           where=np.array(ic_values) > 0, alpha=0.3, color='green', label='Positive IC')
            ax.fill_between(rebalance_dates, 0, ic_values,
                           where=np.array(ic_values) <= 0, alpha=0.3, color='red', label='Negative IC')

            ax.set_title('Information Coefficient (IC) Time Series', fontsize=12, fontweight='bold')
            ax.set_xlabel('Date')
            ax.set_ylabel('IC Value')
            ax.legend()
            ax.grid(True, alpha=0.3)

            # 格式化x轴
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.YearLocator())
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        else:
            ax.text(0.5, 0.5, 'IC Data Not Available', ha='center', va='center', transform=ax.transAxes)
            ax.set_title('Information Coefficient (IC) Time Series', fontsize=12, fontweight='bold')

    def _plot_monthly_win_rate(self, ax, portfolio_returns, rebalance_dates):
        """绘制月度胜率分析"""
        # 计算各组合的胜率
        win_rates = {}
        for portfolio_name, returns in portfolio_returns.items():
            if returns:
                win_rate = np.sum(np.array(returns) > 0) / len(returns) * 100
                win_rates[portfolio_name] = win_rate

        # 筛选主要组合
        main_portfolios = ['高反转组合', '低反转组合', '全市场组合']
        main_portfolios.extend([f'Group_{i+1}' for i in range(5)])

        filtered_rates = {name: rate for name, rate in win_rates.items()
                         if name in main_portfolios}

        if filtered_rates:
            # 重命名为英文
            english_names = []
            rates = []
            colors = []

            for name, rate in filtered_rates.items():
                if name == '高反转组合':
                    english_names.append('High Reversal')
                    colors.append('red')
                elif name == '低反转组合':
                    english_names.append('Low Reversal')
                    colors.append('blue')
                elif name == '全市场组合':
                    english_names.append('Market')
                    colors.append('black')
                else:
                    english_names.append(name)
                    colors.append('gray')
                rates.append(rate)

            bars = ax.bar(english_names, rates, color=colors, alpha=0.7)

            # 添加50%基准线
            ax.axhline(y=50, color='red', linestyle='--', alpha=0.7, label='50% Baseline')

            # 添加数值标签
            for bar, rate in zip(bars, rates):
                height = bar.get_height()
                ax.text(bar.get_x() + bar.get_width()/2., height + 1,
                       f'{rate:.1f}%', ha='center', va='bottom')

            ax.set_title('Win Rate Comparison', fontsize=12, fontweight='bold')
            ax.set_xlabel('Portfolio')
            ax.set_ylabel('Win Rate (%)')
            ax.set_ylim(0, 100)
            ax.legend()
            ax.grid(True, alpha=0.3, axis='y')

            # 旋转x轴标签
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    def _plot_drawdown_analysis(self, ax, portfolio_returns, rebalance_dates):
        """绘制回撤分析"""
        import matplotlib.dates as mdates
        # 选择主要组合进行回撤分析
        main_portfolios = ['高反转组合', '低反转组合', '全市场组合']

        for portfolio_name in main_portfolios:
            if portfolio_name in portfolio_returns:
                returns = portfolio_returns[portfolio_name]

                # 计算累积收益和回撤
                cumulative_returns = np.cumprod(1 + np.array(returns))
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max * 100

                # 创建日期序列
                dates = rebalance_dates[:len(drawdowns)]

                # 设置颜色和标签
                if portfolio_name == '高反转组合':
                    color = 'red'
                    label = 'High Reversal'
                elif portfolio_name == '低反转组合':
                    color = 'blue'
                    label = 'Low Reversal'
                else:
                    color = 'black'
                    label = 'Market'

                ax.fill_between(dates, 0, drawdowns, alpha=0.3, color=color)
                ax.plot(dates, drawdowns, linewidth=2, label=label, color=color)

        ax.set_title('Drawdown Analysis', fontsize=12, fontweight='bold')
        ax.set_xlabel('Date')
        ax.set_ylabel('Drawdown (%)')
        ax.legend()
        ax.grid(True, alpha=0.3)

        # 格式化x轴
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.YearLocator())
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)


def main():
    """主函数"""
    print("🎯 恒生指数成分股反转因子回测分析")
    print("=" * 80)
    print("📊 基于缓存价格数据进行反转因子有效性验证")
    print("🔬 使用多种反转因子计算方法")
    print("📈 分成5组进行详细分层分析（纯多头策略）")
    print()

    # 创建回测器（月度调仓，分成5组）
    backtest = HSIReversalFactorBacktest(
        cache_dir="hsi_cache_5y",
        rebalance_freq="M",  # 月度再平衡
        enable_short=False,  # 禁用做空策略
        n_groups=5          # 分成5组
    )

    start_time = datetime.now()

    try:
        # 运行完整的因子验证流程
        success = backtest.run_validation('improved_reversal')

        if success:
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n🎉 反转因子回测分析完成！")
            print(f"⏱️  总耗时: {duration}")
            print(f"\n💡 分析要点:")
            print(f"   ✅ 使用了恒生指数成分股5年历史数据")
            print(f"   ✅ 基于改进反转因子（短期+中期反转）")
            print(f"   ✅ 月度调仓策略")
            print(f"   ✅ 分成5组进行详细分层分析")
            print(f"   ✅ 计算了信息系数(IC)和风险调整收益")
            print(f"   ✅ 提供了完整的因子有效性验证")

            # 生成可视化图表
            print(f"\n📊 生成可视化分析图表...")
            backtest.generate_visualizations()
        else:
            print("\n💥 回测过程中遇到问题，请检查错误信息。")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
