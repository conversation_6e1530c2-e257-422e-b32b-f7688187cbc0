#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股低波动因子回测分析
基于历史价格数据计算波动率进行因子有效性验证
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSISmartDividendFactorBacktest:
    """恒生指数智能股息率因子回测器（支持做空策略）"""

    def __init__(self, data_dir: str = "hsi_smart_dividend_yields", rebalance_freq: str = 'Q',
                 enable_short: bool = True, n_groups: int = 5):
        """
        初始化回测器

        Args:
            data_dir: 智能股息率数据目录
            rebalance_freq: 再平衡频率 ('M'=月度, 'Q'=季度, 'H'=半年, 'Y'=年度)
            enable_short: 是否允许做空
            n_groups: 分组数量
        """
        self.data_dir = data_dir
        self.stock_data = {}
        self.backtest_results = {}

        # 回测参数
        self.rebalance_freq = rebalance_freq
        self.enable_short = enable_short
        self.n_groups = n_groups
        self.lookback_days = 252   # 1年回看期
        
        # 根据再平衡频率调整前瞻期
        if rebalance_freq == 'M':
            self.forward_days = 21     # 1个月前瞻期
        elif rebalance_freq == 'Q':
            self.forward_days = 63     # 3个月前瞻期
        elif rebalance_freq == 'H':
            self.forward_days = 126    # 6个月前瞻期
        else:  # 'Y'
            self.forward_days = 252    # 1年前瞻期
        
        freq_names = {'M': '月度', 'Q': '季度', 'H': '半年度', 'Y': '年度'}
        print("🚀 恒生指数智能股息率因子回测器已初始化（支持做空策略）")
        print(f"📁 数据目录: {data_dir}")
        print(f"🔄 再平衡频率: {freq_names.get(rebalance_freq, rebalance_freq)}")
        print(f"📊 分组数量: {n_groups}")
        print(f"🔄 做空策略: {'启用' if enable_short else '禁用'}")
    
    def load_stock_data(self) -> bool:
        """加载所有股票的智能股息率数据"""
        try:
            print("📊 加载股票数据...")

            # 找到最新的数据文件（按时间戳排序）
            csv_files = []
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.csv') and '_smart_dividend_yield_' in filename and not filename.startswith('hsi_'):
                    csv_files.append(filename)

            # 按时间戳分组，每个股票选择最新的文件
            stock_latest_files = {}
            for filename in csv_files:
                parts = filename.split('_')
                if len(parts) >= 5:
                    stock_code = parts[0]
                    stock_name = parts[1]
                    timestamp = parts[4] + '_' + parts[5].replace('.csv', '')

                    # 保留每个股票最新的文件
                    if stock_code not in stock_latest_files or timestamp > stock_latest_files[stock_code]['timestamp']:
                        stock_latest_files[stock_code] = {
                            'filename': filename,
                            'stock_name': stock_name,
                            'timestamp': timestamp
                        }

            print(f"📁 找到 {len(stock_latest_files)} 只股票的最新数据文件")

            loaded_count = 0
            for stock_code, file_info in stock_latest_files.items():
                filename = file_info['filename']
                stock_name = file_info['stock_name']

                # 加载股票数据
                file_path = os.path.join(self.data_dir, filename)
                stock_df = self._load_stock_file(file_path, stock_code, stock_name)

                if stock_df is not None and len(stock_df) > 0:
                    self.stock_data[stock_code] = {
                        'name': stock_name,
                        'data': stock_df
                    }
                    loaded_count += 1
            
            print(f"✅ 成功加载 {loaded_count} 只股票的智能股息率数据")
            
            if loaded_count == 0:
                print("❌ 未找到有效的股票数据文件")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 加载股票数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _load_stock_file(self, file_path: str, stock_code: str, stock_name: str) -> Optional[pd.DataFrame]:
        """加载单个股票文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            # 检查必要的列
            required_cols = ['日期', '股价(港元)', '智能股息率(%)', '智能年度分红(港元)']
            if not all(col in df.columns for col in required_cols):
                print(f"⚠️  {stock_code} 数据格式不正确，跳过")
                return None
            
            # 处理数据
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.sort_values('日期')
            df.set_index('日期', inplace=True)
            
            # 清理数据
            df = df.dropna(subset=['股价(港元)', '智能股息率(%)'])
            
            # 重命名列以便使用
            df['price'] = df['股价(港元)']
            df['dividend_yield'] = df['智能股息率(%)']
            df['annual_dividend'] = df['智能年度分红(港元)']
            
            # 计算收益率
            df['return_1d'] = df['price'].pct_change()
            df['return_5d'] = df['price'].pct_change(5)
            df['return_20d'] = df['price'].pct_change(20)
            df['return_60d'] = df['price'].pct_change(60)
            
            return df
            
        except Exception as e:
            print(f"⚠️  加载 {stock_code} 数据失败: {e}")
            return None
    
    def calculate_factor_scores(self, date: datetime) -> pd.Series:
        """计算指定日期的智能股息率因子得分"""
        scores = {}
        
        for stock_code, stock_info in self.stock_data.items():
            df = stock_info['data']
            
            # 找到指定日期之前的最新数据
            available_dates = df.index[df.index <= date]
            if len(available_dates) > 0:
                latest_date = available_dates[-1]
                
                # 获取智能股息率作为因子得分
                dividend_yield = df.loc[latest_date, 'dividend_yield']
                
                # 确保股息率有效
                if pd.notna(dividend_yield) and dividend_yield >= 0:
                    scores[stock_code] = dividend_yield
        
        return pd.Series(scores)
    
    def create_portfolios(self, factor_scores: pd.Series) -> Dict[str, Dict]:
        """基于因子得分创建投资组合（支持做空策略）"""
        if len(factor_scores) == 0:
            return {}

        # 按因子得分排序（降序，高股息率在前）
        sorted_scores = factor_scores.sort_values(ascending=False)

        portfolios = {}

        # 分组
        n_stocks_per_group = len(sorted_scores) // self.n_groups

        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(sorted_scores)
            else:
                end_idx = (i + 1) * n_stocks_per_group

            group_stocks = sorted_scores.iloc[start_idx:end_idx].index.tolist()

            # 为每个组合设置权重和方向
            if i == 0:  # 最高股息率组合 - 做多
                portfolios[f'Group_{i+1}'] = {
                    'stocks': group_stocks,
                    'weights': {stock: 1.0/len(group_stocks) for stock in group_stocks},
                    'direction': 'long'
                }
            elif i == self.n_groups - 1 and self.enable_short:  # 最低股息率组合 - 做空
                portfolios[f'Group_{i+1}'] = {
                    'stocks': group_stocks,
                    'weights': {stock: -1.0/len(group_stocks) for stock in group_stocks},
                    'direction': 'short'
                }
            else:  # 中间组合 - 纯多头
                portfolios[f'Group_{i+1}'] = {
                    'stocks': group_stocks,
                    'weights': {stock: 1.0/len(group_stocks) for stock in group_stocks},
                    'direction': 'long'
                }

        # 添加特殊组合
        total_stocks = len(sorted_scores)

        # 高股息率组合（前20%）- 做多
        top_20_count = max(1, int(total_stocks * 0.2))
        top_stocks = sorted_scores.head(top_20_count).index.tolist()
        portfolios['高股息率组合'] = {
            'stocks': top_stocks,
            'weights': {stock: 1.0/len(top_stocks) for stock in top_stocks},
            'direction': 'long'
        }

        # 低股息率组合（后20%）- 做空（如果启用）
        bottom_20_count = max(1, int(total_stocks * 0.2))
        bottom_stocks = sorted_scores.tail(bottom_20_count).index.tolist()
        if self.enable_short:
            portfolios['低股息率组合'] = {
                'stocks': bottom_stocks,
                'weights': {stock: -1.0/len(bottom_stocks) for stock in bottom_stocks},
                'direction': 'short'
            }
        else:
            portfolios['低股息率组合'] = {
                'stocks': bottom_stocks,
                'weights': {stock: 1.0/len(bottom_stocks) for stock in bottom_stocks},
                'direction': 'long'
            }

        # 多空组合（高股息率做多 + 低股息率做空）
        if self.enable_short:
            all_weights = {}
            # 高股息率做多（权重0.5）
            for stock in top_stocks:
                all_weights[stock] = 0.5 / len(top_stocks)
            # 低股息率做空（权重-0.5）
            for stock in bottom_stocks:
                all_weights[stock] = -0.5 / len(bottom_stocks)

            portfolios['多空组合'] = {
                'stocks': top_stocks + bottom_stocks,
                'weights': all_weights,
                'direction': 'long_short'
            }

        # 全市场组合（所有股票等权重做多）
        all_stocks = sorted_scores.index.tolist()
        portfolios['全市场组合'] = {
            'stocks': all_stocks,
            'weights': {stock: 1.0/len(all_stocks) for stock in all_stocks},
            'direction': 'long'
        }

        return portfolios

    def calculate_total_return_with_dividends(self, stock_code: str, start_date: datetime, end_date: datetime) -> float:
        """计算包含分红的总收益率"""
        if stock_code not in self.stock_data:
            return 0.0

        df = self.stock_data[stock_code]['data']

        # 获取期间数据
        period_data = df[(df.index >= start_date) & (df.index <= end_date)]

        if len(period_data) < 2:
            return 0.0

        # 获取起始和结束价格
        start_price = period_data['price'].iloc[0]
        end_price = period_data['price'].iloc[-1]

        if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
            return 0.0

        # 计算资本利得收益率
        capital_gain_return = (end_price - start_price) / start_price

        # 计算期间分红收益率
        dividend_return = 0.0

        # 获取期间内的分红数据
        # 这里我们使用智能年度分红数据来估算期间分红
        # 假设分红在期间内均匀分布
        period_days = (end_date - start_date).days
        annual_days = 365

        if period_days > 0:
            # 获取期间中点的年度分红
            mid_date = start_date + timedelta(days=period_days // 2)

            # 找到中点日期的数据
            mid_data = period_data[period_data.index <= mid_date]
            if len(mid_data) > 0:
                latest_mid_data = mid_data.iloc[-1]
                annual_dividend = latest_mid_data.get('annual_dividend', 0)

                if pd.notna(annual_dividend) and annual_dividend > 0:
                    # 按期间比例计算分红
                    period_dividend = annual_dividend * (period_days / annual_days)
                    dividend_return = period_dividend / start_price

        # 总收益率 = 资本利得收益率 + 分红收益率
        total_return = capital_gain_return + dividend_return

        return total_return

    def calculate_portfolio_returns(self, portfolios: Dict[str, Dict],
                                  start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """计算投资组合在指定期间的收益率（包含分红收益，支持做空策略）"""
        portfolio_returns = {}

        for portfolio_name, portfolio_info in portfolios.items():
            stocks = portfolio_info['stocks']
            weights = portfolio_info['weights']
            direction = portfolio_info['direction']

            if not stocks:
                continue

            portfolio_return = 0.0
            valid_stocks = 0

            for stock_code in stocks:
                if stock_code in self.stock_data and stock_code in weights:
                    # 使用新的包含分红的总收益率计算方法
                    stock_total_return = self.calculate_total_return_with_dividends(
                        stock_code, start_date, end_date
                    )

                    if stock_total_return != 0.0:  # 有效收益率
                        weight = weights[stock_code]

                        # 根据权重计算贡献（负权重表示做空）
                        portfolio_return += weight * stock_total_return
                        valid_stocks += 1

            # 保存组合收益率
            if valid_stocks > 0:
                portfolio_returns[portfolio_name] = portfolio_return
            else:
                portfolio_returns[portfolio_name] = 0.0

        return portfolio_returns
    
    def get_rebalance_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取再平衡日期"""
        dates = []
        current_date = start_date
        
        while current_date <= end_date:
            dates.append(current_date)
            
            # 根据再平衡频率确定下一个日期
            if self.rebalance_freq == 'M':  # 月度
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1, day=1)
            elif self.rebalance_freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_month = 6
                elif current_date.month <= 6:
                    next_month = 9
                elif current_date.month <= 9:
                    next_month = 12
                else:
                    next_month = 3
                    current_date = current_date.replace(year=current_date.year + 1)
                
                if next_month <= 12:
                    current_date = current_date.replace(month=next_month, day=1)
            elif self.rebalance_freq == 'H':  # 半年度
                if current_date.month <= 6:
                    current_date = current_date.replace(month=12, day=1)
                else:
                    current_date = current_date.replace(year=current_date.year + 1, month=6, day=1)
            else:  # 年度
                current_date = current_date.replace(year=current_date.year + 1)
        
        return dates

    def run_backtest(self) -> bool:
        """运行智能股息率因子回测"""
        try:
            print("🔄 开始智能股息率因子回测...")

            # 确定回测期间
            all_dates = set()
            for stock_info in self.stock_data.values():
                all_dates.update(stock_info['data'].index)

            if not all_dates:
                print("❌ 没有可用的日期数据")
                return False

            start_date = min(all_dates) + timedelta(days=self.lookback_days)
            end_date = max(all_dates) - timedelta(days=self.forward_days)

            print(f"📅 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

            # 获取再平衡日期
            rebalance_dates = self.get_rebalance_dates(start_date, end_date)
            print(f"🔄 再平衡次数: {len(rebalance_dates)}")

            # 存储所有组合的收益率
            all_portfolio_returns = defaultdict(list)
            all_factor_scores = []

            for i, rebalance_date in enumerate(rebalance_dates):
                print(f"🔄 再平衡 {i+1}/{len(rebalance_dates)}: {rebalance_date.strftime('%Y-%m-%d')}")

                # 计算因子得分
                factor_scores = self.calculate_factor_scores(rebalance_date)
                if factor_scores.empty:
                    continue

                all_factor_scores.append(factor_scores)

                # 创建投资组合
                portfolios = self.create_portfolios(factor_scores)

                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    hold_end = rebalance_dates[i + 1]
                else:
                    hold_end = end_date

                # 计算期间收益
                period_returns = self.calculate_portfolio_returns(
                    portfolios, rebalance_date, hold_end
                )

                # 累积收益
                for portfolio_name, returns in period_returns.items():
                    all_portfolio_returns[portfolio_name].append(returns)

                # 显示组合信息（仅第一次）
                if i == 0:
                    print("📊 投资组合构成:")
                    for portfolio_name, portfolio_info in portfolios.items():
                        if portfolio_name.startswith('Group_'):
                            stocks = portfolio_info['stocks']
                            direction = portfolio_info['direction']
                            avg_yield = factor_scores[stocks].mean() if stocks else 0
                            direction_text = "做多" if direction == "long" else "做空" if direction == "short" else "多空"
                            print(f"   {portfolio_name}: {len(stocks)}只股票, 平均股息率: {avg_yield:.2f}%, 策略: {direction_text}")

            # 保存回测结果
            self.backtest_results = {
                'portfolio_returns': dict(all_portfolio_returns),
                'factor_scores': all_factor_scores,
                'rebalance_dates': rebalance_dates,
                'start_date': start_date,
                'end_date': end_date
            }

            print("✅ 回测完成")
            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def analyze_factor_performance(self) -> Dict:
        """分析因子表现"""
        if not self.backtest_results:
            print("❌ 请先运行回测")
            return {}

        print("📊 分析因子表现...")

        portfolio_returns = self.backtest_results['portfolio_returns']
        analysis_results = {}

        # 计算各组合的统计指标
        for portfolio_name, returns in portfolio_returns.items():
            if not returns:
                continue

            returns_array = np.array(returns)

            # 基本统计
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0

            # 累积收益
            cumulative_return = np.prod(1 + returns_array) - 1

            # 年化收益率
            if self.rebalance_freq == 'M':
                periods_per_year = 12
            elif self.rebalance_freq == 'Q':
                periods_per_year = 4
            elif self.rebalance_freq == 'H':
                periods_per_year = 2
            else:  # 'Y'
                periods_per_year = 1

            annual_return = (1 + mean_return) ** periods_per_year - 1

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # 胜率
            win_rate = np.sum(returns_array > 0) / len(returns_array)

            analysis_results[portfolio_name] = {
                'mean_return': mean_return,
                'std_return': std_return,
                'sharpe_ratio': sharpe_ratio,
                'cumulative_return': cumulative_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_periods': len(returns)
            }

        return analysis_results

    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数（IC）"""
        if not self.backtest_results:
            return {}

        print("📈 计算信息系数...")

        factor_scores = self.backtest_results['factor_scores']
        ic_values = []

        for i, scores in enumerate(factor_scores):
            if i >= len(factor_scores) - 1:
                break

            # 获取下期收益率
            next_period_returns = {}
            rebalance_date = self.backtest_results['rebalance_dates'][i]
            next_date = self.backtest_results['rebalance_dates'][i + 1] if i + 1 < len(self.backtest_results['rebalance_dates']) else self.backtest_results['end_date']

            for stock_code in scores.index:
                if stock_code in self.stock_data:
                    # 使用包含分红的总收益率计算IC
                    stock_total_return = self.calculate_total_return_with_dividends(
                        stock_code, rebalance_date, next_date
                    )

                    if stock_total_return != 0.0:
                        next_period_returns[stock_code] = stock_total_return

            # 计算IC
            if len(next_period_returns) >= 5:  # 至少需要5只股票
                factor_values = []
                return_values = []

                for stock_code in scores.index:
                    if stock_code in next_period_returns:
                        factor_values.append(scores[stock_code])
                        return_values.append(next_period_returns[stock_code])

                if len(factor_values) >= 5:
                    ic = np.corrcoef(factor_values, return_values)[0, 1]
                    if not np.isnan(ic):
                        ic_values.append(ic)

        if ic_values:
            return {
                'ic_mean': np.mean(ic_values),
                'ic_std': np.std(ic_values),
                'ic_ir': np.mean(ic_values) / np.std(ic_values) if np.std(ic_values) > 0 else 0,
                'ic_values': ic_values,
                'ic_positive_rate': np.sum(np.array(ic_values) > 0) / len(ic_values)
            }
        else:
            return {}

    def generate_report(self, analysis_results: Dict, ic_results: Dict):
        """生成分析报告"""
        print("\n" + "="*80)
        print("恒生指数成分股智能股息率因子回测分析报告")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: {self.data_dir}")
        print(f"分析股票数: {len(self.stock_data)}")
        strategy_type = "多空策略" if self.enable_short else "纯多头策略"
        print(f"投资策略: 基于智能股息率因子的{strategy_type}")
        print(f"分组数量: {self.n_groups}")
        print(f"做空策略: {'启用' if self.enable_short else '禁用'}")
        print(f"收益计算: 包含分红收益的总收益率")
        print()

        # 回测基本信息
        if self.backtest_results:
            print("回测基本信息")
            print("-"*40)
            print(f"回测期间: {self.backtest_results['start_date'].strftime('%Y-%m-%d')} 至 {self.backtest_results['end_date'].strftime('%Y-%m-%d')}")
            print(f"再平衡频率: {self.rebalance_freq}")
            print(f"再平衡次数: {len(self.backtest_results['rebalance_dates'])}")
            print()

        # 组合表现分析
        if analysis_results:
            print("投资组合表现分析")
            print("-"*90)
            print(f"{'组合名称':<15} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'最大回撤':<8} {'胜率':<8}")
            print("-"*90)

            # 按预期顺序显示组合
            portfolio_order = [f'Group_{i+1}' for i in range(self.n_groups)]
            portfolio_order.extend(['高股息率组合', '低股息率组合', '全市场组合'])
            if self.enable_short:
                portfolio_order.append('多空组合')

            for portfolio_name in portfolio_order:
                if portfolio_name in analysis_results:
                    perf = analysis_results[portfolio_name]
                    print(f"{portfolio_name:<15} {perf['annual_return']*100:<10.2f}% "
                          f"{perf['std_return']*100:<8.2f}% {perf['sharpe_ratio']:<8.2f} "
                          f"{abs(perf['max_drawdown'])*100:<8.2f}% {perf['win_rate']*100:<8.1f}%")
            print()

        # 股息率因子有效性分析
        print("股息率因子有效性分析")
        print("-"*40)

        if '高股息率组合' in analysis_results and '低股息率组合' in analysis_results:
            high_div = analysis_results['高股息率组合']
            low_div = analysis_results['低股息率组合']

            print(f"高股息率组合年化收益: {high_div['annual_return']*100:.2f}%")
            print(f"低股息率组合年化收益: {low_div['annual_return']*100:.2f}%")

            if self.enable_short:
                # 做空策略下，低股息率组合收益为负表示做空盈利
                long_short_return = high_div['annual_return'] - low_div['annual_return']
                print(f"多空收益差: {long_short_return*100:.2f}%")

                if '多空组合' in analysis_results:
                    multi_short = analysis_results['多空组合']
                    print(f"多空组合年化收益: {multi_short['annual_return']*100:.2f}%")
                    print(f"多空组合夏普比率: {multi_short['sharpe_ratio']:.3f}")

                if high_div['annual_return'] > 0 and low_div['annual_return'] < 0:
                    print("✅ 股息率因子表现优异：高股息率做多盈利，低股息率做空盈利")
                elif high_div['annual_return'] > low_div['annual_return']:
                    print("✅ 股息率因子表现正向：高股息率股票收益更高")
                else:
                    print("❌ 股息率因子表现负向：策略需要调整")
            else:
                # 纯多头策略
                print(f"收益差: {(high_div['annual_return'] - low_div['annual_return'])*100:.2f}%")
                if high_div['annual_return'] > low_div['annual_return']:
                    print("✅ 股息率因子表现正向：高股息率股票收益更高")
                else:
                    print("❌ 股息率因子表现负向：低股息率股票收益更高")
            print()

        # IC分析
        if ic_results:
            print("信息系数（IC）分析")
            print("-"*40)
            print(f"平均IC: {ic_results['ic_mean']:.4f}")
            print(f"IC标准差: {ic_results['ic_std']:.4f}")
            print(f"IC信息比率: {ic_results['ic_ir']:.4f}")
            print(f"IC正值比例: {ic_results['ic_positive_rate']*100:.1f}%")

            if ic_results['ic_mean'] > 0.02:
                print("✅ IC表现良好：因子预测能力较强")
            elif ic_results['ic_mean'] > 0:
                print("⚠️ IC表现一般：因子预测能力有限")
            else:
                print("❌ IC表现较差：因子预测能力不足")
            print()

    def plot_results(self, analysis_results: Dict, save_path: str = "hsi_smart_dividend_factor_backtest.png"):
        """绘制回测结果图表"""
        try:
            if not analysis_results:
                print("❌ 没有分析结果可供绘制")
                return

            print(f"📊 绘制回测结果图表...")

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('恒生指数成分股智能股息率因子回测结果', fontsize=16, fontweight='bold')

            # 子图1: 累积收益率
            ax1 = axes[0, 0]
            portfolio_returns = self.backtest_results['portfolio_returns']
            rebalance_dates = self.backtest_results['rebalance_dates']

            # 只显示5个分组
            display_portfolios = [f'Group_{i+1}' for i in range(self.n_groups)]

            for portfolio_name in display_portfolios:
                if portfolio_name in portfolio_returns:
                    returns = portfolio_returns[portfolio_name]
                    cumulative = np.cumprod(1 + np.array(returns))
                    # 使用实际的再平衡日期作为x轴
                    dates_for_plot = rebalance_dates[:len(cumulative)]
                    ax1.plot(dates_for_plot, cumulative, label=portfolio_name, linewidth=2)

            ax1.set_title('累积收益率对比（5个分组）')
            ax1.set_xlabel('时间')
            ax1.set_ylabel('累积收益率')
            ax1.legend()
            ax1.grid(True, alpha=0.3)
            # 格式化x轴日期显示
            ax1.tick_params(axis='x', rotation=45)
            import matplotlib.dates as mdates
            ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=6))

            # 子图2: 年化收益率对比
            ax2 = axes[0, 1]
            portfolios = []
            annual_returns = []

            for i in range(self.n_groups):
                portfolio_name = f'Group_{i+1}'
                if portfolio_name in analysis_results:
                    portfolios.append(portfolio_name)
                    annual_returns.append(analysis_results[portfolio_name]['annual_return'] * 100)

            # 动态生成颜色
            colors = ['red', 'orange', 'yellow', 'lightgreen', 'green'][:len(portfolios)]
            bars = ax2.bar(portfolios, annual_returns, color=colors)
            ax2.set_title('各组合年化收益率')
            ax2.set_ylabel('年化收益率 (%)')
            ax2.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, annual_returns):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        f'{value:.1f}%', ha='center', va='bottom')

            # 子图3: 夏普比率对比
            ax3 = axes[1, 0]
            sharpe_ratios = []

            for portfolio_name in portfolios:
                if portfolio_name in analysis_results:
                    sharpe_ratios.append(analysis_results[portfolio_name]['sharpe_ratio'])

            bars = ax3.bar(portfolios, sharpe_ratios, color=colors)
            ax3.set_title('各组合夏普比率')
            ax3.set_ylabel('夏普比率')
            ax3.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, sharpe_ratios):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.2f}', ha='center', va='bottom')

            # 子图4: IC时间序列
            ax4 = axes[1, 1]
            ic_results = self.calculate_information_coefficient()
            if ic_results and 'ic_values' in ic_results:
                ic_values = ic_results['ic_values']
                # IC值对应的日期（去掉最后一个日期，因为IC计算需要下期收益）
                ic_dates = rebalance_dates[:len(ic_values)]
                ax4.plot(ic_dates, ic_values, 'b-', alpha=0.7, linewidth=1)
                ax4.axhline(y=0, color='r', linestyle='--', alpha=0.5)
                ax4.axhline(y=ic_results['ic_mean'], color='g', linestyle='-', alpha=0.7,
                           label=f'平均IC: {ic_results["ic_mean"]:.3f}')
                ax4.set_title('信息系数(IC)时间序列')
                ax4.set_xlabel('时间')
                ax4.set_ylabel('IC值')
                ax4.legend()
                ax4.grid(True, alpha=0.3)
                # 格式化x轴日期显示
                ax4.tick_params(axis='x', rotation=45)
                ax4.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
                ax4.xaxis.set_major_locator(mdates.MonthLocator(interval=6))

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存: {save_path}")

        except Exception as e:
            print(f"❌ 绘制图表失败: {e}")

    def save_results(self, analysis_results: Dict, ic_results: Dict,
                    output_file: str = "hsi_smart_dividend_factor_backtest_results.json"):
        """保存回测结果"""
        try:
            results = {
                'backtest_info': {
                    'data_dir': self.data_dir,
                    'rebalance_freq': self.rebalance_freq,
                    'stock_count': len(self.stock_data),
                    'start_date': self.backtest_results['start_date'].strftime('%Y-%m-%d'),
                    'end_date': self.backtest_results['end_date'].strftime('%Y-%m-%d'),
                    'rebalance_count': len(self.backtest_results['rebalance_dates'])
                },
                'portfolio_performance': analysis_results,
                'ic_analysis': ic_results,
                'portfolio_returns': self.backtest_results['portfolio_returns']
            }

            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            print(f"💾 回测结果已保存: {output_file}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def run_validation(self) -> bool:
        """运行完整的因子验证流程"""
        try:
            # 1. 加载数据
            if not self.load_stock_data():
                return False

            # 2. 运行回测
            if not self.run_backtest():
                return False

            # 3. 分析表现
            analysis_results = self.analyze_factor_performance()
            if not analysis_results:
                return False

            # 4. 计算IC
            ic_results = self.calculate_information_coefficient()

            # 5. 生成报告
            self.generate_report(analysis_results, ic_results)

            # 6. 绘制图表
            self.plot_results(analysis_results)

            # 7. 保存结果
            self.save_results(analysis_results, ic_results)

            return True

        except Exception as e:
            print(f"❌ 验证过程失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎯 恒生指数成分股智能股息率因子回测分析（5组分层）")
    print("=" * 80)
    print("📊 基于完整版智能股息率数据进行因子有效性验证")
    print("🔬 使用智能分红频率识别算法计算的股息率数据")
    print("📈 分成5组进行详细分层分析（纯多头策略）")
    print()

    # 创建回测器（禁用做空策略，分成5组）
    backtest = HSISmartDividendFactorBacktest(
        data_dir="hsi_smart_dividend_yields",
        rebalance_freq="Q",  # 季度再平衡
        enable_short=False,  # 禁用做空策略
        n_groups=5          # 分成5组
    )

    start_time = datetime.now()

    try:
        # 运行完整的因子验证流程
        success = backtest.run_validation()

        if success:
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n🎉 智能股息率因子回测分析完成！")
            print(f"⏱️  总耗时: {duration}")
            print(f"\n📁 输出文件:")
            print(f"   📊 图表: hsi_smart_dividend_factor_backtest.png")
            print(f"   💾 结果: hsi_smart_dividend_factor_backtest_results.json")
            print(f"\n💡 分析要点:")
            print(f"   ✅ 使用了完整版智能股息率数据")
            print(f"   ✅ 基于智能分红频率识别算法")
            print(f"   ✅ 避免了前瞻性偏差")
            print(f"   ✅ 包含分红收益的总收益率计算")
            print(f"   ✅ 分成5组进行详细分层分析")
            print(f"   ✅ 计算了信息系数(IC)和风险调整收益")
            print(f"   ✅ 提供了完整的因子有效性验证")
        else:
            print("\n💥 回测过程中遇到问题，请检查错误信息。")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
