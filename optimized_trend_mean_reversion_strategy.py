#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化版长期趋势+短期均值回归策略
测试不同参数组合，寻找最优策略
"""

import pandas as pd
import numpy as np
import os
import pickle
from datetime import datetime
import itertools
import warnings
warnings.filterwarnings('ignore')

class OptimizedTrendMeanReversionStrategy:
    """优化版趋势均值回归策略"""
    
    def __init__(self, price_cache_dir='sp500_price_cache'):
        self.price_cache_dir = price_cache_dir
        self.price_data = {}
        self.optimization_results = []
        
    def load_price_data(self, max_stocks=30):
        """加载价格数据"""
        print(f"📈 加载价格数据 (最多{max_stocks}只股票)...")
        
        cache_files = [f for f in os.listdir(self.price_cache_dir) 
                      if f.endswith('_price.pkl')]
        
        loaded_count = 0
        for file in cache_files[:max_stocks]:
            symbol = file.split('_')[0]
            file_path = os.path.join(self.price_cache_dir, file)
            
            try:
                with open(file_path, 'rb') as f:
                    cached_data = pickle.load(f)
                    price_data = cached_data['price_data']
                    
                    if len(price_data) > 300:
                        self.price_data[symbol] = price_data
                        loaded_count += 1
            except:
                continue
        
        print(f"✅ 成功加载 {loaded_count} 只股票的价格数据")
        return loaded_count > 0
    
    def backtest_strategy(self, symbol, long_ma=200, short_ma=20, deviation_threshold=0.05, stop_loss=0.10):
        """回测单只股票的策略"""
        price_series = self.price_data[symbol]
        
        # 计算技术指标
        long_ma_series = price_series.rolling(window=long_ma).mean()
        short_ma_series = price_series.rolling(window=short_ma).mean()
        deviation = (price_series - short_ma_series) / short_ma_series
        
        # 生成信号
        signals = pd.DataFrame(index=price_series.index)
        signals['price'] = price_series
        signals['long_ma'] = long_ma_series
        signals['short_ma'] = short_ma_series
        signals['position'] = 0
        signals['signal'] = 0
        
        position = 0
        buy_price = 0
        trades = []
        
        for i in range(long_ma, len(signals)):
            current_price = signals.iloc[i]['price']
            current_long_ma = signals.iloc[i]['long_ma']
            current_short_ma = signals.iloc[i]['short_ma']
            current_deviation = deviation.iloc[i]
            
            if pd.isna(current_price) or pd.isna(current_long_ma) or pd.isna(current_short_ma):
                continue
            
            # 买入条件
            if position == 0:
                # 长期趋势向上 且 短期偏离
                if (current_price > current_long_ma and 
                    current_deviation < -deviation_threshold):
                    position = 1
                    buy_price = current_price
                    signals.iloc[i, signals.columns.get_loc('signal')] = 1
            
            # 卖出条件
            elif position == 1:
                sell_signal = False
                sell_reason = ""
                
                # 条件1: 价格回到短期均线之上
                if current_price > current_short_ma:
                    sell_signal = True
                    sell_reason = "profit_taking"
                
                # 条件2: 价格跌破长期均线
                elif current_price < current_long_ma:
                    sell_signal = True
                    sell_reason = "trend_break"
                
                # 条件3: 止损
                elif (current_price - buy_price) / buy_price < -stop_loss:
                    sell_signal = True
                    sell_reason = "stop_loss"
                
                if sell_signal:
                    position = 0
                    trade_return = (current_price - buy_price) / buy_price
                    trades.append({
                        'buy_price': buy_price,
                        'sell_price': current_price,
                        'return': trade_return,
                        'reason': sell_reason
                    })
                    signals.iloc[i, signals.columns.get_loc('signal')] = -1
            
            signals.iloc[i, signals.columns.get_loc('position')] = position
        
        # 计算策略收益
        signals['returns'] = signals['price'].pct_change()
        signals['strategy_returns'] = signals['position'].shift(1) * signals['returns']
        
        # 计算绩效指标
        if len(trades) > 0:
            total_strategy_return = (1 + signals['strategy_returns']).prod() - 1
            total_buy_hold_return = (signals['price'].iloc[-1] / signals['price'].iloc[long_ma]) - 1
            
            win_rate = sum(1 for t in trades if t['return'] > 0) / len(trades)
            avg_trade_return = np.mean([t['return'] for t in trades])
            
            # 按卖出原因分类
            profit_taking_trades = [t for t in trades if t['reason'] == 'profit_taking']
            trend_break_trades = [t for t in trades if t['reason'] == 'trend_break']
            stop_loss_trades = [t for t in trades if t['reason'] == 'stop_loss']
            
            return {
                'symbol': symbol,
                'total_strategy_return': total_strategy_return * 100,
                'total_buy_hold_return': total_buy_hold_return * 100,
                'excess_return': (total_strategy_return - total_buy_hold_return) * 100,
                'total_trades': len(trades),
                'win_rate': win_rate * 100,
                'avg_trade_return': avg_trade_return * 100,
                'profit_taking_trades': len(profit_taking_trades),
                'trend_break_trades': len(trend_break_trades),
                'stop_loss_trades': len(stop_loss_trades),
                'trades': trades
            }
        else:
            return None
    
    def optimize_parameters(self):
        """参数优化"""
        print("🔧 开始参数优化...")
        
        # 参数范围
        long_ma_range = [150, 200, 250]
        short_ma_range = [10, 20, 30]
        deviation_range = [0.03, 0.05, 0.08]
        stop_loss_range = [0.08, 0.10, 0.15]
        
        param_combinations = list(itertools.product(
            long_ma_range, short_ma_range, deviation_range, stop_loss_range
        ))
        
        print(f"📊 总共 {len(param_combinations)} 种参数组合")
        
        optimization_results = []
        
        for i, (long_ma, short_ma, deviation, stop_loss) in enumerate(param_combinations, 1):
            print(f"[{i:2d}/{len(param_combinations)}] 测试参数: "
                  f"长期MA={long_ma}, 短期MA={short_ma}, "
                  f"偏离={deviation*100:.0f}%, 止损={stop_loss*100:.0f}%")
            
            stock_results = []
            
            for symbol in self.price_data.keys():
                try:
                    result = self.backtest_strategy(symbol, long_ma, short_ma, deviation, stop_loss)
                    if result:
                        stock_results.append(result)
                except:
                    continue
            
            if stock_results:
                # 汇总统计
                avg_strategy_return = np.mean([r['total_strategy_return'] for r in stock_results])
                avg_buy_hold_return = np.mean([r['total_buy_hold_return'] for r in stock_results])
                avg_excess_return = avg_strategy_return - avg_buy_hold_return
                avg_win_rate = np.mean([r['win_rate'] for r in stock_results])
                avg_trades = np.mean([r['total_trades'] for r in stock_results])
                
                outperform_count = sum(1 for r in stock_results if r['excess_return'] > 0)
                outperform_rate = outperform_count / len(stock_results) * 100
                
                optimization_results.append({
                    'long_ma': long_ma,
                    'short_ma': short_ma,
                    'deviation_threshold': deviation,
                    'stop_loss': stop_loss,
                    'avg_strategy_return': avg_strategy_return,
                    'avg_buy_hold_return': avg_buy_hold_return,
                    'avg_excess_return': avg_excess_return,
                    'avg_win_rate': avg_win_rate,
                    'avg_trades': avg_trades,
                    'outperform_rate': outperform_rate,
                    'stock_count': len(stock_results)
                })
        
        self.optimization_results = optimization_results
        print(f"✅ 参数优化完成")
        
        return optimization_results
    
    def analyze_optimization_results(self):
        """分析优化结果"""
        if not self.optimization_results:
            print("❌ 没有优化结果")
            return
        
        print("\n📊 参数优化结果分析")
        print("=" * 100)
        
        # 按超额收益排序
        sorted_results = sorted(self.optimization_results, 
                               key=lambda x: x['avg_excess_return'], reverse=True)
        
        print("🏆 表现最佳的10组参数:")
        print(f"{'排名':>4} {'长期MA':>7} {'短期MA':>7} {'偏离%':>6} {'止损%':>6} "
              f"{'策略收益':>10} {'超额收益':>10} {'胜率':>6} {'跑赢率':>8} {'交易次数':>8}")
        print("-" * 100)
        
        for i, result in enumerate(sorted_results[:10], 1):
            print(f"{i:>4} "
                  f"{result['long_ma']:>7d} "
                  f"{result['short_ma']:>7d} "
                  f"{result['deviation_threshold']*100:>5.0f}% "
                  f"{result['stop_loss']*100:>5.0f}% "
                  f"{result['avg_strategy_return']:>9.1f}% "
                  f"{result['avg_excess_return']:>9.1f}% "
                  f"{result['avg_win_rate']:>5.1f}% "
                  f"{result['outperform_rate']:>7.1f}% "
                  f"{result['avg_trades']:>7.1f}")
        
        # 最佳参数
        best_params = sorted_results[0]
        print(f"\n🎯 最优参数组合:")
        print(f"   长期均线: {best_params['long_ma']}日")
        print(f"   短期均线: {best_params['short_ma']}日")
        print(f"   偏离阈值: {best_params['deviation_threshold']*100:.0f}%")
        print(f"   止损阈值: {best_params['stop_loss']*100:.0f}%")
        print(f"   平均超额收益: {best_params['avg_excess_return']:.2f}%")
        print(f"   策略跑赢率: {best_params['outperform_rate']:.1f}%")
        
        # 参数敏感性分析
        print(f"\n📈 参数敏感性分析:")
        
        # 长期均线影响
        long_ma_groups = {}
        for result in self.optimization_results:
            long_ma = result['long_ma']
            if long_ma not in long_ma_groups:
                long_ma_groups[long_ma] = []
            long_ma_groups[long_ma].append(result['avg_excess_return'])
        
        print(f"   长期均线影响:")
        for long_ma in sorted(long_ma_groups.keys()):
            avg_excess = np.mean(long_ma_groups[long_ma])
            print(f"     {long_ma}日: 平均超额收益 {avg_excess:.2f}%")
        
        # 短期均线影响
        short_ma_groups = {}
        for result in self.optimization_results:
            short_ma = result['short_ma']
            if short_ma not in short_ma_groups:
                short_ma_groups[short_ma] = []
            short_ma_groups[short_ma].append(result['avg_excess_return'])
        
        print(f"   短期均线影响:")
        for short_ma in sorted(short_ma_groups.keys()):
            avg_excess = np.mean(short_ma_groups[short_ma])
            print(f"     {short_ma}日: 平均超额收益 {avg_excess:.2f}%")
        
        return best_params
    
    def detailed_analysis_with_best_params(self, best_params):
        """使用最优参数进行详细分析"""
        print(f"\n🔍 使用最优参数进行详细分析")
        print("=" * 60)
        
        detailed_results = []
        
        for symbol in self.price_data.keys():
            try:
                result = self.backtest_strategy(
                    symbol, 
                    best_params['long_ma'],
                    best_params['short_ma'],
                    best_params['deviation_threshold'],
                    best_params['stop_loss']
                )
                if result:
                    detailed_results.append(result)
            except:
                continue
        
        # 按超额收益排序
        sorted_results = sorted(detailed_results, key=lambda x: x['excess_return'], reverse=True)
        
        print(f"📊 最优参数下各股票表现:")
        print(f"{'股票':>6} {'策略收益':>10} {'买入持有':>10} {'超额收益':>10} {'胜率':>6} {'交易次数':>8}")
        print("-" * 65)
        
        for result in sorted_results:
            print(f"{result['symbol']:>6} "
                  f"{result['total_strategy_return']:>9.1f}% "
                  f"{result['total_buy_hold_return']:>9.1f}% "
                  f"{result['excess_return']:>9.1f}% "
                  f"{result['win_rate']:>5.1f}% "
                  f"{result['total_trades']:>7d}")
        
        # 交易原因分析
        total_profit_taking = sum(r['profit_taking_trades'] for r in detailed_results)
        total_trend_break = sum(r['trend_break_trades'] for r in detailed_results)
        total_stop_loss = sum(r['stop_loss_trades'] for r in detailed_results)
        total_trades = total_profit_taking + total_trend_break + total_stop_loss
        
        print(f"\n📊 交易原因分析:")
        print(f"   获利了结: {total_profit_taking} 次 ({total_profit_taking/total_trades*100:.1f}%)")
        print(f"   趋势破坏: {total_trend_break} 次 ({total_trend_break/total_trades*100:.1f}%)")
        print(f"   止损退出: {total_stop_loss} 次 ({total_stop_loss/total_trades*100:.1f}%)")
        
        return detailed_results
    
    def generate_optimization_report(self, output_file='trend_strategy_optimization_report.txt'):
        """生成优化报告"""
        print("📋 生成优化报告...")
        
        if not self.optimization_results:
            print("❌ 没有优化结果")
            return
        
        best_params = sorted(self.optimization_results, 
                           key=lambda x: x['avg_excess_return'], reverse=True)[0]
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("长期趋势+短期均值回归策略参数优化报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"优化时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"测试股票数量: {len(self.price_data)}\n")
            f.write(f"参数组合数量: {len(self.optimization_results)}\n\n")
            
            f.write("🎯 最优参数组合:\n")
            f.write(f"   长期均线: {best_params['long_ma']}日\n")
            f.write(f"   短期均线: {best_params['short_ma']}日\n")
            f.write(f"   偏离阈值: {best_params['deviation_threshold']*100:.0f}%\n")
            f.write(f"   止损阈值: {best_params['stop_loss']*100:.0f}%\n\n")
            
            f.write("📊 最优参数表现:\n")
            f.write(f"   平均策略收益: {best_params['avg_strategy_return']:.2f}%\n")
            f.write(f"   平均买入持有收益: {best_params['avg_buy_hold_return']:.2f}%\n")
            f.write(f"   平均超额收益: {best_params['avg_excess_return']:.2f}%\n")
            f.write(f"   平均胜率: {best_params['avg_win_rate']:.1f}%\n")
            f.write(f"   策略跑赢率: {best_params['outperform_rate']:.1f}%\n")
            f.write(f"   平均交易次数: {best_params['avg_trades']:.1f}次\n")
        
        print(f"📋 优化报告已保存: {output_file}")

def main():
    """主函数"""
    print("🚀 长期趋势+短期均值回归策略参数优化")
    print("=" * 70)
    
    # 创建策略优化器
    optimizer = OptimizedTrendMeanReversionStrategy()
    
    # 加载数据
    if not optimizer.load_price_data(max_stocks=30):
        print("❌ 数据加载失败")
        return
    
    # 参数优化
    optimization_results = optimizer.optimize_parameters()
    
    if not optimization_results:
        print("❌ 参数优化失败")
        return
    
    # 分析优化结果
    best_params = optimizer.analyze_optimization_results()
    
    # 使用最优参数进行详细分析
    detailed_results = optimizer.detailed_analysis_with_best_params(best_params)
    
    # 生成报告
    optimizer.generate_optimization_report()
    
    print(f"\n🎯 策略参数优化完成!")
    print(f"💡 关键发现:")
    if best_params['avg_excess_return'] > 0:
        print(f"   ✅ 策略有效：平均超额收益 {best_params['avg_excess_return']:.2f}%")
        print(f"   ✅ 跑赢率：{best_params['outperform_rate']:.1f}%")
    else:
        print(f"   ❌ 策略效果有限：平均超额收益 {best_params['avg_excess_return']:.2f}%")
        print(f"   ❌ 跑赢率：{best_params['outperform_rate']:.1f}%")

if __name__ == "__main__":
    main()
