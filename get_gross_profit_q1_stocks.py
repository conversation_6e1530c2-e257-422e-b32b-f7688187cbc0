#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取毛利润增长加速度最高组（Q1组）股票的简单脚本
"""

from gross_profit_acce_quintile_backtest import get_latest_gross_profit_q1_stocks, GrossProfitQuintileBacktest
import pandas as pd
from datetime import datetime

def main():
    """主函数 - 获取最新毛利润增长加速度Q1组股票"""
    print("🚀 获取毛利润增长加速度最高组（Q1组）股票")
    print("="*60)
    
    try:
        # 方法1：使用便捷函数获取最新Q1组股票
        print("\n📊 方法1：获取最新毛利润增长加速度Q1组股票")
        q1_stocks = get_latest_gross_profit_q1_stocks()
        
        if not q1_stocks.empty:
            print(f"\n✅ 成功获取 {len(q1_stocks)} 只Q1组股票")
            
            # 显示前10只股票的详细信息
            print("\n🏆 前10只股票详情:")
            print(f"{'排名':<4} {'股票代码':<12} {'毛利润增长加速度':<15} {'毛利润增长率':<12} {'报告日期':<12}")
            print("-" * 70)
            
            for i, (_, row) in enumerate(q1_stocks.head(10).iterrows(), 1):
                gross_profit_yoy = row.get('gross_profit_yoy', 'N/A')
                gross_profit_yoy_str = f"{gross_profit_yoy:.2f}%" if isinstance(gross_profit_yoy, (int, float)) else str(gross_profit_yoy)
                print(f"{i:<4} {row['stock_code']:<12} {row['gross_profit_acceleration']:<15.2f} {gross_profit_yoy_str:<12} {row['report_date'].strftime('%Y-%m-%d'):<12}")
        
        # 方法2：指定特定日期获取Q1组股票
        print("\n📊 方法2：获取特定日期的毛利润增长加速度Q1组股票")
        target_date = "2024-12-31"  # 可以修改为你想要的日期
        print(f"目标日期: {target_date}")
        
        q1_stocks_specific = get_latest_gross_profit_q1_stocks(target_date=target_date)
        
        if not q1_stocks_specific.empty:
            print(f"✅ 成功获取 {target_date} 的 {len(q1_stocks_specific)} 只Q1组股票")
        
        # 方法3：使用类实例进行更灵活的操作
        print("\n📊 方法3：使用类实例进行灵活操作")
        backtest = GrossProfitQuintileBacktest()
        
        # 可以调整回看天数
        q1_stocks_custom = backtest.get_latest_q1_stocks(
            target_date="2024-12-31",
            lookback_days=120  # 回看120天
        )
        
        if not q1_stocks_custom.empty:
            print(f"✅ 使用120天回看期获取到 {len(q1_stocks_custom)} 只Q1组股票")
            
            # 获取统计信息
            print(f"\n📈 Q1组股票统计信息:")
            print(f"  毛利润增长加速度范围: {q1_stocks_custom['gross_profit_acceleration'].min():.2f} ~ {q1_stocks_custom['gross_profit_acceleration'].max():.2f}")
            print(f"  毛利润增长加速度均值: {q1_stocks_custom['gross_profit_acceleration'].mean():.2f}")
            print(f"  毛利润增长加速度中位数: {q1_stocks_custom['gross_profit_acceleration'].median():.2f}")
            
            if 'gross_profit_yoy' in q1_stocks_custom.columns:
                gross_profit_yoy_valid = q1_stocks_custom['gross_profit_yoy'].dropna()
                if not gross_profit_yoy_valid.empty:
                    print(f"  毛利润增长率范围: {gross_profit_yoy_valid.min():.2f}% ~ {gross_profit_yoy_valid.max():.2f}%")
                    print(f"  毛利润增长率均值: {gross_profit_yoy_valid.mean():.2f}%")
        
        print("\n" + "="*60)
        print("🎉 毛利润增长加速度Q1组股票获取完成！")
        print("📁 结果已保存到 latest_gross_profit_q1_stocks/ 目录下")
        
        return q1_stocks
        
    except Exception as e:
        print(f"❌ 获取毛利润增长加速度Q1组股票时出错: {e}")
        return pd.DataFrame()


def get_gross_profit_q1_stocks_summary():
    """获取毛利润增长加速度Q1组股票摘要信息"""
    print("📊 获取毛利润增长加速度Q1组股票摘要...")
    
    try:
        q1_stocks = get_latest_gross_profit_q1_stocks()
        
        if q1_stocks.empty:
            print("❌ 未获取到毛利润增长加速度Q1组股票")
            return None
        
        # 创建摘要
        summary = {
            '总股票数': len(q1_stocks),
            '最高加速度': q1_stocks['gross_profit_acceleration'].max(),
            '最低加速度': q1_stocks['gross_profit_acceleration'].min(),
            '平均加速度': q1_stocks['gross_profit_acceleration'].mean(),
            '中位数加速度': q1_stocks['gross_profit_acceleration'].median(),
        }
        
        # 如果有毛利润增长率数据
        if 'gross_profit_yoy' in q1_stocks.columns:
            gross_profit_yoy_valid = q1_stocks['gross_profit_yoy'].dropna()
            if not gross_profit_yoy_valid.empty:
                summary.update({
                    '最高增长率': gross_profit_yoy_valid.max(),
                    '最低增长率': gross_profit_yoy_valid.min(),
                    '平均增长率': gross_profit_yoy_valid.mean(),
                })
        
        print("📈 毛利润增长加速度Q1组股票摘要:")
        for key, value in summary.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")
        
        return summary
        
    except Exception as e:
        print(f"❌ 获取摘要时出错: {e}")
        return None


def compare_with_revenue_acceleration():
    """对比毛利润增长加速度和营收增长加速度的Q1组股票"""
    print("\n📊 对比分析：毛利润增长加速度 vs 营收增长加速度")
    print("="*70)
    
    try:
        # 获取毛利润增长加速度Q1组股票
        gross_profit_q1 = get_latest_gross_profit_q1_stocks()
        
        # 获取营收增长加速度Q1组股票
        from revenue_acce_quintile_backtest import get_latest_q1_stocks
        revenue_q1 = get_latest_q1_stocks()
        
        if gross_profit_q1.empty or revenue_q1.empty:
            print("❌ 无法获取完整的对比数据")
            return
        
        # 找出重叠的股票
        gross_profit_stocks = set(gross_profit_q1['stock_code'].tolist())
        revenue_stocks = set(revenue_q1['stock_code'].tolist())
        
        overlap_stocks = gross_profit_stocks.intersection(revenue_stocks)
        
        print(f"📈 对比结果:")
        print(f"  毛利润增长加速度Q1组股票数: {len(gross_profit_stocks)}")
        print(f"  营收增长加速度Q1组股票数: {len(revenue_stocks)}")
        print(f"  重叠股票数: {len(overlap_stocks)}")
        print(f"  重叠比例: {len(overlap_stocks) / min(len(gross_profit_stocks), len(revenue_stocks)) * 100:.1f}%")
        
        if overlap_stocks:
            print(f"\n🔄 重叠股票列表:")
            for stock in sorted(overlap_stocks):
                print(f"  {stock}")
        
        # 显示各自独有的股票
        gross_profit_only = gross_profit_stocks - revenue_stocks
        revenue_only = revenue_stocks - gross_profit_stocks
        
        print(f"\n📊 仅在毛利润增长加速度Q1组的股票 ({len(gross_profit_only)}只):")
        for stock in sorted(list(gross_profit_only)[:10]):  # 显示前10只
            print(f"  {stock}")
        if len(gross_profit_only) > 10:
            print(f"  ... 还有 {len(gross_profit_only) - 10} 只")
        
        print(f"\n📊 仅在营收增长加速度Q1组的股票 ({len(revenue_only)}只):")
        for stock in sorted(list(revenue_only)[:10]):  # 显示前10只
            print(f"  {stock}")
        if len(revenue_only) > 10:
            print(f"  ... 还有 {len(revenue_only) - 10} 只")
        
    except Exception as e:
        print(f"❌ 对比分析时出错: {e}")


if __name__ == "__main__":
    # 获取毛利润增长加速度Q1组股票
    q1_stocks = main()
    
    # 获取摘要信息
    print("\n" + "="*60)
    summary = get_gross_profit_q1_stocks_summary()
    
    # 进行对比分析
    compare_with_revenue_acceleration()
