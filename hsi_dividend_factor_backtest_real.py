#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子回测分析（基于真实分红数据）

使用刚才爬取的真实分红数据进行股息率因子回测分析
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import seaborn as sns
import akshare as ak
import warnings
from datetime import datetime, timedelta
import os
import time
import csv
import json

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 忽略警告
warnings.filterwarnings('ignore')

class HSIDividendFactorBacktestReal:
    """基于真实分红数据的恒生指数股息率因子回测分析器"""
    
    def __init__(self, 
                 dividend_data_file: str = "hsi_dividend_quick_20250603_141448.csv",
                 period: str = "2y",
                 rebalance_freq: str = "Q",  # Q=季度, M=月度, Y=年度
                 n_groups: int = 5,
                 cache_dir: str = "hsi_dividend_backtest_cache"):
        """
        初始化回测分析器
        
        Args:
            dividend_data_file: 分红数据文件路径
            period: 数据获取周期
            rebalance_freq: 再平衡频率
            n_groups: 分组数量
            cache_dir: 缓存目录
        """
        self.dividend_data_file = dividend_data_file
        self.period = period
        self.rebalance_freq = rebalance_freq
        self.n_groups = n_groups
        self.cache_dir = cache_dir
        
        # 数据存储
        self.dividend_data = {}  # 真实分红数据
        self.stock_data = {}     # 股价数据
        self.benchmark_data = None  # 基准数据
        self.symbols = []        # 股票代码列表
        
        # 回测结果
        self.portfolio_returns = {}
        self.factor_analysis = {}
        self.backtest_results = {}
        
        # 创建缓存目录
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
            print(f"📁 创建缓存目录: {cache_dir}")
        
        print(f"🚀 基于真实分红数据的股息率因子回测分析器已初始化")
        print(f"📊 回测周期: {period}, 再平衡频率: {rebalance_freq}, 分组数: {n_groups}")
    
    def load_real_dividend_data(self) -> bool:
        """加载真实分红数据"""
        try:
            print(f"📁 加载真实分红数据: {self.dividend_data_file}")
            
            if not os.path.exists(self.dividend_data_file):
                print(f"❌ 分红数据文件不存在: {self.dividend_data_file}")
                return False
            
            # 读取CSV文件
            df = pd.read_csv(self.dividend_data_file, encoding='utf-8')
            
            # 过滤有效数据
            valid_df = df[df['状态'] == 'success'].copy()
            
            print(f"📊 总股票数: {len(df)}, 有效数据: {len(valid_df)}")
            
            # 转换数据格式
            for _, row in valid_df.iterrows():
                symbol = row['股票代码']
                name = row['股票名称']
                latest_dividend = float(row['最新分红(港元)'])
                dividend_yield = float(row['股息率(%)'])
                dividend_years = int(row['分红年数'])
                mock_price = float(row['模拟股价(港元)'])
                
                self.dividend_data[symbol] = {
                    'name': name,
                    'latest_dividend': latest_dividend,
                    'dividend_yield': dividend_yield,
                    'dividend_years': dividend_years,
                    'mock_price': mock_price
                }
                
                self.symbols.append(symbol)
            
            print(f"✅ 成功加载 {len(self.symbols)} 只股票的分红数据")
            return True
            
        except Exception as e:
            print(f"❌ 加载分红数据失败: {e}")
            return False
    
    def get_stock_price_data(self, symbol: str) -> pd.DataFrame:
        """获取股票价格数据"""
        try:
            cache_file = os.path.join(self.cache_dir, f"{symbol}_price.pkl")
            
            # 检查缓存
            if os.path.exists(cache_file):
                try:
                    cached_data = pd.read_pickle(cache_file)
                    if not cached_data.empty:
                        print(f"📦 使用缓存数据: {symbol}")
                        return cached_data
                except:
                    pass
            
            print(f"📈 获取 {symbol} 股价数据...")
            
            # 使用akshare获取港股数据
            stock_df = ak.stock_hk_daily(symbol=symbol)
            
            if stock_df.empty:
                print(f"⚠️  {symbol} 无股价数据")
                return pd.DataFrame()
            
            # 数据预处理
            stock_df['date'] = pd.to_datetime(stock_df['date'])
            stock_df.set_index('date', inplace=True)
            stock_df = stock_df.sort_index()
            
            # 过滤最近2年数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=730)
            stock_df = stock_df[stock_df.index >= start_date]
            
            # 保存缓存
            stock_df.to_pickle(cache_file)
            
            print(f"✅ {symbol} 股价数据: {len(stock_df)} 个交易日")
            return stock_df
            
        except Exception as e:
            print(f"❌ 获取 {symbol} 股价数据失败: {e}")
            return pd.DataFrame()
    
    def download_all_data(self) -> bool:
        """下载所有股票的价格数据"""
        try:
            print(f"📥 开始下载 {len(self.symbols)} 只股票的价格数据...")
            
            # 下载恒生指数基准数据
            print("📈 下载恒生指数基准数据...")
            try:
                hsi_data = ak.stock_hk_index_daily_em(symbol="HSI")
                if not hsi_data.empty:
                    hsi_data['date'] = pd.to_datetime(hsi_data['date'])
                    hsi_data.set_index('date', inplace=True)
                    hsi_data = hsi_data.sort_index()
                    
                    # 过滤最近2年数据
                    end_date = datetime.now()
                    start_date = end_date - timedelta(days=730)
                    self.benchmark_data = hsi_data[hsi_data.index >= start_date]
                    print(f"✅ 恒生指数数据: {len(self.benchmark_data)} 个数据点")
            except Exception as e:
                print(f"⚠️  恒生指数数据获取失败: {e}")
            
            # 下载个股数据
            success_count = 0
            for i, symbol in enumerate(self.symbols):
                print(f"[{i+1}/{len(self.symbols)}] 处理 {symbol}...")
                
                price_data = self.get_stock_price_data(symbol)
                if not price_data.empty:
                    self.stock_data[symbol] = price_data
                    success_count += 1
                
                # 添加延时避免API限制
                time.sleep(0.5)
                
                # 进度显示
                if (i + 1) % 10 == 0:
                    print(f"📊 已处理 {i+1}/{len(self.symbols)} 只股票，成功 {success_count} 只")
            
            print(f"✅ 价格数据下载完成: {success_count}/{len(self.symbols)} 只股票")
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 数据下载失败: {e}")
            return False
    
    def calculate_factor_scores(self, date: datetime) -> pd.Series:
        """计算指定日期的股息率因子得分"""
        scores = {}
        
        for symbol in self.symbols:
            if symbol in self.dividend_data:
                # 使用真实的股息率数据
                dividend_yield = self.dividend_data[symbol]['dividend_yield']
                scores[symbol] = dividend_yield
        
        return pd.Series(scores)
    
    def create_portfolios(self, factor_scores: pd.Series) -> dict:
        """基于因子得分创建投资组合"""
        # 按股息率排序（降序）
        sorted_scores = factor_scores.sort_values(ascending=False)
        
        # 分组
        n_stocks_per_group = len(sorted_scores) // self.n_groups
        portfolios = {}
        
        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(sorted_scores)
            else:
                end_idx = (i + 1) * n_stocks_per_group
            
            group_stocks = sorted_scores.iloc[start_idx:end_idx].index.tolist()
            portfolios[f'Group_{i+1}'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_returns(self, portfolios: dict, 
                                  start_date: datetime, end_date: datetime) -> dict:
        """计算投资组合收益率"""
        portfolio_returns = {}
        
        for portfolio_name, stocks in portfolios.items():
            returns_list = []
            
            for symbol in stocks:
                if symbol in self.stock_data:
                    stock_df = self.stock_data[symbol]
                    # 过滤日期范围
                    period_data = stock_df[(stock_df.index >= start_date) & 
                                         (stock_df.index <= end_date)]
                    
                    if len(period_data) > 1:
                        # 计算收益率
                        stock_returns = period_data['close'].pct_change().fillna(0)
                        returns_list.append(stock_returns)
            
            if returns_list:
                # 等权重组合
                portfolio_df = pd.concat(returns_list, axis=1)
                portfolio_returns[portfolio_name] = portfolio_df.mean(axis=1)
            else:
                # 如果没有数据，创建零收益序列
                date_range = pd.date_range(start=start_date, end=end_date, freq='D')
                portfolio_returns[portfolio_name] = pd.Series(0, index=date_range)
        
        return portfolio_returns
    
    def run_backtest(self) -> bool:
        """运行股息率因子回测"""
        try:
            print("🚀 开始股息率因子回测...")
            
            # 1. 加载真实分红数据
            if not self.load_real_dividend_data():
                return False
            
            # 2. 下载股价数据
            if not self.download_all_data():
                return False
            
            # 3. 确定回测时间范围
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)  # 1年回测期
            
            print(f"📅 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")
            
            # 4. 生成再平衡日期
            if self.rebalance_freq == 'Q':
                freq = 'QS'  # 季度开始
            elif self.rebalance_freq == 'M':
                freq = 'MS'  # 月度开始
            else:
                freq = 'YS'  # 年度开始
            
            rebalance_dates = pd.date_range(start=start_date, end=end_date, freq=freq)
            rebalance_dates = [d.to_pydatetime() for d in rebalance_dates]
            
            print(f"🔄 再平衡次数: {len(rebalance_dates)}")
            
            # 5. 执行回测
            all_portfolio_returns = {}
            
            for i, rebalance_date in enumerate(rebalance_dates):
                print(f"🔄 再平衡 {i+1}/{len(rebalance_dates)}: {rebalance_date.strftime('%Y-%m-%d')}")
                
                # 计算因子得分（使用真实股息率）
                factor_scores = self.calculate_factor_scores(rebalance_date)
                if factor_scores.empty:
                    continue
                
                # 创建投资组合
                portfolios = self.create_portfolios(factor_scores)
                
                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    hold_end = rebalance_dates[i + 1]
                else:
                    hold_end = end_date
                
                # 计算期间收益
                period_returns = self.calculate_portfolio_returns(
                    portfolios, rebalance_date, hold_end
                )
                
                # 累积收益
                for portfolio_name, returns in period_returns.items():
                    if portfolio_name not in all_portfolio_returns:
                        all_portfolio_returns[portfolio_name] = []
                    all_portfolio_returns[portfolio_name].append(returns)
            
            # 6. 合并所有期间的收益
            self.portfolio_returns = {}
            for portfolio_name, returns_list in all_portfolio_returns.items():
                if returns_list:
                    combined_returns = pd.concat(returns_list)
                    combined_returns = combined_returns.sort_index()
                    self.portfolio_returns[portfolio_name] = combined_returns
            
            print("✅ 回测完成!")
            return True
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def analyze_factor_performance(self) -> dict:
        """分析因子表现"""
        if not self.portfolio_returns:
            print("❌ 没有回测结果可供分析")
            return {}
        
        analysis = {}
        
        print("📊 分析因子表现...")
        
        # 计算基准收益
        if self.benchmark_data is not None:
            benchmark_returns = self.benchmark_data['close'].pct_change().fillna(0)
            analysis['benchmark_returns'] = benchmark_returns
        
        # 计算各组合的绩效指标
        for portfolio_name, returns in self.portfolio_returns.items():
            if len(returns) > 0:
                # 基本统计
                total_return = (1 + returns).prod() - 1
                annual_return = (1 + total_return) ** (252 / len(returns)) - 1
                volatility = returns.std() * np.sqrt(252)
                sharpe_ratio = annual_return / volatility if volatility > 0 else 0
                
                # 最大回撤
                cumulative = (1 + returns).cumprod()
                running_max = cumulative.expanding().max()
                drawdown = (cumulative - running_max) / running_max
                max_drawdown = drawdown.min()
                
                analysis[portfolio_name] = {
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'returns': returns
                }
        
        self.factor_analysis = analysis
        return analysis

    def plot_backtest_results(self, save_path: str = "hsi_dividend_factor_backtest.png"):
        """绘制回测结果图表"""
        try:
            if not self.factor_analysis:
                print("❌ 没有分析结果可供绘制")
                return

            print(f"📊 绘制回测结果图表...")

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('恒生指数成分股股息率因子回测结果', fontsize=16, fontweight='bold')

            # 子图1: 累积收益率
            ax1 = axes[0, 0]
            for portfolio_name, analysis in self.factor_analysis.items():
                if 'returns' in analysis:
                    returns = analysis['returns']
                    cumulative = (1 + returns).cumprod()
                    ax1.plot(cumulative.index, cumulative.values,
                            label=portfolio_name, linewidth=2)

            ax1.set_title('累积收益率对比', fontsize=14)
            ax1.set_xlabel('日期')
            ax1.set_ylabel('累积收益率')
            ax1.legend()
            ax1.grid(True, alpha=0.3)

            # 子图2: 年化收益率对比
            ax2 = axes[0, 1]
            portfolio_names = []
            annual_returns = []

            for portfolio_name, analysis in self.factor_analysis.items():
                if 'annual_return' in analysis:
                    portfolio_names.append(portfolio_name)
                    annual_returns.append(analysis['annual_return'] * 100)

            bars = ax2.bar(portfolio_names, annual_returns, alpha=0.7)
            ax2.set_title('年化收益率对比', fontsize=14)
            ax2.set_xlabel('投资组合')
            ax2.set_ylabel('年化收益率 (%)')
            ax2.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, annual_returns):
                ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        f'{value:.1f}%', ha='center', va='bottom')

            # 子图3: 夏普比率对比
            ax3 = axes[1, 0]
            sharpe_ratios = []

            for portfolio_name in portfolio_names:
                if portfolio_name in self.factor_analysis:
                    sharpe_ratios.append(self.factor_analysis[portfolio_name]['sharpe_ratio'])

            bars = ax3.bar(portfolio_names, sharpe_ratios, alpha=0.7, color='orange')
            ax3.set_title('夏普比率对比', fontsize=14)
            ax3.set_xlabel('投资组合')
            ax3.set_ylabel('夏普比率')
            ax3.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, sharpe_ratios):
                ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                        f'{value:.2f}', ha='center', va='bottom')

            # 子图4: 最大回撤对比
            ax4 = axes[1, 1]
            max_drawdowns = []

            for portfolio_name in portfolio_names:
                if portfolio_name in self.factor_analysis:
                    max_drawdowns.append(abs(self.factor_analysis[portfolio_name]['max_drawdown']) * 100)

            bars = ax4.bar(portfolio_names, max_drawdowns, alpha=0.7, color='red')
            ax4.set_title('最大回撤对比', fontsize=14)
            ax4.set_xlabel('投资组合')
            ax4.set_ylabel('最大回撤 (%)')
            ax4.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar, value in zip(bars, max_drawdowns):
                ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                        f'{value:.1f}%', ha='center', va='bottom')

            plt.tight_layout()
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存: {save_path}")
            plt.close()

        except Exception as e:
            print(f"❌ 绘制图表失败: {e}")
            import traceback
            traceback.print_exc()

    def generate_backtest_report(self, save_path: str = "hsi_dividend_factor_backtest_report.txt"):
        """生成回测分析报告"""
        try:
            if not self.factor_analysis:
                print("❌ 没有分析结果可供报告")
                return

            print(f"📄 生成回测分析报告...")

            report_lines = []
            report_lines.append("=" * 80)
            report_lines.append("恒生指数成分股股息率因子回测分析报告")
            report_lines.append("=" * 80)
            report_lines.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
            report_lines.append(f"数据来源: 真实分红数据 + akshare股价数据")
            report_lines.append(f"回测周期: {self.period}")
            report_lines.append(f"再平衡频率: {self.rebalance_freq}")
            report_lines.append(f"分组数量: {self.n_groups}")
            report_lines.append("")

            # 基本信息
            report_lines.append("基本信息")
            report_lines.append("-" * 40)
            report_lines.append(f"股票数量: {len(self.symbols)}")
            report_lines.append(f"有价格数据股票: {len(self.stock_data)}")
            report_lines.append(f"有分红数据股票: {len(self.dividend_data)}")
            report_lines.append("")

            # 分红数据概览
            if self.dividend_data:
                dividend_yields = [data['dividend_yield'] for data in self.dividend_data.values()]
                report_lines.append("分红数据概览")
                report_lines.append("-" * 40)
                report_lines.append(f"平均股息率: {np.mean(dividend_yields):.2f}%")
                report_lines.append(f"最高股息率: {max(dividend_yields):.2f}%")
                report_lines.append(f"最低股息率: {min(dividend_yields):.2f}%")
                report_lines.append(f"股息率标准差: {np.std(dividend_yields):.2f}%")
                report_lines.append("")

            # 回测结果
            report_lines.append("回测结果")
            report_lines.append("-" * 60)
            report_lines.append(f"{'组合名称':<12} {'总收益率':<10} {'年化收益率':<10} {'波动率':<8} {'夏普比率':<8} {'最大回撤':<8}")
            report_lines.append("-" * 60)

            for portfolio_name, analysis in self.factor_analysis.items():
                if 'total_return' in analysis:
                    total_ret = f"{analysis['total_return']*100:.1f}%"
                    annual_ret = f"{analysis['annual_return']*100:.1f}%"
                    vol = f"{analysis['volatility']*100:.1f}%"
                    sharpe = f"{analysis['sharpe_ratio']:.2f}"
                    drawdown = f"{abs(analysis['max_drawdown'])*100:.1f}%"

                    report_lines.append(f"{portfolio_name:<12} {total_ret:<10} {annual_ret:<10} {vol:<8} {sharpe:<8} {drawdown:<8}")

            report_lines.append("")

            # 因子有效性分析
            if len(self.factor_analysis) >= 2:
                report_lines.append("因子有效性分析")
                report_lines.append("-" * 40)

                # 获取高股息率组合和低股息率组合的表现
                group_names = sorted([name for name in self.factor_analysis.keys() if 'Group_' in name])
                if len(group_names) >= 2:
                    high_dividend_group = group_names[0]  # Group_1 是最高股息率组
                    low_dividend_group = group_names[-1]  # Group_5 是最低股息率组

                    high_return = self.factor_analysis[high_dividend_group]['annual_return']
                    low_return = self.factor_analysis[low_dividend_group]['annual_return']

                    spread = high_return - low_return

                    report_lines.append(f"高股息率组合 ({high_dividend_group}) 年化收益率: {high_return*100:.2f}%")
                    report_lines.append(f"低股息率组合 ({low_dividend_group}) 年化收益率: {low_return*100:.2f}%")
                    report_lines.append(f"多空收益差: {spread*100:.2f}%")

                    if spread > 0:
                        report_lines.append("✅ 股息率因子表现正向，高股息率股票表现更好")
                    else:
                        report_lines.append("❌ 股息率因子表现负向，低股息率股票表现更好")

                    report_lines.append("")

            # 投资建议
            report_lines.append("投资建议")
            report_lines.append("-" * 40)

            # 找出表现最好的组合
            best_portfolio = None
            best_sharpe = -999

            for portfolio_name, analysis in self.factor_analysis.items():
                if 'sharpe_ratio' in analysis and analysis['sharpe_ratio'] > best_sharpe:
                    best_sharpe = analysis['sharpe_ratio']
                    best_portfolio = portfolio_name

            if best_portfolio:
                report_lines.append(f"最佳风险调整收益组合: {best_portfolio}")
                report_lines.append(f"夏普比率: {best_sharpe:.2f}")

                best_analysis = self.factor_analysis[best_portfolio]
                report_lines.append(f"年化收益率: {best_analysis['annual_return']*100:.2f}%")
                report_lines.append(f"最大回撤: {abs(best_analysis['max_drawdown'])*100:.2f}%")

            report_lines.append("")
            report_lines.append("=" * 80)

            # 保存报告
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write('\n'.join(report_lines))

            print(f"📄 报告已保存: {save_path}")

            # 同时打印到控制台
            print("\n".join(report_lines))

        except Exception as e:
            print(f"❌ 生成报告失败: {e}")
            import traceback
            traceback.print_exc()

    def save_backtest_results(self, save_path: str = "hsi_dividend_factor_backtest_results.json"):
        """保存回测结果到JSON文件"""
        try:
            results = {
                'backtest_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'parameters': {
                    'period': self.period,
                    'rebalance_freq': self.rebalance_freq,
                    'n_groups': self.n_groups,
                    'dividend_data_file': self.dividend_data_file
                },
                'data_summary': {
                    'total_stocks': len(self.symbols),
                    'stocks_with_price_data': len(self.stock_data),
                    'stocks_with_dividend_data': len(self.dividend_data)
                },
                'performance_summary': {}
            }

            # 添加绩效摘要
            for portfolio_name, analysis in self.factor_analysis.items():
                if 'total_return' in analysis:
                    results['performance_summary'][portfolio_name] = {
                        'total_return': float(analysis['total_return']),
                        'annual_return': float(analysis['annual_return']),
                        'volatility': float(analysis['volatility']),
                        'sharpe_ratio': float(analysis['sharpe_ratio']),
                        'max_drawdown': float(analysis['max_drawdown'])
                    }

            with open(save_path, 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)

            print(f"💾 回测结果已保存: {save_path}")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数 - 执行完整的股息率因子回测分析"""
    print("🎯 恒生指数成分股股息率因子回测分析（基于真实分红数据）")
    print("=" * 70)

    # 创建回测分析器
    backtest = HSIDividendFactorBacktestReal(
        dividend_data_file="hsi_dividend_quick_20250603_141448.csv",
        period="2y",           # 使用2年数据
        rebalance_freq="Q",    # 季度再平衡
        n_groups=5,            # 分为5组
        cache_dir="hsi_dividend_backtest_cache"
    )

    start_time = time.time()

    try:
        # 1. 运行回测
        print("\n🚀 第一步：运行因子回测...")
        if not backtest.run_backtest():
            print("❌ 回测失败，程序退出")
            return

        # 2. 分析因子表现
        print("\n📊 第二步：分析因子表现...")
        analysis_results = backtest.analyze_factor_performance()

        if not analysis_results:
            print("❌ 因子分析失败")
            return

        # 3. 生成可视化图表
        print("\n📈 第三步：生成可视化图表...")
        backtest.plot_backtest_results("hsi_dividend_factor_backtest.png")

        # 4. 生成分析报告
        print("\n📄 第四步：生成分析报告...")
        backtest.generate_backtest_report("hsi_dividend_factor_backtest_report.txt")

        # 5. 保存回测结果
        print("\n💾 第五步：保存回测结果...")
        backtest.save_backtest_results("hsi_dividend_factor_backtest_results.json")

        # 6. 显示完成信息
        end_time = time.time()
        duration = end_time - start_time

        print(f"\n✅ 股息率因子回测分析完成！总耗时: {duration/60:.1f} 分钟")
        print(f"\n📁 输出文件:")
        print(f"   📊 图表: hsi_dividend_factor_backtest.png")
        print(f"   📄 报告: hsi_dividend_factor_backtest_report.txt")
        print(f"   💾 结果: hsi_dividend_factor_backtest_results.json")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
