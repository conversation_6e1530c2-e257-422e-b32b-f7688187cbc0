#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实时30年期抵押贷款利率数据收集器

这个脚本从多个数据源获取最新的30年期抵押贷款利率数据：
1. FRED API (官方数据，每周更新)
2. Freddie Mac Primary Mortgage Market Survey (每周四更新)
3. Mortgage News Daily (每日更新)
4. Bankrate.com (实时更新)
5. Yahoo Finance (实时更新)

数据更新频率：
- FRED: 每周四
- Freddie Mac: 每周四
- Mortgage News Daily: 每日
- Bankrate: 实时
- Yahoo Finance: 实时
"""

import os
import requests
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import json
import time
from bs4 import BeautifulSoup
import yfinance as yf
import warnings
warnings.filterwarnings('ignore')

# 设置目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

class RealtimeMortgageRateCollector:
    def __init__(self):
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
        })

    def get_fred_data(self):
        """从FRED获取官方数据（每周更新）"""
        print("正在从FRED获取官方30年期抵押贷款利率数据...")
        try:
            from pandas_datareader import data as pdr
            # 获取最近30天的数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=30)

            df = pdr.get_data_fred('MORTGAGE30US', start=start_date, end=end_date)
            if not df.empty:
                latest_rate = df.iloc[-1, 0]
                latest_date = df.index[-1]
                print(f"FRED数据 - 日期: {latest_date.strftime('%Y-%m-%d')}, 利率: {latest_rate:.2f}%")
                return {
                    'source': 'FRED',
                    'rate': latest_rate,
                    'date': latest_date,
                    'update_frequency': '每周四'
                }
        except Exception as e:
            print(f"FRED数据获取失败: {str(e)}")
        return None

    def get_freddie_mac_data(self):
        """从Freddie Mac获取Primary Mortgage Market Survey数据"""
        print("正在从Freddie Mac获取PMMS数据...")
        try:
            # Freddie Mac PMMS API
            url = "http://www.freddiemac.com/pmms/pmms30.html"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                # 查找包含利率数据的表格或元素
                # 这里需要根据实际网页结构调整
                rate_element = soup.find('span', class_='rate') or soup.find('td', class_='rate')
                if rate_element:
                    rate_text = rate_element.get_text().strip()
                    rate = float(rate_text.replace('%', ''))
                    print(f"Freddie Mac PMMS - 利率: {rate:.2f}%")
                    return {
                        'source': 'Freddie Mac PMMS',
                        'rate': rate,
                        'date': datetime.now(),
                        'update_frequency': '每周四'
                    }
        except Exception as e:
            print(f"Freddie Mac数据获取失败: {str(e)}")
        return None

    def get_mortgage_news_daily_data(self):
        """从Mortgage News Daily获取每日数据"""
        print("正在从Mortgage News Daily获取数据...")
        try:
            url = "https://www.mortgagenewsdaily.com/mortgage-rates/30-year-fixed"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                # 查找利率数据
                rate_element = soup.find('span', class_='rate-display') or soup.find('div', class_='current-rate')
                if rate_element:
                    rate_text = rate_element.get_text().strip()
                    rate = float(rate_text.replace('%', ''))
                    print(f"Mortgage News Daily - 利率: {rate:.2f}%")
                    return {
                        'source': 'Mortgage News Daily',
                        'rate': rate,
                        'date': datetime.now(),
                        'update_frequency': '每日'
                    }
        except Exception as e:
            print(f"Mortgage News Daily数据获取失败: {str(e)}")
        return None

    def get_bankrate_data(self):
        """从Bankrate获取实时数据"""
        print("正在从Bankrate获取实时数据...")
        try:
            url = "https://www.bankrate.com/mortgages/mortgage-rates/"
            response = self.session.get(url, timeout=10)

            if response.status_code == 200:
                soup = BeautifulSoup(response.content, 'html.parser')
                # 查找30年固定利率
                rate_elements = soup.find_all('td', class_='rate')
                for element in rate_elements:
                    parent = element.find_parent('tr')
                    if parent and '30-year fixed' in parent.get_text().lower():
                        rate_text = element.get_text().strip()
                        rate = float(rate_text.replace('%', ''))
                        print(f"Bankrate - 利率: {rate:.2f}%")
                        return {
                            'source': 'Bankrate',
                            'rate': rate,
                            'date': datetime.now(),
                            'update_frequency': '实时'
                        }
        except Exception as e:
            print(f"Bankrate数据获取失败: {str(e)}")
        return None

    def get_yahoo_finance_data(self):
        """从Yahoo Finance获取相关债券收益率数据"""
        print("正在从Yahoo Finance获取10年期国债收益率...")
        try:
            # 获取10年期国债收益率作为参考
            ticker = "^TNX"  # 10年期国债收益率
            data = yf.download(ticker, period="5d", interval="1d", progress=False)

            if not data.empty:
                latest_yield = float(data['Close'].iloc[-1])
                # 抵押贷款利率通常比10年期国债收益率高1.5-2.5个百分点
                estimated_mortgage_rate = latest_yield + 1.8  # 使用1.8%的平均利差

                print(f"Yahoo Finance - 10年期国债收益率: {latest_yield:.2f}%")
                print(f"估算抵押贷款利率: {estimated_mortgage_rate:.2f}%")

                return {
                    'source': 'Yahoo Finance (估算)',
                    'rate': estimated_mortgage_rate,
                    'treasury_yield': latest_yield,
                    'date': datetime.now(),
                    'update_frequency': '实时'
                }
        except Exception as e:
            print(f"Yahoo Finance数据获取失败: {str(e)}")
        return None

    def get_alternative_api_data(self):
        """从其他API获取数据"""
        print("正在尝试其他数据源...")
        try:
            # 尝试使用Alpha Vantage API (需要免费API密钥)
            # 这里提供一个示例，实际使用时需要注册获取API密钥
            api_key = "YOUR_ALPHA_VANTAGE_API_KEY"  # 需要替换为实际的API密钥

            if api_key != "YOUR_ALPHA_VANTAGE_API_KEY":
                url = f"https://www.alphavantage.co/query?function=FEDERAL_FUNDS_RATE&apikey={api_key}"
                response = self.session.get(url, timeout=10)

                if response.status_code == 200:
                    data = response.json()
                    # 处理API响应数据
                    # 这里需要根据实际API响应格式调整
                    pass
        except Exception as e:
            print(f"其他API数据获取失败: {str(e)}")
        return None

    def collect_all_sources(self):
        """从所有数据源收集数据"""
        print("开始从多个数据源收集30年期抵押贷款利率数据...")
        print("=" * 60)

        sources_data = []

        # 收集各个数据源的数据
        fred_data = self.get_fred_data()
        if fred_data:
            sources_data.append(fred_data)

        freddie_mac_data = self.get_freddie_mac_data()
        if freddie_mac_data:
            sources_data.append(freddie_mac_data)

        mnd_data = self.get_mortgage_news_daily_data()
        if mnd_data:
            sources_data.append(mnd_data)

        bankrate_data = self.get_bankrate_data()
        if bankrate_data:
            sources_data.append(bankrate_data)

        yahoo_data = self.get_yahoo_finance_data()
        if yahoo_data:
            sources_data.append(yahoo_data)

        return sources_data

    def analyze_and_save_data(self, sources_data):
        """分析并保存数据"""
        if not sources_data:
            print("未获取到任何数据源的数据")
            return

        print("\n" + "=" * 60)
        print("数据汇总分析:")
        print("=" * 60)

        # 创建DataFrame
        df = pd.DataFrame(sources_data)

        # 显示所有数据源的结果
        for i, data in enumerate(sources_data):
            print(f"{i+1}. {data['source']}: {data['rate']:.2f}% (更新频率: {data['update_frequency']})")

        # 计算统计信息
        rates = [data['rate'] for data in sources_data if 'rate' in data]
        if rates:
            avg_rate = np.mean(rates)
            median_rate = np.median(rates)
            min_rate = np.min(rates)
            max_rate = np.max(rates)
            std_rate = np.std(rates)

            print(f"\n统计分析:")
            print(f"平均利率: {avg_rate:.2f}%")
            print(f"中位数利率: {median_rate:.2f}%")
            print(f"最低利率: {min_rate:.2f}%")
            print(f"最高利率: {max_rate:.2f}%")
            print(f"标准差: {std_rate:.2f}%")

            # 保存数据
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

            # 保存详细数据
            df.to_csv(os.path.join(DATA_DIR, f'realtime_mortgage_rates_{timestamp}.csv'), index=False)

            # 保存汇总数据
            summary = {
                'timestamp': datetime.now().isoformat(),
                'average_rate': avg_rate,
                'median_rate': median_rate,
                'min_rate': min_rate,
                'max_rate': max_rate,
                'std_rate': std_rate,
                'sources_count': len(rates),
                'sources_data': sources_data
            }

            with open(os.path.join(DATA_DIR, f'mortgage_rate_summary_{timestamp}.json'), 'w') as f:
                json.dump(summary, f, indent=2, default=str)

            print(f"\n数据已保存到:")
            print(f"- 详细数据: realtime_mortgage_rates_{timestamp}.csv")
            print(f"- 汇总数据: mortgage_rate_summary_{timestamp}.json")

            return summary

        return None

def main():
    """主函数"""
    collector = RealtimeMortgageRateCollector()

    # 收集数据
    sources_data = collector.collect_all_sources()

    # 分析并保存数据
    summary = collector.analyze_and_save_data(sources_data)

    if summary:
        print(f"\n当前推荐利率范围: {summary['min_rate']:.2f}% - {summary['max_rate']:.2f}%")
        print(f"市场平均利率: {summary['average_rate']:.2f}%")

        # 提供建议
        if summary['std_rate'] > 0.5:
            print("\n⚠️  注意: 各数据源利率差异较大，建议多方比较")
        else:
            print("\n✅ 各数据源利率相对一致")

if __name__ == "__main__":
    main()
