#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
5年期TIPS隐含通胀预期数据可视化

该脚本读取收集的5年期TIPS隐含通胀预期数据并生成可视化图表。
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import os
from datetime import datetime, timedelta
import warnings

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
CHARTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'charts')
os.makedirs(CHARTS_DIR, exist_ok=True)

def load_tips_data():
    """加载5年期TIPS隐含通胀预期数据"""
    file_path = os.path.join(DATA_DIR, 'tips_5y_inflation.csv')
    
    if not os.path.exists(file_path):
        print(f"数据文件不存在: {file_path}")
        print("请先运行 collect_tips_5y_inflation.py 收集数据")
        return None
    
    try:
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        print(f"成功加载数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据时发生错误: {str(e)}")
        return None

def plot_full_history(df):
    """绘制完整历史数据图表"""
    fig, ax = plt.subplots(figsize=(15, 8))
    
    # 绘制主线
    ax.plot(df.index, df['TIPS_5Y_通胀预期'], linewidth=1.5, color='#2E86AB', alpha=0.8)
    
    # 添加重要事件标注
    events = [
        ('2008-09-15', '雷曼兄弟破产', -1.5),
        ('2020-03-11', 'COVID-19疫情', 0.5),
        ('2022-03-16', '美联储加息周期开始', 2.8),
    ]
    
    for date_str, event, y_pos in events:
        try:
            event_date = pd.to_datetime(date_str)
            if event_date >= df.index.min() and event_date <= df.index.max():
                ax.axvline(x=event_date, color='red', linestyle='--', alpha=0.7)
                ax.text(event_date, y_pos, event, rotation=90, 
                       verticalalignment='bottom', fontsize=9, color='red')
        except:
            continue
    
    # 添加平均线
    mean_value = df['TIPS_5Y_通胀预期'].mean()
    ax.axhline(y=mean_value, color='orange', linestyle='-', alpha=0.7, 
               label=f'Historical Average: {mean_value:.2f}%')
    
    # 设置标题和标签
    ax.set_title('5-Year TIPS Breakeven Inflation Rate (2003-2025)', fontsize=16, fontweight='bold')
    ax.set_xlabel('Year', fontsize=12)
    ax.set_ylabel('Inflation Expectation (%)', fontsize=12)
    
    # 格式化x轴
    ax.xaxis.set_major_locator(mdates.YearLocator(2))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax.xaxis.set_minor_locator(mdates.YearLocator())
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, 'tips_5y_inflation_full_history.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"完整历史图表已保存到: {chart_path}")
    
    plt.show()

def plot_recent_years(df, years=5):
    """绘制最近几年的数据"""
    # 获取最近几年的数据
    cutoff_date = df.index.max() - timedelta(days=365*years)
    recent_df = df[df.index >= cutoff_date]
    
    fig, ax = plt.subplots(figsize=(12, 6))
    
    # 绘制线图
    ax.plot(recent_df.index, recent_df['TIPS_5Y_通胀预期'], 
            linewidth=2, color='#A23B72', alpha=0.8)
    
    # 添加最新值标注
    latest_value = recent_df['TIPS_5Y_通胀预期'].iloc[-1]
    latest_date = recent_df.index[-1]
    ax.scatter([latest_date], [latest_value], color='red', s=50, zorder=5)
    ax.text(latest_date, latest_value + 0.1, f'{latest_value:.2f}%', 
            ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 添加平均线
    mean_value = recent_df['TIPS_5Y_通胀预期'].mean()
    ax.axhline(y=mean_value, color='green', linestyle='--', alpha=0.7, 
               label=f'{years}-Year Average: {mean_value:.2f}%')
    
    # 设置标题和标签
    ax.set_title(f'5-Year TIPS Breakeven Inflation Rate (Recent {years} Years)', 
                fontsize=14, fontweight='bold')
    ax.set_xlabel('Date', fontsize=12)
    ax.set_ylabel('Inflation Expectation (%)', fontsize=12)
    
    # 格式化x轴
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=6))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax.xaxis.set_minor_locator(mdates.MonthLocator(interval=3))
    
    # 旋转x轴标签
    plt.xticks(rotation=45)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, f'tips_5y_inflation_recent_{years}years.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"最近{years}年图表已保存到: {chart_path}")
    
    plt.show()

def plot_annual_statistics(df):
    """绘制年度统计图表"""
    # 计算年度统计
    annual_stats = df.groupby(df.index.year).agg({
        'TIPS_5Y_通胀预期': ['mean', 'min', 'max', 'std']
    }).round(2)
    
    # 扁平化列名
    annual_stats.columns = ['Mean', 'Min', 'Max', 'Std']
    
    # 只显示有完整数据的年份
    annual_stats = annual_stats[annual_stats.index <= 2024]
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10))
    
    # 上图：年度平均值和范围
    years = annual_stats.index
    means = annual_stats['Mean']
    mins = annual_stats['Min']
    maxs = annual_stats['Max']
    
    ax1.plot(years, means, marker='o', linewidth=2, markersize=6, 
             color='#2E86AB', label='Annual Average')
    ax1.fill_between(years, mins, maxs, alpha=0.3, color='#2E86AB', 
                     label='Annual Range (Min-Max)')
    
    ax1.set_title('Annual Statistics of 5-Year TIPS Breakeven Inflation Rate', 
                  fontsize=14, fontweight='bold')
    ax1.set_ylabel('Inflation Expectation (%)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend()
    
    # 下图：年度波动率
    ax2.bar(years, annual_stats['Std'], color='#F18F01', alpha=0.7, 
            label='Annual Volatility (Std Dev)')
    ax2.set_title('Annual Volatility of 5-Year TIPS Breakeven Inflation Rate', 
                  fontsize=14, fontweight='bold')
    ax2.set_xlabel('Year', fontsize=12)
    ax2.set_ylabel('Standard Deviation (%)', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend()
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, 'tips_5y_inflation_annual_stats.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"年度统计图表已保存到: {chart_path}")
    
    plt.show()

def main():
    """主函数"""
    print("5年期TIPS隐含通胀预期数据可视化")
    print("="*50)
    
    # 加载数据
    df = load_tips_data()
    if df is None:
        return
    
    print(f"数据时间范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
    print(f"最新通胀预期: {df['TIPS_5Y_通胀预期'].iloc[-1]:.2f}%")
    print()
    
    # 生成图表
    print("正在生成图表...")
    
    # 1. 完整历史数据
    plot_full_history(df)
    
    # 2. 最近5年数据
    plot_recent_years(df, years=5)
    
    # 3. 年度统计
    plot_annual_statistics(df)
    
    print("\n所有图表已生成完成！")
    print(f"图表保存目录: {CHARTS_DIR}")

if __name__ == "__main__":
    main()
