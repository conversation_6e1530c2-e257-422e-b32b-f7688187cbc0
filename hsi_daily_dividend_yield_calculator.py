import os
import json
import pandas as pd
import numpy as np
import akshare as ak
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
import warnings
warnings.filterwarnings('ignore')

class HSIDailyDividendYieldCalculator:
    """
    恒生指数成分股每日股息率计算器
    使用滚动12个月分红总额计算股息率，避免未来数据泄露
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841", 
                 cache_dir: str = "cache"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.dividend_data = {}
        self.price_data = {}
        self.daily_dividend_yields = {}
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
    def load_dividend_data(self) -> Dict:
        """加载所有股票的分红数据"""
        print("📁 加载分红数据...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        
        for file in dividend_files:
            file_path = os.path.join(self.dividend_data_dir, file)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                stock_code = data['stock_code']
                stock_name = data['stock_name']
                
                # 处理分红历史数据
                dividend_history = []
                for div in data['dividend_history']:
                    try:
                        # 解析除权日期
                        ex_date = pd.to_datetime(div['ex_date'])
                        amount = float(div['amount'])
                        
                        dividend_history.append({
                            'ex_date': ex_date,
                            'amount': amount,
                            'year': div['year'],
                            'report_type': div['report_type']
                        })
                    except Exception as e:
                        print(f"⚠️  处理 {stock_code} 分红记录失败: {e}")
                        continue
                
                # 按除权日期排序
                dividend_history.sort(key=lambda x: x['ex_date'])
                
                self.dividend_data[stock_code] = {
                    'stock_name': stock_name,
                    'dividend_history': dividend_history
                }
                
            except Exception as e:
                print(f"❌ 加载 {file} 失败: {e}")
                continue
        
        print(f"✅ 成功加载 {len(self.dividend_data)} 只股票的分红数据")
        return self.dividend_data
    
    def get_stock_price_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取单只股票的价格数据"""
        cache_file = os.path.join(self.cache_dir, f"{symbol}_price.pkl")
        
        # 检查缓存
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                    if not data.empty:
                        print(f"💾 从缓存加载 {symbol} 价格数据")
                        return data
            except:
                pass
        
        try:
            print(f"🌐 下载 {symbol} 价格数据...")
            # 使用akshare获取港股数据
            df = ak.stock_hk_daily(symbol=symbol)

            if not df.empty:
                # 数据预处理
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                df = df.sort_index()

                # 过滤最近5年数据以确保有足够的历史数据
                end_date = datetime.now()
                start_date = end_date - timedelta(days=1825)  # 5年
                df = df[df.index >= start_date]

                # 保存到缓存
                with open(cache_file, 'wb') as f:
                    pickle.dump(df, f)

                time.sleep(0.5)  # 避免API限制
                return df
            else:
                print(f"⚠️  {symbol} 无价格数据")
                return None
                
        except Exception as e:
            print(f"❌ 获取 {symbol} 价格数据失败: {e}")
            return None

    def calculate_rolling_12m_dividends(self, symbol: str, target_date: pd.Timestamp) -> float:
        """
        计算指定日期前12个月的分红总额
        确保不使用未来数据
        """
        if symbol not in self.dividend_data:
            return 0.0

        dividend_history = self.dividend_data[symbol]['dividend_history']

        # 计算12个月前的日期
        start_date = target_date - timedelta(days=365)

        # 找到在这个时间窗口内的所有分红
        rolling_dividends = 0.0
        for div in dividend_history:
            ex_date = div['ex_date']
            # 只考虑在目标日期之前且在12个月窗口内的分红
            if start_date <= ex_date <= target_date:
                rolling_dividends += div['amount']

        return rolling_dividends

    def calculate_daily_dividend_yield(self, symbol: str) -> Optional[pd.Series]:
        """
        计算单只股票的每日股息率
        股息率 = 滚动12个月分红总额 / 当日股价
        """
        if symbol not in self.dividend_data:
            print(f"⚠️  {symbol} 无分红数据")
            return None

        # 获取价格数据
        price_df = self.get_stock_price_data(symbol)
        if price_df is None or price_df.empty:
            print(f"⚠️  {symbol} 无价格数据")
            return None

        stock_name = self.dividend_data[symbol]['stock_name']
        print(f"📊 计算 {symbol} ({stock_name}) 每日股息率...")

        # 创建结果序列
        dividend_yields = pd.Series(index=price_df.index, dtype=float)

        # 为每个交易日计算股息率
        for date in price_df.index:
            try:
                # 获取当日收盘价
                close_price = price_df.loc[date, 'close']

                if pd.isna(close_price) or close_price <= 0:
                    dividend_yields.loc[date] = np.nan
                    continue

                # 计算滚动12个月分红总额
                rolling_dividends = self.calculate_rolling_12m_dividends(symbol, date)

                # 计算股息率 (年化)
                if rolling_dividends > 0:
                    dividend_yield = (rolling_dividends / close_price) * 100  # 转换为百分比
                else:
                    dividend_yield = 0.0

                dividend_yields.loc[date] = dividend_yield

            except Exception as e:
                print(f"⚠️  计算 {symbol} 在 {date} 的股息率失败: {e}")
                dividend_yields.loc[date] = np.nan

        return dividend_yields

    def calculate_all_stocks_dividend_yields(self) -> Dict[str, pd.Series]:
        """计算所有股票的每日股息率"""
        print("🔄 开始计算所有股票的每日股息率...")

        # 先加载分红数据
        self.load_dividend_data()

        results = {}
        total_stocks = len(self.dividend_data)

        for i, symbol in enumerate(self.dividend_data.keys(), 1):
            print(f"\n📈 处理第 {i}/{total_stocks} 只股票: {symbol}")

            dividend_yield_series = self.calculate_daily_dividend_yield(symbol)

            if dividend_yield_series is not None:
                results[symbol] = dividend_yield_series
                print(f"✅ {symbol} 计算完成")
            else:
                print(f"❌ {symbol} 计算失败")

        self.daily_dividend_yields = results
        print(f"\n🎉 完成！成功计算 {len(results)} 只股票的每日股息率")
        return results

    def save_results_to_csv(self, output_file: str = None) -> str:
        """将结果保存为CSV文件"""
        if not self.daily_dividend_yields:
            print("❌ 没有计算结果可保存")
            return None

        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"hsi_daily_dividend_yields_{timestamp}.csv"

        print(f"💾 保存结果到 {output_file}...")

        # 创建DataFrame
        df = pd.DataFrame(self.daily_dividend_yields)

        # 添加股票名称到列名
        new_columns = []
        for symbol in df.columns:
            if symbol in self.dividend_data:
                stock_name = self.dividend_data[symbol]['stock_name']
                new_columns.append(f"{symbol}_{stock_name}")
            else:
                new_columns.append(symbol)

        df.columns = new_columns

        # 保存到CSV
        df.to_csv(output_file, encoding='utf-8-sig')

        print(f"✅ 结果已保存到 {output_file}")
        print(f"   数据维度: {df.shape}")
        print(f"   日期范围: {df.index.min()} 至 {df.index.max()}")

        return output_file

    def analyze_dividend_yields(self) -> pd.DataFrame:
        """分析股息率统计信息"""
        if not self.daily_dividend_yields:
            print("❌ 没有计算结果可分析")
            return None

        print("📊 分析股息率统计信息...")

        analysis_results = []

        for symbol, yield_series in self.daily_dividend_yields.items():
            stock_name = self.dividend_data[symbol]['stock_name']

            # 过滤掉0值和NaN值进行统计
            valid_yields = yield_series.dropna()
            non_zero_yields = valid_yields[valid_yields > 0]

            if len(non_zero_yields) > 0:
                stats = {
                    '股票代码': symbol,
                    '股票名称': stock_name,
                    '数据点数': len(valid_yields),
                    '有分红天数': len(non_zero_yields),
                    '平均股息率(%)': non_zero_yields.mean(),
                    '最大股息率(%)': non_zero_yields.max(),
                    '最小股息率(%)': non_zero_yields.min(),
                    '标准差(%)': non_zero_yields.std(),
                    '最新股息率(%)': valid_yields.iloc[-1] if len(valid_yields) > 0 else 0,
                    '数据起始日期': valid_yields.index.min(),
                    '数据结束日期': valid_yields.index.max()
                }
            else:
                stats = {
                    '股票代码': symbol,
                    '股票名称': stock_name,
                    '数据点数': len(valid_yields),
                    '有分红天数': 0,
                    '平均股息率(%)': 0,
                    '最大股息率(%)': 0,
                    '最小股息率(%)': 0,
                    '标准差(%)': 0,
                    '最新股息率(%)': 0,
                    '数据起始日期': valid_yields.index.min() if len(valid_yields) > 0 else None,
                    '数据结束日期': valid_yields.index.max() if len(valid_yields) > 0 else None
                }

            analysis_results.append(stats)

        # 创建分析结果DataFrame
        analysis_df = pd.DataFrame(analysis_results)

        # 按平均股息率排序
        analysis_df = analysis_df.sort_values('平均股息率(%)', ascending=False)

        print("📈 股息率统计分析完成")
        print(f"   总股票数: {len(analysis_df)}")
        print(f"   有分红股票数: {len(analysis_df[analysis_df['有分红天数'] > 0])}")

        return analysis_df

    def save_analysis_to_csv(self, analysis_df: pd.DataFrame, output_file: str = None) -> str:
        """保存分析结果到CSV"""
        if analysis_df is None:
            print("❌ 没有分析结果可保存")
            return None

        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"hsi_dividend_yield_analysis_{timestamp}.csv"

        analysis_df.to_csv(output_file, index=False, encoding='utf-8-sig')
        print(f"✅ 分析结果已保存到 {output_file}")

        return output_file

def main():
    """主函数 - 演示如何使用股息率计算器"""
    print("🚀 恒生指数成分股每日股息率计算器")
    print("=" * 50)

    # 创建计算器实例
    calculator = HSIDailyDividendYieldCalculator()

    # 计算所有股票的每日股息率
    results = calculator.calculate_all_stocks_dividend_yields()

    if results:
        # 保存详细结果
        csv_file = calculator.save_results_to_csv()

        # 分析统计信息
        analysis_df = calculator.analyze_dividend_yields()

        if analysis_df is not None:
            # 显示前10只股票的统计信息
            print("\n📊 股息率最高的前10只股票:")
            print(analysis_df.head(10)[['股票代码', '股票名称', '平均股息率(%)', '最新股息率(%)', '有分红天数']].to_string(index=False))

            # 保存分析结果
            analysis_file = calculator.save_analysis_to_csv(analysis_df)

            print(f"\n📁 文件输出:")
            print(f"   详细数据: {csv_file}")
            print(f"   统计分析: {analysis_file}")

        print("\n✅ 计算完成！")
    else:
        print("❌ 计算失败，请检查数据和网络连接")

def demo_single_stock():
    """演示单只股票的计算"""
    print("🔍 单只股票股息率计算演示")
    print("=" * 30)

    calculator = HSIDailyDividendYieldCalculator()
    calculator.load_dividend_data()

    # 以腾讯控股为例
    symbol = "00700"
    if symbol in calculator.dividend_data:
        print(f"📈 计算 {symbol} ({calculator.dividend_data[symbol]['stock_name']}) 的股息率...")

        yield_series = calculator.calculate_daily_dividend_yield(symbol)

        if yield_series is not None:
            # 显示最近10天的股息率
            print("\n📊 最近10天的股息率:")
            recent_yields = yield_series.dropna().tail(10)
            for date, yield_val in recent_yields.items():
                print(f"   {date.strftime('%Y-%m-%d')}: {yield_val:.4f}%")

            # 显示统计信息
            non_zero_yields = yield_series[yield_series > 0]
            if len(non_zero_yields) > 0:
                print(f"\n📈 统计信息:")
                print(f"   平均股息率: {non_zero_yields.mean():.4f}%")
                print(f"   最大股息率: {non_zero_yields.max():.4f}%")
                print(f"   最小股息率: {non_zero_yields.min():.4f}%")
                print(f"   有分红天数: {len(non_zero_yields)}")
        else:
            print("❌ 计算失败")
    else:
        print(f"❌ 找不到股票 {symbol} 的分红数据")

if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "demo":
        demo_single_stock()
    else:
        main()
