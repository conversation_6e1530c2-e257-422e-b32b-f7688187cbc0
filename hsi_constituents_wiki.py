import requests
from bs4 import BeautifulSoup
import pandas as pd
import os
from datetime import datetime

def scrape_hsi_constituents_from_wiki():
    """
    Scrape Hang Seng Index constituents from Wikipedia
    
    Returns:
        pandas.DataFrame: DataFrame with HSI constituent stocks
    """
    print("Scraping Hang Seng Index constituents from Wikipedia...")
    
    # URL of the Wikipedia page for HSI constituents
    url = "https://en.wikipedia.org/wiki/Hang_Seng_Index"
    
    try:
        # Send HTTP request to the website
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors
        
        # Parse the HTML content
        soup = BeautifulSoup(response.text, 'html.parser')
        
        # Find the table with HSI constituents
        # Usually it's a table with a caption containing "Hang Seng Index constituents"
        tables = soup.find_all('table', {'class': 'wikitable'})
        
        constituents_table = None
        for table in tables:
            caption = table.find('caption')
            if caption and ('Hang Seng Index' in caption.get_text() or 'HSI' in caption.get_text()):
                constituents_table = table
                break
        
        if not constituents_table:
            # If we couldn't find a table with a caption, look for tables with specific headers
            for table in tables:
                headers = [th.get_text().strip() for th in table.find_all('th')]
                if 'Company' in headers and ('Stock code' in headers or 'Ticker' in headers):
                    constituents_table = table
                    break
        
        if not constituents_table:
            print("Could not find HSI constituents table on Wikipedia.")
            return None
        
        # Extract data from the table
        rows = constituents_table.find_all('tr')
        headers = [th.get_text().strip() for th in rows[0].find_all('th')]
        
        # Clean up headers
        clean_headers = []
        for header in headers:
            # Remove any newlines and extra spaces
            header = ' '.join(header.split())
            clean_headers.append(header)
        
        # Extract data rows
        data = []
        for row in rows[1:]:  # Skip the header row
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 2:  # Ensure there are at least company name and stock code
                row_data = {}
                for i, cell in enumerate(cells):
                    if i < len(clean_headers):
                        row_data[clean_headers[i]] = cell.get_text().strip()
                data.append(row_data)
        
        # Convert to DataFrame
        df = pd.DataFrame(data)
        
        # Clean up column names
        df.columns = [col.strip() for col in df.columns]
        
        # Save to CSV
        output_dir = "data"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
        
        timestamp = datetime.now().strftime("%Y%m%d")
        output_file = os.path.join(output_dir, f"hsi_constituents_{timestamp}.csv")
        df.to_csv(output_file, index=False)
        
        # Also save to a standard filename for easy access
        standard_file = os.path.join(output_dir, "hsi_constituents.csv")
        df.to_csv(standard_file, index=False)
        
        print(f"Successfully scraped {len(df)} HSI constituent stocks from Wikipedia")
        print(f"Data saved to {output_file} and {standard_file}")
        
        return df
    
    except Exception as e:
        print(f"Error scraping HSI constituents from Wikipedia: {str(e)}")
        return None

def main():
    # Scrape HSI constituents from Wikipedia
    constituents_df = scrape_hsi_constituents_from_wiki()
    
    if constituents_df is not None:
        # Display the first few rows
        print("\nHSI Constituents Preview:")
        print(constituents_df.head())
        
        # Display summary
        print(f"\nTotal number of constituents: {len(constituents_df)}")

if __name__ == "__main__":
    print("Starting HSI constituents scraper (Wikipedia)...")
    main()
    print("Script execution completed.")
