#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子有效性验证

基于 hsi_individual_dividend_yields 目录下的真实股息率和股价数据，
验证股息率因子在恒生指数成分股中的有效性。
"""

import os
import csv
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIDividendFactorValidator:
    """恒生指数股息率因子验证器"""
    
    def __init__(self, data_dir: str = "hsi_individual_dividend_yields", rebalance_freq: str = 'Q'):
        """
        初始化验证器

        Args:
            data_dir: 股息率数据目录
            rebalance_freq: 再平衡频率 ('M'=月度, 'Q'=季度, 'H'=半年, 'Y'=年度)
        """
        self.data_dir = data_dir
        self.stock_data = {}
        self.summary_data = {}
        self.factor_data = {}
        self.backtest_results = {}

        # 回测参数
        self.rebalance_freq = rebalance_freq
        self.n_groups = 5          # 分为5组
        self.lookback_days = 252   # 1年回看期

        # 根据再平衡频率调整前瞻期
        if rebalance_freq == 'M':
            self.forward_days = 21     # 1个月前瞻期
        elif rebalance_freq == 'Q':
            self.forward_days = 63     # 3个月前瞻期
        elif rebalance_freq == 'H':
            self.forward_days = 126    # 6个月前瞻期
        else:  # 'Y'
            self.forward_days = 252    # 1年前瞻期

        freq_names = {'M': '月度', 'Q': '季度', 'H': '半年度', 'Y': '年度'}
        print("🚀 恒生指数股息率因子验证器已初始化")
        print(f"📁 数据目录: {data_dir}")
        print(f"🔄 再平衡频率: {freq_names.get(rebalance_freq, rebalance_freq)}")
    
    def load_stock_data(self) -> bool:
        """加载所有股票的股息率和股价数据"""
        try:
            print("📊 加载股票数据...")
            
            # 首先加载汇总数据
            summary_file = os.path.join(self.data_dir, "hsi_all_stocks_summary_20250603_175949.csv")
            if os.path.exists(summary_file):
                self.summary_data = self._load_summary_data(summary_file)
                print(f"✅ 加载汇总数据: {len(self.summary_data)} 只股票")
            
            # 加载各股票的详细数据
            loaded_count = 0
            for filename in os.listdir(self.data_dir):
                if filename.endswith('_dividend_yield_20250603_175937.csv') or \
                   filename.endswith('_dividend_yield_20250603_175938.csv') or \
                   filename.endswith('_dividend_yield_20250603_175939.csv') or \
                   filename.endswith('_dividend_yield_20250603_175940.csv') or \
                   filename.endswith('_dividend_yield_20250603_175941.csv') or \
                   filename.endswith('_dividend_yield_20250603_175942.csv') or \
                   filename.endswith('_dividend_yield_20250603_175943.csv') or \
                   filename.endswith('_dividend_yield_20250603_175944.csv') or \
                   filename.endswith('_dividend_yield_20250603_175945.csv') or \
                   filename.endswith('_dividend_yield_20250603_175946.csv') or \
                   filename.endswith('_dividend_yield_20250603_175947.csv') or \
                   filename.endswith('_dividend_yield_20250603_175948.csv') or \
                   filename.endswith('_dividend_yield_20250603_175949.csv'):
                    
                    # 提取股票代码和名称
                    parts = filename.split('_')
                    if len(parts) >= 3:
                        stock_code = parts[0]
                        stock_name = parts[1]
                        
                        # 加载股票数据
                        file_path = os.path.join(self.data_dir, filename)
                        stock_df = self._load_stock_file(file_path, stock_code, stock_name)
                        
                        if stock_df is not None and len(stock_df) > 0:
                            self.stock_data[stock_code] = {
                                'name': stock_name,
                                'data': stock_df
                            }
                            loaded_count += 1
            
            print(f"✅ 成功加载 {loaded_count} 只股票的详细数据")
            
            if loaded_count == 0:
                print("❌ 未找到有效的股票数据文件")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 加载股票数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _load_summary_data(self, file_path: str) -> Dict:
        """加载汇总数据"""
        summary = {}
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['stock_code']:
                        summary[row['stock_code']] = {
                            'name': row['stock_name'],
                            'avg_yield': float(row['avg_yield']) if row['avg_yield'] else 0,
                            'max_yield': float(row['max_yield']) if row['max_yield'] else 0,
                            'min_yield': float(row['min_yield']) if row['min_yield'] else 0,
                            'latest_yield': float(row['latest_yield']) if row['latest_yield'] else 0,
                            'coverage_rate': float(row['coverage_rate']) if row['coverage_rate'] else 0
                        }
        except Exception as e:
            print(f"⚠️  加载汇总数据失败: {e}")
        
        return summary
    
    def _load_stock_file(self, file_path: str, stock_code: str, stock_name: str) -> Optional[pd.DataFrame]:
        """加载单个股票文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 检查必要的列
            required_cols = ['日期', '股价(港元)', '股息率(%)', '滚动12月分红总额(港元)']
            if not all(col in df.columns for col in required_cols):
                print(f"⚠️  {stock_code} 数据格式不正确，跳过")
                return None
            
            # 处理数据
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.sort_values('日期')
            df.set_index('日期', inplace=True)
            
            # 清理数据
            df = df.dropna(subset=['股价(港元)', '股息率(%)'])
            
            # 计算收益率
            df['price'] = df['股价(港元)']
            df['dividend_yield'] = df['股息率(%)']
            df['return_1d'] = df['price'].pct_change()
            df['return_5d'] = df['price'].pct_change(5)
            df['return_20d'] = df['price'].pct_change(20)
            df['return_60d'] = df['price'].pct_change(60)
            
            return df
            
        except Exception as e:
            print(f"⚠️  加载 {stock_code} 数据失败: {e}")
            return None
    
    def calculate_factor_scores(self, date: datetime) -> pd.Series:
        """计算指定日期的股息率因子得分"""
        scores = {}
        
        for stock_code, stock_info in self.stock_data.items():
            df = stock_info['data']
            
            # 找到指定日期之前的最新数据
            available_dates = df.index[df.index <= date]
            if len(available_dates) > 0:
                latest_date = available_dates[-1]
                
                # 获取股息率作为因子得分
                dividend_yield = df.loc[latest_date, 'dividend_yield']
                
                # 确保股息率有效
                if pd.notna(dividend_yield) and dividend_yield > 0:
                    scores[stock_code] = dividend_yield
        
        return pd.Series(scores)
    
    def create_portfolios(self, factor_scores: pd.Series) -> Dict[str, List[str]]:
        """基于因子得分创建投资组合"""
        if len(factor_scores) == 0:
            return {}
        
        # 按因子得分排序（降序，高股息率在前）
        sorted_scores = factor_scores.sort_values(ascending=False)
        
        # 分组
        n_stocks_per_group = len(sorted_scores) // self.n_groups
        portfolios = {}
        
        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(sorted_scores)
            else:
                end_idx = (i + 1) * n_stocks_per_group
            
            group_stocks = sorted_scores.iloc[start_idx:end_idx].index.tolist()
            portfolios[f'Group_{i+1}'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_returns(self, portfolios: Dict[str, List[str]], 
                                  start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """计算投资组合在指定期间的收益率"""
        portfolio_returns = {}
        
        for portfolio_name, stocks in portfolios.items():
            if not stocks:
                continue
            
            stock_returns = []
            
            for stock_code in stocks:
                if stock_code in self.stock_data:
                    df = self.stock_data[stock_code]['data']
                    
                    # 获取期间数据
                    period_data = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if len(period_data) >= 2:
                        # 计算期间收益率
                        start_price = period_data['price'].iloc[0]
                        end_price = period_data['price'].iloc[-1]
                        
                        if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                            stock_return = (end_price - start_price) / start_price
                            stock_returns.append(stock_return)
            
            # 计算组合平均收益率
            if stock_returns:
                portfolio_returns[portfolio_name] = np.mean(stock_returns)
            else:
                portfolio_returns[portfolio_name] = 0.0
        
        return portfolio_returns
    
    def get_rebalance_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取再平衡日期"""
        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)

            # 根据再平衡频率确定下一个日期
            if self.rebalance_freq == 'M':  # 月度
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1, day=1)
            elif self.rebalance_freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_month = 6
                elif current_date.month <= 6:
                    next_month = 9
                elif current_date.month <= 9:
                    next_month = 12
                else:
                    next_month = 3
                    current_date = current_date.replace(year=current_date.year + 1)

                if next_month <= 12:
                    current_date = current_date.replace(month=next_month, day=1)
            elif self.rebalance_freq == 'H':  # 半年度
                if current_date.month <= 6:
                    current_date = current_date.replace(month=12, day=1)
                else:
                    current_date = current_date.replace(year=current_date.year + 1, month=6, day=1)
            else:  # 年度
                current_date = current_date.replace(year=current_date.year + 1)

        return dates

    def run_backtest(self) -> bool:
        """运行股息率因子回测"""
        try:
            print("🔄 开始股息率因子回测...")

            # 确定回测期间
            all_dates = set()
            for stock_info in self.stock_data.values():
                all_dates.update(stock_info['data'].index)

            if not all_dates:
                print("❌ 没有可用的日期数据")
                return False

            start_date = min(all_dates) + timedelta(days=self.lookback_days)
            end_date = max(all_dates) - timedelta(days=self.forward_days)

            print(f"📅 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

            # 获取再平衡日期
            rebalance_dates = self.get_rebalance_dates(start_date, end_date)
            print(f"🔄 再平衡次数: {len(rebalance_dates)}")

            # 存储所有组合的收益率
            all_portfolio_returns = defaultdict(list)
            all_factor_scores = []

            for i, rebalance_date in enumerate(rebalance_dates):
                print(f"🔄 再平衡 {i+1}/{len(rebalance_dates)}: {rebalance_date.strftime('%Y-%m-%d')}")

                # 计算因子得分
                factor_scores = self.calculate_factor_scores(rebalance_date)
                if factor_scores.empty:
                    continue

                all_factor_scores.append(factor_scores)

                # 创建投资组合
                portfolios = self.create_portfolios(factor_scores)

                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    hold_end = rebalance_dates[i + 1]
                else:
                    hold_end = end_date

                # 计算期间收益
                period_returns = self.calculate_portfolio_returns(
                    portfolios, rebalance_date, hold_end
                )

                # 累积收益
                for portfolio_name, returns in period_returns.items():
                    all_portfolio_returns[portfolio_name].append(returns)

            # 保存回测结果
            self.backtest_results = {
                'portfolio_returns': dict(all_portfolio_returns),
                'factor_scores': all_factor_scores,
                'rebalance_dates': rebalance_dates,
                'start_date': start_date,
                'end_date': end_date
            }

            print("✅ 回测完成")
            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def analyze_factor_performance(self) -> Dict:
        """分析因子表现"""
        if not self.backtest_results:
            print("❌ 请先运行回测")
            return {}

        print("📊 分析因子表现...")

        portfolio_returns = self.backtest_results['portfolio_returns']
        analysis_results = {}

        # 计算各组合的统计指标
        for portfolio_name, returns in portfolio_returns.items():
            if not returns:
                continue

            returns_array = np.array(returns)

            # 基本统计
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0

            # 累积收益
            cumulative_return = np.prod(1 + returns_array) - 1

            # 年化收益率
            if self.rebalance_freq == 'M':
                periods_per_year = 12
            elif self.rebalance_freq == 'Q':
                periods_per_year = 4
            elif self.rebalance_freq == 'H':
                periods_per_year = 2
            else:  # 'Y'
                periods_per_year = 1

            annual_return = (1 + mean_return) ** periods_per_year - 1

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # 胜率
            win_rate = np.sum(returns_array > 0) / len(returns_array)

            analysis_results[portfolio_name] = {
                'mean_return': mean_return,
                'std_return': std_return,
                'sharpe_ratio': sharpe_ratio,
                'cumulative_return': cumulative_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_periods': len(returns)
            }

        return analysis_results

    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数（IC）"""
        if not self.backtest_results:
            return {}

        print("📈 计算信息系数...")

        factor_scores = self.backtest_results['factor_scores']
        ic_values = []

        for i, scores in enumerate(factor_scores):
            if i >= len(factor_scores) - 1:
                break

            # 获取下期收益率
            next_period_returns = {}
            rebalance_date = self.backtest_results['rebalance_dates'][i]
            next_date = self.backtest_results['rebalance_dates'][i + 1] if i + 1 < len(self.backtest_results['rebalance_dates']) else self.backtest_results['end_date']

            for stock_code in scores.index:
                if stock_code in self.stock_data:
                    df = self.stock_data[stock_code]['data']
                    period_data = df[(df.index >= rebalance_date) & (df.index <= next_date)]

                    if len(period_data) >= 2:
                        start_price = period_data['price'].iloc[0]
                        end_price = period_data['price'].iloc[-1]

                        if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                            stock_return = (end_price - start_price) / start_price
                            next_period_returns[stock_code] = stock_return

            # 计算IC
            if len(next_period_returns) >= 5:  # 至少需要5只股票
                factor_values = []
                return_values = []

                for stock_code in scores.index:
                    if stock_code in next_period_returns:
                        factor_values.append(scores[stock_code])
                        return_values.append(next_period_returns[stock_code])

                if len(factor_values) >= 5:
                    ic = np.corrcoef(factor_values, return_values)[0, 1]
                    if not np.isnan(ic):
                        ic_values.append(ic)

        if ic_values:
            return {
                'ic_mean': np.mean(ic_values),
                'ic_std': np.std(ic_values),
                'ic_ir': np.mean(ic_values) / np.std(ic_values) if np.std(ic_values) > 0 else 0,
                'ic_values': ic_values,
                'ic_positive_rate': np.sum(np.array(ic_values) > 0) / len(ic_values)
            }
        else:
            return {}

    def generate_report(self, analysis_results: Dict, ic_results: Dict):
        """生成分析报告"""
        print("\n" + "="*80)
        print("恒生指数成分股股息率因子有效性验证报告")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: {self.data_dir}")
        print(f"分析股票数: {len(self.stock_data)}")
        print()

        # 回测基本信息
        if self.backtest_results:
            print("回测基本信息")
            print("-"*40)
            print(f"回测期间: {self.backtest_results['start_date'].strftime('%Y-%m-%d')} 至 {self.backtest_results['end_date'].strftime('%Y-%m-%d')}")
            print(f"再平衡频率: {self.rebalance_freq}")
            print(f"投资组合数: {self.n_groups}")
            print(f"再平衡次数: {len(self.backtest_results['rebalance_dates'])}")
            print()

        # 组合表现分析
        if analysis_results:
            print("投资组合表现分析")
            print("-"*80)
            print(f"{'组合':<8} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'最大回撤':<8} {'胜率':<8}")
            print("-"*80)

            for group_name in sorted(analysis_results.keys()):
                if group_name.startswith('Group_'):
                    perf = analysis_results[group_name]
                    print(f"{group_name:<8} {perf['annual_return']*100:<10.2f}% "
                          f"{perf['std_return']*100:<8.2f}% {perf['sharpe_ratio']:<8.2f} "
                          f"{abs(perf['max_drawdown'])*100:<8.2f}% {perf['win_rate']*100:<8.1f}%")
            print()

        # 因子有效性分析
        if 'Group_1' in analysis_results and 'Group_5' in analysis_results:
            high_group = analysis_results['Group_1']
            low_group = analysis_results['Group_5']

            return_spread = high_group['annual_return'] - low_group['annual_return']
            sharpe_spread = high_group['sharpe_ratio'] - low_group['sharpe_ratio']

            print("因子有效性分析")
            print("-"*40)
            print(f"高股息率组合年化收益: {high_group['annual_return']*100:.2f}%")
            print(f"低股息率组合年化收益: {low_group['annual_return']*100:.2f}%")
            print(f"多空收益差: {return_spread*100:.2f}%")
            print(f"夏普比率差异: {sharpe_spread:.2f}")
            print()

            if return_spread > 0:
                print("✅ 股息率因子表现正向：高股息率股票表现更好")
            else:
                print("❌ 股息率因子表现负向：低股息率股票表现更好")
            print()

        # 信息系数分析
        if ic_results:
            print("信息系数分析")
            print("-"*40)
            print(f"平均IC: {ic_results['ic_mean']:.4f}")
            print(f"IC标准差: {ic_results['ic_std']:.4f}")
            print(f"IC信息比率: {ic_results['ic_ir']:.4f}")
            print(f"IC正值比例: {ic_results['ic_positive_rate']*100:.1f}%")
            print()

            # IC有效性判断
            if abs(ic_results['ic_mean']) > 0.02 and ic_results['ic_positive_rate'] > 0.5:
                print("✅ 因子具有较强的预测能力")
            elif abs(ic_results['ic_mean']) > 0.01:
                print("⚠️  因子具有一定的预测能力")
            else:
                print("❌ 因子预测能力较弱")
            print()

        print("="*80)

    def create_visualizations(self, analysis_results: Dict, ic_results: Dict):
        """创建可视化图表"""
        try:
            print("📊 生成可视化图表...")

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('恒生指数股息率因子有效性验证', fontsize=16, fontweight='bold')

            # 1. 组合累积收益图
            if self.backtest_results and analysis_results:
                portfolio_returns = self.backtest_results['portfolio_returns']

                for group_name in sorted(portfolio_returns.keys()):
                    if group_name.startswith('Group_'):
                        returns = portfolio_returns[group_name]
                        cumulative_returns = np.cumprod(1 + np.array(returns))
                        ax1.plot(range(len(cumulative_returns)), cumulative_returns,
                                label=group_name, linewidth=2)

                ax1.set_title('投资组合累积收益', fontweight='bold')
                ax1.set_xlabel('再平衡期数')
                ax1.set_ylabel('累积收益')
                ax1.legend()
                ax1.grid(True, alpha=0.3)

            # 2. 年化收益率对比
            if analysis_results:
                groups = []
                annual_returns = []

                for group_name in sorted(analysis_results.keys()):
                    if group_name.startswith('Group_'):
                        groups.append(group_name)
                        annual_returns.append(analysis_results[group_name]['annual_return'] * 100)

                bars = ax2.bar(groups, annual_returns, color=['red', 'orange', 'yellow', 'lightgreen', 'green'])
                ax2.set_title('各组合年化收益率', fontweight='bold')
                ax2.set_ylabel('年化收益率 (%)')
                ax2.grid(True, alpha=0.3)

                # 添加数值标签
                for bar, value in zip(bars, annual_returns):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1,
                            f'{value:.1f}%', ha='center', va='bottom')

            # 3. 夏普比率对比
            if analysis_results:
                groups = []
                sharpe_ratios = []

                for group_name in sorted(analysis_results.keys()):
                    if group_name.startswith('Group_'):
                        groups.append(group_name)
                        sharpe_ratios.append(analysis_results[group_name]['sharpe_ratio'])

                bars = ax3.bar(groups, sharpe_ratios, color=['darkred', 'red', 'orange', 'lightblue', 'blue'])
                ax3.set_title('各组合夏普比率', fontweight='bold')
                ax3.set_ylabel('夏普比率')
                ax3.grid(True, alpha=0.3)

                # 添加数值标签
                for bar, value in zip(bars, sharpe_ratios):
                    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                            f'{value:.2f}', ha='center', va='bottom')

            # 4. IC时间序列
            if ic_results and 'ic_values' in ic_results:
                ic_values = ic_results['ic_values']
                ax4.plot(range(len(ic_values)), ic_values, 'b-', linewidth=1, alpha=0.7)
                ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)
                ax4.axhline(y=ic_results['ic_mean'], color='green', linestyle='-', alpha=0.8,
                           label=f"平均IC: {ic_results['ic_mean']:.3f}")
                ax4.set_title('信息系数时间序列', fontweight='bold')
                ax4.set_xlabel('期数')
                ax4.set_ylabel('IC值')
                ax4.legend()
                ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            output_file = 'hsi_dividend_factor_validation.png'
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存: {output_file}")

            plt.show()

        except Exception as e:
            print(f"⚠️  生成图表失败: {e}")

    def save_results(self, analysis_results: Dict, ic_results: Dict):
        """保存分析结果"""
        try:
            print("💾 保存分析结果...")

            # 保存详细结果
            results = {
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': self.data_dir,
                'total_stocks': len(self.stock_data),
                'backtest_params': {
                    'rebalance_freq': self.rebalance_freq,
                    'n_groups': self.n_groups,
                    'lookback_days': self.lookback_days,
                    'forward_days': self.forward_days
                },
                'portfolio_performance': analysis_results,
                'information_coefficient': ic_results
            }

            # 保存JSON文件
            with open('hsi_dividend_factor_validation_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            print("💾 详细结果已保存: hsi_dividend_factor_validation_results.json")

            # 保存CSV汇总
            if analysis_results:
                with open('hsi_dividend_factor_validation_summary.csv', 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['组合', '年化收益(%)', '波动率(%)', '夏普比率', '最大回撤(%)', '胜率(%)', '总期数'])

                    for group_name in sorted(analysis_results.keys()):
                        if group_name.startswith('Group_'):
                            perf = analysis_results[group_name]
                            writer.writerow([
                                group_name,
                                f"{perf['annual_return']*100:.2f}",
                                f"{perf['std_return']*100:.2f}",
                                f"{perf['sharpe_ratio']:.2f}",
                                f"{abs(perf['max_drawdown'])*100:.2f}",
                                f"{perf['win_rate']*100:.1f}",
                                perf['total_periods']
                            ])

                print("📊 汇总已保存: hsi_dividend_factor_validation_summary.csv")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def run_validation(self) -> bool:
        """运行完整的因子验证流程"""
        try:
            print("🎯 开始股息率因子有效性验证...")

            # 1. 加载数据
            print("\n📊 第一步：加载股票数据")
            if not self.load_stock_data():
                return False

            # 2. 运行回测
            print("\n🔄 第二步：运行因子回测")
            if not self.run_backtest():
                return False

            # 3. 分析表现
            print("\n📈 第三步：分析因子表现")
            analysis_results = self.analyze_factor_performance()

            # 4. 计算IC
            print("\n📊 第四步：计算信息系数")
            ic_results = self.calculate_information_coefficient()

            # 5. 生成报告
            print("\n📄 第五步：生成分析报告")
            self.generate_report(analysis_results, ic_results)

            # 6. 创建可视化
            print("\n📊 第六步：生成可视化图表")
            self.create_visualizations(analysis_results, ic_results)

            # 7. 保存结果
            print("\n💾 第七步：保存分析结果")
            self.save_results(analysis_results, ic_results)

            print("\n✅ 股息率因子有效性验证完成！")

            # 总结
            if 'Group_1' in analysis_results and 'Group_5' in analysis_results:
                high_return = analysis_results['Group_1']['annual_return']
                low_return = analysis_results['Group_5']['annual_return']
                spread = high_return - low_return

                print(f"\n🎯 验证结果总结:")
                print(f"   高股息率组合年化收益: {high_return*100:.2f}%")
                print(f"   低股息率组合年化收益: {low_return*100:.2f}%")
                print(f"   多空收益差: {spread*100:.2f}%")

                if spread > 0:
                    print("   ✅ 股息率因子在恒生指数成分股中显示正向效应")
                    print("   💡 建议：可以考虑在投资策略中纳入股息率因子")
                else:
                    print("   ❌ 股息率因子在恒生指数成分股中显示负向效应")
                    print("   💡 建议：需要进一步研究或考虑其他因子")

            if ic_results:
                print(f"   📊 平均信息系数: {ic_results['ic_mean']:.4f}")
                if abs(ic_results['ic_mean']) > 0.02:
                    print("   ✅ 因子具有较强的预测能力")
                elif abs(ic_results['ic_mean']) > 0.01:
                    print("   ⚠️  因子具有一定的预测能力")
                else:
                    print("   ❌ 因子预测能力较弱")

            return True

        except Exception as e:
            print(f"❌ 验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎯 恒生指数成分股股息率因子有效性验证")
    print("=" * 60)

    # 创建验证器
    validator = HSIDividendFactorValidator("hsi_individual_dividend_yields")

    # 运行验证
    success = validator.run_validation()

    if success:
        print("\n🎉 股息率因子有效性验证完成！")
        print("\n📁 输出文件:")
        print("   📊 图表: hsi_dividend_factor_validation.png")
        print("   📄 详细结果: hsi_dividend_factor_validation_results.json")
        print("   📊 汇总: hsi_dividend_factor_validation_summary.csv")
    else:
        print("\n💥 验证过程中遇到问题，请检查错误信息。")


if __name__ == "__main__":
    main()
