#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子真实数据回测

基于真实的分红数据和历史价格数据进行股息率因子回测
"""

import csv
import json
import pickle
import os
import time
import re
from datetime import datetime, timedelta
from collections import defaultdict
import urllib.request
import urllib.parse

try:
    import akshare as ak
    HAS_AKSHARE = True
except ImportError:
    HAS_AKSHARE = False
    print("⚠️  akshare未安装，将使用模拟数据")

class HSIDividendFactorRealBacktest:
    """基于真实数据的股息率因子回测分析器"""
    
    def __init__(self, 
                 dividend_data_file: str = "hsi_dividend_quick_20250603_141448.csv",
                 start_date: str = "2022-01-01",
                 end_date: str = "2024-12-31",
                 cache_dir: str = "hsi_real_backtest_cache"):
        """
        初始化回测分析器
        
        Args:
            dividend_data_file: 分红数据文件
            start_date: 回测开始日期
            end_date: 回测结束日期
            cache_dir: 缓存目录
        """
        self.dividend_data_file = dividend_data_file
        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.cache_dir = cache_dir
        self.n_groups = 5
        
        # 创建缓存目录
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
        
        # 数据存储
        self.stocks = []  # 股票基本信息
        self.dividend_history = {}  # 历史分红数据 {symbol: [(date, amount), ...]}
        self.price_data = {}  # 历史价格数据 {symbol: {date: price}}
        
        # 回测结果
        self.portfolio_returns = defaultdict(list)
        
        print("🚀 基于真实数据的股息率因子回测分析器已初始化")
        print(f"📅 回测期间: {start_date} 至 {end_date}")
        print(f"📁 缓存目录: {cache_dir}")
    
    def load_stock_list(self) -> bool:
        """加载股票列表"""
        try:
            print("📁 加载股票列表...")
            
            with open(self.dividend_data_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['状态'] == 'success':
                        symbol = row['股票代码']
                        name = row['股票名称']
                        latest_dividend = float(row['最新分红(港元)'])
                        
                        self.stocks.append({
                            'symbol': symbol,
                            'name': name,
                            'latest_dividend': latest_dividend
                        })
            
            print(f"✅ 加载了 {len(self.stocks)} 只股票")
            return True
            
        except Exception as e:
            print(f"❌ 加载股票列表失败: {e}")
            return False
    
    def fetch_historical_dividend_data(self, symbol: str) -> list:
        """获取历史分红数据"""
        cache_file = os.path.join(self.cache_dir, f"{symbol}_dividend.json")
        
        # 检查缓存
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'r', encoding='utf-8') as f:
                    cached_data = json.load(f)
                    print(f"💾 从缓存加载 {symbol} 分红数据")
                    return [(datetime.strptime(item['date'], '%Y-%m-%d'), item['amount']) 
                           for item in cached_data]
            except:
                pass
        
        try:
            print(f"🌐 获取 {symbol} 历史分红数据...")
            
            base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://datacenter.eastmoney.com/',
                'Accept': 'application/json, text/plain, */*',
            }
            
            params = {
                'reportName': 'RPT_HKF10_MAIN_DIVBASIC',
                'columns': 'SECURITY_CODE,EX_DIVIDEND_DATE,DIVIDEND_DATE,YEAR,PLAN_EXPLAIN',
                'quoteColumns': '',
                'filter': f'(SECURITY_CODE="{symbol}")',
                'pageNumber': 1,
                'pageSize': 50,
                'sortTypes': '-1,-1',
                'sortColumns': 'NOTICE_DATE,EX_DIVIDEND_DATE',
                'source': 'F10',
                'client': 'PC',
                'v': str(int(time.time() * 1000))
            }
            
            query_string = urllib.parse.urlencode(params)
            full_url = f"{base_url}?{query_string}"
            req = urllib.request.Request(full_url, headers=headers)
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if 'result' in data and 'data' in data['result']:
                        raw_records = data['result']['data']
                        
                        dividend_records = []
                        cache_data = []
                        
                        for record in raw_records:
                            ex_date_str = record.get('EX_DIVIDEND_DATE')
                            plan_explain = record.get('PLAN_EXPLAIN', '')
                            
                            if ex_date_str and plan_explain:
                                try:
                                    ex_date = datetime.strptime(ex_date_str[:10], '%Y-%m-%d')
                                    amount = self.parse_dividend_amount(plan_explain)
                                    
                                    if amount > 0:
                                        dividend_records.append((ex_date, amount))
                                        cache_data.append({
                                            'date': ex_date.strftime('%Y-%m-%d'),
                                            'amount': amount
                                        })
                                except:
                                    continue
                        
                        # 保存到缓存
                        with open(cache_file, 'w', encoding='utf-8') as f:
                            json.dump(cache_data, f, ensure_ascii=False, indent=2)
                        
                        dividend_records.sort(key=lambda x: x[0])
                        print(f"   ✅ 获取到 {len(dividend_records)} 条分红记录")
                        return dividend_records
                    else:
                        print(f"   ⚠️  无分红数据")
                        return []
                else:
                    print(f"   ❌ HTTP错误: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
            return []
    
    def parse_dividend_amount(self, plan_explain: str) -> float:
        """解析分红金额"""
        if not plan_explain or plan_explain == "未派发或宣派股息":
            return 0.0

        plan_text = str(plan_explain)

        # 港币金额模式
        hkd_patterns = [
            r'港币(\d+\.?\d*)元',
            r'相当于港币(\d+\.?\d+)元',
            r'港币(\d+\.?\d+)',
            r'每股派港币(\d+\.?\d*)元',
            r'派港币(\d+\.?\d*)元',
            r'派息港币(\d+\.?\d*)元',
        ]

        for pattern in hkd_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue

        # 其他格式
        other_patterns = [
            r'每股派(\d+\.?\d*)港币',
            r'派(\d+\.?\d*)港币',
            r'每股(\d+\.?\d*)港币',
            r'每股派(\d+\.?\d*)元',
            r'派(\d+\.?\d*)元',
            r'每股(\d+\.?\d*)仙',
            r'派(\d+\.?\d*)仙',
            r'(\d+\.?\d*)港币',
        ]

        for pattern in other_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    amount = float(match.group(1))
                    if '仙' in pattern:
                        amount = amount / 100
                    return amount
                except:
                    continue

        # 括号中的港币等值
        bracket_pattern = r'\(相当于港币(\d+\.?\d+)元'
        match = re.search(bracket_pattern, plan_text)
        if match:
            try:
                return float(match.group(1))
            except:
                pass

        return 0.0
    
    def fetch_historical_price_data(self, symbol: str) -> dict:
        """获取历史价格数据"""
        cache_file = os.path.join(self.cache_dir, f"{symbol}_price.pkl")
        
        # 检查缓存
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    print(f"💾 从缓存加载 {symbol} 价格数据")
                    return cached_data
            except:
                pass
        
        if not HAS_AKSHARE:
            print(f"⚠️  akshare未安装，跳过 {symbol}")
            return {}
        
        try:
            print(f"📈 获取 {symbol} 历史价格数据...")
            
            # 使用akshare获取港股数据
            df = ak.stock_hk_daily(symbol=symbol)
            
            if df.empty:
                print(f"   ⚠️  无价格数据")
                return {}
            
            # 转换为字典格式
            price_dict = {}
            for _, row in df.iterrows():
                try:
                    date = datetime.strptime(str(row['date'])[:10], '%Y-%m-%d')
                    if self.start_date <= date <= self.end_date:
                        price_dict[date] = float(row['close'])
                except:
                    continue
            
            # 保存到缓存
            with open(cache_file, 'wb') as f:
                pickle.dump(price_dict, f)
            
            print(f"   ✅ 获取到 {len(price_dict)} 个交易日数据")
            time.sleep(1)  # 避免API限制
            return price_dict
            
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
            return {}
    
    def calculate_dividend_yield(self, symbol: str, calc_date: datetime) -> float:
        """计算指定日期的股息率（过去12个月分红/当前股价）"""
        try:
            # 获取过去12个月的分红
            start_period = calc_date - timedelta(days=365)
            
            total_dividend = 0.0
            if symbol in self.dividend_history:
                for div_date, amount in self.dividend_history[symbol]:
                    if start_period <= div_date <= calc_date:
                        total_dividend += amount
            
            # 获取当前股价
            if symbol not in self.price_data:
                return 0.0
            
            # 找到最接近的价格
            current_price = None
            for date in sorted(self.price_data[symbol].keys()):
                if date <= calc_date:
                    current_price = self.price_data[symbol][date]
                else:
                    break
            
            if current_price is None or current_price <= 0:
                return 0.0
            
            # 计算股息率（百分比）
            dividend_yield = (total_dividend / current_price) * 100
            return dividend_yield
            
        except Exception as e:
            return 0.0
    
    def generate_rebalance_dates(self) -> list:
        """生成季度再平衡日期"""
        dates = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            dates.append(current_date)
            # 下一个季度
            if current_date.month <= 3:
                next_date = current_date.replace(month=4, day=1)
            elif current_date.month <= 6:
                next_date = current_date.replace(month=7, day=1)
            elif current_date.month <= 9:
                next_date = current_date.replace(month=10, day=1)
            else:
                next_date = current_date.replace(year=current_date.year+1, month=1, day=1)
            current_date = next_date
        
        return dates
    
    def create_portfolios_by_dividend_yield(self, calc_date: datetime) -> dict:
        """基于股息率创建投资组合"""
        # 计算所有股票的股息率
        stock_yields = []
        for stock in self.stocks:
            symbol = stock['symbol']
            if symbol in self.price_data and symbol in self.dividend_history:
                dividend_yield = self.calculate_dividend_yield(symbol, calc_date)
                stock_yields.append((symbol, dividend_yield, stock['name']))
        
        if len(stock_yields) < self.n_groups:
            return {}
        
        # 按股息率排序（降序）
        stock_yields.sort(key=lambda x: x[1], reverse=True)
        
        # 分组
        n_stocks_per_group = len(stock_yields) // self.n_groups
        portfolios = {}
        
        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(stock_yields)
            else:
                end_idx = (i + 1) * n_stocks_per_group
            
            group_stocks = stock_yields[start_idx:end_idx]
            portfolios[f'Group_{i+1}'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_return(self, portfolio: list, start_date: datetime, end_date: datetime) -> float:
        """计算投资组合在持有期间的收益率"""
        if not portfolio:
            return 0.0
        
        stock_returns = []
        
        for symbol, yield_rate, name in portfolio:
            if symbol in self.price_data:
                # 获取开始和结束价格
                start_price = None
                end_price = None
                
                for date in sorted(self.price_data[symbol].keys()):
                    if date <= start_date and start_price is None:
                        start_price = self.price_data[symbol][date]
                    if date <= end_date:
                        end_price = self.price_data[symbol][date]
                
                if start_price and end_price and start_price > 0:
                    stock_return = (end_price - start_price) / start_price
                    stock_returns.append(stock_return)
        
        if stock_returns:
            # 等权重组合收益率
            return sum(stock_returns) / len(stock_returns)
        else:
            return 0.0

    def run_backtest(self) -> bool:
        """运行完整的回测"""
        try:
            print("\n🚀 开始基于真实数据的股息率因子回测...")

            # 1. 加载股票列表
            if not self.load_stock_list():
                return False

            # 2. 获取历史分红数据（选择部分股票进行测试）
            print("\n💰 获取历史分红数据...")
            test_symbols = [stock['symbol'] for stock in self.stocks[:20]]  # 测试前20只

            for i, symbol in enumerate(test_symbols):
                print(f"[{i+1}/{len(test_symbols)}] 处理 {symbol}...")
                dividend_data = self.fetch_historical_dividend_data(symbol)
                self.dividend_history[symbol] = dividend_data
                time.sleep(0.5)  # 避免API限制

            # 3. 获取历史价格数据
            print("\n📈 获取历史价格数据...")
            for i, symbol in enumerate(test_symbols):
                print(f"[{i+1}/{len(test_symbols)}] 处理 {symbol}...")
                price_data = self.fetch_historical_price_data(symbol)
                self.price_data[symbol] = price_data
                time.sleep(0.5)  # 避免API限制

            # 过滤有完整数据的股票
            valid_stocks = []
            for stock in self.stocks:
                symbol = stock['symbol']
                if (symbol in self.dividend_history and
                    symbol in self.price_data and
                    len(self.price_data[symbol]) > 0):
                    valid_stocks.append(stock)

            self.stocks = valid_stocks
            print(f"\n✅ 有完整数据的股票: {len(self.stocks)} 只")

            if len(self.stocks) < self.n_groups:
                print(f"❌ 股票数量不足，无法分为 {self.n_groups} 组")
                return False

            # 4. 生成再平衡日期
            rebalance_dates = self.generate_rebalance_dates()
            print(f"🔄 生成 {len(rebalance_dates)} 个再平衡日期")

            # 5. 执行回测
            for i, rebalance_date in enumerate(rebalance_dates[:-1]):
                print(f"\n🔄 再平衡 {i+1}/{len(rebalance_dates)-1}: {rebalance_date.strftime('%Y-%m-%d')}")

                # 基于当时的股息率创建投资组合
                portfolios = self.create_portfolios_by_dividend_yield(rebalance_date)

                if not portfolios:
                    print("   ⚠️  无法创建投资组合")
                    continue

                # 显示组合信息
                for group_name, portfolio in portfolios.items():
                    if portfolio:
                        avg_yield = sum(s[1] for s in portfolio) / len(portfolio)
                        print(f"   {group_name}: {len(portfolio)}只股票, 平均股息率: {avg_yield:.2f}%")

                # 计算持有期间表现
                next_rebalance = rebalance_dates[i+1]

                for group_name, portfolio in portfolios.items():
                    portfolio_return = self.calculate_portfolio_return(portfolio, rebalance_date, next_rebalance)
                    self.portfolio_returns[group_name].append(portfolio_return)

            print("\n✅ 回测完成!")
            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def analyze_results(self) -> dict:
        """分析回测结果"""
        if not self.portfolio_returns:
            print("❌ 没有回测结果可供分析")
            return {}

        print("\n📊 回测结果分析")
        print("=" * 80)

        # 计算各组合的绩效指标
        results = {}

        for group_name, returns in self.portfolio_returns.items():
            if returns:
                # 基本统计
                avg_return = sum(returns) / len(returns)

                # 累积收益
                cumulative_return = 1.0
                for r in returns:
                    cumulative_return *= (1 + r)
                cumulative_return -= 1

                # 年化收益（假设季度再平衡）
                periods_per_year = 4
                total_periods = len(returns)
                if total_periods > 0:
                    annual_return = (1 + cumulative_return) ** (periods_per_year / total_periods) - 1
                else:
                    annual_return = 0

                # 波动率
                if len(returns) > 1:
                    import math
                    variance = sum((r - avg_return) ** 2 for r in returns) / (len(returns) - 1)
                    volatility = math.sqrt(variance) * math.sqrt(periods_per_year)
                else:
                    volatility = 0

                # 夏普比率（假设无风险利率为3%）
                risk_free_rate = 0.03
                sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0

                # 胜率
                win_rate = sum(1 for r in returns if r > 0) / len(returns)

                results[group_name] = {
                    'periods': len(returns),
                    'avg_return': avg_return,
                    'cumulative_return': cumulative_return,
                    'annual_return': annual_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'win_rate': win_rate
                }

        # 显示结果表格
        print(f"{'组合':<10} {'期数':<6} {'平均收益':<10} {'累积收益':<10} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'胜率':<8}")
        print("-" * 80)

        for group_name in sorted(results.keys()):
            r = results[group_name]
            print(f"{group_name:<10} {r['periods']:<6} {r['avg_return']*100:<10.2f}% "
                  f"{r['cumulative_return']*100:<10.1f}% {r['annual_return']*100:<10.1f}% "
                  f"{r['volatility']*100:<8.1f}% {r['sharpe_ratio']:<8.2f} {r['win_rate']*100:<8.1f}%")

        # 因子有效性分析
        if 'Group_1' in results and 'Group_5' in results:
            high_group = results['Group_1']
            low_group = results['Group_5']

            return_spread = high_group['annual_return'] - low_group['annual_return']

            print(f"\n📈 因子有效性分析:")
            print(f"高股息率组合年化收益: {high_group['annual_return']*100:.2f}%")
            print(f"低股息率组合年化收益: {low_group['annual_return']*100:.2f}%")
            print(f"多空收益差: {return_spread*100:.2f}%")

            if return_spread > 0:
                print("✅ 股息率因子表现正向：高股息率股票表现更好")
            else:
                print("❌ 股息率因子表现负向：低股息率股票表现更好")

        return results

    def save_results(self, results: dict):
        """保存回测结果"""
        try:
            # 保存到JSON
            output_data = {
                'backtest_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'backtest_period': f"{self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}",
                'methodology': 'Real data dividend yield factor backtest',
                'data_sources': {
                    'dividend_data': '东方财富网API',
                    'price_data': 'akshare港股数据'
                },
                'stocks_analyzed': len(self.stocks),
                'rebalance_frequency': 'Quarterly',
                'number_of_groups': self.n_groups,
                'results': results
            }

            with open('hsi_dividend_factor_real_backtest_results.json', 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)

            print(f"\n💾 回测结果已保存到: hsi_dividend_factor_real_backtest_results.json")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    print("🎯 恒生指数成分股股息率因子真实数据回测")
    print("📊 基于真实分红数据和历史价格数据")
    print("=" * 60)

    # 创建回测器
    backtest = HSIDividendFactorRealBacktest(
        dividend_data_file="hsi_dividend_quick_20250603_141448.csv",
        start_date="2023-01-01",
        end_date="2024-12-31",
        cache_dir="hsi_real_backtest_cache"
    )

    try:
        # 运行回测
        if backtest.run_backtest():
            # 分析结果
            results = backtest.analyze_results()

            # 保存结果
            if results:
                backtest.save_results(results)

            print(f"\n🎉 真实数据股息率因子回测完成！")
            print(f"📋 使用了真实的分红数据和历史价格数据")
            print(f"📈 验证了股息率因子在港股市场的有效性")
        else:
            print("❌ 回测失败")

    except Exception as e:
        print(f"❌ 回测过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
