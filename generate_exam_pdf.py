#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
<PERSON>rip<PERSON> to generate a PDF from collected Zhejiang province's public institution exam past questions.
"""

import os
import json
import logging
import glob
from datetime import datetime
from fpdf import FPDF

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("zj_exam_pdf_generator.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Constants
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
DATA_DIR = os.path.join(BASE_DIR, "data")
OUTPUT_DIR = os.path.join(BASE_DIR, "output")
EXAM_QUESTIONS_DIR = os.path.join(DATA_DIR, "exam_questions")
PDF_FILENAME = os.path.join(OUTPUT_DIR, f"浙江省事业单位统考历年真题_{datetime.now().strftime('%Y%m%d')}.pdf")
MARKDOWN_FILENAME = os.path.join(OUTPUT_DIR, f"浙江省事业单位统考历年真题_{datetime.now().strftime('%Y%m%d')}.md")

# Create necessary directories
os.makedirs(DATA_DIR, exist_ok=True)
os.makedirs(OUTPUT_DIR, exist_ok=True)
os.makedirs(EXAM_QUESTIONS_DIR, exist_ok=True)

def load_exam_questions():
    """Load all exam questions from JSON files."""
    json_files = glob.glob(os.path.join(EXAM_QUESTIONS_DIR, "*.json"))

    if not json_files:
        logger.warning("No exam question files found")
        return []

    all_exams = []
    for json_file in json_files:
        try:
            with open(json_file, 'r', encoding='utf-8') as f:
                exam_data = json.load(f)
                all_exams.append(exam_data)
        except Exception as e:
            logger.error(f"Error loading {json_file}: {e}")

    # Sort by year
    all_exams.sort(key=lambda x: x.get("year", "0000"), reverse=True)

    logger.info(f"Loaded {len(all_exams)} sets of exam questions")
    return all_exams

def create_markdown(exams):
    """Create a markdown file from exam questions."""
    if not exams:
        logger.warning("No exam data to create markdown")
        return False

    logger.info(f"Creating markdown file: {MARKDOWN_FILENAME}")

    with open(MARKDOWN_FILENAME, 'w', encoding='utf-8') as f:
        # Write title
        f.write("# 浙江省事业单位统考历年真题汇总\n\n")
        f.write(f"*生成日期: {datetime.now().strftime('%Y-%m-%d')}*\n\n")
        f.write("## 目录\n\n")

        # Create table of contents
        for i, exam in enumerate(exams):
            year = exam.get("year", "未知年份")
            title = exam.get("title", "未知标题")
            f.write(f"{i+1}. [{year} - {title}](#{year})\n")

        f.write("\n---\n\n")

        # Write each exam's questions
        for exam in exams:
            year = exam.get("year", "未知年份")
            title = exam.get("title", "未知标题")
            source = exam.get("source", "未知来源")
            url = exam.get("url", "")
            questions = exam.get("questions", [])

            f.write(f"## <a id='{year}'></a>{year} - {title}\n\n")
            f.write(f"来源: {source}\n\n")

            if url:
                f.write(f"原文链接: [{url}]({url})\n\n")

            f.write("### 试题内容\n\n")

            if questions:
                for question in questions:
                    f.write(f"{question}\n\n")
            else:
                f.write("*暂无试题内容*\n\n")

            f.write("---\n\n")

    logger.info(f"Markdown file created: {MARKDOWN_FILENAME}")
    return True

# Removed WeasyPrint-based PDF generation due to dependency issues

def generate_pdf_with_fpdf():
    """Generate a PDF file using FPDF as a fallback method."""
    if not os.path.exists(MARKDOWN_FILENAME):
        logger.error(f"Markdown file not found: {MARKDOWN_FILENAME}")
        return False

    logger.info(f"Generating PDF with FPDF: {PDF_FILENAME}")

    try:
        # Create PDF object
        pdf = FPDF()
        pdf.add_page()

        # Use default font
        pdf.set_font('Arial', '', 12)

        # Read markdown file
        with open(MARKDOWN_FILENAME, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        # Process each line
        for line in lines:
            line = line.strip()

            # Handle headings
            if line.startswith('# '):
                pdf.add_page()
                pdf.set_font('Arial', 'B', 20)
                pdf.cell(0, 10, line[2:], 0, 1, 'C')
                pdf.set_font('Arial', '', 12)
            elif line.startswith('## '):
                pdf.add_page()
                pdf.set_font('Arial', 'B', 16)
                pdf.cell(0, 10, line[3:], 0, 1, 'L')
                pdf.set_font('Arial', '', 12)
            elif line.startswith('### '):
                pdf.set_font('Arial', 'B', 14)
                pdf.cell(0, 10, line[4:], 0, 1, 'L')
                pdf.set_font('Arial', '', 12)
            # Handle regular text
            elif line:
                # Skip Chinese characters that can't be displayed with the default font
                filtered_line = ''.join(c for c in line if ord(c) < 128)
                if filtered_line:
                    pdf.multi_cell(0, 8, filtered_line)
                    pdf.ln(4)

        # Save PDF file
        pdf.output(PDF_FILENAME)

        logger.info(f"PDF file created: {PDF_FILENAME}")
        logger.warning("Note: Chinese characters may not display correctly in the PDF.")
        logger.info("Please view the Markdown file for the complete content.")
        return True

    except Exception as e:
        logger.error(f"Error generating PDF with FPDF: {e}")
        return False

def main():
    """Main function to orchestrate the process."""
    logger.info("Starting to generate Markdown from collected exam questions")

    # Load exam questions
    exams = load_exam_questions()

    if not exams:
        logger.error("No exam questions found. Please run collect_zj_exam_questions.py first")
        return

    # Create markdown
    if not create_markdown(exams):
        logger.error("Failed to create markdown file")
        return

    # Skip PDF generation due to encoding issues
    logger.info("Markdown generation completed successfully")
    logger.info(f"Output file: {MARKDOWN_FILENAME}")
    logger.info("Note: PDF generation is skipped due to encoding issues with Chinese characters.")
    logger.info("You can manually convert the Markdown file to PDF using other tools.")

if __name__ == "__main__":
    main()
