#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美股5年股息率数据汇总报告生成器
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def generate_summary_report():
    """生成5年股息率汇总报告"""
    data_dir = 'us_dividend_yield_data_5y'
    
    if not os.path.exists(data_dir):
        print(f"❌ 数据目录不存在: {data_dir}")
        return
    
    # 加载所有数据
    data = {}
    csv_files = [f for f in os.listdir(data_dir) if f.endswith('_5y_daily_dividend_yield.csv')]
    
    for file in csv_files:
        symbol = file.split('_')[0]
        file_path = os.path.join(data_dir, file)
        
        try:
            df = pd.read_csv(file_path)
            df['date'] = pd.to_datetime(df['date'], utc=True)
            df.set_index('date', inplace=True)
            data[symbol] = df
            print(f"✅ 加载 {symbol} 数据: {len(df)} 个数据点")
        except Exception as e:
            print(f"❌ 加载 {symbol} 数据失败: {e}")
    
    if not data:
        print("❌ 没有找到数据文件")
        return
    
    print(f"\n📊 总共加载 {len(data)} 只股票的5年股息率数据")
    
    # 生成汇总报告
    report_path = '5year_dividend_summary_report.txt'
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write("美股5年股息率数据分析报告\n")
        f.write("=" * 60 + "\n")
        f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"分析股票数量: {len(data)}\n")
        f.write(f"数据期间: 2020年6月 至 2025年6月\n\n")
        
        # 整体统计
        all_yields = []
        for df in data.values():
            all_yields.extend(df['dividend_yield'].tolist())
        
        f.write("📊 整体统计:\n")
        f.write("-" * 40 + "\n")
        f.write(f"总数据点数: {len(all_yields):,}\n")
        f.write(f"整体平均股息率: {np.mean(all_yields):.3f}%\n")
        f.write(f"整体最高股息率: {np.max(all_yields):.3f}%\n")
        f.write(f"整体最低股息率: {np.min(all_yields):.3f}%\n")
        f.write(f"整体标准差: {np.std(all_yields):.3f}%\n\n")
        
        # 个股统计
        stats = []
        for symbol, df in data.items():
            stats.append({
                'symbol': symbol,
                'latest': df['dividend_yield'].iloc[-1],
                'mean': df['dividend_yield'].mean(),
                'max': df['dividend_yield'].max(),
                'min': df['dividend_yield'].min(),
                'std': df['dividend_yield'].std(),
                'data_points': len(df)
            })
        
        # 按平均股息率排序
        stats.sort(key=lambda x: x['mean'], reverse=True)
        
        f.write("📈 个股详细统计 (按5年平均股息率排序):\n")
        f.write("-" * 80 + "\n")
        f.write(f"{'排名':>4} {'股票':>6} {'最新':>8} {'5年平均':>10} {'最高':>8} {'最低':>8} {'波动性':>8} {'数据点':>8}\n")
        f.write("-" * 80 + "\n")
        
        for i, stat in enumerate(stats, 1):
            f.write(f"{i:>4} {stat['symbol']:>6} "
                   f"{stat['latest']:>7.2f}% "
                   f"{stat['mean']:>9.2f}% "
                   f"{stat['max']:>7.2f}% "
                   f"{stat['min']:>7.2f}% "
                   f"{stat['std']:>7.2f}% "
                   f"{stat['data_points']:>7,}\n")
        
        # 分类分析
        f.write(f"\n🏷️  分类分析:\n")
        f.write("-" * 40 + "\n")
        
        high_yield = [s for s in stats if s['mean'] >= 5.0]
        medium_yield = [s for s in stats if 2.0 <= s['mean'] < 5.0]
        low_yield = [s for s in stats if s['mean'] < 2.0]
        
        f.write(f"🔴 高股息股票 (≥5%): {len(high_yield)} 只\n")
        for s in high_yield:
            f.write(f"   {s['symbol']}: 平均 {s['mean']:.2f}%, 最新 {s['latest']:.2f}%\n")
        
        f.write(f"\n🟡 中等股息股票 (2-5%): {len(medium_yield)} 只\n")
        for s in medium_yield:
            f.write(f"   {s['symbol']}: 平均 {s['mean']:.2f}%, 最新 {s['latest']:.2f}%\n")
        
        f.write(f"\n🟢 低股息股票 (<2%): {len(low_yield)} 只\n")
        for s in low_yield:
            f.write(f"   {s['symbol']}: 平均 {s['mean']:.2f}%, 最新 {s['latest']:.2f}%\n")
        
        # 趋势分析
        f.write(f"\n📈 趋势分析:\n")
        f.write("-" * 40 + "\n")
        
        upward_trend = []
        downward_trend = []
        stable_trend = []
        
        for symbol, df in data.items():
            # 计算2020年和2024年的平均股息率
            try:
                data_2020 = df[df.index.to_series().dt.year == 2020]
                data_2024 = df[df.index.to_series().dt.year == 2024]
            except:
                # 如果时间索引有问题，跳过这个股票
                continue
            
            if not data_2020.empty and not data_2024.empty:
                avg_2020 = data_2020['dividend_yield'].mean()
                avg_2024 = data_2024['dividend_yield'].mean()
                change = avg_2024 - avg_2020
                
                if change > 0.5:
                    upward_trend.append((symbol, change))
                elif change < -0.5:
                    downward_trend.append((symbol, change))
                else:
                    stable_trend.append((symbol, change))
        
        f.write(f"📈 上升趋势 (2024年比2020年提高>0.5%): {len(upward_trend)} 只\n")
        for symbol, change in sorted(upward_trend, key=lambda x: x[1], reverse=True):
            f.write(f"   {symbol}: +{change:.2f}%\n")
        
        f.write(f"\n📉 下降趋势 (2024年比2020年下降>0.5%): {len(downward_trend)} 只\n")
        for symbol, change in sorted(downward_trend, key=lambda x: x[1]):
            f.write(f"   {symbol}: {change:.2f}%\n")
        
        f.write(f"\n➡️  稳定趋势 (变化±0.5%以内): {len(stable_trend)} 只\n")
        for symbol, change in stable_trend:
            f.write(f"   {symbol}: {change:+.2f}%\n")
        
        # 投资建议
        f.write(f"\n💡 投资建议:\n")
        f.write("-" * 40 + "\n")
        f.write("基于5年股息率数据分析:\n\n")
        
        f.write("🏆 高股息稳定型 (推荐长期持有):\n")
        stable_high = [s for s in stats if s['mean'] >= 4.0 and s['std'] <= 2.0]
        for s in stable_high[:5]:
            f.write(f"   {s['symbol']}: 平均{s['mean']:.2f}%, 波动{s['std']:.2f}%\n")
        
        f.write(f"\n⚡ 高增长潜力型:\n")
        growth_potential = [s for s in stats if s['latest'] > s['mean'] * 1.1]
        for s in growth_potential[:3]:
            f.write(f"   {s['symbol']}: 最新{s['latest']:.2f}% > 平均{s['mean']:.2f}%\n")
        
        f.write(f"\n⚠️  风险提示:\n")
        high_volatility = [s for s in stats if s['std'] >= 2.0]
        for s in high_volatility[:3]:
            f.write(f"   {s['symbol']}: 波动性较高 {s['std']:.2f}%\n")
        
        f.write(f"\n📝 数据说明:\n")
        f.write("- 股息率 = 年化股息 / 股价 × 100%\n")
        f.write("- 数据基于每日收盘价和滚动12个月股息计算\n")
        f.write("- 波动性用标准差衡量，数值越大波动越大\n")
        f.write("- 投资有风险，建议结合其他指标综合分析\n")
    
    print(f"\n📋 汇总报告已生成: {report_path}")
    
    # 显示简要统计
    print(f"\n📊 简要统计:")
    print(f"   高股息股票 (≥5%): {len(high_yield)} 只")
    print(f"   中等股息股票 (2-5%): {len(medium_yield)} 只") 
    print(f"   低股息股票 (<2%): {len(low_yield)} 只")
    print(f"   整体平均股息率: {np.mean(all_yields):.2f}%")
    
    return report_path

def main():
    """主函数"""
    print("📊 美股5年股息率汇总报告生成器")
    print("=" * 50)
    
    report_path = generate_summary_report()
    
    if report_path:
        print(f"\n🎯 报告生成完成!")
        print(f"📁 文件位置: {report_path}")
        print(f"\n💡 使用方法:")
        print(f"   cat {report_path}")
        print(f"   或直接打开文件查看详细分析")

if __name__ == "__main__":
    main()
