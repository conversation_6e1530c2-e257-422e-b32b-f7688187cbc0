#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优化参数策略回测分析
5%分位数买入 + 95%分位数+1天回落卖出（无最大持有期限制）
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.filters.hp_filter import hpfilter
import pickle
import os
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class OptimizedStrategyAnalyzer:
    """优化参数策略分析器"""

    def __init__(self, buy_threshold=0.05, sell_threshold=0.95, pullback_days=1):
        self.buy_threshold = buy_threshold
        self.sell_threshold = sell_threshold
        self.pullback_days = pullback_days
        self.data = None
        self.processed_data = None
        self.trades = []
        self.current_position = None
        self.daily_returns = None
        self.cumulative_returns = None
        self.drawdowns = None
        self.performance_stats = {}

    def load_and_process_data(self):
        """加载和处理数据"""
        try:
            cache_file = 'cache/511260_price_data.pkl'
            with open(cache_file, 'rb') as f:
                self.data = pickle.load(f)

            # 确保索引是日期类型
            if not isinstance(self.data.index, pd.DatetimeIndex):
                if 'date' in self.data.columns:
                    self.data['date'] = pd.to_datetime(self.data['date'])
                    self.data.set_index('date', inplace=True)
                else:
                    self.data.index = pd.to_datetime(self.data.index)

            # 标准化列名
            if 'close' not in self.data.columns:
                for col in self.data.columns:
                    if 'close' in col.lower() or '收盘' in col:
                        self.data['close'] = self.data[col]
                        break

            # 筛选最近10年数据
            end_date = self.data.index[-1]
            start_date = end_date - pd.DateOffset(years=10)
            self.data = self.data[self.data.index >= start_date]

            print(f"✅ 成功加载511260数据")
            print(f"   数据期间: {self.data.index[0].strftime('%Y-%m-%d')} 至 {self.data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   数据点数: {len(self.data)} 个")

            # 数据处理
            price = self.data['close']

            # HP滤波去趋势
            cycle, trend = hpfilter(price.dropna(), lamb=7000000)

            # 创建处理后的数据
            self.processed_data = pd.DataFrame(index=price.index)
            self.processed_data['价格'] = price
            self.processed_data['趋势成分'] = trend.reindex(price.index)
            self.processed_data['去趋势数据'] = cycle.reindex(price.index)

            # 计算百分位数
            self.processed_data['百分位数'] = self.processed_data['去趋势数据'].rolling(252).rank(pct=True)

            # 计算价格变化和回落天数
            self.processed_data['价格变化'] = self.processed_data['价格'].pct_change()
            self.processed_data['连续下跌天数'] = 0

            # 计算连续下跌天数
            for i in range(1, len(self.processed_data)):
                if self.processed_data['价格变化'].iloc[i] < 0:
                    self.processed_data['连续下跌天数'].iloc[i] = self.processed_data['连续下跌天数'].iloc[i-1] + 1
                else:
                    self.processed_data['连续下跌天数'].iloc[i] = 0

            # 生成买卖信号
            self.processed_data['买入信号'] = self.processed_data['百分位数'] <= self.buy_threshold

            # 回落卖出信号：95%分位数 + 连续回落
            self.processed_data['卖出信号'] = (
                (self.processed_data['百分位数'] >= self.sell_threshold) &
                (self.processed_data['连续下跌天数'] >= self.pullback_days)
            )

            return True

        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def execute_optimized_strategy(self):
        """执行优化参数策略"""
        print(f"\n🔄 执行优化参数策略 ({self.buy_threshold*100:.0f}%买入, {self.sell_threshold*100:.0f}%分位数+{self.pullback_days}天回落卖出)...")

        self.trades = []
        position = 0  # 0: 空仓, 1: 持仓
        buy_date = None
        buy_price = None

        for date, row in self.processed_data.iterrows():
            # 如果空仓且有买入信号
            if position == 0 and row['买入信号'] and not pd.isna(row['百分位数']):
                position = 1
                buy_date = date
                buy_price = row['价格']
                print(f"   🟢 {date.strftime('%Y-%m-%d')}: 买入 价格{buy_price:.2f} 百分位数{row['百分位数']*100:.1f}%")

            # 如果持仓且有回落卖出信号
            elif position == 1 and row['卖出信号'] and not pd.isna(row['百分位数']):
                sell_date = date
                sell_price = row['价格']
                trade_return = (sell_price - buy_price) / buy_price
                hold_days_actual = (date - buy_date).days

                print(f"   🔴 {date.strftime('%Y-%m-%d')}: 卖出 价格{sell_price:.2f} 收益{trade_return*100:+.2f}% 持有{hold_days_actual}天 回落{int(row['连续下跌天数'])}天")

                self.trades.append({
                    'buy_date': buy_date,
                    'sell_date': sell_date,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'hold_days': hold_days_actual,
                    'return': trade_return,
                    'sell_reason': f"{self.sell_threshold*100:.0f}%分位数+{int(row['连续下跌天数'])}天回落",
                    'pullback_days': row['连续下跌天数']
                })

                position = 0
                buy_date = None
                buy_price = None

        # 检查当前状态
        if position == 1:
            current_price = self.processed_data['价格'].iloc[-1]
            current_return = (current_price - buy_price) / buy_price
            hold_days = (self.processed_data.index[-1] - buy_date).days

            self.current_position = {
                'buy_date': buy_date,
                'buy_price': buy_price,
                'current_price': current_price,
                'current_return': current_return,
                'hold_days': hold_days
            }

            print(f"   📍 当前持仓: 买入{buy_date.strftime('%Y-%m-%d')} 价格{buy_price:.2f}")
            print(f"   📍 当前状态: 价格{current_price:.2f} 浮动收益{current_return*100:+.2f}% 持有{hold_days}天")
        else:
            self.current_position = None
            print(f"   📍 当前空仓")

        print(f"✅ 优化参数策略执行完成，共完成{len(self.trades)}笔交易")
        return True

    def calculate_performance_metrics(self):
        """计算策略表现指标"""
        print(f"\n📊 计算策略表现指标...")

        # 初始化每日收益序列
        self.daily_returns = pd.Series(0.0, index=self.processed_data.index)

        # 处理已完成的交易
        for trade in self.trades:
            buy_date = trade['buy_date']
            sell_date = trade['sell_date']

            # 在持仓期间计算每日收益
            mask = (self.processed_data.index >= buy_date) & (self.processed_data.index <= sell_date)
            price_returns = self.processed_data['价格'].pct_change()
            self.daily_returns[mask] = price_returns[mask]

        # 处理当前持仓（如果有）
        if self.current_position:
            buy_date = self.current_position['buy_date']
            mask = self.processed_data.index >= buy_date
            price_returns = self.processed_data['价格'].pct_change()
            self.daily_returns[mask] = price_returns[mask]

        # 计算累积收益
        self.cumulative_returns = (1 + self.daily_returns).cumprod()

        # 计算回撤
        rolling_max = self.cumulative_returns.expanding().max()
        self.drawdowns = (self.cumulative_returns - rolling_max) / rolling_max

        # 计算买入持有基准
        buy_hold_returns = self.processed_data['价格'] / self.processed_data['价格'].iloc[0]
        bh_daily_returns = self.processed_data['价格'].pct_change()
        bh_cumulative = buy_hold_returns
        bh_rolling_max = bh_cumulative.expanding().max()
        bh_drawdowns = (bh_cumulative - bh_rolling_max) / bh_rolling_max

        # 策略统计指标
        total_return = self.cumulative_returns.iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / len(self.daily_returns)) - 1
        volatility = self.daily_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        max_drawdown = self.drawdowns.min()

        # 交易统计
        if self.trades:
            trade_returns = [t['return'] for t in self.trades]
            win_rate = sum(1 for r in trade_returns if r > 0) / len(trade_returns)
            avg_return = np.mean(trade_returns)
            avg_hold_days = np.mean([t['hold_days'] for t in self.trades])
            max_hold_days = max([t['hold_days'] for t in self.trades])
            min_hold_days = min([t['hold_days'] for t in self.trades])

            # 回落天数统计
            avg_pullback_days = np.mean([t['pullback_days'] for t in self.trades])
        else:
            trade_returns = []
            win_rate = 0
            avg_return = 0
            avg_hold_days = 0
            max_hold_days = 0
            min_hold_days = 0
            avg_pullback_days = 0

        # 买入持有基准统计
        bh_total_return = bh_cumulative.iloc[-1] - 1
        bh_annual_return = (1 + bh_total_return) ** (252 / len(bh_daily_returns)) - 1
        bh_volatility = bh_daily_returns.std() * np.sqrt(252)
        bh_sharpe = bh_annual_return / bh_volatility if bh_volatility > 0 else 0
        bh_max_dd = bh_drawdowns.min()

        self.performance_stats = {
            'strategy': {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'avg_hold_days': avg_hold_days,
                'max_hold_days': max_hold_days,
                'min_hold_days': min_hold_days,
                'total_trades': len(self.trades),
                'avg_pullback_days': avg_pullback_days
            },
            'buy_hold': {
                'total_return': bh_total_return,
                'annual_return': bh_annual_return,
                'volatility': bh_volatility,
                'sharpe_ratio': bh_sharpe,
                'max_drawdown': bh_max_dd
            }
        }

        print(f"✅ 策略表现指标计算完成")
        return True

    def print_performance_summary(self):
        """打印策略表现总结"""
        print("\n" + "="*80)
        print(f"📊 优化参数策略表现总结 ({self.buy_threshold*100:.0f}%买入 + {self.sell_threshold*100:.0f}%分位数+{self.pullback_days}天回落卖出)")
        print("="*80)

        strategy = self.performance_stats['strategy']
        buy_hold = self.performance_stats['buy_hold']

        print(f"\n🎯 优化参数策略表现:")
        print(f"   总收益率: {strategy['total_return']*100:+.2f}%")
        print(f"   年化收益率: {strategy['annual_return']*100:+.2f}%")
        print(f"   年化波动率: {strategy['volatility']*100:.2f}%")
        print(f"   夏普比率: {strategy['sharpe_ratio']:.2f}")
        print(f"   最大回撤: {strategy['max_drawdown']*100:.2f}%")
        print(f"   胜率: {strategy['win_rate']*100:.1f}%")
        print(f"   平均单次收益: {strategy['avg_return']*100:+.2f}%")
        print(f"   平均持有天数: {strategy['avg_hold_days']:.1f}天")
        print(f"   持有期范围: {strategy['min_hold_days']}-{strategy['max_hold_days']}天")
        print(f"   总交易次数: {strategy['total_trades']}次")
        print(f"   平均回落天数: {strategy['avg_pullback_days']:.1f}天")

        if self.current_position:
            print(f"   当前持仓收益: {self.current_position['current_return']*100:+.2f}%")
            print(f"   当前持有天数: {self.current_position['hold_days']}天")

        print(f"\n📈 买入持有基准:")
        print(f"   总收益率: {buy_hold['total_return']*100:+.2f}%")
        print(f"   年化收益率: {buy_hold['annual_return']*100:+.2f}%")
        print(f"   年化波动率: {buy_hold['volatility']*100:.2f}%")
        print(f"   夏普比率: {buy_hold['sharpe_ratio']:.2f}")
        print(f"   最大回撤: {buy_hold['max_drawdown']*100:.2f}%")

        print(f"\n🏆 优化参数策略优势:")
        excess_return = strategy['annual_return'] - buy_hold['annual_return']
        risk_reduction = buy_hold['volatility'] - strategy['volatility']
        dd_improvement = buy_hold['max_drawdown'] - strategy['max_drawdown']

        print(f"   超额年化收益: {excess_return*100:+.2f}%")
        print(f"   波动率变化: {risk_reduction*100:+.2f}%")
        print(f"   回撤改善: {dd_improvement*100:+.2f}%")
        print(f"   夏普比率提升: {strategy['sharpe_ratio'] - buy_hold['sharpe_ratio']:+.2f}")

    def analyze_optimized_trades(self):
        """分析优化参数交易的特点"""
        print(f"\n📋 优化参数策略交易分析:")
        print("-" * 60)

        if not self.trades:
            print("❌ 没有交易记录")
            return

        print(f"交易概况:")
        print(f"   总交易次数: {len(self.trades)}次")
        print(f"   100%优化参数驱动 ({self.buy_threshold*100:.0f}%买入+{self.sell_threshold*100:.0f}%分位数+{self.pullback_days}天回落)")

        # 回落天数分析
        pullback_days = [t['pullback_days'] for t in self.trades]
        print(f"\n回落卖出分析:")
        print(f"   平均回落天数: {np.mean(pullback_days):.1f}天")
        print(f"   回落天数范围: {min(pullback_days)}-{max(pullback_days)}天")

        # 持有期分析
        hold_days = [t['hold_days'] for t in self.trades]
        returns = [t['return'] * 100 for t in self.trades]

        print(f"\n持有期分析:")
        print(f"   最短持有: {min(hold_days)}天")
        print(f"   最长持有: {max(hold_days)}天")
        print(f"   平均持有: {np.mean(hold_days):.1f}天")
        print(f"   持有期标准差: {np.std(hold_days):.1f}天")

        # 收益分析
        print(f"\n收益分析:")
        print(f"   最大收益: {max(returns):+.2f}%")
        print(f"   最小收益: {min(returns):+.2f}%")
        print(f"   平均收益: {np.mean(returns):+.2f}%")
        print(f"   收益标准差: {np.std(returns):.2f}%")

        # 年度交易分布
        yearly_trades = {}
        for trade in self.trades:
            year = trade['buy_date'].year
            if year not in yearly_trades:
                yearly_trades[year] = []
            yearly_trades[year].append(trade['return'])

        print(f"\n年度交易分布:")
        for year in sorted(yearly_trades.keys()):
            year_returns = yearly_trades[year]
            avg_return = np.mean(year_returns) * 100
            trade_count = len(year_returns)
            print(f"   {year}年: {trade_count}笔交易, 平均收益{avg_return:+.2f}%")

        # 详细交易列表
        print(f"\n详细交易记录:")
        for i, trade in enumerate(self.trades, 1):
            print(f"   第{i}笔: {trade['buy_date'].strftime('%Y-%m-%d')} 买入 → "
                  f"{trade['sell_date'].strftime('%Y-%m-%d')} 卖出, "
                  f"收益{trade['return']*100:+.2f}%, 持有{trade['hold_days']}天, "
                  f"回落{int(trade['pullback_days'])}天")

    def compare_with_original_strategy(self):
        """与原策略对比"""
        print(f"\n📊 与原策略(15%-90%-2天)对比分析:")
        print("-" * 60)

        # 原策略表现（从之前的分析结果）
        original_stats = {
            'annual_return': 0.0457,  # 4.57%
            'sharpe_ratio': 2.20,
            'max_drawdown': -0.0197,  # -1.97%
            'total_trades': 7,
            'avg_return': 0.0503      # 5.03%
        }

        current_stats = self.performance_stats['strategy']

        print(f"参数对比:")
        print(f"   买入阈值: {self.buy_threshold*100:.0f}% vs 15% (原策略)")
        print(f"   卖出阈值: {self.sell_threshold*100:.0f}% vs 90% (原策略)")
        print(f"   回落天数: {self.pullback_days}天 vs 2天 (原策略)")

        print(f"\n表现对比:")
        print(f"   年化收益率: {current_stats['annual_return']*100:.2f}% vs {original_stats['annual_return']*100:.2f}% (原策略)")
        print(f"   夏普比率: {current_stats['sharpe_ratio']:.2f} vs {original_stats['sharpe_ratio']:.2f} (原策略)")
        print(f"   最大回撤: {current_stats['max_drawdown']*100:.2f}% vs {original_stats['max_drawdown']*100:.2f}% (原策略)")
        print(f"   交易次数: {current_stats['total_trades']}次 vs {original_stats['total_trades']}次 (原策略)")
        print(f"   平均单次收益: {current_stats['avg_return']*100:.2f}% vs {original_stats['avg_return']*100:.2f}% (原策略)")

        print(f"\n改进效果:")
        annual_improvement = current_stats['annual_return'] - original_stats['annual_return']
        sharpe_improvement = current_stats['sharpe_ratio'] - original_stats['sharpe_ratio']
        dd_improvement = original_stats['max_drawdown'] - current_stats['max_drawdown']

        print(f"   年化收益率改进: {annual_improvement*100:+.2f}%")
        print(f"   夏普比率改进: {sharpe_improvement:+.2f}")
        print(f"   回撤改进: {dd_improvement*100:+.2f}%")
        print(f"   交易次数变化: {current_stats['total_trades'] - original_stats['total_trades']:+d}次")

    def create_optimized_strategy_visualization(self):
        """创建优化策略的可视化分析"""
        print(f"\n📊 创建优化参数策略可视化分析...")

        fig, axes = plt.subplots(3, 2, figsize=(20, 18))
        fig.suptitle(f'511260优化参数策略回测分析 ({self.buy_threshold*100:.0f}%买入 + {self.sell_threshold*100:.0f}%分位数+{self.pullback_days}天回落卖出)',
                    fontsize=16, fontweight='bold')

        # 1. 价格走势与优化交易点
        ax1 = axes[0, 0]
        ax1.plot(self.processed_data.index, self.processed_data['价格'],
                linewidth=1, alpha=0.8, color='blue', label='511260价格')
        ax1.plot(self.processed_data.index, self.processed_data['趋势成分'],
                linewidth=2, color='red', label='长期趋势')

        # 标记所有交易点
        for trade in self.trades:
            ax1.scatter(trade['buy_date'], trade['buy_price'],
                       color='green', s=150, marker='^', alpha=0.9, zorder=5,
                       edgecolors='black', linewidth=2)
            ax1.scatter(trade['sell_date'], trade['sell_price'],
                       color='red', s=150, marker='v', alpha=0.9, zorder=5,
                       edgecolors='black', linewidth=2)

        # 标记当前持仓的买入点（如果有）
        if self.current_position:
            ax1.scatter(self.current_position['buy_date'], self.current_position['buy_price'],
                       color='blue', s=200, marker='^', alpha=0.9, zorder=6,
                       edgecolors='black', linewidth=3)

        # 添加图例
        ax1.scatter([], [], color='green', s=150, marker='^',
                   edgecolors='black', linewidth=2, label='优化买入点')
        ax1.scatter([], [], color='red', s=150, marker='v',
                   edgecolors='black', linewidth=2, label='优化卖出点')
        if self.current_position:
            ax1.scatter([], [], color='blue', s=200, marker='^',
                       edgecolors='black', linewidth=3, label='当前持仓')

        ax1.set_title('价格走势与优化参数交易点')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 百分位数与优化信号条件
        ax2 = axes[0, 1]
        ax2.plot(self.processed_data.index, self.processed_data['百分位数'],
                color='purple', label='百分位数', linewidth=1)

        # 添加阈值线
        ax2.axhline(y=self.buy_threshold, color='green', linestyle='--', alpha=0.7,
                   label=f'买入阈值({self.buy_threshold*100:.0f}%)')
        ax2.axhline(y=self.sell_threshold, color='red', linestyle=':', alpha=0.7,
                   label=f'卖出阈值({self.sell_threshold*100:.0f}%)')

        # 标记优化的买卖信号
        buy_signals = self.processed_data[self.processed_data['买入信号']]
        sell_signals = self.processed_data[self.processed_data['卖出信号']]

        if len(buy_signals) > 0:
            ax2.scatter(buy_signals.index, buy_signals['百分位数'],
                       color='green', s=30, alpha=0.6, label='优化买入信号')
        if len(sell_signals) > 0:
            ax2.scatter(sell_signals.index, sell_signals['百分位数'],
                       color='red', s=30, alpha=0.6, label='优化卖出信号')

        ax2.set_title('百分位数指标与优化信号条件')
        ax2.set_ylabel('百分位数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 累积收益对比
        ax3 = axes[1, 0]

        # 优化策略累积收益
        ax3.plot(self.cumulative_returns.index, (self.cumulative_returns - 1) * 100,
                linewidth=3, color='red', label='优化参数策略')

        # 买入持有基准
        buy_hold_returns = self.processed_data['价格'] / self.processed_data['价格'].iloc[0]
        ax3.plot(buy_hold_returns.index, (buy_hold_returns - 1) * 100,
                linewidth=2, color='blue', alpha=0.7, label='买入持有')

        # 标记重要时点
        strategy_final = (self.cumulative_returns.iloc[-1] - 1) * 100
        bh_final = (buy_hold_returns.iloc[-1] - 1) * 100

        ax3.text(0.02, 0.98, f'优化策略: {strategy_final:+.1f}%',
                transform=ax3.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='red', alpha=0.1))
        ax3.text(0.02, 0.90, f'买入持有: {bh_final:+.1f}%',
                transform=ax3.transAxes, verticalalignment='top',
                bbox=dict(boxstyle='round', facecolor='blue', alpha=0.1))

        ax3.set_title('累积收益对比')
        ax3.set_ylabel('累积收益率 (%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 回撤分析
        ax4 = axes[1, 1]

        # 计算买入持有回撤
        bh_rolling_max = buy_hold_returns.expanding().max()
        bh_drawdowns = (buy_hold_returns - bh_rolling_max) / bh_rolling_max

        # 策略回撤
        ax4.fill_between(self.drawdowns.index, self.drawdowns * 100, 0,
                        alpha=0.3, color='red', label='优化策略回撤')
        ax4.plot(self.drawdowns.index, self.drawdowns * 100,
                linewidth=2, color='red')

        # 买入持有回撤
        ax4.fill_between(bh_drawdowns.index, bh_drawdowns * 100, 0,
                        alpha=0.3, color='blue', label='买入持有回撤')
        ax4.plot(bh_drawdowns.index, bh_drawdowns * 100,
                linewidth=2, color='blue', alpha=0.7)

        # 标记最大回撤
        strategy_max_dd = self.drawdowns.min() * 100
        bh_max_dd = bh_drawdowns.min() * 100

        ax4.text(0.02, 0.02, f'优化策略最大回撤: {strategy_max_dd:.2f}%',
                transform=ax4.transAxes, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='red', alpha=0.1))
        ax4.text(0.02, 0.10, f'买入持有最大回撤: {bh_max_dd:.2f}%',
                transform=ax4.transAxes, verticalalignment='bottom',
                bbox=dict(boxstyle='round', facecolor='blue', alpha=0.1))

        ax4.set_title('回撤分析')
        ax4.set_ylabel('回撤 (%)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 交易时间线和收益分布
        ax5 = axes[2, 0]

        if self.trades:
            # 创建交易时间线
            for i, trade in enumerate(self.trades):
                # 绘制持仓期间的线段
                color = 'green' if trade['return'] > 0 else 'red'
                ax5.plot([trade['buy_date'], trade['sell_date']],
                        [i+1, i+1], linewidth=8, alpha=0.7, color=color)

                # 标记买入和卖出点
                ax5.scatter(trade['buy_date'], i+1, color='green', s=100,
                           marker='^', zorder=5, edgecolors='black')
                ax5.scatter(trade['sell_date'], i+1, color='red', s=100,
                           marker='v', zorder=5, edgecolors='black')

                # 添加收益标签
                mid_date = trade['buy_date'] + (trade['sell_date'] - trade['buy_date']) / 2
                ax5.text(mid_date, i+1.2, f"{trade['return']*100:+.1f}%",
                        ha='center', va='bottom', fontweight='bold', fontsize=10)

            ax5.set_ylim(0.5, len(self.trades) + 0.5)
            ax5.set_yticks(range(1, len(self.trades) + 1))
            ax5.set_yticklabels([f'第{i}笔' for i in range(1, len(self.trades) + 1)])
            ax5.set_title('优化策略交易时间线')
            ax5.set_ylabel('交易序号')
            ax5.grid(True, alpha=0.3)

        # 6. 策略表现对比雷达图
        ax6 = axes[2, 1]

        # 准备雷达图数据
        categories = ['年化收益率', '夏普比率', '回撤控制', '交易效率', '胜率']

        # 标准化指标（转换为0-1范围）
        strategy_stats = self.performance_stats['strategy']
        bh_stats = self.performance_stats['buy_hold']

        strategy_values = [
            strategy_stats['annual_return'] / 0.1,  # 年化收益率/10%
            strategy_stats['sharpe_ratio'] / 3,     # 夏普比率/3
            (1 + strategy_stats['max_drawdown']) / 1,  # 回撤控制（越小越好，转换为越大越好）
            min(strategy_stats['avg_return'] / 0.1, 1),  # 交易效率
            strategy_stats['win_rate']               # 胜率
        ]

        bh_values = [
            bh_stats['annual_return'] / 0.1,
            bh_stats['sharpe_ratio'] / 3,
            (1 + bh_stats['max_drawdown']) / 1,
            0.5,  # 买入持有没有交易效率概念，给中等值
            0.5   # 买入持有没有胜率概念，给中等值
        ]

        # 绘制雷达图
        angles = np.linspace(0, 2 * np.pi, len(categories), endpoint=False).tolist()
        strategy_values += strategy_values[:1]  # 闭合图形
        bh_values += bh_values[:1]
        angles += angles[:1]

        ax6.plot(angles, strategy_values, 'o-', linewidth=2, label='优化策略', color='red')
        ax6.fill(angles, strategy_values, alpha=0.25, color='red')
        ax6.plot(angles, bh_values, 'o-', linewidth=2, label='买入持有', color='blue')
        ax6.fill(angles, bh_values, alpha=0.25, color='blue')

        ax6.set_xticks(angles[:-1])
        ax6.set_xticklabels(categories)
        ax6.set_ylim(0, 1)
        ax6.set_title('策略表现对比雷达图')
        ax6.legend()
        ax6.grid(True)

        plt.tight_layout()

        # 保存图表
        chart_path = f'data/optimized_strategy_backtest_{self.buy_threshold*100:.0f}_{self.sell_threshold*100:.0f}_{self.pullback_days}day.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✅ 优化参数策略回测分析图表已保存: {chart_path}")

        plt.show()
        return chart_path

def main():
    """主函数"""
    print("🚀 511260优化参数策略回测分析")
    print("="*60)
    print("策略参数：5%分位数买入 + 95%分位数+1天回落卖出（无最大持有期限制）")
    print("="*60)

    # 创建分析器实例
    analyzer = OptimizedStrategyAnalyzer(
        buy_threshold=0.05,
        sell_threshold=0.95,
        pullback_days=1
    )

    # 加载和处理数据
    if not analyzer.load_and_process_data():
        return None

    # 执行优化策略
    if not analyzer.execute_optimized_strategy():
        return None

    # 计算表现指标
    if not analyzer.calculate_performance_metrics():
        return None

    # 打印表现总结
    analyzer.print_performance_summary()

    # 分析优化交易
    analyzer.analyze_optimized_trades()

    # 与原策略对比
    analyzer.compare_with_original_strategy()

    # 创建可视化
    analyzer.create_optimized_strategy_visualization()

    print("\n" + "="*60)
    print("✅ 优化参数策略回测分析完成！")
    print("="*60)

    return analyzer

if __name__ == "__main__":
    result = main()
