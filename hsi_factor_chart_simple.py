#!/usr/bin/env python3
"""
HSI股息率因子图表生成器 - 简化版
专门解决中文字体显示问题
"""

import pandas as pd
import numpy as np
import sqlite3
import matplotlib.pyplot as plt
import matplotlib
import os
import warnings

warnings.filterwarnings('ignore')

# 设置matplotlib后端
matplotlib.use('Agg')

# 设置中文字体 - 针对不同操作系统
def setup_chinese_font():
    """设置中文字体"""
    import platform
    import matplotlib.font_manager as fm

    # 禁用字体警告
    import logging
    logging.getLogger('matplotlib.font_manager').disabled = True

    system = platform.system()

    # 获取系统可用字体
    available_fonts = [f.name for f in fm.fontManager.ttflist]

    if system == "Darwin":  # macOS
        fonts = ['Arial Unicode MS', 'Hiragino Sans GB', 'PingFang SC', 'STHeiti']
    elif system == "Windows":
        fonts = ['Microsoft YaHei', 'SimHei', 'KaiTi', 'FangSong']
    else:  # Linux
        fonts = ['WenQuanYi Micro Hei', 'DejaVu Sans']

    # 找到第一个可用的中文字体
    chinese_font = None
    for font in fonts:
        if font in available_fonts:
            chinese_font = font
            break

    if chinese_font:
        plt.rcParams['font.sans-serif'] = [chinese_font, 'DejaVu Sans']
        plt.rcParams['axes.unicode_minus'] = False
        print(f"✅ 成功设置中文字体: {chinese_font}")
        return True
    else:
        # 使用英文字体
        plt.rcParams['font.family'] = 'DejaVu Sans'
        plt.rcParams['axes.unicode_minus'] = False
        print("⚠️ 未找到中文字体，将使用英文标签")
        return False

def load_factor_data():
    """加载股息率因子数据"""
    print("正在加载股息率因子数据...")
    
    conn = sqlite3.connect('hsi_factor_data.db')
    
    query = """
    SELECT stock_code, stock_name, date, close_price, dividend_yield
    FROM daily_dividend_yield
    WHERE dividend_yield IS NOT NULL 
    AND close_price IS NOT NULL
    AND date >= '2021-01-01'
    AND date <= '2024-12-31'
    ORDER BY date, stock_code
    """
    
    data = pd.read_sql_query(query, conn)
    conn.close()
    
    data['date'] = pd.to_datetime(data['date'])
    
    print(f"✅ 数据加载完成: {len(data)} 条记录")
    print(f"📊 股票数量: {data['stock_code'].nunique()}")
    print(f"📅 时间范围: {data['date'].min()} 到 {data['date'].max()}")
    
    return data

def calculate_ic_metrics(data, periods=[1, 5, 10, 20, 60, 120, 252]):
    """计算IC指标"""
    print("正在计算IC指标...")
    
    # 创建透视表
    price_data = data.pivot(index='date', columns='stock_code', values='close_price')
    factor_data = data.pivot(index='date', columns='stock_code', values='dividend_yield')
    
    # 数据清洗
    stock_coverage = factor_data.count() / len(factor_data)
    valid_stocks = stock_coverage[stock_coverage >= 0.3].index  # 至少30%覆盖率
    
    price_data = price_data[valid_stocks]
    factor_data = factor_data[valid_stocks]
    
    print(f"📊 有效股票数量: {len(valid_stocks)}")
    
    results = {}
    
    for period in periods:
        print(f"  计算 {period} 天期IC...")
        
        # 计算前向收益
        returns = price_data.pct_change(period).shift(-period)
        
        ic_series = []
        valid_dates = []
        
        for date in factor_data.index:
            if date in returns.index:
                factor_values = factor_data.loc[date].dropna()
                return_values = returns.loc[date].dropna()
                
                # 找到共同股票
                common_stocks = factor_values.index.intersection(return_values.index)
                if len(common_stocks) >= 5:  # 至少5只股票
                    factor_common = factor_values[common_stocks]
                    return_common = return_values[common_stocks]
                    
                    # 计算相关系数
                    ic = factor_common.corr(return_common)
                    if not np.isnan(ic):
                        ic_series.append(ic)
                        valid_dates.append(date)
        
        if ic_series:
            ic_mean = np.mean(ic_series)
            ic_std = np.std(ic_series)
            ic_ir = ic_mean / ic_std if ic_std > 0 else 0
            
            results[period] = {
                'IC_mean': ic_mean,
                'IC_std': ic_std,
                'IC_IR': ic_ir,
                'IC_series': ic_series,
                'dates': valid_dates,
                'sample_size': len(ic_series)
            }
    
    return results, factor_data

def calculate_quantile_performance(data, periods=[1, 5, 10, 20, 60, 120, 252], quantiles=5):
    """计算分组回测表现"""
    print("正在计算分组回测表现...")

    # 创建透视表
    price_data = data.pivot(index='date', columns='stock_code', values='close_price')
    factor_data = data.pivot(index='date', columns='stock_code', values='dividend_yield')

    # 数据清洗
    stock_coverage = factor_data.count() / len(factor_data)
    valid_stocks = stock_coverage[stock_coverage >= 0.3].index

    price_data = price_data[valid_stocks]
    factor_data = factor_data[valid_stocks]

    results = {}

    for period in periods:
        print(f"  计算 {period} 天期分组表现...")

        # 计算前向收益
        returns = price_data.pct_change(period).shift(-period)

        # 存储每个分组的收益
        quantile_returns = {f'Q{i+1}': [] for i in range(quantiles)}

        for date in factor_data.index:
            if date in returns.index:
                factor_values = factor_data.loc[date].dropna()
                return_values = returns.loc[date].dropna()

                # 找到共同股票
                common_stocks = factor_values.index.intersection(return_values.index)
                if len(common_stocks) >= quantiles * 2:  # 至少需要足够的股票进行分组
                    factor_common = factor_values[common_stocks]
                    return_common = return_values[common_stocks]

                    try:
                        # 按因子值分组 (股息率高的为Q1)
                        factor_quantiles = pd.qcut(factor_common, quantiles, labels=False, duplicates='drop')

                        # 计算各分组收益 (反转分组，使得股息率高的为Q1)
                        group_returns = {}
                        for q in range(quantiles):
                            mask = factor_quantiles == q
                            if mask.sum() > 0:
                                group_return = return_common[mask].mean()
                                if not np.isnan(group_return):
                                    # 反转分组编号，使得股息率最高的为Q1
                                    reversed_q = quantiles - q
                                    group_returns[f'Q{reversed_q}'] = group_return

                        # 如果所有分组都有数据，则记录
                        if len(group_returns) == quantiles:
                            for q_name, q_return in group_returns.items():
                                quantile_returns[q_name].append(q_return)
                    except:
                        continue

        # 计算各分组统计指标
        period_results = {}
        for q_name, q_returns in quantile_returns.items():
            if q_returns:
                q_returns_array = np.array(q_returns)
                # 计算年化收益和累计收益
                mean_return = np.mean(q_returns_array)
                annual_return = mean_return * (252 / period)  # 年化收益

                # 计算累计收益：基于年化收益和回测年数
                # 回测期间大约4年（2021-2024）
                backtest_years = len(q_returns_array) * period / 252

                # 使用年化收益计算总累计收益
                if annual_return > -0.99:
                    cumulative_return = (1 + annual_return) ** backtest_years - 1
                else:
                    cumulative_return = -0.99

                # 限制合理范围
                cumulative_return = max(-0.99, min(cumulative_return, 5.0))  # 最大500%收益

                period_results[q_name] = {
                    'mean_return': mean_return,
                    'annual_return': annual_return,
                    'std_return': np.std(q_returns_array),
                    'sharpe': mean_return / np.std(q_returns_array) if np.std(q_returns_array) > 0 else 0,
                    'count': len(q_returns_array),
                    'cumulative_return': cumulative_return,
                    'win_rate': np.sum(q_returns_array > 0) / len(q_returns_array),
                    'returns': q_returns_array
                }

        # 计算多空价差 (Q1高股息率 - Q5低股息率)
        if 'Q1' in period_results and f'Q{quantiles}' in period_results:
            long_short_returns = np.array(period_results['Q1']['returns']) - np.array(period_results[f'Q{quantiles}']['returns'])
            # 计算多空价差的统计指标
            ls_mean_return = np.mean(long_short_returns)
            ls_annual_return = ls_mean_return * (252 / period)

            # 计算多空价差累计收益
            backtest_years = len(long_short_returns) * period / 252

            if ls_annual_return > -0.99:
                ls_cumulative_return = (1 + ls_annual_return) ** backtest_years - 1
            else:
                ls_cumulative_return = -0.99

            # 限制合理范围
            ls_cumulative_return = max(-0.99, min(ls_cumulative_return, 5.0))

            period_results['Long_Short'] = {
                'mean_return': ls_mean_return,
                'annual_return': ls_annual_return,
                'std_return': np.std(long_short_returns),
                'sharpe': ls_mean_return / np.std(long_short_returns) if np.std(long_short_returns) > 0 else 0,
                'count': len(long_short_returns),
                'cumulative_return': ls_cumulative_return,
                'win_rate': np.sum(long_short_returns > 0) / len(long_short_returns),
                'returns': long_short_returns
            }

        results[period] = period_results

    return results

def calculate_cumulative_returns(quantile_results, periods=[20, 120, 252]):
    """计算累计收益曲线数据"""
    print("正在计算累计收益曲线...")

    cumulative_data = {}

    for period in periods:
        if period in quantile_results:
            period_data = quantile_results[period]
            groups = [g for g in period_data.keys() if g.startswith('Q')]
            groups.sort()

            cumulative_data[period] = {}

            for group in groups:
                if 'returns' in period_data[group]:
                    returns = period_data[group]['returns']

                    # 对于累计收益曲线，我们需要更保守的计算
                    # 限制单期收益在合理范围内
                    clipped_returns = np.clip(returns, -0.5, 1.0)  # 单期最大损失50%，最大收益100%

                    # 计算累计收益曲线
                    cumulative_returns = np.cumprod(1 + clipped_returns)

                    # 进一步限制累计收益范围
                    cumulative_returns = np.clip(cumulative_returns, 0.1, 10)  # 最大损失90%，最大收益1000%

                    cumulative_data[period][group] = cumulative_returns

            # 添加多空价差累计收益
            if 'Long_Short' in period_data and 'returns' in period_data['Long_Short']:
                ls_returns = period_data['Long_Short']['returns']

                # 同样限制多空价差的收益
                clipped_ls_returns = np.clip(ls_returns, -0.5, 1.0)
                ls_cumulative = np.cumprod(1 + clipped_ls_returns)
                ls_cumulative = np.clip(ls_cumulative, 0.1, 10)

                cumulative_data[period]['Long_Short'] = ls_cumulative

    return cumulative_data

def create_analysis_charts(ic_results, factor_data, use_chinese=True):
    """创建分析图表"""
    print("正在生成分析图表...")
    
    # 设置标签语言
    if use_chinese:
        labels = {
            'title': 'HSI股息率因子分析报告',
            'ic_mean': 'IC均值',
            'ic_ir': 'IC信息比率(IR)',
            'ic_timeseries': 'IC时间序列',
            'factor_dist': '股息率因子分布',
            'days': '天',
            'months': '个月',
            'year': '年',
            'mean': '均值',
            'median': '中位数',
            'frequency': '频次',
            'dividend_yield': '股息率 (%)'
        }
        period_labels = {
            1: '1天', 5: '5天', 10: '10天', 20: '20天',
            60: '2个月', 120: '4个月', 252: '1年'
        }
    else:
        labels = {
            'title': 'HSI Dividend Yield Factor Analysis',
            'ic_mean': 'IC Mean',
            'ic_ir': 'Information Ratio (IR)',
            'ic_timeseries': 'IC Time Series',
            'factor_dist': 'Dividend Yield Distribution',
            'days': 'Days',
            'months': 'Months',
            'year': 'Year',
            'mean': 'Mean',
            'median': 'Median',
            'frequency': 'Frequency',
            'dividend_yield': 'Dividend Yield (%)'
        }
        period_labels = {
            1: '1D', 5: '5D', 10: '10D', 20: '20D',
            60: '2M', 120: '4M', 252: '1Y'
        }
    
    # 创建图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(labels['title'], fontsize=18, fontweight='bold', y=0.95)
    
    # 准备数据
    periods = sorted(ic_results.keys())
    ic_means = [ic_results[p]['IC_mean'] for p in periods]
    ic_irs = [ic_results[p]['IC_IR'] for p in periods]
    period_names = [period_labels.get(p, f'{p}D') for p in periods]
    
    # 1. IC均值柱状图
    bars1 = axes[0, 0].bar(period_names, ic_means, color='steelblue', alpha=0.8, edgecolor='navy')
    axes[0, 0].set_title(labels['ic_mean'], fontsize=14, fontweight='bold')
    axes[0, 0].set_ylabel('IC')
    axes[0, 0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
    axes[0, 0].grid(True, alpha=0.3)
    axes[0, 0].tick_params(axis='x', rotation=45)
    
    # 添加数值标签
    for bar, value in zip(bars1, ic_means):
        height = bar.get_height()
        axes[0, 0].text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.3f}', ha='center', 
                       va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 2. IC信息比率
    bars2 = axes[0, 1].bar(period_names, ic_irs, color='orange', alpha=0.8, edgecolor='darkorange')
    axes[0, 1].set_title(labels['ic_ir'], fontsize=14, fontweight='bold')
    axes[0, 1].set_ylabel('IR')
    axes[0, 1].axhline(y=0, color='red', linestyle='--', alpha=0.7)
    axes[0, 1].grid(True, alpha=0.3)
    axes[0, 1].tick_params(axis='x', rotation=45)
    
    for bar, value in zip(bars2, ic_irs):
        height = bar.get_height()
        axes[0, 1].text(bar.get_x() + bar.get_width()/2., height,
                       f'{value:.3f}', ha='center', 
                       va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 3. IC时间序列（选择最长期间）
    longest_period = max(periods)
    if longest_period in ic_results:
        ic_series = ic_results[longest_period]['IC_series']
        dates = ic_results[longest_period]['dates']
        
        axes[1, 0].plot(dates, ic_series, color='green', alpha=0.8, linewidth=1.5)
        axes[1, 0].set_title(f'{period_labels[longest_period]} {labels["ic_timeseries"]}', 
                           fontsize=14, fontweight='bold')
        axes[1, 0].set_ylabel('IC')
        axes[1, 0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        axes[1, 0].grid(True, alpha=0.3)
        
        # 添加统计信息
        ic_mean = np.mean(ic_series)
        axes[1, 0].axhline(y=ic_mean, color='blue', linestyle='-', alpha=0.8,
                          label=f'{labels["mean"]}: {ic_mean:.3f}')
        axes[1, 0].legend()
    
    # 4. 因子分布
    factor_values = factor_data.values.flatten()
    factor_values = factor_values[~np.isnan(factor_values)]
    
    axes[1, 1].hist(factor_values, bins=40, color='purple',
                    alpha=0.7, edgecolor='black')
    axes[1, 1].set_title(labels['factor_dist'], fontsize=14, fontweight='bold')
    axes[1, 1].set_xlabel(labels['dividend_yield'])
    axes[1, 1].set_ylabel(labels['frequency'])
    
    # 添加统计线
    mean_val = np.mean(factor_values)
    median_val = np.median(factor_values)
    axes[1, 1].axvline(x=mean_val, color='red', linestyle='-', alpha=0.8,
                      label=f'{labels["mean"]}: {mean_val:.2f}%')
    axes[1, 1].axvline(x=median_val, color='orange', linestyle='--', alpha=0.8,
                      label=f'{labels["median"]}: {median_val:.2f}%')
    axes[1, 1].legend()
    axes[1, 1].grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    filename = 'hsi_dividend_yield_factor_analysis.png'
    output_path = os.path.abspath(filename)
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()
    
    print(f"✅ 图表已保存为: {output_path}")
    
    return output_path

def create_quantile_charts(quantile_results, use_chinese=True):
    """创建分组回测图表"""
    print("正在生成分组回测图表...")

    # 设置标签语言
    if use_chinese:
        labels = {
            'title': 'HSI股息率因子分组回测分析',
            'mean_return': '平均收益',
            'sharpe_ratio': '夏普比率',
            'cumulative_return': '累计收益曲线',
            'win_rate': '胜率',
            'long_short': '多空价差',
            'high_dividend': '高股息率(Q1)',
            'low_dividend': '低股息率(Q5)'
        }
        period_labels = {
            1: '1天', 5: '5天', 10: '10天', 20: '20天',
            60: '2个月', 120: '4个月', 252: '1年'
        }
    else:
        labels = {
            'title': 'HSI Dividend Yield Factor Quantile Analysis',
            'mean_return': 'Mean Return',
            'sharpe_ratio': 'Sharpe Ratio',
            'cumulative_return': 'Cumulative Return Curves',
            'win_rate': 'Win Rate',
            'long_short': 'Long-Short',
            'high_dividend': 'High Dividend(Q1)',
            'low_dividend': 'Low Dividend(Q5)'
        }
        period_labels = {
            1: '1D', 5: '5D', 10: '10D', 20: '20D',
            60: '2M', 120: '4M', 252: '1Y'
        }

    # 计算累计收益曲线数据
    cumulative_data = calculate_cumulative_returns(quantile_results)

    # 创建图表 (3行2列)
    fig, axes = plt.subplots(3, 2, figsize=(16, 18))
    fig.suptitle(labels['title'], fontsize=18, fontweight='bold', y=0.98)

    # 获取可用期间
    periods = sorted(quantile_results.keys())

    # 1. 平均收益对比（选择几个代表性期间）
    selected_periods = [1, 20, 120, 252] if 252 in periods else [1, 20, 60]
    selected_periods = [p for p in selected_periods if p in periods][:2]  # 只取前2个

    # 1. 平均收益对比（第一行）
    for i, period in enumerate(selected_periods):
        if i >= 2:
            break
        period_data = quantile_results[period]
        groups = [g for g in period_data.keys() if g.startswith('Q')]
        groups.sort()

        if groups:
            mean_returns = [period_data[g]['mean_return'] * 100 for g in groups]  # 转换为百分比

            # 设置颜色：Q1(高股息率)为绿色，Q5(低股息率)为红色
            colors = ['darkgreen', 'green', 'orange', 'red', 'darkred'][:len(groups)]

            bars = axes[0, i].bar(groups, mean_returns, color=colors, alpha=0.7)

            period_label = period_labels.get(period, f'{period}D')
            axes[0, i].set_title(f'{period_label} {labels["mean_return"]}', fontsize=12, fontweight='bold')
            axes[0, i].set_ylabel('收益率 (%)' if use_chinese else 'Return (%)')
            axes[0, i].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            axes[0, i].grid(True, alpha=0.3)

            # 添加数值标签
            for bar, value in zip(bars, mean_returns):
                height = bar.get_height()
                axes[0, i].text(bar.get_x() + bar.get_width()/2., height,
                               f'{value:.2f}%', ha='center',
                               va='bottom' if height >= 0 else 'top', fontweight='bold')

    # 2. 夏普比率对比（第二行）
    for i, period in enumerate(selected_periods):
        if i >= 2:
            break
        period_data = quantile_results[period]
        groups = [g for g in period_data.keys() if g.startswith('Q')]
        groups.sort()

        if groups:
            sharpe_ratios = [period_data[g]['sharpe'] for g in groups]
            colors = ['darkgreen', 'green', 'orange', 'red', 'darkred'][:len(groups)]

            bars = axes[1, i].bar(groups, sharpe_ratios, color=colors, alpha=0.7)

            period_label = period_labels.get(period, f'{period}D')
            axes[1, i].set_title(f'{period_label} {labels["sharpe_ratio"]}', fontsize=12, fontweight='bold')
            axes[1, i].set_ylabel('夏普比率' if use_chinese else 'Sharpe Ratio')
            axes[1, i].axhline(y=0, color='black', linestyle='-', alpha=0.3)
            axes[1, i].grid(True, alpha=0.3)

            # 添加数值标签
            for bar, value in zip(bars, sharpe_ratios):
                height = bar.get_height()
                axes[1, i].text(bar.get_x() + bar.get_width()/2., height,
                               f'{value:.2f}', ha='center',
                               va='bottom' if height >= 0 else 'top', fontweight='bold')

    # 3. 累计收益曲线（第三行）
    curve_periods = [20, 252] if 252 in cumulative_data else [20, 120]
    curve_periods = [p for p in curve_periods if p in cumulative_data][:2]

    for i, period in enumerate(curve_periods):
        if i >= 2:
            break

        period_data = cumulative_data[period]
        period_label = period_labels.get(period, f'{period}D')

        # 绘制各分组累计收益曲线
        groups = [g for g in period_data.keys() if g.startswith('Q')]
        groups.sort()

        colors = ['darkgreen', 'green', 'orange', 'red', 'darkred']

        for j, group in enumerate(groups):
            if group in period_data:
                cumulative_returns = period_data[group]
                axes[2, i].plot(range(len(cumulative_returns)), cumulative_returns,
                               color=colors[j], label=f'{group}', linewidth=2, alpha=0.8)

        # 绘制多空价差曲线
        if 'Long_Short' in period_data:
            ls_cumulative = period_data['Long_Short']
            axes[2, i].plot(range(len(ls_cumulative)), ls_cumulative,
                           color='black', label=labels['long_short'], linewidth=3, linestyle='--')

        axes[2, i].set_title(f'{period_label} {labels["cumulative_return"]}', fontsize=12, fontweight='bold')
        axes[2, i].set_ylabel('累计收益' if use_chinese else 'Cumulative Return')
        axes[2, i].set_xlabel('时间' if use_chinese else 'Time')
        axes[2, i].grid(True, alpha=0.3)
        axes[2, i].legend(fontsize=10)
        axes[2, i].set_yscale('log')  # 使用对数坐标

    plt.tight_layout()

    # 保存图表
    filename = 'hsi_dividend_yield_quantile_analysis.png'
    output_path = os.path.abspath(filename)
    plt.savefig(output_path, dpi=300, bbox_inches='tight', facecolor='white')
    plt.close()

    print(f"✅ 分组回测图表已保存为: {output_path}")

    return output_path

def print_quantile_summary(quantile_results):
    """打印分组回测摘要"""
    print("\n" + "="*80)
    print("📊 HSI股息率因子分组回测摘要 (Q1=高股息率, Q5=低股息率)")
    print("="*80)

    period_labels = {
        1: '1天', 5: '5天', 10: '10天', 20: '20天',
        60: '2个月', 120: '4个月', 252: '1年'
    }

    group_labels = {
        'Q1': 'Q1(高股息)',
        'Q2': 'Q2',
        'Q3': 'Q3',
        'Q4': 'Q4',
        'Q5': 'Q5(低股息)'
    }

    for period in sorted(quantile_results.keys()):
        period_data = quantile_results[period]
        period_label = period_labels.get(period, f'{period}天')

        print(f"\n{period_label} 分组回测结果:")
        print(f"{'分组':<12} {'平均收益':<12} {'年化收益':<12} {'夏普比率':<10} {'胜率':<8} {'累计收益':<12} {'样本数':<8}")
        print("-" * 90)

        # 显示各分组结果
        groups = [g for g in period_data.keys() if g.startswith('Q')]
        groups.sort()

        for group in groups:
            metrics = period_data[group]
            group_label = group_labels.get(group, group)
            annual_return = metrics.get('annual_return', metrics['mean_return'] * 252)
            print(f"{group_label:<12} {metrics['mean_return']*100:<12.2f}% {annual_return*100:<12.1f}% {metrics['sharpe']:<10.2f} "
                  f"{metrics['win_rate']*100:<8.1f}% {metrics['cumulative_return']*100:<12.1f}% {metrics['count']:<8}")

        # 显示多空价差
        if 'Long_Short' in period_data:
            ls_metrics = period_data['Long_Short']
            ls_annual_return = ls_metrics.get('annual_return', ls_metrics['mean_return'] * 252)
            print(f"{'Q1-Q5价差':<12} {ls_metrics['mean_return']*100:<12.2f}% {ls_annual_return*100:<12.1f}% {ls_metrics['sharpe']:<10.2f} "
                  f"{ls_metrics['win_rate']*100:<8.1f}% {ls_metrics['cumulative_return']*100:<12.1f}% {ls_metrics['count']:<8}")

def print_summary(ic_results):
    """打印分析摘要"""
    print("\n" + "="*60)
    print("📊 HSI股息率因子分析摘要")
    print("="*60)
    
    period_labels = {
        1: '1天', 5: '5天', 10: '10天', 20: '20天',
        60: '2个月', 120: '4个月', 252: '1年'
    }
    
    print(f"{'持有期':<8} {'IC均值':<10} {'IC标准差':<10} {'信息比率':<10} {'样本数':<8}")
    print("-" * 55)
    
    for period in sorted(ic_results.keys()):
        metrics = ic_results[period]
        period_label = period_labels.get(period, f'{period}天')
        print(f"{period_label:<8} {metrics['IC_mean']:<10.4f} {metrics['IC_std']:<10.4f} "
              f"{metrics['IC_IR']:<10.4f} {metrics['sample_size']:<8}")
    
    # 找出最佳表现
    best_ic_period = max(ic_results.keys(), key=lambda x: abs(ic_results[x]['IC_mean']))
    best_ir_period = max(ic_results.keys(), key=lambda x: abs(ic_results[x]['IC_IR']))
    
    print(f"\n🏆 最佳IC表现: {period_labels[best_ic_period]} (IC={ic_results[best_ic_period]['IC_mean']:.4f})")
    print(f"🏆 最佳IR表现: {period_labels[best_ir_period]} (IR={ic_results[best_ir_period]['IC_IR']:.4f})")

def main():
    """主函数"""
    print("🎨 开始生成HSI股息率因子分析图表")
    
    try:
        # 设置中文字体
        use_chinese = setup_chinese_font()
        
        # 加载数据
        data = load_factor_data()
        
        # 计算IC指标
        ic_results, factor_data = calculate_ic_metrics(data)

        # 计算分组回测
        quantile_results = calculate_quantile_performance(data)

        # 生成IC分析图表
        ic_chart_path = create_analysis_charts(ic_results, factor_data, use_chinese)

        # 生成分组回测图表
        quantile_chart_path = create_quantile_charts(quantile_results, use_chinese)

        # 打印摘要
        print_summary(ic_results)
        print_quantile_summary(quantile_results)

        print(f"\n✅ 分析完成！")
        print(f"📊 IC分析图表: {ic_chart_path}")
        print(f"📈 分组回测图表: {quantile_chart_path}")
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
