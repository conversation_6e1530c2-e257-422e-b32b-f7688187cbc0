#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
Script to generate sample data for Hangzhou city-level public institution recruitment announcements.
This is a fallback solution when web scraping is not possible.
"""

import os
import logging
from datetime import datetime, timedelta
import pandas as pd
import random

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hangzhou_hrss_sample.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger("hangzhou_hrss_sample")

# 配置
OUTPUT_DIR = "hangzhou_hrss_data"
OUTPUT_FILE = os.path.join(OUTPUT_DIR, "杭州市市属事业单位招聘公告.txt")
CSV_FILE = os.path.join(OUTPUT_DIR, "杭州市市属事业单位招聘公告.csv")
YEARS_TO_GENERATE = 3  # 生成最近3年的数据

def generate_sample_announcements():
    """生成示例招聘公告数据"""
    announcements = []
    
    # 生成最近3年的日期范围
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365 * YEARS_TO_GENERATE)
    
    # 生成不同年份的公告
    current_date = end_date
    
    # 2025年公告
    announcements.extend([
        {
            "title": "2025年杭州市市属事业单位公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2025/4/15/art_1229782005_59123456.html",
            "publish_date": "2025-04-15"
        },
        {
            "title": "杭州市卫生健康委员会所属事业单位2025年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2025/3/20/art_1229782005_59122222.html",
            "publish_date": "2025-03-20"
        },
        {
            "title": "杭州市教育局所属事业单位2025年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2025/2/25/art_1229782005_59121111.html",
            "publish_date": "2025-02-25"
        }
    ])
    
    # 2024年公告
    announcements.extend([
        {
            "title": "2024年杭州市市属事业单位公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2024/4/10/art_1229782005_58123456.html",
            "publish_date": "2024-04-10"
        },
        {
            "title": "杭州市文化广电旅游局所属事业单位2024年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2024/3/15/art_1229782005_58122222.html",
            "publish_date": "2024-03-15"
        },
        {
            "title": "杭州市科学技术局所属事业单位2024年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2024/2/20/art_1229782005_58121111.html",
            "publish_date": "2024-02-20"
        },
        {
            "title": "杭州市市场监督管理局所属事业单位2024年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2024/1/15/art_1229782005_58120000.html",
            "publish_date": "2024-01-15"
        }
    ])
    
    # 2023年公告
    announcements.extend([
        {
            "title": "2023年杭州市市属事业单位公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2023/4/12/art_1229782005_57123456.html",
            "publish_date": "2023-04-12"
        },
        {
            "title": "杭州市财政局所属事业单位2023年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2023/3/18/art_1229782005_57122222.html",
            "publish_date": "2023-03-18"
        },
        {
            "title": "杭州市城市管理局所属事业单位2023年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2023/2/22/art_1229782005_57121111.html",
            "publish_date": "2023-02-22"
        },
        {
            "title": "杭州市交通运输局所属事业单位2023年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2023/1/20/art_1229782005_57120000.html",
            "publish_date": "2023-01-20"
        },
        {
            "title": "杭州市人力资源和社会保障局所属事业单位2023年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2023/1/10/art_1229782005_57119999.html",
            "publish_date": "2023-01-10"
        }
    ])
    
    # 2022年公告
    announcements.extend([
        {
            "title": "2022年杭州市市属事业单位公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2022/4/15/art_1229782005_56123456.html",
            "publish_date": "2022-04-15"
        },
        {
            "title": "杭州市生态环境局所属事业单位2022年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2022/3/20/art_1229782005_56122222.html",
            "publish_date": "2022-03-20"
        },
        {
            "title": "杭州市水利局所属事业单位2022年公开招聘工作人员公告",
            "url": "https://hrss.hangzhou.gov.cn/art/2022/2/25/art_1229782005_56121111.html",
            "publish_date": "2022-02-25"
        }
    ])
    
    return announcements

def format_announcements(announcements):
    """格式化招聘公告为易读的文本"""
    if not announcements:
        return "未找到符合条件的招聘公告"
    
    # 按年份分组
    year_announcements = {}
    for announcement in announcements:
        publish_date = announcement.get('publish_date', '')
        if publish_date:
            year = publish_date.split('-')[0] if '-' in publish_date else publish_date[:4]
            if year not in year_announcements:
                year_announcements[year] = []
            year_announcements[year].append(announcement)
    
    # 格式化文本
    text = f"杭州市市属事业单位招聘公告（最近{YEARS_TO_GENERATE}年）\n"
    text += f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
    text += f"总共找到 {len(announcements)} 个招聘公告\n"
    text += "=" * 80 + "\n\n"
    
    # 按年份输出（从新到旧）
    for year in sorted(year_announcements.keys(), reverse=True):
        text += f"【{year}年】 - 共 {len(year_announcements[year])} 个招聘公告\n"
        text += "-" * 80 + "\n\n"
        
        # 按日期排序（最新的在前）
        sorted_announcements = sorted(year_announcements[year], 
                                     key=lambda x: x.get('publish_date', ''), 
                                     reverse=True)
        
        # 输出每个招聘公告
        for announcement in sorted_announcements:
            text += f"标题: {announcement['title']}\n"
            text += f"发布日期: {announcement['publish_date']}\n"
            text += f"链接: {announcement['url']}\n"
            text += "\n" + "-" * 40 + "\n\n"
        
        text += "\n"
    
    return text

def save_to_csv(announcements, file_path):
    """保存招聘公告到CSV文件"""
    try:
        df = pd.DataFrame(announcements)
        df.to_csv(file_path, index=False, encoding='utf-8-sig')
        logger.info(f"Saved {len(announcements)} announcements to {file_path}")
        return True
    except Exception as e:
        logger.error(f"Error saving to CSV: {e}")
        return False

def main():
    """主函数"""
    logger.info("Starting sample data generation")
    
    # 创建输出目录
    os.makedirs(OUTPUT_DIR, exist_ok=True)
    
    # 生成示例数据
    announcements = generate_sample_announcements()
    
    logger.info(f"Total sample announcements generated: {len(announcements)}")
    
    # 格式化招聘公告
    formatted_text = format_announcements(announcements)
    
    # 保存到文件
    with open(OUTPUT_FILE, 'w', encoding='utf-8') as f:
        f.write(formatted_text)
    
    logger.info(f"Sample announcements saved to {OUTPUT_FILE}")
    
    # 保存到CSV
    save_to_csv(announcements, CSV_FILE)
    
    # 打印结果
    print(f"\n生成了 {len(announcements)} 个杭州市市属事业单位招聘公告示例")
    print(f"招聘公告已保存到: {OUTPUT_FILE}")
    print(f"CSV文件已保存到: {CSV_FILE}")
    print("\n注意：这些是示例数据，不是真实的招聘公告。")
    print("由于网站结构或访问限制，无法直接爬取真实数据。")
    print("您可以使用这些示例数据来了解输出格式，并在需要时手动访问网站获取真实信息。")
    
    # 显示部分示例数据
    print("\n部分招聘公告示例:")
    print("-" * 80)
    
    # 按日期排序（最新的在前）
    sorted_announcements = sorted(announcements, 
                                 key=lambda x: x.get('publish_date', ''), 
                                 reverse=True)
    
    for i, announcement in enumerate(sorted_announcements[:5], 1):
        print(f"{i}. 标题: {announcement['title']}")
        print(f"   发布日期: {announcement['publish_date']}")
        print(f"   链接: {announcement['url']}")
        print("-" * 80)
    
    return 0

if __name__ == "__main__":
    main()
