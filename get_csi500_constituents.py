"""
获取中证500指数成分股列表

This script retrieves the list of constituent stocks for the CSI 500 Index (中证500)
using the AKShare library and saves it to a CSV file.
"""

import akshare as ak
import pandas as pd
import os
from datetime import datetime

def get_csi500_constituents():
    """
    获取中证500指数成分股列表
    
    Returns:
        pandas.DataFrame: 中证500成分股数据，包含股票代码、名称和权重等信息
    """
    print("正在获取中证500指数成分股列表...")
    
    try:
        # 使用AKShare的index_stock_cons_weight_csindex函数获取中证500成分股
        # symbol="000905"是中证500指数的代码
        csi500_stocks = ak.index_stock_cons_weight_csindex(symbol="000905")
        
        print(f"成功获取到{len(csi500_stocks)}只中证500成分股")
        return csi500_stocks
    except Exception as e:
        print(f"获取中证500成分股失败: {str(e)}")
        return None

def save_to_csv(df, output_dir="data"):
    """
    将数据保存为CSV文件
    
    Args:
        df: 要保存的DataFrame
        output_dir: 输出目录
    
    Returns:
        str: 保存的文件路径
    """
    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    # 生成文件名，包含当前日期
    current_date = datetime.now().strftime("%Y%m%d")
    filename = f"csi500_constituents_{current_date}.csv"
    file_path = os.path.join(output_dir, filename)
    
    # 保存为CSV
    df.to_csv(file_path, index=False, encoding="utf-8-sig")
    print(f"中证500成分股列表已保存至: {file_path}")
    
    return file_path

def main():
    # 获取中证500成分股
    csi500_stocks = get_csi500_constituents()
    
    if csi500_stocks is not None and not csi500_stocks.empty:
        # 显示前10只成分股
        print("\n中证500指数前10只成分股:")
        print(csi500_stocks.head(10))
        
        # 保存到CSV文件
        save_to_csv(csi500_stocks)
    else:
        print("未能获取中证500成分股数据")

if __name__ == "__main__":
    main()
