"""
获取沪深300成分股的季度财务数据

This script retrieves quarterly financial data (revenue and profit) for CSI 300 constituent stocks
over the past three years using AKShare.
"""

import akshare as ak
import pandas as pd
import os
import time
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_financial_data.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_financial_data"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def get_stock_financial_data(stock_code, stock_name):
    """
    获取单个股票的季度财务数据

    Args:
        stock_code: 股票代码
        stock_name: 股票名称

    Returns:
        pandas.DataFrame: 包含季度财务数据的DataFrame，如果获取失败则返回None
    """
    try:
        # 对股票代码进行处理，确保格式正确
        # 上交所股票代码前需要加上'sh'，深交所股票代码前需要加上'sz'
        if stock_code.startswith('6') or stock_code.startswith('688'):
            formatted_code = f"sh{stock_code}"
        else:
            formatted_code = f"sz{stock_code}"

        logger.info(f"获取 {stock_code} ({stock_name}) 的财务数据...")

        # 使用AKShare获取财务报表数据
        try:
            # 获取利润表数据 - 使用正确的参数
            income_df = ak.stock_financial_report_sina(symbol=formatted_code)

            # 如果数据为空，尝试使用另一种方式获取
            if income_df is None or income_df.empty:
                logger.warning(f"{stock_code} ({stock_name}) 使用新浪接口获取利润表数据失败，尝试使用东方财富接口...")
                income_df = ak.stock_financial_abstract(symbol=stock_code)
        except Exception as e:
            logger.warning(f"{stock_code} ({stock_name}) 使用新浪接口获取数据失败: {str(e)}，尝试使用其他接口...")
            try:
                # 尝试使用其他AKShare接口
                income_df = ak.stock_financial_abstract(symbol=stock_code)
            except Exception as e2:
                logger.warning(f"{stock_code} ({stock_name}) 使用东方财富接口获取数据也失败: {str(e2)}")
                income_df = None

        # 如果仍然为空，记录错误并返回None
        if income_df is None or income_df.empty:
            logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
            return None

        # 处理数据，提取营收和利润信息
        # 根据实际返回的数据结构进行调整
        try:
            # 检查数据结构并适应不同的格式
            if income_df is None or income_df.empty:
                logger.error(f"{stock_code} ({stock_name}) 获取的数据为空")
                return None

            # 打印列名以便调试
            logger.info(f"{stock_code} ({stock_name}) 数据列名: {income_df.columns.tolist()}")

            # 处理新浪财经API返回的数据格式
            if '报告期' in income_df.columns and '项目' in income_df.columns:
                # 转换日期格式
                income_df['报告期'] = pd.to_datetime(income_df['报告期'], errors='coerce')

                # 过滤最近3年的数据
                three_years_ago = datetime.now() - timedelta(days=365*3)
                income_df = income_df[income_df['报告期'] >= three_years_ago]

                if income_df.empty:
                    logger.warning(f"{stock_code} ({stock_name}) 过滤后没有最近3年的数据")
                    return None

                # 提取营收和净利润数据
                # 根据实际数据结构调整列名
                revenue_data = income_df[income_df['项目'] == '营业收入']
                profit_data = income_df[income_df['项目'] == '净利润']

                if revenue_data.empty or profit_data.empty:
                    # 尝试其他可能的列名
                    revenue_data = income_df[income_df['项目'].str.contains('营业收入|营业总收入', na=False)]
                    profit_data = income_df[income_df['项目'].str.contains('净利润|归属于母公司所有者的净利润', na=False)]

                if revenue_data.empty or profit_data.empty:
                    logger.error(f"{stock_code} ({stock_name}) 未找到营收或净利润数据")
                    return None

                # 合并数据
                result_df = pd.DataFrame()
                result_df['报告期'] = revenue_data['报告期'].values
                result_df['股票代码'] = stock_code
                result_df['股票名称'] = stock_name
                result_df['营业收入'] = revenue_data['值'].values
                result_df['净利润'] = profit_data['值'].values

            # 处理东方财富API返回的数据格式
            elif '日期' in income_df.columns:
                # 转换日期格式
                income_df['日期'] = pd.to_datetime(income_df['日期'], errors='coerce')

                # 过滤最近3年的数据
                three_years_ago = datetime.now() - timedelta(days=365*3)
                income_df = income_df[income_df['日期'] >= three_years_ago]

                if income_df.empty:
                    logger.warning(f"{stock_code} ({stock_name}) 过滤后没有最近3年的数据")
                    return None

                # 创建结果DataFrame
                result_df = pd.DataFrame()
                result_df['报告期'] = income_df['日期']
                result_df['股票代码'] = stock_code
                result_df['股票名称'] = stock_name

                # 尝试找到营收和净利润列
                revenue_col = None
                profit_col = None

                for col in income_df.columns:
                    if '营业收入' in col or '营业总收入' in col:
                        revenue_col = col
                    elif '净利润' in col or '归属于母公司' in col:
                        profit_col = col

                if revenue_col and profit_col:
                    result_df['营业收入'] = income_df[revenue_col]
                    result_df['净利润'] = income_df[profit_col]
                else:
                    logger.error(f"{stock_code} ({stock_name}) 未找到营收或净利润列")
                    return None

            else:
                logger.error(f"{stock_code} ({stock_name}) 数据结构不符合预期")
                return None

            # 确保数值列是数值类型
            for col in ['营业收入', '净利润']:
                if col in result_df.columns:
                    result_df[col] = pd.to_numeric(result_df[col], errors='coerce')

            # 计算同比增长率（如果有足够的历史数据）
            if len(result_df) > 4:
                result_df['营业收入同比增长率'] = result_df['营业收入'].pct_change(4) * 100
                result_df['净利润同比增长率'] = result_df['净利润'].pct_change(4) * 100

            # 排序并返回
            result_df = result_df.sort_values('报告期', ascending=False)
            return result_df

        except Exception as e:
            logger.error(f"{stock_code} ({stock_name}) 处理财务数据时出错: {str(e)}")
            return None

    except Exception as e:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据时出错: {str(e)}")
        return None

def process_stock(stock_info):
    """
    处理单个股票的财务数据并保存

    Args:
        stock_info: 包含股票代码和名称的元组 (code, name)

    Returns:
        tuple: (股票代码, 成功/失败)
    """
    stock_code, stock_name = stock_info

    # 检查是否已经处理过
    output_file = os.path.join(OUTPUT_DIR, f"{stock_code}_financial_data.csv")
    if os.path.exists(output_file):
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已存在，跳过处理")
        return stock_code, True

    # 获取财务数据
    df = get_stock_financial_data(stock_code, stock_name)

    if df is not None and not df.empty:
        # 保存到CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已保存到 {output_file}")
        return stock_code, True
    else:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
        return stock_code, False

def main():
    # 读取沪深300成分股列表
    try:
        # 查找最新的格式化文件
        data_dir = "data"
        files = [f for f in os.listdir(data_dir) if f.startswith("csi300_constituents_formatted_") and f.endswith(".csv")]
        if not files:
            logger.error("未找到沪深300成分股数据文件")
            return

        # 按文件名排序，获取最新的文件
        files.sort(reverse=True)
        input_file = os.path.join(data_dir, files[0])

        # 读取CSV文件
        df = pd.read_csv(input_file, encoding="utf-8-sig")
        logger.info(f"从 {input_file} 读取了 {len(df)} 只沪深300成分股")

        # 准备股票列表
        # 确保股票代码为6位，不足6位的用0补齐
        stock_list = list(zip(df["成分券代码"].astype(str).str.zfill(6), df["成分券名称"]))

        # 使用线程池并行处理
        success_count = 0
        fail_count = 0

        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = {executor.submit(process_stock, stock_info): stock_info for stock_info in stock_list}

            # 使用tqdm显示进度
            for future in tqdm(as_completed(futures), total=len(futures), desc="处理进度"):
                stock_info = futures[future]
                try:
                    stock_code, success = future.result()
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    logger.error(f"处理 {stock_info[0]} ({stock_info[1]}) 时发生异常: {str(e)}")
                    fail_count += 1

                # 添加延迟以避免请求过于频繁
                time.sleep(0.5)

        logger.info(f"处理完成: 成功 {success_count} 只, 失败 {fail_count} 只")

        # 创建汇总文件
        create_summary_file()

    except Exception as e:
        logger.error(f"处理沪深300成分股财务数据时出错: {str(e)}")

def create_summary_file():
    """创建汇总文件，包含所有公司的关键财务指标"""
    try:
        # 获取所有已处理的文件
        all_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith("_financial_data.csv")]

        if not all_files:
            logger.warning("没有找到已处理的财务数据文件，无法创建汇总")
            return

        # 读取并合并所有数据
        all_data = []
        for file in all_files:
            try:
                file_path = os.path.join(OUTPUT_DIR, file)
                df = pd.read_csv(file_path, encoding='utf-8-sig')

                # 只保留最新一期的数据
                if not df.empty:
                    latest_data = df.iloc[0].to_dict()
                    all_data.append(latest_data)
            except Exception as e:
                logger.error(f"读取文件 {file} 时出错: {str(e)}")

        if all_data:
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(all_data)

            # 保存汇总文件
            summary_file = os.path.join(OUTPUT_DIR, "csi300_financial_summary.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            logger.info(f"汇总文件已保存到 {summary_file}")
        else:
            logger.warning("没有有效的财务数据，无法创建汇总")

    except Exception as e:
        logger.error(f"创建汇总文件时出错: {str(e)}")

if __name__ == "__main__":
    main()
