#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股分红数据批量测试

先测试少量股票，验证批量爬取流程
"""

import urllib.request
import urllib.parse
import json
import re
import csv
import time
from datetime import datetime

class HSIDividendBatchTest:
    """恒生指数分红数据批量测试器"""
    
    def __init__(self):
        self.base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        self.results = []
        
        print("🚀 恒生指数分红数据批量测试器已初始化")
    
    def parse_dividend_amount(self, plan_explain):
        """解析分红金额（改进版）"""
        if not plan_explain or plan_explain == "未派发或宣派股息":
            return 0.0

        plan_text = str(plan_explain)

        # 优先查找港币金额（包括括号内的港币等值）
        hkd_patterns = [
            r'港币(\d+\.?\d*)元',
            r'相当于港币(\d+\.?\d+)元',
            r'港币(\d+\.?\d+)',
            r'每股派港币(\d+\.?\d*)元',
            r'派港币(\d+\.?\d*)元',
            r'派息港币(\d+\.?\d*)元',
        ]

        for pattern in hkd_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue

        # 其他格式
        other_patterns = [
            r'每股派(\d+\.?\d*)港币',
            r'派(\d+\.?\d*)港币',
            r'每股(\d+\.?\d*)港币',
            r'每股派(\d+\.?\d*)元',
            r'派(\d+\.?\d*)元',
            r'每股(\d+\.?\d*)仙',
            r'派(\d+\.?\d*)仙',
            r'(\d+\.?\d*)港币',
        ]

        for pattern in other_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    amount = float(match.group(1))
                    if '仙' in pattern:
                        amount = amount / 100
                    return amount
                except:
                    continue

        # 特殊处理：从括号中提取港币等值金额
        bracket_pattern = r'\(相当于港币(\d+\.?\d+)元'
        match = re.search(bracket_pattern, plan_text)
        if match:
            try:
                return float(match.group(1))
            except:
                pass

        return 0.0
    
    def fetch_stock_dividend(self, stock_code, stock_name):
        """获取单只股票的分红数据"""
        try:
            print(f"📊 处理 {stock_code} ({stock_name})...")
            
            # API参数
            params = {
                'reportName': 'RPT_HKF10_MAIN_DIVBASIC',
                'columns': 'SECURITY_CODE,UPDATE_DATE,REPORT_TYPE,EX_DIVIDEND_DATE,DIVIDEND_DATE,TRANSFER_END_DATE,YEAR,PLAN_EXPLAIN,IS_BFP',
                'quoteColumns': '',
                'filter': f'(SECURITY_CODE="{stock_code}")',
                'pageNumber': 1,
                'pageSize': 20,
                'sortTypes': '-1,-1',
                'sortColumns': 'NOTICE_DATE,EX_DIVIDEND_DATE',
                'source': 'F10',
                'client': 'PC',
                'v': str(int(time.time() * 1000))
            }
            
            # 构建URL
            query_string = urllib.parse.urlencode(params)
            full_url = f"{self.base_url}?{query_string}"
            
            # 发送请求
            req = urllib.request.Request(full_url, headers=self.headers)
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if 'result' in data and 'data' in data['result']:
                        raw_records = data['result']['data']
                        
                        # 解析有效分红记录
                        valid_dividends = []
                        for record in raw_records:
                            plan_explain = record.get('PLAN_EXPLAIN', '')
                            amount = self.parse_dividend_amount(plan_explain)
                            
                            if amount > 0:
                                valid_dividends.append({
                                    'year': record.get('YEAR'),
                                    'amount': amount,
                                    'ex_date': record.get('EX_DIVIDEND_DATE'),
                                    'plan': plan_explain
                                })
                        
                        # 按年份排序
                        valid_dividends.sort(key=lambda x: x['year'] if x['year'] else 0, reverse=True)
                        
                        # 计算统计信息
                        latest_dividend = valid_dividends[0]['amount'] if valid_dividends else 0
                        dividend_years = len(valid_dividends)
                        
                        # 模拟股价（实际应用中应该获取真实股价）
                        import hashlib
                        hash_value = int(hashlib.md5(stock_code.encode()).hexdigest()[:8], 16)
                        mock_price = (hash_value % 400) + 100  # 100-500之间的模拟价格
                        
                        # 计算股息率
                        dividend_yield = (latest_dividend / mock_price) * 100 if latest_dividend > 0 else 0
                        
                        result = {
                            'code': stock_code,
                            'name': stock_name,
                            'latest_dividend': latest_dividend,
                            'dividend_years': dividend_years,
                            'mock_price': mock_price,
                            'dividend_yield': dividend_yield,
                            'total_records': len(raw_records),
                            'valid_records': len(valid_dividends),
                            'status': 'success'
                        }
                        
                        print(f"   ✅ 成功 - 分红: {latest_dividend:.3f} 港元, 股息率: {dividend_yield:.2f}%")
                        return result
                    else:
                        print(f"   ❌ 数据格式异常")
                        return {'code': stock_code, 'name': stock_name, 'status': 'data_error'}
                else:
                    print(f"   ❌ HTTP错误: {response.status}")
                    return {'code': stock_code, 'name': stock_name, 'status': 'http_error'}
                    
        except Exception as e:
            print(f"   ❌ 异常: {e}")
            return {'code': stock_code, 'name': stock_name, 'status': 'exception', 'error': str(e)}
    
    def run_batch_test(self, test_stocks):
        """运行批量测试"""
        print(f"\n🚀 开始批量测试 {len(test_stocks)} 只股票...")
        print("=" * 60)
        
        success_count = 0
        
        for i, stock in enumerate(test_stocks):
            print(f"\n[{i+1}/{len(test_stocks)}]", end=" ")
            
            result = self.fetch_stock_dividend(stock['code'], stock['name'])
            self.results.append(result)
            
            if result.get('status') == 'success':
                success_count += 1
            
            # 添加延时
            time.sleep(1.5)
        
        print(f"\n✅ 批量测试完成！成功: {success_count}/{len(test_stocks)}")
        return success_count
    
    def generate_summary(self):
        """生成测试结果汇总"""
        if not self.results:
            print("❌ 没有测试结果")
            return
        
        print(f"\n📊 测试结果汇总")
        print("=" * 80)
        
        # 统计状态
        status_count = {}
        successful_results = []
        
        for result in self.results:
            status = result.get('status', 'unknown')
            status_count[status] = status_count.get(status, 0) + 1
            
            if status == 'success':
                successful_results.append(result)
        
        print(f"状态统计:")
        for status, count in status_count.items():
            print(f"   {status}: {count}")
        
        if successful_results:
            print(f"\n成功获取分红数据的股票 (按股息率排序):")
            print("-" * 80)
            print(f"{'代码':<8} {'名称':<20} {'最新分红':<10} {'股息率':<8} {'分红年数':<8}")
            print("-" * 80)
            
            # 按股息率排序
            successful_results.sort(key=lambda x: x['dividend_yield'], reverse=True)
            
            for result in successful_results:
                code = result['code']
                name = result['name'][:18] + '..' if len(result['name']) > 18 else result['name']
                dividend = f"{result['latest_dividend']:.3f}"
                yield_rate = f"{result['dividend_yield']:.2f}%"
                years = result['dividend_years']
                
                print(f"{code:<8} {name:<20} {dividend:<10} {yield_rate:<8} {years:<8}")
            
            # 统计信息
            dividends = [r['latest_dividend'] for r in successful_results if r['latest_dividend'] > 0]
            yields = [r['dividend_yield'] for r in successful_results if r['dividend_yield'] > 0]
            
            if dividends:
                print(f"\n📈 统计信息:")
                print(f"   有分红股票数: {len(dividends)}")
                print(f"   平均分红金额: {sum(dividends)/len(dividends):.3f} 港元")
                print(f"   最高分红金额: {max(dividends):.3f} 港元")
                print(f"   平均股息率: {sum(yields)/len(yields):.2f}%" if yields else "   平均股息率: 0.00%")
                print(f"   最高股息率: {max(yields):.2f}%" if yields else "   最高股息率: 0.00%")
    
    def save_results(self, filename="hsi_dividend_test_results.csv"):
        """保存测试结果"""
        try:
            with open(filename, 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['股票代码', '股票名称', '最新分红(港元)', '股息率(%)', '分红年数', '模拟股价(港元)', '状态'])
                
                for result in self.results:
                    if result.get('status') == 'success':
                        writer.writerow([
                            result['code'],
                            result['name'],
                            f"{result['latest_dividend']:.3f}",
                            f"{result['dividend_yield']:.2f}",
                            result['dividend_years'],
                            f"{result['mock_price']:.2f}",
                            result['status']
                        ])
                    else:
                        writer.writerow([
                            result['code'],
                            result['name'],
                            'N/A',
                            'N/A',
                            'N/A',
                            'N/A',
                            result['status']
                        ])
            
            print(f"💾 测试结果已保存到: {filename}")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    print("🎯 恒生指数成分股分红数据批量测试")
    print("📡 数据来源: 东方财富网API")
    print("=" * 60)
    
    # 选择测试股票（包含已知有分红的和一些其他的）
    test_stocks = [
        {'code': '00700', 'name': '腾讯控股'},      # 已知有分红
        {'code': '00005', 'name': '汇丰控股'},      # 银行股，通常有分红
        {'code': '00939', 'name': '建设银行'},      # 银行股
        {'code': '01398', 'name': '工商银行'},      # 银行股
        {'code': '03988', 'name': '中国银行'},      # 银行股
        {'code': '02318', 'name': '中国平安'},      # 保险股
        {'code': '01299', 'name': '友邦保险'},      # 保险股
        {'code': '00388', 'name': '香港交易所'},     # 交易所
        {'code': '00002', 'name': '中电控股'},      # 公用事业
        {'code': '00003', 'name': '香港中华煤气'},   # 公用事业
        {'code': '09988', 'name': '阿里巴巴-W'},    # 科技股
        {'code': '09999', 'name': '网易-S'},       # 科技股
        {'code': '01093', 'name': '石药集团'},      # 医药股
        {'code': '00016', 'name': '新鸿基地产'},     # 地产股
        {'code': '01113', 'name': '长实集团'},      # 地产股
    ]
    
    # 创建测试器
    tester = HSIDividendBatchTest()
    
    start_time = time.time()
    
    try:
        # 运行批量测试
        success_count = tester.run_batch_test(test_stocks)
        
        # 生成汇总
        tester.generate_summary()
        
        # 保存结果
        tester.save_results()
        
        # 显示完成信息
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"\n✅ 测试完成！总耗时: {duration:.1f} 秒")
        print(f"📊 成功率: {success_count}/{len(test_stocks)} ({success_count/len(test_stocks)*100:.1f}%)")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断测试")
    except Exception as e:
        print(f"\n❌ 测试过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
