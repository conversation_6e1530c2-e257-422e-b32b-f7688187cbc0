#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
511260华安中证全债ETF百分位数阈值优化
寻找最佳的买入和卖出阈值组合
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.filters.hp_filter import hpfilter
import pickle
import os
from itertools import product
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PercentileThresholdOptimizer:
    """百分位数阈值优化器"""

    def __init__(self):
        self.data = None
        self.processed_data = None
        self.optimization_results = {}

    def load_data(self):
        """加载511260数据"""
        try:
            cache_file = 'cache/511260_price_data.pkl'
            if os.path.exists(cache_file):
                print("正在从本地缓存加载511260数据...")
                with open(cache_file, 'rb') as f:
                    self.data = pickle.load(f)

                # 确保索引是日期类型
                if not isinstance(self.data.index, pd.DatetimeIndex):
                    if 'date' in self.data.columns:
                        self.data['date'] = pd.to_datetime(self.data['date'])
                        self.data.set_index('date', inplace=True)
                    else:
                        self.data.index = pd.to_datetime(self.data.index)

                # 标准化列名
                if 'close' not in self.data.columns:
                    for col in self.data.columns:
                        if 'close' in col.lower() or '收盘' in col:
                            self.data['close'] = self.data[col]
                            break

                # 筛选最近10年数据
                end_date = self.data.index[-1]
                start_date = end_date - pd.DateOffset(years=10)
                self.data = self.data[self.data.index >= start_date]

                print(f"✅ 成功加载511260数据")
                print(f"   数据期间: {self.data.index[0].strftime('%Y-%m-%d')} 至 {self.data.index[-1].strftime('%Y-%m-%d')}")
                print(f"   数据点数: {len(self.data)} 个")

                return True
            else:
                print("❌ 未找到511260本地缓存数据")
                return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def process_data(self):
        """数据处理和指标计算"""
        print("\n🔄 进行数据处理和指标计算...")

        try:
            price = self.data['close']

            # HP滤波去趋势
            cycle, trend = hpfilter(price.dropna(), lamb=7000000)

            # 创建处理后的数据
            self.processed_data = pd.DataFrame(index=price.index)
            self.processed_data['价格'] = price
            self.processed_data['去趋势数据'] = cycle.reindex(price.index)

            # 计算百分位数
            self.processed_data['百分位数'] = self.processed_data['去趋势数据'].rolling(252).rank(pct=True)

            print(f"✅ 数据处理完成")
            return True

        except Exception as e:
            print(f"❌ 数据处理失败: {e}")
            return False

    def backtest_single_combination(self, buy_threshold, sell_threshold):
        """回测单个阈值组合"""
        try:
            # 生成信号
            buy_signals = self.processed_data['百分位数'] <= buy_threshold
            sell_signals = self.processed_data['百分位数'] >= sell_threshold

            # 执行策略
            trades = []
            position = 0  # 0: 空仓, 1: 持仓
            buy_date = None
            buy_price = None
            max_hold_days = 120  # 最大持有期保护

            for date, row in self.processed_data.iterrows():
                # 如果空仓且有买入信号
                if position == 0 and buy_signals.loc[date] and not pd.isna(row['百分位数']):
                    position = 1
                    buy_date = date
                    buy_price = row['价格']

                # 如果持仓且有卖出信号或达到最大持有期
                elif position == 1:
                    should_sell = False
                    sell_reason = ""
                    hold_days_actual = (date - buy_date).days

                    # 检查卖出条件
                    if sell_signals.loc[date] and not pd.isna(row['百分位数']):
                        should_sell = True
                        sell_reason = "高点卖出"
                    elif hold_days_actual >= max_hold_days:
                        should_sell = True
                        sell_reason = "最大持有期"

                    if should_sell:
                        sell_date = date
                        sell_price = row['价格']
                        trade_return = (sell_price - buy_price) / buy_price

                        trades.append({
                            'buy_date': buy_date,
                            'sell_date': sell_date,
                            'buy_price': buy_price,
                            'sell_price': sell_price,
                            'hold_days': hold_days_actual,
                            'return': trade_return,
                            'sell_reason': sell_reason
                        })

                        position = 0
                        buy_date = None
                        buy_price = None

            # 计算策略表现
            if not trades:
                return None

            trade_returns = [t['return'] for t in trades]
            total_return = sum(trade_returns)
            avg_return = np.mean(trade_returns)
            win_rate = sum(1 for r in trade_returns if r > 0) / len(trade_returns)
            avg_hold_days = np.mean([t['hold_days'] for t in trades])

            # 计算年化收益率
            total_days = (self.processed_data.index[-1] - self.processed_data.index[0]).days
            annual_return = total_return * (365.25 / total_days)

            # 计算夏普比率（简化版）
            volatility = np.std(trade_returns) if len(trade_returns) > 1 else 0
            sharpe_ratio = avg_return / volatility if volatility > 0 else 0

            # 计算高点卖出比例
            high_point_exits = sum(1 for t in trades if t['sell_reason'] == "高点卖出")
            high_point_ratio = high_point_exits / len(trades) if trades else 0

            return {
                'buy_threshold': buy_threshold,
                'sell_threshold': sell_threshold,
                'total_trades': len(trades),
                'total_return': total_return,
                'annual_return': annual_return,
                'avg_return': avg_return,
                'win_rate': win_rate,
                'avg_hold_days': avg_hold_days,
                'sharpe_ratio': sharpe_ratio,
                'high_point_ratio': high_point_ratio,
                'trades': trades
            }

        except Exception as e:
            return None

    def optimize_thresholds(self):
        """优化阈值参数"""
        print("\n🔍 开始阈值优化...")

        # 定义搜索范围
        buy_thresholds = [0.05, 0.10, 0.15, 0.20, 0.25, 0.30]  # 买入阈值
        sell_thresholds = [0.70, 0.75, 0.80, 0.85, 0.90, 0.95]  # 卖出阈值

        results = []
        total_combinations = len(buy_thresholds) * len(sell_thresholds)
        current = 0

        for buy_thresh, sell_thresh in product(buy_thresholds, sell_thresholds):
            current += 1
            print(f"   进度: {current}/{total_combinations} - 测试买入{buy_thresh*100:.0f}% 卖出{sell_thresh*100:.0f}%")

            # 确保买入阈值小于卖出阈值
            if buy_thresh >= sell_thresh:
                continue

            result = self.backtest_single_combination(buy_thresh, sell_thresh)
            if result and result['total_trades'] >= 3:  # 至少要有3笔交易
                results.append(result)

        self.optimization_results = pd.DataFrame(results)
        print(f"✅ 优化完成，共测试{len(results)}个有效组合")
        return self.optimization_results

    def analyze_results(self):
        """分析优化结果"""
        if self.optimization_results.empty:
            print("❌ 没有优化结果")
            return

        print("\n📊 优化结果分析")
        print("="*80)

        # 按不同指标排序找出最佳组合
        metrics = {
            'annual_return': '年化收益率',
            'sharpe_ratio': '夏普比率',
            'win_rate': '胜率',
            'high_point_ratio': '高点卖出比例'
        }

        print("\n🏆 各指标最佳组合:")
        for metric, name in metrics.items():
            best = self.optimization_results.nlargest(1, metric).iloc[0]
            print(f"\n{name}最佳:")
            print(f"   买入阈值: {best['buy_threshold']*100:.0f}%")
            print(f"   卖出阈值: {best['sell_threshold']*100:.0f}%")
            print(f"   {name}: {best[metric]*100:.2f}%" if metric != 'sharpe_ratio' else f"   {name}: {best[metric]:.2f}")
            print(f"   年化收益率: {best['annual_return']*100:+.2f}%")
            print(f"   交易次数: {best['total_trades']}次")
            print(f"   胜率: {best['win_rate']*100:.1f}%")

        # 综合评分
        print(f"\n🎯 综合评分排名 (年化收益率40% + 夏普比率30% + 胜率20% + 高点卖出比例10%):")

        # 标准化各指标
        df = self.optimization_results.copy()
        df['annual_return_norm'] = (df['annual_return'] - df['annual_return'].min()) / (df['annual_return'].max() - df['annual_return'].min())
        df['sharpe_ratio_norm'] = (df['sharpe_ratio'] - df['sharpe_ratio'].min()) / (df['sharpe_ratio'].max() - df['sharpe_ratio'].min())
        df['win_rate_norm'] = (df['win_rate'] - df['win_rate'].min()) / (df['win_rate'].max() - df['win_rate'].min())
        df['high_point_ratio_norm'] = (df['high_point_ratio'] - df['high_point_ratio'].min()) / (df['high_point_ratio'].max() - df['high_point_ratio'].min())

        # 计算综合评分
        df['composite_score'] = (df['annual_return_norm'] * 0.4 +
                                df['sharpe_ratio_norm'] * 0.3 +
                                df['win_rate_norm'] * 0.2 +
                                df['high_point_ratio_norm'] * 0.1)

        top_5 = df.nlargest(5, 'composite_score')

        for i, (_, row) in enumerate(top_5.iterrows(), 1):
            print(f"\n第{i}名:")
            print(f"   买入阈值: {row['buy_threshold']*100:.0f}%")
            print(f"   卖出阈值: {row['sell_threshold']*100:.0f}%")
            print(f"   综合评分: {row['composite_score']:.3f}")
            print(f"   年化收益率: {row['annual_return']*100:+.2f}%")
            print(f"   夏普比率: {row['sharpe_ratio']:.2f}")
            print(f"   胜率: {row['win_rate']*100:.1f}%")
            print(f"   高点卖出比例: {row['high_point_ratio']*100:.1f}%")
            print(f"   交易次数: {row['total_trades']}次")

        return top_5

    def create_optimization_visualization(self):
        """创建优化结果可视化"""
        print(f"\n📊 创建优化结果可视化...")

        if self.optimization_results.empty:
            print("❌ 没有优化结果")
            return

        fig, axes = plt.subplots(2, 3, figsize=(20, 12))
        fig.suptitle('511260百分位数阈值优化结果分析', fontsize=16, fontweight='bold')

        # 1. 年化收益率热力图
        ax1 = axes[0, 0]
        pivot_annual = self.optimization_results.pivot_table(
            values='annual_return',
            index='buy_threshold',
            columns='sell_threshold'
        )
        sns.heatmap(pivot_annual * 100, annot=True, fmt='.1f', cmap='RdYlGn',
                   ax=ax1, cbar_kws={'label': '年化收益率 (%)'})
        ax1.set_title('年化收益率热力图')
        ax1.set_xlabel('卖出阈值')
        ax1.set_ylabel('买入阈值')

        # 2. 夏普比率热力图
        ax2 = axes[0, 1]
        pivot_sharpe = self.optimization_results.pivot_table(
            values='sharpe_ratio',
            index='buy_threshold',
            columns='sell_threshold'
        )
        sns.heatmap(pivot_sharpe, annot=True, fmt='.1f', cmap='RdYlGn',
                   ax=ax2, cbar_kws={'label': '夏普比率'})
        ax2.set_title('夏普比率热力图')
        ax2.set_xlabel('卖出阈值')
        ax2.set_ylabel('买入阈值')

        # 3. 胜率热力图
        ax3 = axes[0, 2]
        pivot_winrate = self.optimization_results.pivot_table(
            values='win_rate',
            index='buy_threshold',
            columns='sell_threshold'
        )
        sns.heatmap(pivot_winrate * 100, annot=True, fmt='.0f', cmap='RdYlGn',
                   ax=ax3, cbar_kws={'label': '胜率 (%)'})
        ax3.set_title('胜率热力图')
        ax3.set_xlabel('卖出阈值')
        ax3.set_ylabel('买入阈值')

        # 4. 交易次数热力图
        ax4 = axes[1, 0]
        pivot_trades = self.optimization_results.pivot_table(
            values='total_trades',
            index='buy_threshold',
            columns='sell_threshold'
        )
        sns.heatmap(pivot_trades, annot=True, fmt='.0f', cmap='Blues',
                   ax=ax4, cbar_kws={'label': '交易次数'})
        ax4.set_title('交易次数热力图')
        ax4.set_xlabel('卖出阈值')
        ax4.set_ylabel('买入阈值')

        # 5. 高点卖出比例热力图
        ax5 = axes[1, 1]
        pivot_high_point = self.optimization_results.pivot_table(
            values='high_point_ratio',
            index='buy_threshold',
            columns='sell_threshold'
        )
        sns.heatmap(pivot_high_point * 100, annot=True, fmt='.0f', cmap='RdYlGn',
                   ax=ax5, cbar_kws={'label': '高点卖出比例 (%)'})
        ax5.set_title('高点卖出比例热力图')
        ax5.set_xlabel('卖出阈值')
        ax5.set_ylabel('买入阈值')

        # 6. 综合评分散点图
        ax6 = axes[1, 2]

        # 计算综合评分
        df = self.optimization_results.copy()
        df['annual_return_norm'] = (df['annual_return'] - df['annual_return'].min()) / (df['annual_return'].max() - df['annual_return'].min())
        df['sharpe_ratio_norm'] = (df['sharpe_ratio'] - df['sharpe_ratio'].min()) / (df['sharpe_ratio'].max() - df['sharpe_ratio'].min())
        df['win_rate_norm'] = (df['win_rate'] - df['win_rate'].min()) / (df['win_rate'].max() - df['win_rate'].min())
        df['high_point_ratio_norm'] = (df['high_point_ratio'] - df['high_point_ratio'].min()) / (df['high_point_ratio'].max() - df['high_point_ratio'].min())

        df['composite_score'] = (df['annual_return_norm'] * 0.4 +
                                df['sharpe_ratio_norm'] * 0.3 +
                                df['win_rate_norm'] * 0.2 +
                                df['high_point_ratio_norm'] * 0.1)

        scatter = ax6.scatter(df['annual_return'] * 100, df['sharpe_ratio'],
                             c=df['composite_score'], cmap='RdYlGn',
                             s=df['total_trades'] * 10, alpha=0.7)

        # 标记最佳点
        best_idx = df['composite_score'].idxmax()
        best_row = df.loc[best_idx]
        ax6.scatter(best_row['annual_return'] * 100, best_row['sharpe_ratio'],
                   color='red', s=200, marker='*', edgecolor='black', linewidth=2,
                   label=f'最佳组合: {best_row["buy_threshold"]*100:.0f}%买入, {best_row["sell_threshold"]*100:.0f}%卖出')

        ax6.set_xlabel('年化收益率 (%)')
        ax6.set_ylabel('夏普比率')
        ax6.set_title('综合评分散点图\n(点的大小=交易次数, 颜色=综合评分)')
        ax6.legend()

        # 添加颜色条
        plt.colorbar(scatter, ax=ax6, label='综合评分')

        plt.tight_layout()

        # 保存图表
        chart_path = 'data/percentile_threshold_optimization.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✅ 优化结果可视化已保存: {chart_path}")

        plt.show()
        return chart_path

    def detailed_analysis_top_strategies(self, top_n=3):
        """详细分析前几名策略"""
        print(f"\n🔍 详细分析前{top_n}名策略")
        print("="*80)

        # 计算综合评分
        df = self.optimization_results.copy()
        df['annual_return_norm'] = (df['annual_return'] - df['annual_return'].min()) / (df['annual_return'].max() - df['annual_return'].min())
        df['sharpe_ratio_norm'] = (df['sharpe_ratio'] - df['sharpe_ratio'].min()) / (df['sharpe_ratio'].max() - df['sharpe_ratio'].min())
        df['win_rate_norm'] = (df['win_rate'] - df['win_rate'].min()) / (df['win_rate'].max() - df['win_rate'].min())
        df['high_point_ratio_norm'] = (df['high_point_ratio'] - df['high_point_ratio'].min()) / (df['high_point_ratio'].max() - df['high_point_ratio'].min())

        df['composite_score'] = (df['annual_return_norm'] * 0.4 +
                                df['sharpe_ratio_norm'] * 0.3 +
                                df['win_rate_norm'] * 0.2 +
                                df['high_point_ratio_norm'] * 0.1)

        top_strategies = df.nlargest(top_n, 'composite_score')

        for i, (_, strategy) in enumerate(top_strategies.iterrows(), 1):
            print(f"\n🏅 第{i}名策略详细分析:")
            print(f"   买入阈值: {strategy['buy_threshold']*100:.0f}%")
            print(f"   卖出阈值: {strategy['sell_threshold']*100:.0f}%")
            print(f"   综合评分: {strategy['composite_score']:.3f}")
            print(f"   年化收益率: {strategy['annual_return']*100:+.2f}%")
            print(f"   夏普比率: {strategy['sharpe_ratio']:.2f}")
            print(f"   胜率: {strategy['win_rate']*100:.1f}%")
            print(f"   高点卖出比例: {strategy['high_point_ratio']*100:.1f}%")
            print(f"   总交易次数: {strategy['total_trades']}次")
            print(f"   平均单次收益: {strategy['avg_return']*100:+.2f}%")
            print(f"   平均持有天数: {strategy['avg_hold_days']:.1f}天")

            # 分析交易详情
            trades = strategy['trades']
            if trades:
                returns = [t['return'] * 100 for t in trades]
                hold_days = [t['hold_days'] for t in trades]

                print(f"   收益分布: 最大{max(returns):+.2f}%, 最小{min(returns):+.2f}%, 标准差{np.std(returns):.2f}%")
                print(f"   持有期分布: 最长{max(hold_days)}天, 最短{min(hold_days)}天, 标准差{np.std(hold_days):.1f}天")

                # 卖出原因统计
                sell_reasons = {}
                for trade in trades:
                    reason = trade['sell_reason']
                    sell_reasons[reason] = sell_reasons.get(reason, 0) + 1

                print(f"   卖出原因:")
                for reason, count in sell_reasons.items():
                    percentage = count / len(trades) * 100
                    print(f"     {reason}: {count}次 ({percentage:.1f}%)")

def main():
    """主函数"""
    print("🚀 511260百分位数阈值优化启动")
    print("="*80)

    # 创建优化器实例
    optimizer = PercentileThresholdOptimizer()

    # 加载数据
    if not optimizer.load_data():
        return None

    # 数据处理
    if not optimizer.process_data():
        return None

    # 优化阈值
    results = optimizer.optimize_thresholds()
    if results is None or results.empty:
        print("❌ 优化失败")
        return None

    # 分析结果
    top_strategies = optimizer.analyze_results()

    # 创建可视化
    optimizer.create_optimization_visualization()

    # 详细分析前3名策略
    optimizer.detailed_analysis_top_strategies(3)

    print("\n" + "="*80)
    print("✅ 百分位数阈值优化分析完成！")
    print("="*80)

    return optimizer

if __name__ == "__main__":
    result = main()
