#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
University of Michigan Consumer Sentiment Index Data Processor (SCA Table Format)

This script processes the specific format of UMich Consumer Sentiment data
downloaded from the SCA (Survey of Consumers Archive) website.

Data source: https://data.sca.isr.umich.edu/data-archive/mine.php
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import matplotlib.dates as mdates

# Set up directories
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

def process_sca_table_data(file_path):
    """
    Process the SCA table format UMich Consumer Sentiment data file.

    Args:
        file_path (str): Path to the downloaded data file

    Returns:
        pandas.DataFrame: Processed sentiment data
    """
    print(f"Processing UMich Consumer Sentiment data from SCA table: {file_path}")

    try:
        # Read the CSV file, skipping the first row (title) and using the second row as headers
        df = pd.read_csv(file_path, skiprows=1)

        # Print the first few rows to inspect the data structure
        print("\nFirst few rows of the raw data:")
        print(df.head())

        # Print column names
        print("\nColumn names in the raw data:")
        print(df.columns.tolist())

        # Clean up the data
        # First, let's see what we have
        print(f"Data shape: {df.shape}")
        print(f"Data types:\n{df.dtypes}")

        # Remove any completely empty rows
        df = df.dropna(how='all')

        # Check if we have the expected columns
        expected_cols = ['Month', 'Year', 'Index']
        if not all(col in df.columns for col in expected_cols):
            print(f"Warning: Expected columns {expected_cols} not found in {df.columns.tolist()}")
            # Try to identify the correct columns
            if len(df.columns) >= 3:
                df.columns = ['Month', 'Year', 'Index'] + [f'Extra_{i}' for i in range(len(df.columns) - 3)]

        # Remove rows where any of the main columns are missing
        df = df.dropna(subset=['Month', 'Year', 'Index'])

        # Convert to appropriate types, handling any conversion errors
        try:
            df['Month'] = pd.to_numeric(df['Month'], errors='coerce').astype('Int64')
            df['Year'] = pd.to_numeric(df['Year'], errors='coerce').astype('Int64')
            df['Index'] = pd.to_numeric(df['Index'], errors='coerce')
        except Exception as e:
            print(f"Error converting data types: {e}")
            print("Sample data:")
            print(df.head())
            return None

        # Remove rows where conversion failed
        df = df.dropna(subset=['Month', 'Year', 'Index'])

        print(f"After cleaning: {len(df)} rows remaining")

        # Create a proper date column
        df['Date'] = pd.to_datetime(df[['Year', 'Month']].assign(day=1))

        # Create the result DataFrame
        result = pd.DataFrame({
            'UMICH_Sentiment': df['Index']
        }, index=df['Date'])

        # Sort by date
        result.sort_index(inplace=True)

        # Remove any duplicate dates (keep the last one)
        result = result[~result.index.duplicated(keep='last')]

        print("\nProcessed data:")
        print(result.head())
        print(f"\nTotal records: {len(result)}")
        print(f"Date range: {result.index.min().strftime('%Y-%m-%d')} to {result.index.max().strftime('%Y-%m-%d')}")

        return result

    except Exception as e:
        print(f"Error processing data: {str(e)}")
        return None

def calculate_indicators(df):
    """
    Calculate additional indicators for the sentiment data.

    Args:
        df (pandas.DataFrame): DataFrame containing sentiment data

    Returns:
        pandas.DataFrame: DataFrame with additional indicators
    """
    if df is None or df.empty:
        print("No data to calculate indicators for.")
        return df

    # Make a copy to avoid modifying the original
    result = df.copy()

    # Calculate moving averages
    result['MA_3M'] = result['UMICH_Sentiment'].rolling(window=3).mean()
    result['MA_12M'] = result['UMICH_Sentiment'].rolling(window=12).mean()

    # Calculate rate of change (month-over-month)
    result['MoM_Change'] = result['UMICH_Sentiment'].pct_change() * 100

    # Calculate rate of change (year-over-year)
    result['YoY_Change'] = result['UMICH_Sentiment'].pct_change(periods=12) * 100

    # Calculate Z-score (standardized value) using 5-year rolling window
    window = 60  # 5 years (60 months)
    result['Z_Score'] = result['UMICH_Sentiment'].rolling(window=window, min_periods=12).apply(
        lambda x: (x.iloc[-1] - x.mean()) / x.std() if x.std() != 0 else 0
    )

    # Calculate percentile rank using 5-year rolling window
    result['Percentile_Rank'] = result['UMICH_Sentiment'].rolling(window=window, min_periods=12).apply(
        lambda x: pd.Series(x).rank(pct=True).iloc[-1] * 100
    )

    # Add sentiment level categories
    result['Sentiment_Level'] = pd.cut(
        result['UMICH_Sentiment'],
        bins=[-float('inf'), 60, 80, 100, float('inf')],
        labels=['Low', 'Moderate', 'High', 'Very High']
    )

    return result

def save_data(df, filename='umich_sentiment_sca.csv'):
    """
    Save data to CSV file.

    Args:
        df (pandas.DataFrame): DataFrame to save
        filename (str): Name of the file to save to
    """
    if df is None or df.empty:
        print("No data to save.")
        return

    file_path = os.path.join(DATA_DIR, filename)
    df.to_csv(file_path)
    print(f"Data saved to {file_path}")

def plot_sentiment(df, filename='umich_sentiment_sca.png'):
    """
    Create a plot of the sentiment data.

    Args:
        df (pandas.DataFrame): DataFrame containing sentiment data
        filename (str): Name of the file to save the plot to
    """
    if df is None or df.empty:
        print("No data to plot.")
        return

    # Create figure and primary axis
    fig, ax1 = plt.subplots(figsize=(12, 8))

    # Plot sentiment index
    ax1.plot(df.index, df['UMICH_Sentiment'], 'b-', label='Consumer Sentiment Index (SCA Official)')

    # Plot moving averages if available
    if 'MA_3M' in df.columns:
        ax1.plot(df.index, df['MA_3M'], 'r--', label='3-Month MA')
    if 'MA_12M' in df.columns:
        ax1.plot(df.index, df['MA_12M'], 'g--', label='12-Month MA')

    # Set labels and title
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Sentiment Index', color='b')
    ax1.tick_params('y', colors='b')
    ax1.set_title('University of Michigan Consumer Sentiment Index (SCA Official Data)')

    # Format x-axis to show years
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax1.xaxis.set_major_locator(mdates.YearLocator(2))  # Show every 2 years

    # Add grid
    ax1.grid(True, alpha=0.3)

    # Add legend
    ax1.legend(loc='upper left')

    # Create a secondary axis for the YoY change if available
    if 'YoY_Change' in df.columns:
        ax2 = ax1.twinx()
        ax2.plot(df.index, df['YoY_Change'], 'c-', alpha=0.5, label='YoY % Change')
        ax2.set_ylabel('Year-over-Year % Change', color='c')
        ax2.tick_params('y', colors='c')

        # Add horizontal line at YoY = 0%
        ax2.axhline(y=0, color='k', linestyle='-', alpha=0.2)

        # Add legend for secondary axis
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    # Highlight major economic events
    events = [
        ('2008-09-01', 'Financial Crisis', 'red'),
        ('2020-03-01', 'COVID-19 Pandemic', 'orange'),
        ('2022-02-01', 'Russia-Ukraine War', 'purple'),
    ]

    for event_date, event_name, color in events:
        try:
            event_ts = pd.Timestamp(event_date)
            if df.index.min() <= event_ts <= df.index.max():
                ax1.axvline(x=event_ts, color=color, linestyle='--', alpha=0.7, label=event_name)
        except:
            continue

    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(os.path.join(DATA_DIR, filename))
    print(f"Plot saved to {os.path.join(DATA_DIR, filename)}")

    # Show plot
    plt.show()

def plot_recent_trend(df, months=24, filename='umich_sentiment_recent.png'):
    """
    Create a plot of the recent sentiment trend.

    Args:
        df (pandas.DataFrame): DataFrame containing sentiment data
        months (int): Number of months to show
        filename (str): Name of the file to save the plot to
    """
    if df is None or df.empty:
        print("No data to plot.")
        return

    # Calculate the start date for the recent trend
    end_date = df.index.max()
    start_date = end_date - pd.DateOffset(months=months)

    # Filter the data for the recent period
    recent_df = df[df.index >= start_date]

    if recent_df.empty:
        print(f"No data available for the last {months} months.")
        return

    # Create figure and axis
    fig, ax = plt.subplots(figsize=(12, 6))

    # Plot sentiment index
    ax.plot(recent_df.index, recent_df['UMICH_Sentiment'], 'b-', linewidth=2, label='Consumer Sentiment Index')

    # Plot moving averages if available
    if 'MA_3M' in recent_df.columns:
        ax.plot(recent_df.index, recent_df['MA_3M'], 'r--', label='3-Month MA')

    # Set labels and title
    ax.set_xlabel('Date')
    ax.set_ylabel('Sentiment Index')
    ax.set_title(f'University of Michigan Consumer Sentiment Index - Last {months} Months')

    # Format x-axis to show months
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%b %Y'))
    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))  # Show every 3 months
    plt.xticks(rotation=45)

    # Add grid
    ax.grid(True, alpha=0.3)

    # Add legend
    ax.legend(loc='upper right')

    # Annotate the latest value
    latest_date = recent_df.index.max()
    latest_value = recent_df.loc[latest_date, 'UMICH_Sentiment']
    ax.annotate(f'{latest_value:.1f}',
                xy=(latest_date, latest_value),
                xytext=(10, 0),
                textcoords='offset points',
                fontsize=12,
                fontweight='bold')

    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(os.path.join(DATA_DIR, filename))
    print(f"Recent trend plot saved to {os.path.join(DATA_DIR, filename)}")

    # Show plot
    plt.show()

def main():
    """Main function to run the data processing."""
    import sys

    # Check if a file path was provided as a command-line argument
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
    else:
        # Default to the SCA table file
        file_path = os.path.join(DATA_DIR, 'sca-table1-on-2025-May-24.csv')

    # Check if the file exists
    if not os.path.isfile(file_path):
        print(f"Error: File not found: {file_path}")
        print("Please provide the path to the SCA table CSV file.")
        return

    # Process the data
    sentiment_data = process_sca_table_data(file_path)

    if sentiment_data is not None:
        # Calculate indicators
        sentiment_data_with_indicators = calculate_indicators(sentiment_data)

        # Save data
        save_data(sentiment_data_with_indicators)

        # Plot data
        plot_sentiment(sentiment_data_with_indicators)

        # Plot recent trend
        plot_recent_trend(sentiment_data_with_indicators, months=24)

        # Print recent data
        print("\nMost recent data:")
        print(sentiment_data_with_indicators.tail())

        # Print summary statistics
        print("\nSummary statistics:")
        print(sentiment_data_with_indicators['UMICH_Sentiment'].describe())

        # Print current sentiment level
        latest = sentiment_data_with_indicators.iloc[-1]
        sentiment_level = latest['Sentiment_Level']

        print(f"\nCurrent Consumer Sentiment Index: {latest['UMICH_Sentiment']:.1f}")
        print(f"Sentiment level: {sentiment_level}")
        print(f"Month-over-month change: {latest['MoM_Change']:.1f}%")
        print(f"Year-over-year change: {latest['YoY_Change']:.1f}%")
        print(f"Percentile rank (5-year window): {latest['Percentile_Rank']:.1f}%")
        print(f"Z-Score (5-year window): {latest['Z_Score']:.2f}")

        # Historical comparison
        historical_values = {
            'Current': latest['UMICH_Sentiment'],
            '1 year ago': sentiment_data_with_indicators.iloc[-13]['UMICH_Sentiment'] if len(sentiment_data_with_indicators) > 13 else None,
            '5 years ago': sentiment_data_with_indicators.iloc[-61]['UMICH_Sentiment'] if len(sentiment_data_with_indicators) > 61 else None,
            'All-time high': sentiment_data_with_indicators['UMICH_Sentiment'].max(),
            'All-time low': sentiment_data_with_indicators['UMICH_Sentiment'].min(),
            'Long-term average': sentiment_data_with_indicators['UMICH_Sentiment'].mean()
        }

        print("\nHistorical comparison:")
        for period, value in historical_values.items():
            if value is not None:
                print(f"{period}: {value:.1f}")

if __name__ == "__main__":
    main()
