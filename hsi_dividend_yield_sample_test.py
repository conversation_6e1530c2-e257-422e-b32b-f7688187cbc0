#!/usr/bin/env python3
"""
恒生指数成分股每日股息率计算器 - 样本测试版本
测试几只代表性股票的股息率计算功能
"""

from hsi_daily_dividend_yield_calculator import HSIDailyDividendYieldCalculator
import pandas as pd
from datetime import datetime

def test_sample_stocks():
    """测试几只代表性股票的股息率计算"""
    print("🧪 恒生指数成分股股息率计算 - 样本测试")
    print("=" * 50)
    
    # 选择几只代表性股票进行测试
    test_stocks = [
        "00700",  # 腾讯控股
        "00005",  # 汇丰控股
        "00388",  # 香港交易所
        "00941",  # 中国移动
        "01299",  # 友邦保险
    ]
    
    calculator = HSIDailyDividendYieldCalculator()
    calculator.load_dividend_data()
    
    results = {}
    
    print(f"📊 测试 {len(test_stocks)} 只股票...")
    
    for i, symbol in enumerate(test_stocks, 1):
        if symbol in calculator.dividend_data:
            stock_name = calculator.dividend_data[symbol]['stock_name']
            print(f"\n📈 [{i}/{len(test_stocks)}] 计算 {symbol} ({stock_name})...")
            
            yield_series = calculator.calculate_daily_dividend_yield(symbol)
            
            if yield_series is not None:
                results[symbol] = yield_series
                
                # 显示基本统计信息
                non_zero_yields = yield_series[yield_series > 0]
                if len(non_zero_yields) > 0:
                    print(f"   ✅ 成功计算 {len(yield_series)} 个交易日")
                    print(f"   📊 平均股息率: {non_zero_yields.mean():.4f}%")
                    print(f"   📊 最新股息率: {yield_series.dropna().iloc[-1]:.4f}%")
                    print(f"   📊 有分红天数: {len(non_zero_yields)}")
                else:
                    print(f"   ⚠️  无分红数据")
            else:
                print(f"   ❌ 计算失败")
        else:
            print(f"\n❌ 找不到股票 {symbol} 的分红数据")
    
    if results:
        print(f"\n🎉 成功计算 {len(results)} 只股票的股息率")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        output_file = f"hsi_sample_dividend_yields_{timestamp}.csv"
        
        # 创建DataFrame并保存
        df = pd.DataFrame(results)
        
        # 添加股票名称到列名
        new_columns = []
        for symbol in df.columns:
            if symbol in calculator.dividend_data:
                stock_name = calculator.dividend_data[symbol]['stock_name']
                new_columns.append(f"{symbol}_{stock_name}")
            else:
                new_columns.append(symbol)
        
        df.columns = new_columns
        df.to_csv(output_file, encoding='utf-8-sig')
        
        print(f"💾 结果已保存到: {output_file}")
        print(f"   数据维度: {df.shape}")
        print(f"   日期范围: {df.index.min()} 至 {df.index.max()}")
        
        # 显示汇总统计
        print("\n📊 汇总统计:")
        for symbol in results.keys():
            stock_name = calculator.dividend_data[symbol]['stock_name']
            yield_series = results[symbol]
            non_zero_yields = yield_series[yield_series > 0]
            
            if len(non_zero_yields) > 0:
                print(f"   {symbol} ({stock_name}): 平均 {non_zero_yields.mean():.4f}%, "
                      f"最新 {yield_series.dropna().iloc[-1]:.4f}%")
        
        return output_file
    else:
        print("❌ 没有成功计算任何股票的股息率")
        return None

def analyze_dividend_patterns():
    """分析分红模式"""
    print("\n🔍 分析分红模式...")
    
    calculator = HSIDailyDividendYieldCalculator()
    calculator.load_dividend_data()
    
    # 分析所有股票的分红频率和模式
    dividend_analysis = []
    
    for symbol, data in calculator.dividend_data.items():
        stock_name = data['stock_name']
        dividend_history = data['dividend_history']
        
        if dividend_history:
            # 计算分红频率
            years = set([div['year'] for div in dividend_history])
            avg_dividends_per_year = len(dividend_history) / len(years) if years else 0
            
            # 计算平均分红金额
            amounts = [div['amount'] for div in dividend_history]
            avg_amount = sum(amounts) / len(amounts) if amounts else 0
            
            # 最新分红
            latest_dividend = dividend_history[-1] if dividend_history else None
            
            dividend_analysis.append({
                '股票代码': symbol,
                '股票名称': stock_name,
                '分红次数': len(dividend_history),
                '分红年数': len(years),
                '年均分红次数': avg_dividends_per_year,
                '平均分红金额': avg_amount,
                '最新分红金额': latest_dividend['amount'] if latest_dividend else 0,
                '最新分红日期': latest_dividend['ex_date'] if latest_dividend else None
            })
    
    # 创建分析DataFrame
    analysis_df = pd.DataFrame(dividend_analysis)
    analysis_df = analysis_df.sort_values('平均分红金额', ascending=False)
    
    # 保存分析结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    analysis_file = f"hsi_dividend_pattern_analysis_{timestamp}.csv"
    analysis_df.to_csv(analysis_file, index=False, encoding='utf-8-sig')
    
    print(f"📊 分红模式分析完成，结果保存到: {analysis_file}")
    print(f"   总股票数: {len(analysis_df)}")
    print(f"   有分红股票数: {len(analysis_df[analysis_df['分红次数'] > 0])}")
    
    # 显示前10只股票
    print("\n📈 平均分红金额最高的前10只股票:")
    top_10 = analysis_df.head(10)[['股票代码', '股票名称', '平均分红金额', '年均分红次数', '最新分红金额']]
    print(top_10.to_string(index=False))
    
    return analysis_file

if __name__ == "__main__":
    # 运行样本测试
    result_file = test_sample_stocks()
    
    # 分析分红模式
    analysis_file = analyze_dividend_patterns()
    
    print(f"\n📁 输出文件:")
    if result_file:
        print(f"   股息率数据: {result_file}")
    if analysis_file:
        print(f"   分红模式分析: {analysis_file}")
    
    print("\n✅ 测试完成！")
