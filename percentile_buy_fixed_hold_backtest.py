#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
511260华安中证全债ETF百分位数对称策略回测
20%分位数买入 + 80%分位数卖出
包含累积收益曲线和回撤分析
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from statsmodels.tsa.filters.hp_filter import hpfilter
import pickle
import os
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PercentileSymmetricBacktest:
    """百分位数对称策略回测器"""

    def __init__(self, buy_threshold=0.20, sell_threshold=0.80):
        self.data = None
        self.processed_data = None
        self.trades = []
        self.daily_returns = None
        self.cumulative_returns = None
        self.drawdowns = None
        self.buy_threshold = buy_threshold
        self.sell_threshold = sell_threshold

    def load_data(self):
        """加载511260数据"""
        try:
            cache_file = 'cache/511260_price_data.pkl'
            if os.path.exists(cache_file):
                print("正在从本地缓存加载511260数据...")
                with open(cache_file, 'rb') as f:
                    self.data = pickle.load(f)

                # 确保索引是日期类型
                if not isinstance(self.data.index, pd.DatetimeIndex):
                    if 'date' in self.data.columns:
                        self.data['date'] = pd.to_datetime(self.data['date'])
                        self.data.set_index('date', inplace=True)
                    else:
                        self.data.index = pd.to_datetime(self.data.index)

                # 标准化列名
                if 'close' not in self.data.columns:
                    for col in self.data.columns:
                        if 'close' in col.lower() or '收盘' in col:
                            self.data['close'] = self.data[col]
                            break

                # 筛选最近10年数据
                end_date = self.data.index[-1]
                start_date = end_date - pd.DateOffset(years=10)
                self.data = self.data[self.data.index >= start_date]

                print(f"✅ 成功加载511260数据")
                print(f"   数据期间: {self.data.index[0].strftime('%Y-%m-%d')} 至 {self.data.index[-1].strftime('%Y-%m-%d')}")
                print(f"   数据点数: {len(self.data)} 个")
                print(f"   价格范围: {self.data['close'].min():.2f} - {self.data['close'].max():.2f}")

                return True
            else:
                print("❌ 未找到511260本地缓存数据")
                return False
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def process_data(self):
        """数据处理和指标计算"""
        print("\n🔄 进行数据处理和指标计算...")

        try:
            price = self.data['close']

            # HP滤波去趋势
            cycle, trend = hpfilter(price.dropna(), lamb=7000000)

            # 创建处理后的数据
            self.processed_data = pd.DataFrame(index=price.index)
            self.processed_data['价格'] = price
            self.processed_data['趋势成分'] = trend.reindex(price.index)
            self.processed_data['去趋势数据'] = cycle.reindex(price.index)

            # 计算百分位数
            self.processed_data['百分位数'] = self.processed_data['去趋势数据'].rolling(252).rank(pct=True)

            print(f"✅ 数据处理完成")
            return True

        except Exception as e:
            print(f"❌ 数据处理失败: {e}")
            return False

    def generate_signals(self):
        """生成买卖信号"""
        print(f"\n📊 生成买卖信号...")

        # 买入信号：百分位数低点
        buy_signals = self.processed_data['百分位数'] <= self.buy_threshold

        # 卖出信号：百分位数高点
        sell_signals = self.processed_data['百分位数'] >= self.sell_threshold

        self.processed_data['买入信号'] = buy_signals
        self.processed_data['卖出信号'] = sell_signals

        buy_count = buy_signals.sum()
        sell_count = sell_signals.sum()

        print(f"   买入信号: {buy_count}次 (百分位数 <= {self.buy_threshold*100:.0f}%)")
        print(f"   卖出信号: {sell_count}次 (百分位数 >= {self.sell_threshold*100:.0f}%)")

        return True

    def execute_strategy(self):
        """执行完整的交易策略"""
        print("\n🔄 执行完整交易策略...")

        self.trades = []
        position = 0  # 0: 空仓, 1: 持仓
        buy_date = None
        buy_price = None
        max_hold_days = 120  # 最大持有期保护

        for i, (date, row) in enumerate(self.processed_data.iterrows()):
            # 如果空仓且有买入信号
            if position == 0 and row['买入信号'] and not pd.isna(row['百分位数']):
                position = 1
                buy_date = date
                buy_price = row['价格']

            # 如果持仓且有卖出信号或达到最大持有期
            elif position == 1:
                should_sell = False
                sell_reason = ""
                hold_days_actual = (date - buy_date).days

                # 检查卖出条件
                if row['卖出信号'] and not pd.isna(row['百分位数']):
                    should_sell = True
                    sell_reason = f"分位数达到{self.sell_threshold*100:.0f}%高点"
                elif hold_days_actual >= max_hold_days:
                    should_sell = True
                    sell_reason = f"最大持有期{max_hold_days}天"

                if should_sell:
                    sell_date = date
                    sell_price = row['价格']
                    trade_return = (sell_price - buy_price) / buy_price

                    self.trades.append({
                        'buy_date': buy_date,
                        'sell_date': sell_date,
                        'buy_price': buy_price,
                        'sell_price': sell_price,
                        'hold_days': hold_days_actual,
                        'return': trade_return,
                        'sell_reason': sell_reason
                    })

                    position = 0
                    buy_date = None
                    buy_price = None

        print(f"✅ 策略执行完成，共完成{len(self.trades)}笔交易")
        return True

    def calculate_performance(self):
        """计算策略表现"""
        print("\n📈 计算策略表现...")

        if not self.trades:
            print("❌ 没有交易记录")
            return False

        # 创建每日收益序列
        self.daily_returns = pd.Series(0.0, index=self.processed_data.index)

        # 计算每笔交易的每日收益
        for trade in self.trades:
            buy_date = trade['buy_date']
            sell_date = trade['sell_date']
            trade_return = trade['return']
            hold_days = trade['hold_days']

            # 将交易收益分摊到持有期间的每一天
            if hold_days > 0:
                daily_return = trade_return / hold_days
                date_range = pd.date_range(start=buy_date, end=sell_date, freq='D')
                for date in date_range:
                    if date in self.daily_returns.index:
                        self.daily_returns[date] = daily_return

        # 计算累积收益
        self.cumulative_returns = (1 + self.daily_returns).cumprod()

        # 计算回撤
        rolling_max = self.cumulative_returns.expanding().max()
        self.drawdowns = (self.cumulative_returns - rolling_max) / rolling_max

        # 计算买入持有基准
        buy_hold_returns = self.processed_data['价格'] / self.processed_data['价格'].iloc[0]

        # 计算策略统计指标
        total_return = self.cumulative_returns.iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / len(self.daily_returns)) - 1
        volatility = self.daily_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        max_drawdown = self.drawdowns.min()

        # 交易统计
        trade_returns = [t['return'] for t in self.trades]
        win_rate = sum(1 for r in trade_returns if r > 0) / len(trade_returns)
        avg_return = np.mean(trade_returns)
        avg_hold_days = np.mean([t['hold_days'] for t in self.trades])

        # 买入持有基准统计
        bh_total_return = buy_hold_returns.iloc[-1] - 1
        bh_annual_return = (1 + bh_total_return) ** (252 / len(buy_hold_returns)) - 1
        bh_volatility = buy_hold_returns.pct_change().std() * np.sqrt(252)
        bh_sharpe = bh_annual_return / bh_volatility if bh_volatility > 0 else 0
        bh_max_dd = ((buy_hold_returns - buy_hold_returns.expanding().max()) / buy_hold_returns.expanding().max()).min()

        self.performance_stats = {
            'strategy': {
                'total_return': total_return,
                'annual_return': annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'avg_return': avg_return,
                'avg_hold_days': avg_hold_days,
                'total_trades': len(self.trades)
            },
            'buy_hold': {
                'total_return': bh_total_return,
                'annual_return': bh_annual_return,
                'volatility': bh_volatility,
                'sharpe_ratio': bh_sharpe,
                'max_drawdown': bh_max_dd
            }
        }

        print(f"✅ 策略表现计算完成")
        return True

    def print_performance_summary(self):
        """打印策略表现总结"""
        print("\n" + "="*80)
        print(f"📊 百分位数{self.buy_threshold*100:.0f}%买入+{self.sell_threshold*100:.0f}%卖出策略回测结果")
        print("="*80)

        strategy = self.performance_stats['strategy']
        buy_hold = self.performance_stats['buy_hold']

        print(f"\n🎯 策略表现:")
        print(f"   总收益率: {strategy['total_return']*100:+.2f}%")
        print(f"   年化收益率: {strategy['annual_return']*100:+.2f}%")
        print(f"   年化波动率: {strategy['volatility']*100:.2f}%")
        print(f"   夏普比率: {strategy['sharpe_ratio']:.2f}")
        print(f"   最大回撤: {strategy['max_drawdown']*100:.2f}%")
        print(f"   胜率: {strategy['win_rate']*100:.1f}%")
        print(f"   平均单次收益: {strategy['avg_return']*100:+.2f}%")
        print(f"   平均持有天数: {strategy['avg_hold_days']:.1f}天")
        print(f"   总交易次数: {strategy['total_trades']}次")

        print(f"\n📈 买入持有基准:")
        print(f"   总收益率: {buy_hold['total_return']*100:+.2f}%")
        print(f"   年化收益率: {buy_hold['annual_return']*100:+.2f}%")
        print(f"   年化波动率: {buy_hold['volatility']*100:.2f}%")
        print(f"   夏普比率: {buy_hold['sharpe_ratio']:.2f}")
        print(f"   最大回撤: {buy_hold['max_drawdown']*100:.2f}%")

        print(f"\n🏆 策略优势:")
        excess_return = strategy['annual_return'] - buy_hold['annual_return']
        risk_reduction = buy_hold['volatility'] - strategy['volatility']
        dd_improvement = buy_hold['max_drawdown'] - strategy['max_drawdown']

        print(f"   超额年化收益: {excess_return*100:+.2f}%")
        print(f"   波动率变化: {risk_reduction*100:+.2f}%")
        print(f"   回撤改善: {dd_improvement*100:+.2f}%")
        print(f"   夏普比率提升: {strategy['sharpe_ratio'] - buy_hold['sharpe_ratio']:+.2f}")

    def analyze_trades(self):
        """分析交易详情"""
        print(f"\n📋 交易详情分析:")
        print("-" * 60)

        # 收益分布
        returns = [t['return'] * 100 for t in self.trades]
        print(f"收益分布:")
        print(f"   最大收益: {max(returns):+.2f}%")
        print(f"   最小收益: {min(returns):+.2f}%")
        print(f"   平均收益: {np.mean(returns):+.2f}%")
        print(f"   收益标准差: {np.std(returns):.2f}%")

        # 持有期分布
        hold_days = [t['hold_days'] for t in self.trades]
        print(f"\n持有期分布:")
        print(f"   最长持有: {max(hold_days)}天")
        print(f"   最短持有: {min(hold_days)}天")
        print(f"   平均持有: {np.mean(hold_days):.1f}天")
        print(f"   持有期标准差: {np.std(hold_days):.1f}天")

        # 年度交易统计
        yearly_trades = {}
        for trade in self.trades:
            year = trade['buy_date'].year
            if year not in yearly_trades:
                yearly_trades[year] = []
            yearly_trades[year].append(trade['return'])

        print(f"\n年度交易统计:")
        for year in sorted(yearly_trades.keys()):
            year_returns = yearly_trades[year]
            avg_return = np.mean(year_returns) * 100
            trade_count = len(year_returns)
            win_rate = sum(1 for r in year_returns if r > 0) / len(year_returns) * 100
            print(f"   {year}年: {trade_count}笔交易, 平均收益{avg_return:+.2f}%, 胜率{win_rate:.1f}%")

    def create_comprehensive_visualization(self):
        """创建完整的可视化分析"""
        print(f"\n📊 创建可视化分析...")

        fig, axes = plt.subplots(3, 2, figsize=(20, 18))
        fig.suptitle(f'511260百分位数{self.buy_threshold*100:.0f}%买入+{self.sell_threshold*100:.0f}%卖出策略回测分析',
                    fontsize=16, fontweight='bold')

        # 1. 价格走势与买卖点
        ax1 = axes[0, 0]
        ax1.plot(self.processed_data.index, self.processed_data['价格'],
                linewidth=1, alpha=0.8, color='blue', label='511260价格')
        ax1.plot(self.processed_data.index, self.processed_data['趋势成分'],
                linewidth=2, color='red', label='长期趋势')

        # 标记买卖点
        for trade in self.trades:
            ax1.scatter(trade['buy_date'], trade['buy_price'],
                       color='green', s=50, marker='^', alpha=0.8, zorder=5)
            ax1.scatter(trade['sell_date'], trade['sell_price'],
                       color='red', s=50, marker='v', alpha=0.8, zorder=5)

        # 添加图例
        ax1.scatter([], [], color='green', s=50, marker='^', label='买入点')
        ax1.scatter([], [], color='red', s=50, marker='v', label='卖出点')

        ax1.set_title('价格走势与交易点')
        ax1.set_ylabel('价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 百分位数指标
        ax2 = axes[0, 1]
        ax2.plot(self.processed_data.index, self.processed_data['百分位数'],
                color='purple', label='百分位数', linewidth=1)

        # 添加阈值线
        ax2.axhline(y=self.buy_threshold, color='red', linestyle='--', alpha=0.7,
                   label=f'买入阈值({self.buy_threshold*100:.0f}%)')
        ax2.axhline(y=self.sell_threshold, color='green', linestyle=':', alpha=0.7,
                   label=f'卖出阈值({self.sell_threshold*100:.0f}%)')

        ax2.set_title('百分位数买入信号')
        ax2.set_ylabel('百分位数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 累积收益对比
        ax3 = axes[1, 0]

        # 策略累积收益
        ax3.plot(self.cumulative_returns.index, (self.cumulative_returns - 1) * 100,
                linewidth=2, color='red', label=f'{self.buy_threshold*100:.0f}%买入+{self.sell_threshold*100:.0f}%卖出')

        # 买入持有基准
        buy_hold_returns = self.processed_data['价格'] / self.processed_data['价格'].iloc[0]
        ax3.plot(buy_hold_returns.index, (buy_hold_returns - 1) * 100,
                linewidth=2, color='blue', label='买入持有')

        ax3.set_title('累积收益对比')
        ax3.set_ylabel('累积收益率 (%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 回撤分析
        ax4 = axes[1, 1]

        # 策略回撤
        ax4.fill_between(self.drawdowns.index, self.drawdowns * 100, 0,
                        alpha=0.3, color='red', label='策略回撤')
        ax4.plot(self.drawdowns.index, self.drawdowns * 100,
                linewidth=1, color='red')

        # 买入持有回撤
        bh_returns = self.processed_data['价格'] / self.processed_data['价格'].iloc[0]
        bh_rolling_max = bh_returns.expanding().max()
        bh_drawdowns = (bh_returns - bh_rolling_max) / bh_rolling_max

        ax4.fill_between(bh_drawdowns.index, bh_drawdowns * 100, 0,
                        alpha=0.3, color='blue', label='买入持有回撤')
        ax4.plot(bh_drawdowns.index, bh_drawdowns * 100,
                linewidth=1, color='blue')

        ax4.set_title('回撤分析')
        ax4.set_ylabel('回撤 (%)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 交易收益分布
        ax5 = axes[2, 0]

        returns = [t['return'] * 100 for t in self.trades]
        ax5.hist(returns, bins=20, alpha=0.7, color='green', edgecolor='black')
        ax5.axvline(x=np.mean(returns), color='red', linestyle='--',
                   label=f'平均收益: {np.mean(returns):.2f}%')
        ax5.set_title('单次交易收益分布')
        ax5.set_xlabel('收益率 (%)')
        ax5.set_ylabel('频次')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. 年度交易次数
        ax6 = axes[2, 1]

        # 统计年度交易次数
        yearly_counts = {}
        for trade in self.trades:
            year = trade['buy_date'].year
            yearly_counts[year] = yearly_counts.get(year, 0) + 1

        years = list(yearly_counts.keys())
        counts = list(yearly_counts.values())

        ax6.bar(years, counts, alpha=0.7, color='orange', edgecolor='black')
        ax6.set_title('年度交易次数')
        ax6.set_xlabel('年份')
        ax6.set_ylabel('交易次数')
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        chart_path = f'data/percentile_{self.buy_threshold*100:.0f}_buy_{self.sell_threshold*100:.0f}_sell_backtest.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✅ 回测分析图表已保存: {chart_path}")

        plt.show()
        return chart_path

def main():
    """主函数"""
    print("🚀 511260百分位数对称策略回测启动")
    print("="*80)

    # 可以调整这些参数
    buy_threshold = 0.20  # 20%分位数买入
    sell_threshold = 0.80  # 80%分位数卖出

    print(f"策略参数：百分位数{buy_threshold*100:.0f}%买入 + {sell_threshold*100:.0f}%卖出")
    print("="*80)

    # 创建回测器实例
    backtest = PercentileSymmetricBacktest(buy_threshold, sell_threshold)

    # 加载数据
    if not backtest.load_data():
        return None

    # 数据处理
    if not backtest.process_data():
        return None

    # 生成信号
    if not backtest.generate_signals():
        return None

    # 执行策略
    if not backtest.execute_strategy():
        return None

    # 计算表现
    if not backtest.calculate_performance():
        return None

    # 打印结果
    backtest.print_performance_summary()
    backtest.analyze_trades()

    # 创建可视化
    backtest.create_comprehensive_visualization()

    print("\n" + "="*80)
    print("✅ 百分位数对称策略回测分析完成！")
    print("="*80)

    return backtest

if __name__ == "__main__":
    result = main()
