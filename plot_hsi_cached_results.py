#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plot HSI (Hang Seng Index) Relative Strength Results from Cached Data
恒生指数成分股相对强度分析和可视化
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
from hsi_cached_analyzer import CachedHSIAnalyzer

warnings.filterwarnings('ignore')

# 设置中文字体
import matplotlib
matplotlib.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'PingFang SC', 'Microsoft YaHei', 'DejaVu Sans']
matplotlib.rcParams['axes.unicode_minus'] = False
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'PingFang SC', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_hsi_stock_names():
    """加载HSI股票代码和名称的映射"""
    try:
        import pandas as pd
        # 指定代码列为字符串类型，避免自动转换为数字
        df = pd.read_csv('data_files/hsi_constituents.csv', dtype={'代码': str})

        # 创建代码到名称的映射
        stock_names = {}
        for _, row in df.iterrows():
            code_with_zero = str(row['代码']).strip()  # 带前导0的格式，如 "09999"
            code_without_zero = code_with_zero.lstrip('0')  # 去掉前导0的格式，如 "9999"
            name = str(row['名称']).strip()

            # 同时存储两种格式的映射，以确保兼容性
            stock_names[code_with_zero] = name      # "09999" -> "网易-S"
            stock_names[code_without_zero] = name   # "9999" -> "网易-S"

        print(f"✅ 已加载 {len(df)} 只港股的名称信息（双格式映射）")
        print(f"📋 示例映射: 09999->{stock_names.get('09999', 'N/A')}, 9999->{stock_names.get('9999', 'N/A')}")
        return stock_names
    except Exception as e:
        print(f"⚠️  加载港股名称失败: {e}")
        return {}

def plot_relative_strength_analysis():
    """创建恒生指数成分股相对强度综合可视化"""

    print("🎯 创建恒生指数成分股相对强度可视化（从缓存）")
    print("=" * 60)

    # 加载股票名称映射
    stock_names = load_hsi_stock_names()

    # 创建分析器并运行分析（将使用缓存）
    analyzer = CachedHSIAnalyzer(period="1y", benchmark="^HSI", cache_dir="hsi_cache")
    
    print("🔄 使用缓存数据运行分析...")
    start_time = datetime.now()
    
    if not analyzer.run_analysis(force_refresh=False):
        print("❌ 分析失败")
        return
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    print(f"✅ 分析完成，耗时 {duration:.1f} 秒（使用缓存）")
    
    # 创建综合可视化
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('恒生指数成分股相对强度分析 - 完整市场概览', 
                fontsize=16, fontweight='bold')
    
    # 1. 前15强势股相对强度线图（左上）
    ax1 = axes[0, 0]
    top_15 = analyzer.analysis_results.head(15)
    colors = plt.cm.tab20(np.linspace(0, 1, 15))
    
    for i, (_, stock) in enumerate(top_15.iterrows()):
        symbol = stock['symbol']
        if symbol in analyzer.relative_strength_data:
            rs_data = analyzer.relative_strength_data[symbol]
            # 获取股票名称，如果没有则使用代码
            stock_name = stock_names.get(symbol, symbol)
            display_name = f"{stock_name}({symbol})" if len(stock_name) <= 6 else f"{stock_name[:6]}({symbol})"
            ax1.plot(rs_data.index, rs_data.values,
                    label=f"{display_name} ({stock['strength_score']:.0f})",
                    color=colors[i], linewidth=2, alpha=0.8)
    
    ax1.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='恒指基准线')
    ax1.set_title('前15强势港股', fontsize=12, fontweight='bold')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('相对强度')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 2. 强度评分分布（中上）
    ax2 = axes[0, 1]
    scores = analyzer.analysis_results['strength_score']
    bins = [0, 30, 50, 70, 85, 100]
    labels = ['弱势\n(0-30)', '偏弱\n(30-50)', '中等\n(50-70)', '强势\n(70-85)', '很强\n(85-100)']
    colors_hist = ['red', 'orange', 'yellow', 'lightgreen', 'green']
    
    counts, _, patches = ax2.hist(scores, bins=bins, alpha=0.7, edgecolor='black')

    # 手动设置柱状图颜色
    for i, patch in enumerate(patches):
        patch.set_facecolor(colors_hist[i])
    ax2.set_title(f'强度分布\n({len(analyzer.analysis_results)} 只港股)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('强度评分')
    ax2.set_ylabel('股票数量')
    ax2.set_xticks([(bins[i] + bins[i+1])/2 for i in range(len(bins)-1)])
    ax2.set_xticklabels(labels, rotation=45, ha='right')
    
    for i, count in enumerate(counts):
        ax2.text((bins[i] + bins[i+1])/2, count + 0.5, f'{int(count)}', 
                ha='center', va='bottom', fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 相对恒指表现（右上）
    ax3 = axes[0, 2]
    above_hsi = len(analyzer.analysis_results[analyzer.analysis_results['current_rs'] > 100])
    below_hsi = len(analyzer.analysis_results) - above_hsi
    
    performance_data = [above_hsi, below_hsi]
    performance_labels = [f'跑赢恒指\n({above_hsi})', f'跑输恒指\n({below_hsi})']
    colors_pie = ['lightgreen', 'lightcoral']
    
    wedges, texts, autotexts = ax3.pie(performance_data, labels=performance_labels, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax3.set_title('相对恒指表现', fontsize=12, fontweight='bold')
    
    # 4. 上升股票柱状图（左下）
    ax4 = axes[1, 0]
    rising_stocks = analyzer.get_rising_stocks(min_score=60)
    
    if not rising_stocks.empty:
        top_rising = rising_stocks.head(12)
        bars = ax4.barh(range(len(top_rising)), top_rising['current_rs'], 
                       color=plt.cm.RdYlGn(top_rising['strength_score']/100))
        
        ax4.set_title(f'前12上升港股\n(共{len(rising_stocks)}只)', fontsize=12, fontweight='bold')
        ax4.set_xlabel('当前相对强度')
        ax4.set_ylabel('股票排名')
        ax4.set_yticks(range(len(top_rising)))
        # 创建包含股票名称的标签
        y_labels = []
        for i, symbol in enumerate(top_rising['symbol']):
            stock_name = stock_names.get(symbol, symbol)
            display_name = f"{stock_name}" if len(stock_name) <= 4 else f"{stock_name[:4]}"
            y_labels.append(f"{i+1}. {display_name}({symbol})")
        ax4.set_yticklabels(y_labels)
        
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax4.text(width + 2, bar.get_y() + bar.get_height()/2, 
                    f'{width:.0f}', ha='left', va='center', fontsize=8)
        
        ax4.axvline(x=100, color='red', linestyle='--', alpha=0.7, label='恒指基准线')
        ax4.legend()
        ax4.grid(True, alpha=0.3, axis='x')
    
    # 5. 相对强度vs变化散点图（中下）
    ax5 = axes[1, 1]
    top_50 = analyzer.analysis_results.head(50)
    scatter = ax5.scatter(top_50['current_rs'], top_50['total_change_pct'], 
                         c=top_50['strength_score'], cmap='RdYlGn', 
                         s=60, alpha=0.7, edgecolors='black')
    
    # 为前10添加标签
    for _, stock in top_50.head(10).iterrows():
        symbol = stock['symbol']
        stock_name = stock_names.get(symbol, symbol)
        display_name = f"{stock_name}" if len(stock_name) <= 4 else f"{stock_name[:4]}"
        ax5.annotate(f"{display_name}",
                   (stock['current_rs'], stock['total_change_pct']),
                   xytext=(3, 3), textcoords='offset points',
                   fontsize=8, alpha=0.8)
    
    ax5.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax5.axvline(x=100, color='red', linestyle='--', alpha=0.5)
    ax5.set_title('当前相对强度 vs 总变化\n(前50只港股)', fontsize=12, fontweight='bold')
    ax5.set_xlabel('当前相对强度')
    ax5.set_ylabel('总变化 (%)')
    
    cbar = plt.colorbar(scatter, ax=ax5)
    cbar.set_label('强度评分')
    ax5.grid(True, alpha=0.3)
    
    # 6. 市场统计（右下）
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    # 计算统计数据
    total_stocks = len(analyzer.analysis_results)
    high_strength = len(analyzer.analysis_results[analyzer.analysis_results['strength_score'] >= 70])
    rising_count = len(rising_stocks)
    
    # 定义行业分类
    sector_mapping = {
        '700': '科技', '9988': '科技', '3690': '科技', '1810': '科技', 
        '9618': '科技', '1024': '科技', '9888': '科技', '981': '科技',
        '939': '金融', '1398': '金融', '3988': '金融', '2318': '金融', 
        '1299': '金融', '5': '金融', '388': '金融',
        '883': '能源', '857': '能源', '386': '能源', '1088': '能源'
    }
    
    # 计算科技股表现
    tech_stocks = analyzer.analysis_results[analyzer.analysis_results['symbol'].isin(['700', '9988', '3690', '1810', '9618', '1024', '9888', '981'])]
    tech_avg_score = tech_stocks['strength_score'].mean() if not tech_stocks.empty else 0
    
    stats_text = f"""
📊 市场统计

总分析港股数: {total_stocks}
分析周期: 1年
基准: 恒生指数

🏆 表现分析:
• 跑赢恒指: {above_hsi} ({above_hsi/total_stocks*100:.1f}%)
• 跑输恒指: {below_hsi} ({below_hsi/total_stocks*100:.1f}%)

💪 强度分析:
• 高强度(≥70): {high_strength} ({high_strength/total_stocks*100:.1f}%)
• 中等强度(50-69): {len(analyzer.analysis_results[(analyzer.analysis_results['strength_score'] >= 50) & (analyzer.analysis_results['strength_score'] < 70)])} 只
• 低强度(<50): {len(analyzer.analysis_results[analyzer.analysis_results['strength_score'] < 50])} 只

📈 上升股票:
• 持续上升: {rising_count} ({rising_count/total_stocks*100:.1f}%)

🔝 最强表现:
• {stock_names.get(analyzer.analysis_results.iloc[0]['symbol'], analyzer.analysis_results.iloc[0]['symbol'])}({analyzer.analysis_results.iloc[0]['symbol']}): 相对强度 {analyzer.analysis_results.iloc[0]['current_rs']:.1f}

🏭 科技股平均评分: {tech_avg_score:.1f}
"""
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=10,
            verticalalignment='top',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存可视化
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hsi_relative_strength_complete_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📁 综合可视化已保存至: {filename}")

    # 关闭图表以避免阻塞
    plt.close()

    # 创建顶级表现者详细图表
    create_top_performers_detail(analyzer, stock_names)

def create_top_performers_detail(analyzer, stock_names):
    """为顶级表现港股创建详细图表"""

    print("\n📊 为顶级表现港股创建详细图表...")

    # 获取前9名表现者用于3x3网格
    top_performers = analyzer.analysis_results.head(9)
    
    fig, axes = plt.subplots(3, 3, figsize=(18, 12))
    fig.suptitle('前9强势港股 - 详细相对强度分析', 
                fontsize=16, fontweight='bold')
    
    for i, (_, stock) in enumerate(top_performers.iterrows()):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        symbol = stock['symbol']
        if symbol in analyzer.relative_strength_data:
            rs_data = analyzer.relative_strength_data[symbol]
            
            # 绘制相对强度线
            ax.plot(rs_data.index, rs_data.values, 'b-', linewidth=2, label='相对强度')
            
            # 添加移动平均线
            if len(rs_data) >= 20:
                ma20 = rs_data.rolling(20).mean()
                ax.plot(ma20.index, ma20.values, 'orange', linewidth=1, alpha=0.8, label='MA20')
            
            if len(rs_data) >= 50:
                ma50 = rs_data.rolling(50).mean()
                ax.plot(ma50.index, ma50.values, 'red', linewidth=1, alpha=0.8, label='MA50')
            
            # 添加基准线
            ax.axhline(y=100, color='gray', linestyle='--', alpha=0.7, label='恒指基准线')
            
            # 标题包含关键指标和股票名称
            stock_name = stock_names.get(symbol, symbol)
            display_name = f"{stock_name}" if len(stock_name) <= 6 else f"{stock_name[:6]}"
            title = f"{display_name}({symbol})\n评分: {stock['strength_score']:.0f} | 相对强度: {stock['current_rs']:.1f} | 变化: {stock['total_change_pct']:.1f}%"
            ax.set_title(title, fontsize=11, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('相对强度')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存顶级表现者图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hsi_top_performers_detail_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📁 顶级表现者详细图表已保存至: {filename}")

    # 关闭图表以避免阻塞
    plt.close()

def main():
    """主函数"""
    print("🎯 恒生指数成分股相对强度可视化（从缓存）")
    print("=" * 60)

    # 创建可视化
    plot_relative_strength_analysis()

    print("\n✅ 可视化完成!")
    print("📁 请查看生成的PNG文件获取详细图表。")

if __name__ == "__main__":
    main()
