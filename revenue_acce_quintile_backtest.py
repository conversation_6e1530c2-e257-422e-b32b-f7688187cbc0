#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
净利润增长加速度因子5分组季度调仓回测
回测时间：最近10年（2015-2024）
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
import os
try:
    from scipy.stats import spearmanr
except ImportError:
    print("⚠️ scipy未安装，将跳过秩相关系数计算")
    spearmanr = None
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class QuintileBacktest:
    """5分组回测分析类"""
    
    def __init__(self, 
                 financial_db_path: str = "ganggutong_financial_data.db",
                 price_db_path: str = "ganggutong_10year_data.db"):
        self.financial_db_path = financial_db_path
        self.price_db_path = price_db_path
        self.financial_data = None
        self.price_data = {}
        self.factor_data = None
        
    def load_data(self):
        """加载数据"""
        print("📊 加载数据...")
        
        # 加载财务数据
        conn = sqlite3.connect(self.financial_db_path)
        query = """
        SELECT stock_code, report_date, period_type, revenue
        FROM financial_data 
        WHERE revenue IS NOT NULL 
        ORDER BY stock_code, report_date
        """
        self.financial_data = pd.read_sql_query(query, conn)
        conn.close()
        
        self.financial_data['report_date'] = pd.to_datetime(self.financial_data['report_date'])
        self.financial_data['revenue'] = pd.to_numeric(self.financial_data['revenue'], errors='coerce')
        
        # 加载价格数据并转换为字典结构
        conn = sqlite3.connect(self.price_db_path)
        query = """
        SELECT stock_code, date, close
        FROM stock_prices 
        WHERE close IS NOT NULL 
        ORDER BY stock_code, date
        """
        price_df = pd.read_sql_query(query, conn)
        conn.close()
        
        price_df['date'] = pd.to_datetime(price_df['date'])
        price_df['close'] = pd.to_numeric(price_df['close'], errors='coerce')
        
        # 转换为字典结构以提高查询效率
        print("🔄 优化价格数据结构...")
        for stock_code in price_df['stock_code'].unique():
            stock_prices = price_df[price_df['stock_code'] == stock_code].copy()
            stock_prices = stock_prices.set_index('date').sort_index()
            self.price_data[stock_code] = stock_prices['close']
        
        print(f"✅ 财务数据: {len(self.financial_data)} 条")
        print(f"✅ 价格数据: {len(self.price_data)} 只股票")
        
    def calculate_factors(self):
        """计算营收增长加速度因子"""
        print("🧮 计算营收增长加速度因子...")
        
        factor_data = []
        
        for stock_code in self.financial_data['stock_code'].unique():
            stock_data = self.financial_data[
                self.financial_data['stock_code'] == stock_code
            ].copy().sort_values('report_date')
            
            # 分别处理季度和年度数据
            for period_type in ['quarterly', 'annual']:
                period_data = stock_data[
                    stock_data['period_type'] == period_type
                ].copy()
                
                if len(period_data) < 6:
                    continue
                
                # 计算营收增长率
                if period_type == 'quarterly':
                    period_data['revenue_yoy'] = period_data['revenue'].pct_change(4) * 100
                else:
                    period_data['revenue_yoy'] = period_data['revenue'].pct_change(1) * 100
                
                # 计算营收增长加速度
                period_data['revenue_acceleration'] = period_data['revenue_yoy'].diff()
                
                factor_data.append(period_data)
        
        if factor_data:
            self.factor_data = pd.concat(factor_data, ignore_index=True)
            self.factor_data = self.factor_data.dropna(subset=['revenue_acceleration'])
            print(f"✅ 因子数据: {len(self.factor_data)} 条")
        else:
            raise ValueError("无法计算因子数据")
    
    def generate_rebalance_dates(self, start_date: str, end_date: str, freq: str = 'Q'):
        """生成调仓日期

        Args:
            start_date: 开始日期
            end_date: 结束日期
            freq: 调仓频率，'Q'为季度，'M'为月度
        """
        dates = pd.date_range(start=start_date, end=end_date, freq=freq)
        return [d.date() for d in dates]
    
    def get_factor_scores_at_date(self, target_date: pd.Timestamp, lookback_days: int = 90):
        """获取指定日期的因子得分"""
        start_date = target_date - timedelta(days=lookback_days)
        
        recent_data = self.factor_data[
            (self.factor_data['report_date'] >= start_date) & 
            (self.factor_data['report_date'] <= target_date)
        ].copy()
        
        if recent_data.empty:
            return pd.DataFrame()
        
        # 对每只股票，取最新的财务数据
        latest_data = recent_data.groupby('stock_code').last().reset_index()
        
        return latest_data
    
    def get_prices_at_date(self, target_date: pd.Timestamp, stock_list: List[str]):
        """获取指定日期的股票价格"""
        target_prices = {}
        
        for stock_code in stock_list:
            if stock_code in self.price_data:
                stock_prices = self.price_data[stock_code]
                valid_prices = stock_prices[stock_prices.index <= target_date]
                
                if not valid_prices.empty:
                    latest_price = valid_prices.iloc[-1]
                    target_prices[stock_code] = latest_price
        
        return target_prices
    
    def run_quintile_backtest(self,
                            start_date: str = "2020-01-01",
                            end_date: str = "2025-06-08",
                            rebalance_freq: str = 'Q'):
        """运行5分组调仓回测

        Args:
            start_date: 开始日期
            end_date: 结束日期
            rebalance_freq: 调仓频率，'Q'为季度，'M'为月度
        """
        freq_name = "季度" if rebalance_freq == 'Q' else "月度"
        print(f"🚀 开始5分组{freq_name}调仓回测: {start_date} 到 {end_date}")

        # 生成调仓日期
        rebalance_dates = self.generate_rebalance_dates(start_date, end_date, rebalance_freq)
        print(f"📅 调仓日期: {len(rebalance_dates)} 次")
        
        portfolio_returns = []
        
        for i, rebal_date in enumerate(rebalance_dates[:-1]):
            next_rebal_date = rebalance_dates[i + 1]
            
            print(f"\n📅 调仓日期: {rebal_date}")
            
            # 获取因子得分
            target_date = pd.Timestamp(rebal_date)
            factor_scores = self.get_factor_scores_at_date(target_date)
            
            if len(factor_scores) < 25:  # 至少需要25只股票才能分5组
                print(f"⚠️ 可用股票数量不足: {len(factor_scores)}")
                continue
            
            # 按营收增长加速度分为5组（Q1为最高，Q5为最低）
            try:
                factor_scores['quintile'] = pd.qcut(
                    factor_scores['revenue_acceleration'],
                    5,
                    labels=['Q5', 'Q4', 'Q3', 'Q2', 'Q1'],  # 反转标签，Q1为最高
                    duplicates='drop'
                )
            except ValueError as e:
                print(f"⚠️ 分组失败: {e}")
                continue
            
            # 获取各组股票
            quintile_stocks = {}
            for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
                stocks = factor_scores[factor_scores['quintile'] == quintile]['stock_code'].tolist()
                quintile_stocks[quintile] = stocks
            
            print(f"📊 分组情况:")
            for quintile, stocks in quintile_stocks.items():
                print(f"  {quintile}: {len(stocks)} 只股票")
            
            # 获取期初和期末价格
            all_stocks = []
            for stocks in quintile_stocks.values():
                all_stocks.extend(stocks)
            
            start_prices = self.get_prices_at_date(target_date, all_stocks)
            end_date_ts = pd.Timestamp(next_rebal_date)
            end_prices = self.get_prices_at_date(end_date_ts, all_stocks)
            
            # 计算各组收益率
            quintile_returns = {}
            
            for quintile, stocks in quintile_stocks.items():
                returns = []
                for stock in stocks:
                    if stock in start_prices and stock in end_prices:
                        ret = (end_prices[stock] / start_prices[stock] - 1) * 100
                        returns.append(ret)
                
                if returns:
                    quintile_returns[quintile] = np.mean(returns)
                    print(f"📊 {quintile}组收益: {quintile_returns[quintile]:.2f}%")
                else:
                    quintile_returns[quintile] = np.nan
            
            # 计算多空收益（Q1 - Q5，Q1为最高组）
            if 'Q1' in quintile_returns and 'Q5' in quintile_returns:
                long_short_return = quintile_returns['Q1'] - quintile_returns['Q5']
                print(f"💰 多空收益(Q1-Q5): {long_short_return:.2f}%")
            else:
                long_short_return = np.nan
            
            # 保存结果
            result = {
                'rebalance_date': rebal_date,
                'next_rebalance_date': next_rebal_date,
                'long_short_return': long_short_return
            }
            
            # 添加各组收益
            for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
                result[f'{quintile}_return'] = quintile_returns.get(quintile, np.nan)
                result[f'{quintile}_n_stocks'] = len(quintile_stocks.get(quintile, []))
            
            portfolio_returns.append(result)
        
        return pd.DataFrame(portfolio_returns)
    
    def analyze_results(self, results_df: pd.DataFrame):
        """分析5分组回测结果"""
        print("\n" + "="*80)
        print("📊 营收增长加速度因子5分组回测结果分析")
        print("="*80)
        
        if results_df.empty:
            print("❌ 无有效回测结果")
            return
        
        # 基本统计
        print(f"📅 回测期间: {results_df['rebalance_date'].min()} 到 {results_df['next_rebalance_date'].max()}")
        print(f"🔄 调仓次数: {len(results_df)}")
        
        # 各组平均收益
        print(f"\n📊 各组平均收益率:")
        for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
            col = f'{quintile}_return'
            if col in results_df.columns:
                avg_return = results_df[col].mean()
                print(f"  {quintile}组 (增长加速度{'最高' if quintile=='Q1' else '最低' if quintile=='Q5' else '中等'}): {avg_return:.2f}%")
        
        # 多空组合分析
        if 'long_short_return' in results_df.columns:
            ls_returns = results_df['long_short_return'].dropna()
            if not ls_returns.empty:
                print(f"\n💰 多空组合(Q1-Q5)分析:")
                print(f"  平均收益: {ls_returns.mean():.2f}%")
                print(f"  累计收益: {(1 + ls_returns / 100).prod() - 1:.2%}")
                print(f"  胜率: {(ls_returns > 0).mean():.1%}")
                print(f"  最大单期收益: {ls_returns.max():.2f}%")
                print(f"  最大单期亏损: {ls_returns.min():.2f}%")
                
                # 年化指标（根据调仓频率调整）
                # 从结果中推断调仓频率
                if len(ls_returns) > 0:
                    # 计算平均调仓间隔天数
                    date_diffs = pd.to_datetime(results_df['next_rebalance_date']) - pd.to_datetime(results_df['rebalance_date'])
                    avg_days = date_diffs.dt.days.mean()

                    if avg_days <= 35:  # 月度调仓
                        periods_per_year = 12
                    else:  # 季度调仓
                        periods_per_year = 4
                else:
                    periods_per_year = 4  # 默认季度

                total_periods = len(ls_returns)
                years = total_periods / periods_per_year

                cumulative_return = (1 + ls_returns / 100).prod()
                annualized_return = (cumulative_return ** (1/years) - 1) * 100
                annualized_vol = ls_returns.std() * np.sqrt(periods_per_year)
                
                print(f"  年化收益: {annualized_return:.2f}%")
                print(f"  年化波动: {annualized_vol:.2f}%")
                
                if annualized_vol > 0:
                    sharpe_ratio = annualized_return / annualized_vol
                    print(f"  夏普比率: {sharpe_ratio:.2f}")
        
        # 单调性分析
        print(f"\n📈 单调性分析:")
        quintile_means = []
        for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
            col = f'{quintile}_return'
            if col in results_df.columns:
                quintile_means.append(results_df[col].mean())
        
        if len(quintile_means) == 5:
            # 计算秩相关系数（现在期望递减趋势，Q1最高到Q5最低）
            if spearmanr is not None:
                ranks = [5, 4, 3, 2, 1]  # 反转期望排序，Q1应该最高
                rank_corr, p_value = spearmanr(ranks, quintile_means)
                print(f"  秩相关系数: {rank_corr:.4f} (p值: {p_value:.4f})")
            else:
                # 简单的单调性检查（期望递减）
                diffs = np.diff(quintile_means)
                rank_corr = np.sum(diffs < 0) / len(diffs)  # 期望递减
                print(f"  单调性比例: {rank_corr:.1%} (scipy未安装，使用简化计算)")

            # 计算单调性比例（期望递减）
            diffs = np.diff(quintile_means)
            monotonic_count = np.sum(diffs < 0)  # 期望递减
            monotonic_ratio = monotonic_count / len(diffs)
            print(f"  单调性比例: {monotonic_ratio:.1%}")

            # 评价单调性
            if spearmanr is not None:
                abs_corr = abs(rank_corr)
            else:
                abs_corr = rank_corr

            if abs_corr > 0.8:
                monotonicity_rating = "优秀"
            elif abs_corr > 0.6:
                monotonicity_rating = "良好"
            elif abs_corr > 0.3:
                monotonicity_rating = "一般"
            else:
                monotonicity_rating = "较差"

            print(f"  单调性评级: {monotonicity_rating}")
        
        return results_df
    
    def create_visualizations(self, results_df: pd.DataFrame):
        """创建可视化图表"""
        if results_df.empty:
            return
        
        print("📊 创建可视化图表...")
        
        output_dir = "quintile_backtest_results"
        os.makedirs(output_dir, exist_ok=True)
        
        # 1. 各组累计收益曲线
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        
        # 计算累计收益
        ax1 = axes[0, 0]
        for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
            col = f'{quintile}_return'
            if col in results_df.columns:
                cumulative = (1 + results_df[col].fillna(0) / 100).cumprod()
                ax1.plot(results_df['rebalance_date'], cumulative, 
                        linewidth=2, label=f'{quintile}组', marker='o')
        
        ax1.set_title('各组累计收益曲线')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('累计收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 2. 各组平均收益柱状图
        ax2 = axes[0, 1]
        quintile_means = []
        quintile_labels = []
        
        for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
            col = f'{quintile}_return'
            if col in results_df.columns:
                quintile_means.append(results_df[col].mean())
                quintile_labels.append(quintile)
        
        bars = ax2.bar(quintile_labels, quintile_means, 
                      alpha=0.7, color=['red', 'orange', 'yellow', 'lightgreen', 'green'])
        
        # 标注数值
        for bar, mean_val in zip(bars, quintile_means):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height,
                    f'{mean_val:.2f}%', ha='center', va='bottom')
        
        ax2.set_title('各组平均收益率')
        ax2.set_xlabel('分组')
        ax2.set_ylabel('平均收益率 (%)')
        ax2.grid(True, alpha=0.3)
        
        # 3. 多空收益时间序列
        ax3 = axes[1, 0]
        if 'long_short_return' in results_df.columns:
            ls_returns = results_df['long_short_return'].dropna()
            ax3.plot(results_df['rebalance_date'][:len(ls_returns)], ls_returns,
                    linewidth=2, color='purple', marker='o')
            ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
            ax3.axhline(y=ls_returns.mean(), color='red', linestyle='-', alpha=0.7,
                       label=f'均值: {ls_returns.mean():.2f}%')

            ax3.set_title('多空收益时间序列 (Q1-Q5)')
            ax3.set_xlabel('日期')
            ax3.set_ylabel('收益率 (%)')
            ax3.legend()
            ax3.grid(True, alpha=0.3)
        
        # 4. 多空收益分布
        ax4 = axes[1, 1]
        if 'long_short_return' in results_df.columns:
            ls_returns = results_df['long_short_return'].dropna()
            ax4.hist(ls_returns, bins=15, alpha=0.7, edgecolor='black', color='skyblue')
            ax4.axvline(x=ls_returns.mean(), color='red', linestyle='--',
                       label=f'均值: {ls_returns.mean():.2f}%')
            ax4.axvline(x=0, color='black', linestyle='-', alpha=0.5)
            
            ax4.set_title('多空收益分布')
            ax4.set_xlabel('收益率 (%)')
            ax4.set_ylabel('频数')
            ax4.legend()
            ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(f'{output_dir}/quintile_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"✅ 图表已保存到 {output_dir}/quintile_analysis.png")
    
    def get_latest_q1_stocks(self, target_date: str = None, lookback_days: int = 90):
        """获取最新的Q1组股票（营收增长加速度最高组）

        Args:
            target_date: 目标日期，格式为'YYYY-MM-DD'，默认为当前日期
            lookback_days: 回看天数，用于获取最新财务数据

        Returns:
            pd.DataFrame: Q1组股票信息，包含股票代码、因子值等
        """
        print("🔍 获取最新Q1组股票（营收增长加速度最高组）...")

        # 如果没有加载数据，先加载
        if self.factor_data is None:
            self.load_data()
            self.calculate_factors()

        # 设置目标日期
        if target_date is None:
            target_date = pd.Timestamp.now()
        else:
            target_date = pd.Timestamp(target_date)

        print(f"📅 目标日期: {target_date.strftime('%Y-%m-%d')}")

        # 获取最新因子得分
        factor_scores = self.get_factor_scores_at_date(target_date, lookback_days)

        if factor_scores.empty:
            print("❌ 未找到有效的因子数据")
            return pd.DataFrame()

        print(f"📊 可用股票数量: {len(factor_scores)}")

        if len(factor_scores) < 25:
            print("⚠️ 可用股票数量不足25只，无法进行5分组")
            return pd.DataFrame()

        # 按营收增长加速度分为5组（Q1为最高，Q5为最低）
        try:
            factor_scores['quintile'] = pd.qcut(
                factor_scores['revenue_acceleration'],
                5,
                labels=['Q5', 'Q4', 'Q3', 'Q2', 'Q1'],  # 反转标签，Q1为最高
                duplicates='drop'
            )
        except ValueError as e:
            print(f"⚠️ 分组失败: {e}")
            return pd.DataFrame()

        # 获取Q1组股票
        q1_stocks = factor_scores[factor_scores['quintile'] == 'Q1'].copy()
        q1_stocks = q1_stocks.sort_values('revenue_acceleration', ascending=False)

        print(f"\n🏆 Q1组股票（营收增长加速度最高组）:")
        print(f"  总数: {len(q1_stocks)} 只股票")
        print(f"{'股票代码':<12} {'营收增长加速度':<15} {'报告日期':<12} {'营收增长率':<12}")
        print("-" * 60)

        for _, row in q1_stocks.head(15).iterrows():
            revenue_yoy = row.get('revenue_yoy', 'N/A')
            revenue_yoy_str = f"{revenue_yoy:.2f}%" if isinstance(revenue_yoy, (int, float)) else str(revenue_yoy)
            print(f"{row['stock_code']:<12} {row['revenue_acceleration']:<15.2f} {row['report_date'].strftime('%Y-%m-%d'):<12} {revenue_yoy_str:<12}")

        if len(q1_stocks) > 15:
            print(f"... 还有 {len(q1_stocks) - 15} 只股票")

        # 保存Q1组股票到文件
        output_dir = "latest_q1_stocks"
        os.makedirs(output_dir, exist_ok=True)

        filename = f"q1_stocks_{target_date.strftime('%Y%m%d')}.csv"
        filepath = os.path.join(output_dir, filename)

        # 准备保存的数据
        save_data = q1_stocks[['stock_code', 'revenue_acceleration', 'report_date', 'revenue_yoy', 'revenue', 'period_type']].copy()
        save_data.to_csv(filepath, index=False, encoding='utf-8-sig')

        print(f"\n✅ Q1组股票已保存到: {filepath}")

        return q1_stocks

    def run_full_analysis(self, rebalance_freq: str = 'Q'):
        """运行完整的5分组分析

        Args:
            rebalance_freq: 调仓频率，'Q'为季度，'M'为月度
        """
        freq_name = "季度" if rebalance_freq == 'Q' else "月度"
        print(f"🚀 开始营收增长加速度因子5分组{freq_name}调仓回测分析...")

        # 1. 加载数据
        self.load_data()

        # 2. 计算因子
        self.calculate_factors()

        # 3. 运行回测
        results = self.run_quintile_backtest(rebalance_freq=rebalance_freq)

        # 4. 分析结果
        self.analyze_results(results)

        # 5. 创建可视化
        self.create_visualizations(results)

        # 6. 保存结果
        if not results.empty:
            output_dir = "quintile_backtest_results"
            os.makedirs(output_dir, exist_ok=True)
            filename = f'quintile_backtest_data_{freq_name}.csv'
            results.to_csv(f'{output_dir}/{filename}', index=False)
            print(f"\n✅ 数据已保存到 {output_dir}/{filename}")

        print(f"🎉 5分组{freq_name}调仓回测分析完成！")

        return results


class RebalanceFrequencyComparison:
    """调仓频率对比分析类"""

    def __init__(self,
                 financial_db_path: str = "ganggutong_financial_data.db",
                 price_db_path: str = "ganggutong_10year_data.db"):
        self.financial_db_path = financial_db_path
        self.price_db_path = price_db_path
        self.quarterly_results = None
        self.monthly_results = None

    def run_comparison_analysis(self):
        """运行调仓频率对比分析"""
        print("🔄 开始调仓频率对比分析...")
        print("="*80)

        # 创建回测实例
        backtest = QuintileBacktest(self.financial_db_path, self.price_db_path)

        # 加载数据和计算因子（只需要做一次）
        backtest.load_data()
        backtest.calculate_factors()

        # 1. 季度调仓回测
        print("\n📊 第一部分：季度调仓回测")
        print("-" * 50)
        self.quarterly_results = backtest.run_quintile_backtest(rebalance_freq='Q')
        quarterly_analysis = backtest.analyze_results(self.quarterly_results)

        # 2. 月度调仓回测
        print("\n📊 第二部分：月度调仓回测")
        print("-" * 50)
        self.monthly_results = backtest.run_quintile_backtest(rebalance_freq='M')
        monthly_analysis = backtest.analyze_results(self.monthly_results)

        # 3. 对比分析
        print("\n📊 第三部分：调仓频率对比分析")
        print("-" * 50)
        self.compare_results()

        # 4. 创建对比可视化
        self.create_comparison_visualizations()

        # 5. 保存结果
        self.save_comparison_results()

        print("\n🎉 调仓频率对比分析完成！")

        return {
            'quarterly': self.quarterly_results,
            'monthly': self.monthly_results
        }

    def compare_results(self):
        """对比两种调仓频率的结果"""
        if self.quarterly_results is None or self.monthly_results is None:
            print("❌ 缺少回测结果数据")
            return

        print("📊 调仓频率对比分析")
        print("="*60)

        # 提取多空收益数据
        q_ls = self.quarterly_results['long_short_return'].dropna()
        m_ls = self.monthly_results['long_short_return'].dropna()

        if q_ls.empty or m_ls.empty:
            print("❌ 缺少有效的多空收益数据")
            return

        # 基本统计对比
        print(f"\n📅 回测期间对比:")
        print(f"  季度调仓: {len(q_ls)} 次调仓")
        print(f"  月度调仓: {len(m_ls)} 次调仓")

        # 收益指标对比
        print(f"\n💰 收益指标对比:")

        # 平均收益
        q_mean = q_ls.mean()
        m_mean = m_ls.mean()
        print(f"  平均单期收益:")
        print(f"    季度调仓: {q_mean:.2f}%")
        print(f"    月度调仓: {m_mean:.2f}%")

        # 累计收益
        q_cumulative = (1 + q_ls / 100).prod() - 1
        m_cumulative = (1 + m_ls / 100).prod() - 1
        print(f"  累计收益:")
        print(f"    季度调仓: {q_cumulative:.2%}")
        print(f"    月度调仓: {m_cumulative:.2%}")

        # 年化收益
        q_periods_per_year = 4
        m_periods_per_year = 12

        q_years = len(q_ls) / q_periods_per_year
        m_years = len(m_ls) / m_periods_per_year

        q_annualized = ((1 + q_cumulative) ** (1/q_years) - 1) * 100
        m_annualized = ((1 + m_cumulative) ** (1/m_years) - 1) * 100

        print(f"  年化收益:")
        print(f"    季度调仓: {q_annualized:.2f}%")
        print(f"    月度调仓: {m_annualized:.2f}%")

        # 风险指标对比
        print(f"\n📊 风险指标对比:")

        # 波动率
        q_vol = q_ls.std() * np.sqrt(q_periods_per_year)
        m_vol = m_ls.std() * np.sqrt(m_periods_per_year)
        print(f"  年化波动率:")
        print(f"    季度调仓: {q_vol:.2f}%")
        print(f"    月度调仓: {m_vol:.2f}%")

        # 夏普比率
        q_sharpe = q_annualized / q_vol if q_vol > 0 else 0
        m_sharpe = m_annualized / m_vol if m_vol > 0 else 0
        print(f"  夏普比率:")
        print(f"    季度调仓: {q_sharpe:.2f}")
        print(f"    月度调仓: {m_sharpe:.2f}")

        # 胜率
        q_win_rate = (q_ls > 0).mean()
        m_win_rate = (m_ls > 0).mean()
        print(f"  胜率:")
        print(f"    季度调仓: {q_win_rate:.1%}")
        print(f"    月度调仓: {m_win_rate:.1%}")

        # 最大单期收益/亏损
        print(f"  最大单期收益:")
        print(f"    季度调仓: {q_ls.max():.2f}%")
        print(f"    月度调仓: {m_ls.max():.2f}%")

        print(f"  最大单期亏损:")
        print(f"    季度调仓: {q_ls.min():.2f}%")
        print(f"    月度调仓: {m_ls.min():.2f}%")

        # 总结
        print(f"\n📝 对比总结:")
        better_return = "月度调仓" if m_annualized > q_annualized else "季度调仓"
        better_sharpe = "月度调仓" if m_sharpe > q_sharpe else "季度调仓"
        better_winrate = "月度调仓" if m_win_rate > q_win_rate else "季度调仓"

        print(f"  年化收益更高: {better_return}")
        print(f"  夏普比率更高: {better_sharpe}")
        print(f"  胜率更高: {better_winrate}")

        # 调仓成本影响分析
        print(f"\n💸 调仓成本影响分析:")
        print(f"  假设单次调仓成本为0.2%:")

        # 假设调仓成本
        transaction_cost = 0.002  # 0.2%

        q_net_return = q_annualized - (q_periods_per_year * transaction_cost * 100)
        m_net_return = m_annualized - (m_periods_per_year * transaction_cost * 100)

        print(f"    季度调仓净收益: {q_net_return:.2f}%")
        print(f"    月度调仓净收益: {m_net_return:.2f}%")

        net_better = "月度调仓" if m_net_return > q_net_return else "季度调仓"
        print(f"    考虑成本后更优: {net_better}")

    def create_comparison_visualizations(self):
        """创建对比可视化图表"""
        if self.quarterly_results is None or self.monthly_results is None:
            return

        print("📊 创建对比可视化图表...")

        output_dir = "rebalance_frequency_comparison"
        os.makedirs(output_dir, exist_ok=True)

        # 提取多空收益数据
        q_ls = self.quarterly_results['long_short_return'].dropna()
        m_ls = self.monthly_results['long_short_return'].dropna()

        if q_ls.empty or m_ls.empty:
            print("❌ 缺少有效的多空收益数据")
            return

        # 创建对比图表
        fig, axes = plt.subplots(2, 3, figsize=(20, 12))

        # 1. 累计收益对比
        ax1 = axes[0, 0]

        # 季度调仓累计收益
        q_cumulative = (1 + q_ls / 100).cumprod()
        q_dates = pd.to_datetime(self.quarterly_results['rebalance_date'][:len(q_ls)])
        ax1.plot(q_dates, q_cumulative, linewidth=2, label='季度调仓', color='blue', marker='o')

        # 月度调仓累计收益
        m_cumulative = (1 + m_ls / 100).cumprod()
        m_dates = pd.to_datetime(self.monthly_results['rebalance_date'][:len(m_ls)])
        ax1.plot(m_dates, m_cumulative, linewidth=2, label='月度调仓', color='red', marker='s', markersize=4)

        ax1.set_title('累计收益对比 (多空组合 Q1-Q5)')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('累计收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 收益分布对比
        ax2 = axes[0, 1]
        ax2.hist(q_ls, bins=15, alpha=0.6, label='季度调仓', color='blue', edgecolor='black')
        ax2.hist(m_ls, bins=15, alpha=0.6, label='月度调仓', color='red', edgecolor='black')
        ax2.axvline(x=q_ls.mean(), color='blue', linestyle='--', label=f'季度均值: {q_ls.mean():.2f}%')
        ax2.axvline(x=m_ls.mean(), color='red', linestyle='--', label=f'月度均值: {m_ls.mean():.2f}%')
        ax2.set_title('收益分布对比')
        ax2.set_xlabel('收益率 (%)')
        ax2.set_ylabel('频数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 关键指标对比柱状图
        ax3 = axes[0, 2]

        # 计算关键指标
        q_periods_per_year = 4
        m_periods_per_year = 12

        q_years = len(q_ls) / q_periods_per_year
        m_years = len(m_ls) / m_periods_per_year

        q_cumulative_total = (1 + q_ls / 100).prod() - 1
        m_cumulative_total = (1 + m_ls / 100).prod() - 1

        q_annualized = ((1 + q_cumulative_total) ** (1/q_years) - 1) * 100
        m_annualized = ((1 + m_cumulative_total) ** (1/m_years) - 1) * 100

        q_vol = q_ls.std() * np.sqrt(q_periods_per_year)
        m_vol = m_ls.std() * np.sqrt(m_periods_per_year)

        q_sharpe = q_annualized / q_vol if q_vol > 0 else 0
        m_sharpe = m_annualized / m_vol if m_vol > 0 else 0

        q_win_rate = (q_ls > 0).mean() * 100
        m_win_rate = (m_ls > 0).mean() * 100

        metrics = ['年化收益(%)', '年化波动(%)', '夏普比率', '胜率(%)']
        q_values = [q_annualized, q_vol, q_sharpe, q_win_rate]
        m_values = [m_annualized, m_vol, m_sharpe, m_win_rate]

        x = np.arange(len(metrics))
        width = 0.35

        bars1 = ax3.bar(x - width/2, q_values, width, label='季度调仓', color='blue', alpha=0.7)
        bars2 = ax3.bar(x + width/2, m_values, width, label='月度调仓', color='red', alpha=0.7)

        # 标注数值
        for bars in [bars1, bars2]:
            for bar in bars:
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., height,
                        f'{height:.2f}', ha='center', va='bottom')

        ax3.set_title('关键指标对比')
        ax3.set_xlabel('指标')
        ax3.set_ylabel('数值')
        ax3.set_xticks(x)
        ax3.set_xticklabels(metrics)
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 滚动收益对比
        ax4 = axes[1, 0]

        # 计算滚动年化收益（12期滚动窗口）
        window = 12
        if len(q_ls) >= window:
            q_rolling = q_ls.rolling(window=window).apply(lambda x: ((1 + x/100).prod() ** (q_periods_per_year/window) - 1) * 100)
            ax4.plot(q_dates[window-1:], q_rolling[window-1:], linewidth=2, label='季度调仓', color='blue')

        if len(m_ls) >= window:
            m_rolling = m_ls.rolling(window=window).apply(lambda x: ((1 + x/100).prod() ** (m_periods_per_year/window) - 1) * 100)
            ax4.plot(m_dates[window-1:], m_rolling[window-1:], linewidth=2, label='月度调仓', color='red')

        ax4.set_title('滚动年化收益对比 (12期窗口)')
        ax4.set_xlabel('日期')
        ax4.set_ylabel('滚动年化收益 (%)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        # 5. 调仓成本影响分析
        ax5 = axes[1, 1]

        transaction_costs = np.arange(0, 0.01, 0.001)  # 0% 到 1%
        q_net_returns = []
        m_net_returns = []

        for cost in transaction_costs:
            q_net = q_annualized - (q_periods_per_year * cost * 100)
            m_net = m_annualized - (m_periods_per_year * cost * 100)
            q_net_returns.append(q_net)
            m_net_returns.append(m_net)

        ax5.plot(transaction_costs * 100, q_net_returns, linewidth=2, label='季度调仓', color='blue')
        ax5.plot(transaction_costs * 100, m_net_returns, linewidth=2, label='月度调仓', color='red')
        ax5.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax5.axvline(x=0.2, color='gray', linestyle=':', alpha=0.7, label='假设成本0.2%')

        ax5.set_title('调仓成本影响分析')
        ax5.set_xlabel('单次调仓成本 (%)')
        ax5.set_ylabel('净年化收益 (%)')
        ax5.legend()
        ax5.grid(True, alpha=0.3)

        # 6. 月度收益时间序列对比
        ax6 = axes[1, 2]
        ax6.plot(q_dates, q_ls, linewidth=1.5, label='季度调仓', color='blue', marker='o', markersize=4)
        ax6.plot(m_dates, m_ls, linewidth=1.5, label='月度调仓', color='red', marker='s', markersize=3)
        ax6.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax6.axhline(y=q_ls.mean(), color='blue', linestyle=':', alpha=0.7)
        ax6.axhline(y=m_ls.mean(), color='red', linestyle=':', alpha=0.7)

        ax6.set_title('收益时间序列对比')
        ax6.set_xlabel('日期')
        ax6.set_ylabel('收益率 (%)')
        ax6.legend()
        ax6.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/rebalance_frequency_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 对比图表已保存到 {output_dir}/rebalance_frequency_comparison.png")

    def save_comparison_results(self):
        """保存对比分析结果"""
        if self.quarterly_results is None or self.monthly_results is None:
            return

        output_dir = "rebalance_frequency_comparison"
        os.makedirs(output_dir, exist_ok=True)

        # 保存原始数据
        self.quarterly_results.to_csv(f'{output_dir}/quarterly_backtest_results.csv', index=False)
        self.monthly_results.to_csv(f'{output_dir}/monthly_backtest_results.csv', index=False)

        # 创建对比摘要
        q_ls = self.quarterly_results['long_short_return'].dropna()
        m_ls = self.monthly_results['long_short_return'].dropna()

        if not q_ls.empty and not m_ls.empty:
            # 计算关键指标
            q_periods_per_year = 4
            m_periods_per_year = 12

            q_years = len(q_ls) / q_periods_per_year
            m_years = len(m_ls) / m_periods_per_year

            q_cumulative = (1 + q_ls / 100).prod() - 1
            m_cumulative = (1 + m_ls / 100).prod() - 1

            q_annualized = ((1 + q_cumulative) ** (1/q_years) - 1) * 100
            m_annualized = ((1 + m_cumulative) ** (1/m_years) - 1) * 100

            q_vol = q_ls.std() * np.sqrt(q_periods_per_year)
            m_vol = m_ls.std() * np.sqrt(m_periods_per_year)

            q_sharpe = q_annualized / q_vol if q_vol > 0 else 0
            m_sharpe = m_annualized / m_vol if m_vol > 0 else 0

            # 创建对比摘要
            summary = {
                '指标': ['调仓次数', '平均单期收益(%)', '累计收益(%)', '年化收益(%)',
                        '年化波动(%)', '夏普比率', '胜率(%)', '最大单期收益(%)', '最大单期亏损(%)'],
                '季度调仓': [
                    len(q_ls),
                    f"{q_ls.mean():.2f}",
                    f"{q_cumulative:.2%}",
                    f"{q_annualized:.2f}",
                    f"{q_vol:.2f}",
                    f"{q_sharpe:.2f}",
                    f"{(q_ls > 0).mean():.1%}",
                    f"{q_ls.max():.2f}",
                    f"{q_ls.min():.2f}"
                ],
                '月度调仓': [
                    len(m_ls),
                    f"{m_ls.mean():.2f}",
                    f"{m_cumulative:.2%}",
                    f"{m_annualized:.2f}",
                    f"{m_vol:.2f}",
                    f"{m_sharpe:.2f}",
                    f"{(m_ls > 0).mean():.1%}",
                    f"{m_ls.max():.2f}",
                    f"{m_ls.min():.2f}"
                ]
            }

            summary_df = pd.DataFrame(summary)
            summary_df.to_csv(f'{output_dir}/comparison_summary.csv', index=False)

            print(f"✅ 对比结果已保存到 {output_dir}/")
            print(f"  - quarterly_backtest_results.csv: 季度调仓详细结果")
            print(f"  - monthly_backtest_results.csv: 月度调仓详细结果")
            print(f"  - comparison_summary.csv: 对比摘要")


def get_latest_q1_stocks(target_date: str = None):
    """获取最新Q1组股票的便捷函数

    Args:
        target_date: 目标日期，格式为'YYYY-MM-DD'，默认为当前日期

    Returns:
        pd.DataFrame: Q1组股票信息
    """
    print("🚀 获取营收增长加速度最高组股票...")
    print("📝 注意：Q1组为增长加速度最高组")
    print("="*60)

    backtest = QuintileBacktest()
    q1_stocks = backtest.get_latest_q1_stocks(target_date=target_date)

    return q1_stocks


def main():
    """主函数"""
    print("🚀 开始营收增长加速度因子季度调仓回测分析...")
    print("📝 注意：Q1组为增长加速度最高组，Q5组为最低组")
    print("="*80)

    # 运行季度调仓分析
    backtest = QuintileBacktest()
    results = backtest.run_full_analysis(rebalance_freq='Q')

    print("\n" + "="*80)
    print("🎉 分析完成！")
    print("📁 结果文件保存在 quintile_backtest_results/ 目录下")

    return results


if __name__ == "__main__":
    # 如果你只想获取最新的Q1组股票，可以运行：
    # q1_stocks = get_latest_q1_stocks()

    # 运行完整的回测分析
    main()
