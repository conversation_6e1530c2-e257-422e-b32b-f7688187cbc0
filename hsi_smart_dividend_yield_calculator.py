#!/usr/bin/env python3
"""
恒生指数成分股智能股息率计算器 - 升级版本
基于智能分红频率识别的股息率计算方法：
- 季度分红：取最近4次分红
- 半年分红：取最近2次分红
- 年度分红：取最近1次分红
- 支持每日股息率时间序列计算
- 优化缓存机制和批量处理
- 增强错误处理和进度跟踪
"""

import os
import json
import pandas as pd
import numpy as np
import akshare as ak
import pickle
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import time
import warnings
from collections import Counter
warnings.filterwarnings('ignore')

class HSISmartDividendYieldCalculator:
    """
    恒生指数成分股智能股息率计算器
    使用智能分红频率识别算法计算更准确的股息率
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841",
                 cache_dir: str = "cache"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.dividend_data = {}
        self.price_data = {}
        self.price_cache = {}  # 新增：价格缓存
        self.failed_stocks = []  # 新增：失败股票记录

        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
    def load_dividend_data(self) -> Dict:
        """加载所有股票的分红数据"""
        print("📁 加载分红数据...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        
        for file in dividend_files:
            file_path = os.path.join(self.dividend_data_dir, file)
            
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                    
                stock_code = data['stock_code']
                stock_name = data['stock_name']
                
                # 处理分红历史数据
                dividend_history = []
                for div in data['dividend_history']:
                    try:
                        # 处理日期格式
                        ex_date_str = div['ex_date']
                        if '/' in ex_date_str:
                            ex_date = pd.to_datetime(ex_date_str, format='%Y/%m/%d')
                        else:
                            ex_date = pd.to_datetime(ex_date_str)
                        
                        amount = float(div['amount'])
                        
                        dividend_history.append({
                            'ex_date': ex_date,
                            'amount': amount,
                            'year': div['year'],
                            'report_type': div.get('report_type', ''),
                            'plan': div.get('plan', '')
                        })
                    except Exception as e:
                        print(f"⚠️  处理 {stock_code} 分红记录失败: {e}")
                        continue
                
                # 按除权日期排序（最新的在前）
                dividend_history.sort(key=lambda x: x['ex_date'], reverse=True)
                
                self.dividend_data[stock_code] = {
                    'stock_name': stock_name,
                    'dividend_history': dividend_history
                }
                
            except Exception as e:
                print(f"❌ 加载 {file} 失败: {e}")
                continue
        
        print(f"✅ 成功加载 {len(self.dividend_data)} 只股票的分红数据")
        return self.dividend_data
    
    def analyze_dividend_frequency(self, dividend_history):
        """改进的分红频率分析（参考快速排名器的逻辑）"""
        if len(dividend_history) < 2:
            return "年度", 1, "单次分红"

        # 分析分红类型
        report_types = [div.get('report_type', '') for div in dividend_history[:8]]

        # 统计各种分红类型
        annual_count = sum(1 for rt in report_types if '年度' in rt)
        interim_count = sum(1 for rt in report_types if '中期' in rt)
        quarterly_count = sum(1 for rt in report_types if '季' in rt or 'Q' in rt.upper())
        special_count = sum(1 for rt in report_types if '特别' in rt)

        # 计算相邻分红之间的间隔（月数）
        intervals = []
        for i in range(min(len(dividend_history) - 1, 8)):  # 看最近8次间隔
            date1 = dividend_history[i]['ex_date']
            date2 = dividend_history[i + 1]['ex_date']
            months_diff = (date1.year - date2.year) * 12 + (date1.month - date2.month)
            intervals.append(abs(months_diff))

        # 判断分红频率
        if intervals:
            avg_interval = np.mean(intervals)

            # 统计不同间隔的出现次数
            quarterly_intervals = sum(1 for interval in intervals if 2 <= interval <= 4)
            semi_annual_intervals = sum(1 for interval in intervals if 5 <= interval <= 8)
            annual_intervals = sum(1 for interval in intervals if 10 <= interval <= 14)

            # 改进的决策逻辑
            if quarterly_count >= 3:
                return "季度", 4, f"季度分红({quarterly_count}次)"
            elif interim_count >= 1 and annual_count >= 1:
                # 有中期分配和年度分配，按半年分红处理，取最近2次
                return "半年", 2, f"年度+中期({annual_count}+{interim_count})"
            elif quarterly_intervals >= 3:
                # 间隔主要是3-4个月，可能是季度分红
                return "季度", 4, f"间隔季度({quarterly_intervals}次)"
            elif semi_annual_intervals >= 2:
                return "半年", 2, f"半年分红({semi_annual_intervals}次)"
            elif avg_interval >= 10:
                # 平均间隔超过10个月，年度分红
                return "年度", 1, f"年度分红(间隔{avg_interval:.1f}月)"
            else:
                # 默认判断为半年分红
                return "半年", 2, f"混合模式(间隔{avg_interval:.1f}月)"

        return "年度", 1, "默认年度"
    
    def calculate_smart_annual_dividend_at_date(self, stock_code: str, target_date: pd.Timestamp):
        """在指定日期计算智能年度分红总和（避免未来数据泄露）"""
        if stock_code not in self.dividend_data:
            return 0.0, "无数据", 0, ""

        dividend_history = self.dividend_data[stock_code]['dividend_history']

        if not dividend_history:
            return 0.0, "无分红", 0, ""

        # 只考虑目标日期之前的分红记录
        historical_dividends = [div for div in dividend_history if div['ex_date'] <= target_date]

        if not historical_dividends:
            return 0.0, "无历史分红", 0, ""

        # 分析分红频率（基于历史数据）
        frequency_type, take_count, analysis_detail = self.analyze_dividend_frequency(historical_dividends)

        # 取最近N次分红
        recent_dividends = historical_dividends[:take_count]
        total_dividend = sum(div['amount'] for div in recent_dividends)

        return total_dividend, frequency_type, len(recent_dividends), analysis_detail

    def get_individual_price_cache_file(self, stock_code: str) -> str:
        """获取单只股票的价格缓存文件路径"""
        return os.path.join(self.cache_dir, f"{stock_code}_latest_price.pkl")

    def load_individual_price_cache(self, stock_code: str) -> Optional[float]:
        """加载单只股票的价格缓存"""
        cache_file = self.get_individual_price_cache_file(stock_code)
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    # 检查缓存是否过期（2小时）
                    if datetime.now() - cache_data['timestamp'] < timedelta(hours=2):
                        return cache_data['price']
                    else:
                        # 缓存过期，删除文件
                        os.remove(cache_file)
            except Exception as e:
                # 缓存文件损坏，删除
                try:
                    os.remove(cache_file)
                except:
                    pass
        return None

    def save_individual_price_cache(self, stock_code: str, price: float):
        """保存单只股票的价格缓存"""
        cache_file = self.get_individual_price_cache_file(stock_code)
        cache_data = {
            'timestamp': datetime.now(),
            'price': price,
            'stock_code': stock_code
        }
        try:
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
        except Exception as e:
            print(f"⚠️ 保存 {stock_code} 价格缓存失败: {e}")

    def get_latest_stock_price(self, stock_code: str) -> Optional[float]:
        """获取单只股票的最新价格（优先使用缓存）"""
        # 先尝试从缓存加载
        cached_price = self.load_individual_price_cache(stock_code)
        if cached_price is not None:
            return cached_price

        # 缓存不存在或过期，从API获取
        try:
            df = ak.stock_hk_daily(symbol=stock_code)
            if not df.empty:
                price = float(df.iloc[-1]['close'])
                # 保存到缓存
                self.save_individual_price_cache(stock_code, price)
                return price
            else:
                return None
        except Exception as e:
            print(f"⚠️ 获取 {stock_code} 价格失败: {e}")
            return None

    def batch_check_price_cache_status(self, stock_codes):
        """批量检查价格缓存状态"""
        cached_count = 0
        expired_count = 0
        missing_count = 0

        for stock_code in stock_codes:
            cache_file = self.get_individual_price_cache_file(stock_code)
            if os.path.exists(cache_file):
                try:
                    with open(cache_file, 'rb') as f:
                        cache_data = pickle.load(f)
                        if datetime.now() - cache_data['timestamp'] < timedelta(hours=2):
                            cached_count += 1
                        else:
                            expired_count += 1
                except:
                    expired_count += 1
            else:
                missing_count += 1

        print(f"💾 价格缓存状态: 有效 {cached_count}, 过期 {expired_count}, 缺失 {missing_count}")
        return cached_count, expired_count, missing_count

    def get_stock_price_data(self, symbol: str) -> Optional[pd.DataFrame]:
        """获取单只股票的价格数据"""
        cache_file = os.path.join(self.cache_dir, f"{symbol}_price.pkl")
        
        # 检查缓存
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    data = pickle.load(f)
                    if not data.empty:
                        print(f"💾 从缓存加载 {symbol} 价格数据")
                        return data
            except:
                pass
        
        try:
            print(f"🌐 下载 {symbol} 价格数据...")
            # 使用akshare获取港股数据
            df = ak.stock_hk_daily(symbol=symbol)

            if not df.empty:
                # 数据预处理
                df['date'] = pd.to_datetime(df['date'])
                df.set_index('date', inplace=True)
                df = df.sort_index()

                # 过滤最近5年数据以确保有足够的历史数据
                end_date = datetime.now()
                start_date = end_date - timedelta(days=1825)  # 5年
                df = df[df.index >= start_date]

                # 保存到缓存
                with open(cache_file, 'wb') as f:
                    pickle.dump(df, f)

                time.sleep(0.5)  # 避免API限制
                return df
            else:
                print(f"⚠️  {symbol} 无价格数据")
                return None
                
        except Exception as e:
            print(f"❌ 获取 {symbol} 价格数据失败: {e}")
            return None
    
    def calculate_daily_smart_dividend_yield(self, symbol: str) -> Optional[pd.Series]:
        """
        计算单只股票的每日智能股息率（使用单独价格缓存优化）
        股息率 = 智能年度分红总额 / 当日股价
        """
        if symbol not in self.dividend_data:
            print(f"⚠️  {symbol} 无分红数据")
            return None

        # 获取价格数据
        price_df = self.get_stock_price_data(symbol)
        if price_df is None or price_df.empty:
            print(f"⚠️  {symbol} 无价格数据")
            return None

        stock_name = self.dividend_data[symbol]['stock_name']

        # 检查是否有缓存的最新价格
        latest_cached_price = self.load_individual_price_cache(symbol)
        cache_status = "有缓存" if latest_cached_price else "无缓存"

        print(f"📊 计算 {symbol} ({stock_name}) 每日智能股息率... ({cache_status})")

        # 创建结果序列
        dividend_yields = pd.Series(index=price_df.index, dtype=float)

        # 为每个交易日计算股息率
        for date in price_df.index:
            try:
                # 获取当日收盘价
                close_price = price_df.loc[date, 'close']

                if pd.isna(close_price) or close_price <= 0:
                    dividend_yields.loc[date] = np.nan
                    continue

                # 计算智能年度分红总额
                annual_dividend, frequency_type, dividend_count, analysis_detail = self.calculate_smart_annual_dividend_at_date(symbol, date)

                # 计算股息率
                if annual_dividend > 0:
                    dividend_yield = (annual_dividend / close_price) * 100  # 转换为百分比
                else:
                    dividend_yield = 0.0

                dividend_yields.loc[date] = dividend_yield

            except Exception as e:
                print(f"⚠️  计算 {symbol} 在 {date} 的股息率失败: {e}")
                dividend_yields.loc[date] = np.nan

        return dividend_yields

    def calculate_current_dividend_yield_ranking(self, output_dir: str = "hsi_current_dividend_rankings") -> List[Dict]:
        """
        计算当前股息率排名（使用单独价格缓存，快速版本）
        只计算最新的股息率，不计算历史时间序列
        """
        print("🚀 开始计算当前恒生指数成分股股息率排名（快速版本）")
        print("=" * 60)

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 先加载分红数据
        self.load_dividend_data()

        # 检查价格缓存状态
        stock_codes = list(self.dividend_data.keys())
        cached_count, expired_count, missing_count = self.batch_check_price_cache_status(stock_codes)

        results = []
        self.failed_stocks = []
        total_stocks = len(self.dividend_data)

        print(f"📊 开始处理 {total_stocks} 只股票...")
        start_time = datetime.now()

        for i, symbol in enumerate(self.dividend_data.keys(), 1):
            stock_name = self.dividend_data[symbol]['stock_name']

            try:
                # 获取最新价格（优先使用缓存）
                latest_price = self.get_latest_stock_price(symbol)

                if latest_price is None:
                    self.failed_stocks.append({
                        'code': symbol,
                        'name': stock_name,
                        'reason': '无法获取最新价格'
                    })
                    print(f"❌ [{i}/{total_stocks}] {symbol} ({stock_name}) - 无价格")
                    continue

                # 计算当前智能年度分红
                current_date = pd.Timestamp.now()
                annual_dividend, frequency_type, dividend_count, analysis_detail = self.calculate_smart_annual_dividend_at_date(symbol, current_date)

                # 计算股息率
                if annual_dividend > 0 and latest_price > 0:
                    dividend_yield = (annual_dividend / latest_price) * 100
                else:
                    dividend_yield = 0.0

                # 获取分红历史统计
                dividend_history = self.dividend_data[symbol]['dividend_history']
                recent_dividends = [d for d in dividend_history
                                  if d['ex_date'] >= datetime.now() - timedelta(days=1095)]

                # 记录结果
                result = {
                    'stock_code': symbol,
                    'stock_name': stock_name,
                    'latest_price': latest_price,
                    'dividend_yield': dividend_yield,
                    'annual_dividend': annual_dividend,
                    'frequency_type': frequency_type,
                    'analysis_detail': analysis_detail,
                    'dividend_count_used': dividend_count,
                    'dividend_frequency_3y': len(recent_dividends),
                    'last_dividend_date': dividend_history[0]['ex_date'] if dividend_history else None,
                    'last_dividend_amount': dividend_history[0]['amount'] if dividend_history else 0,
                    'calculation_date': datetime.now()
                }

                results.append(result)

                # 检查是否使用了缓存
                cache_status = "💾" if self.load_individual_price_cache(symbol) else "🌐"
                print(f"✅ [{i}/{total_stocks}] {symbol} ({stock_name}) - {dividend_yield:.2f}% {cache_status}")

            except Exception as e:
                self.failed_stocks.append({
                    'code': symbol,
                    'name': stock_name,
                    'reason': str(e)
                })
                print(f"❌ [{i}/{total_stocks}] {symbol} ({stock_name}) - 错误: {e}")

            # 添加延迟避免API限制
            if not self.load_individual_price_cache(symbol):
                time.sleep(0.3)

        end_time = datetime.now()
        total_time = end_time - start_time

        print(f"\n🎉 当前股息率排名计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {len(results)}/{total_stocks} ({len(results)/total_stocks*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")

        # 保存排名结果
        if results:
            self.save_current_ranking_report(results, output_dir)

        return results

    def save_current_ranking_report(self, results: List[Dict], output_dir: str):
        """保存当前股息率排名报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 按股息率排序
        sorted_results = sorted(results, key=lambda x: x['dividend_yield'], reverse=True)

        # 创建DataFrame
        df = pd.DataFrame(sorted_results)
        df['ranking'] = range(1, len(df) + 1)

        # 重新排列列顺序
        columns_order = [
            'ranking', 'stock_code', 'stock_name', 'latest_price',
            'dividend_yield', 'annual_dividend', 'frequency_type', 'analysis_detail', 'dividend_count_used',
            'dividend_frequency_3y', 'last_dividend_date', 'last_dividend_amount', 'calculation_date'
        ]
        df = df[columns_order]

        # 保存详细排名文件
        ranking_file = os.path.join(output_dir, f"hsi_current_dividend_yield_ranking_{timestamp}.csv")
        df.to_csv(ranking_file, index=False, encoding='utf-8-sig')

        # 创建简化版排名文件
        simple_df = df[['ranking', 'stock_code', 'stock_name', 'latest_price', 'dividend_yield', 'frequency_type', 'analysis_detail']].copy()
        simple_df.columns = ['排名', '股票代码', '股票名称', '最新价格(港元)', '股息率(%)', '分红频率', '分析详情']

        simple_file = os.path.join(output_dir, f"hsi_current_dividend_yield_ranking_simple_{timestamp}.csv")
        simple_df.to_csv(simple_file, index=False, encoding='utf-8-sig')

        # 打印前20名
        print(f"\n🏆 当前股息率最高的前20只恒生指数成分股:")
        print("=" * 120)
        print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<20} {'最新价格':<12} {'股息率':<10} {'年度分红':<10} {'分红频率':<8} {'分析详情':<20}")
        print("-" * 120)

        for i, result in enumerate(sorted_results[:20], 1):
            print(f"{i:<4} {result['stock_code']:<8} {result['stock_name']:<20} "
                  f"{result['latest_price']:<12.2f} {result['dividend_yield']:<10.4f}% "
                  f"{result['annual_dividend']:<10.2f} {result['frequency_type']:<8} {result['analysis_detail']:<20}")

        print(f"\n📊 排名报告已生成:")
        print(f"   详细版: {ranking_file}")
        print(f"   简化版: {simple_file}")

        return ranking_file, simple_file

    def calculate_all_stocks_smart_dividend_yields(self, output_dir: str = "hsi_smart_dividend_yields") -> Dict[str, pd.Series]:
        """计算所有股票的智能股息率并保存到独立文件（升级版本）"""
        print("🚀 开始计算所有恒生指数成分股智能股息率（升级版本）")
        print("=" * 60)

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)

        # 先加载分红数据
        self.load_dividend_data()

        # 检查价格缓存状态
        stock_codes = list(self.dividend_data.keys())
        cached_count, expired_count, missing_count = self.batch_check_price_cache_status(stock_codes)

        results = {}
        summary_stats = []
        self.failed_stocks = []  # 重置失败列表
        total_stocks = len(self.dividend_data)

        print(f"📊 开始处理 {total_stocks} 只股票...")
        start_time = datetime.now()

        for i, symbol in enumerate(self.dividend_data.keys(), 1):
            stock_name = self.dividend_data[symbol]['stock_name']
            print(f"\n📈 [{i}/{total_stocks}] 处理 {symbol} ({stock_name})...")

            try:
                dividend_yield_series = self.calculate_daily_smart_dividend_yield(symbol)

                if dividend_yield_series is not None:
                    # 计算统计信息
                    valid_yields = dividend_yield_series.dropna()
                    non_zero_yields = valid_yields[valid_yields > 0]

                    # 获取分红频率信息
                    dividend_history = self.dividend_data[symbol]['dividend_history']
                    frequency_type, take_count, analysis_detail = self.analyze_dividend_frequency(dividend_history)

                    # 计算最新的智能年度分红
                    if len(valid_yields) > 0:
                        latest_date = valid_yields.index[-1]
                        latest_annual_dividend, _, _, _ = self.calculate_smart_annual_dividend_at_date(symbol, latest_date)
                    else:
                        latest_annual_dividend = 0

                    stats = {
                        'stock_code': symbol,
                        'stock_name': stock_name,
                        'total_days': len(valid_yields),
                        'dividend_days': len(non_zero_yields),
                        'coverage_rate': len(non_zero_yields) / len(valid_yields) * 100 if len(valid_yields) > 0 else 0,
                        'avg_yield': non_zero_yields.mean() if len(non_zero_yields) > 0 else 0,
                        'max_yield': non_zero_yields.max() if len(non_zero_yields) > 0 else 0,
                        'min_yield': non_zero_yields.min() if len(non_zero_yields) > 0 else 0,
                        'std_yield': non_zero_yields.std() if len(non_zero_yields) > 0 else 0,
                        'latest_yield': valid_yields.iloc[-1] if len(valid_yields) > 0 else 0,
                        'frequency_type': frequency_type,
                        'dividend_count_used': take_count,
                        'analysis_detail': analysis_detail,  # 新增：分析详情
                        'latest_annual_dividend': latest_annual_dividend,
                        'data_start': valid_yields.index.min() if len(valid_yields) > 0 else None,
                        'data_end': valid_yields.index.max() if len(valid_yields) > 0 else None
                    }

                    # 保存到独立文件
                    self.save_individual_smart_stock_data(symbol, stock_name, dividend_yield_series, stats, output_dir)

                    results[symbol] = dividend_yield_series
                    summary_stats.append(stats)

                    print(f"   ✅ 成功: 平均股息率 {stats['avg_yield']:.4f}% ({frequency_type}: {analysis_detail})")
                else:
                    print(f"   ❌ 计算失败")
                    self.failed_stocks.append({'code': symbol, 'name': stock_name, 'reason': '无有效股息率数据'})

            except Exception as e:
                print(f"   ❌ 处理失败: {e}")
                self.failed_stocks.append({'code': symbol, 'name': stock_name, 'reason': str(e)})

            # 每10只股票显示一次进度
            if i % 10 == 0:
                elapsed = datetime.now() - start_time
                avg_time = elapsed.total_seconds() / i
                remaining = (total_stocks - i) * avg_time
                print(f"\n📊 进度: {i}/{total_stocks} ({i/total_stocks*100:.1f}%), "
                      f"成功: {len(results)}, "
                      f"预计剩余: {remaining/60:.1f}分钟")

        end_time = datetime.now()
        total_time = end_time - start_time

        print(f"\n🎉 智能股息率计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {len(results)}/{total_stocks} ({len(results)/total_stocks*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")

        # 保存总体摘要
        if summary_stats:
            self.save_smart_summary(summary_stats, self.failed_stocks, output_dir)

        return results

    def save_individual_smart_stock_data(self, stock_code, stock_name, yield_series, stats, output_dir):
        """保存单只股票的智能股息率数据到独立文件"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建详细数据文件
        filename = f"{stock_code}_{stock_name}_smart_dividend_yield_{timestamp}.csv"
        filepath = os.path.join(output_dir, filename)

        # 获取价格数据用于完整分析
        price_data = self.get_stock_price_data(stock_code)

        if price_data is not None:
            # 创建完整的分析DataFrame
            analysis_df = pd.DataFrame({
                '日期': yield_series.index,
                '股价(港元)': price_data['close'].reindex(yield_series.index),
                '开盘价(港元)': price_data['open'].reindex(yield_series.index),
                '最高价(港元)': price_data['high'].reindex(yield_series.index),
                '最低价(港元)': price_data['low'].reindex(yield_series.index),
                '成交量': price_data['volume'].reindex(yield_series.index),
                '智能股息率(%)': yield_series.values,
                '智能年度分红(港元)': [self.calculate_smart_annual_dividend_at_date(stock_code, date)[0]
                                    for date in yield_series.index],
                '分红频率': [self.calculate_smart_annual_dividend_at_date(stock_code, date)[1]
                           for date in yield_series.index],
                '分析详情': [self.calculate_smart_annual_dividend_at_date(stock_code, date)[3]
                           for date in yield_series.index]
            })
        else:
            # 如果没有价格数据，只保存股息率
            analysis_df = pd.DataFrame({
                '日期': yield_series.index,
                '智能股息率(%)': yield_series.values,
                '智能年度分红(港元)': [self.calculate_smart_annual_dividend_at_date(stock_code, date)[0]
                                    for date in yield_series.index],
                '分红频率': [self.calculate_smart_annual_dividend_at_date(stock_code, date)[1]
                           for date in yield_series.index],
                '分析详情': [self.calculate_smart_annual_dividend_at_date(stock_code, date)[3]
                           for date in yield_series.index]
            })

        # 保存到CSV文件
        analysis_df.to_csv(filepath, index=False, encoding='utf-8-sig')

        # 创建股票信息摘要文件
        summary_filename = f"{stock_code}_{stock_name}_smart_summary_{timestamp}.txt"
        summary_filepath = os.path.join(output_dir, summary_filename)

        with open(summary_filepath, 'w', encoding='utf-8') as f:
            f.write(f"股票代码: {stock_code}\n")
            f.write(f"股票名称: {stock_name}\n")
            f.write(f"计算方法: 智能分红频率识别\n")
            f.write(f"分红频率: {stats['frequency_type']}分红\n")
            f.write(f"分析详情: {stats['analysis_detail']}\n")
            f.write(f"使用分红次数: {stats['dividend_count_used']}\n")
            f.write(f"数据期间: {stats['data_start']} 至 {stats['data_end']}\n")
            f.write(f"总交易日数: {stats['total_days']}\n")
            f.write(f"有分红天数: {stats['dividend_days']}\n")
            f.write(f"数据覆盖率: {stats['coverage_rate']:.1f}%\n")
            f.write(f"平均股息率: {stats['avg_yield']:.4f}%\n")
            f.write(f"最大股息率: {stats['max_yield']:.4f}%\n")
            f.write(f"最小股息率: {stats['min_yield']:.4f}%\n")
            f.write(f"标准差: {stats['std_yield']:.4f}%\n")
            f.write(f"最新股息率: {stats['latest_yield']:.4f}%\n")
            f.write(f"最新年度分红: {stats['latest_annual_dividend']:.2f}港元\n")
            f.write(f"\n分红历史:\n")

            # 添加分红历史信息
            dividend_history = self.dividend_data[stock_code]['dividend_history']
            for div in dividend_history:
                f.write(f"  {div['year']}年: {div['ex_date'].strftime('%Y-%m-%d')} 除权, "
                       f"分红 {div['amount']} 港元 ({div.get('report_type', '未知类型')})\n")

        print(f"   💾 已保存: {filename}")
        return filepath, summary_filepath

    def save_smart_summary(self, summary_stats, failed_stocks, output_dir):
        """保存智能股息率计算的总体摘要"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 创建统计摘要CSV
        stats_df = pd.DataFrame(summary_stats)
        stats_df = stats_df.sort_values('avg_yield', ascending=False)
        stats_file = os.path.join(output_dir, f"hsi_smart_dividend_yield_summary_{timestamp}.csv")
        stats_df.to_csv(stats_file, index=False, encoding='utf-8-sig')

        # 创建文件清单和分析报告
        report_file = os.path.join(output_dir, f"smart_dividend_yield_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("恒生指数成分股智能股息率计算报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"计算方法: 智能分红频率识别算法\n")
            f.write(f"总股票数: {len(summary_stats)}\n")
            f.write(f"失败股票数: {len(failed_stocks)}\n\n")

            # 分红频率统计
            frequency_stats = Counter([s['frequency_type'] for s in summary_stats])
            f.write("分红频率分布:\n")
            for freq, count in frequency_stats.items():
                f.write(f"  {freq}分红: {count} 只股票\n")

            # 股息率统计
            valid_yields = [s['avg_yield'] for s in summary_stats if s['avg_yield'] > 0]
            if valid_yields:
                f.write(f"\n股息率统计:\n")
                f.write(f"  有分红股票数: {len(valid_yields)}\n")
                f.write(f"  股息率范围: {min(valid_yields):.4f}% - {max(valid_yields):.4f}%\n")
                f.write(f"  平均股息率: {np.mean(valid_yields):.4f}%\n")
                f.write(f"  中位数股息率: {np.median(valid_yields):.4f}%\n")

            # 前20只高股息率股票
            f.write(f"\n前20只高股息率股票:\n")
            for i, stats in enumerate(stats_df.head(20).itertuples(), 1):
                if stats.avg_yield > 0:
                    f.write(f"  {i:2d}. {stats.stock_code} ({stats.stock_name}): "
                           f"{stats.avg_yield:.4f}% ({stats.frequency_type}分红)\n")

            # 失败股票列表
            if failed_stocks:
                f.write(f"\n计算失败的股票:\n")
                for failed in failed_stocks:
                    f.write(f"  {failed['code']} ({failed['name']}): {failed['reason']}\n")

        print(f"📊 智能股息率总体摘要已保存:")
        print(f"   统计文件: {stats_file}")
        print(f"   分析报告: {report_file}")

        return stats_file, report_file

def main():
    """主函数"""
    print("🚀 恒生指数成分股智能股息率计算器（升级版本）")
    print("=" * 60)
    print("基于智能分红频率识别的股息率计算方法")
    print("- 季度分红：取最近4次分红")
    print("- 半年分红：取最近2次分红")
    print("- 年度分红：取最近1次分红")
    print("升级功能：")
    print("- 每只股票价格单独缓存")
    print("- 优化缓存机制和批量处理")
    print("- 增强错误处理和进度跟踪")
    print("- 改进分红频率分析算法")
    print("=" * 60)

    # 创建计算器
    calculator = HSISmartDividendYieldCalculator(
        dividend_data_dir="hsi_dividend_data_20250603_173841"
    )

    try:
        # 提供两种模式选择
        print("\n📋 请选择运行模式:")
        print("1. 快速排名模式 - 只计算当前股息率排名（推荐，速度快）")
        print("2. 完整时间序列模式 - 计算每日股息率历史数据（耗时较长）")

        import sys
        if len(sys.argv) > 1:
            mode = sys.argv[1]
        else:
            mode = input("请输入模式 (1 或 2，默认为 1): ").strip() or "1"

        if mode == "2":
            print("\n🔄 运行完整时间序列模式...")
            # 计算所有股票的智能股息率时间序列
            results = calculator.calculate_all_stocks_smart_dividend_yields()
        else:
            print("\n⚡ 运行快速排名模式...")
            # 计算当前股息率排名
            results = calculator.calculate_current_dividend_yield_ranking()

        if results:
            print(f"\n✅ 智能股息率计算全部完成!")
            print(f"   成功处理: {len(results)} 只股票")

            if mode == "2":
                # 完整时间序列模式的统计
                print(f"   输出目录: hsi_smart_dividend_yields/")
                print(f"   每只股票包含: 数据文件(.csv) + 摘要文件(.txt)")

                # 显示一些统计信息
                avg_yields = []
                frequency_counts = Counter()

                for symbol in results.keys():
                    dividend_history = calculator.dividend_data[symbol]['dividend_history']
                    frequency_type, _, _ = calculator.analyze_dividend_frequency(dividend_history)
                    frequency_counts[frequency_type] += 1

                    # 计算平均股息率
                    yield_series = results[symbol]
                    valid_yields = yield_series.dropna()
                    non_zero_yields = valid_yields[valid_yields > 0]
                    if len(non_zero_yields) > 0:
                        avg_yields.append(non_zero_yields.mean())

                if avg_yields:
                    print(f"\n📊 整体统计:")
                    print(f"   有分红股票数: {len(avg_yields)}")
                    print(f"   股息率范围: {min(avg_yields):.4f}% - {max(avg_yields):.4f}%")
                    print(f"   平均股息率: {np.mean(avg_yields):.4f}%")

                    print(f"\n📈 分红频率分布:")
                    for freq, count in frequency_counts.items():
                        print(f"   {freq}分红: {count} 只股票")
            else:
                # 快速排名模式的统计
                print(f"   输出目录: hsi_current_dividend_rankings/")
                print(f"   包含: 详细排名(.csv) + 简化排名(.csv)")

                # 显示统计信息
                valid_results = [r for r in results if r['dividend_yield'] > 0]
                if valid_results:
                    yields = [r['dividend_yield'] for r in valid_results]
                    frequency_counts = Counter([r['frequency_type'] for r in valid_results])

                    print(f"\n📊 当前统计:")
                    print(f"   有分红股票数: {len(valid_results)}")
                    print(f"   股息率范围: {min(yields):.4f}% - {max(yields):.4f}%")
                    print(f"   平均股息率: {np.mean(yields):.4f}%")
                    print(f"   中位数股息率: {np.median(yields):.4f}%")

                    print(f"\n📈 分红频率分布:")
                    for freq, count in frequency_counts.items():
                        print(f"   {freq}分红: {count} 只股票")
        else:
            print("❌ 没有成功计算任何股票的智能股息率")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
