#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版动量因子回测系统
基于标普500成分股价格动量进行因子回测
"""

import pandas as pd
import numpy as np
import yfinance as yf
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

def load_sample_data():
    """加载样本数据进行测试"""
    print("📊 获取样本数据...")
    
    # 使用一些知名股票作为样本
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'JPM', 'JNJ', 'PG',
               'KO', 'DIS', 'HD', 'WMT', 'V', 'MA', 'UNH', 'PFE', 'BAC', 'XOM']
    
    price_data = {}
    
    try:
        # 获取数据
        data = yf.download(symbols, start='2020-01-01', end='2025-06-01', progress=False)
        
        if 'Adj Close' in data.columns:
            for symbol in symbols:
                try:
                    if symbol in data['Adj Close'].columns:
                        series = data['Adj Close'][symbol].dropna()
                        if len(series) > 500:  # 确保有足够数据
                            price_data[symbol] = series
                except:
                    continue
        
        print(f"✅ 成功获取 {len(price_data)} 只股票的价格数据")
        return price_data
        
    except Exception as e:
        print(f"❌ 获取数据失败: {e}")
        return {}

def calculate_momentum(price_data, lookback_period=60):
    """计算动量因子"""
    print(f"🔄 计算 {lookback_period} 日动量因子...")
    
    momentum_data = {}
    
    for symbol, prices in price_data.items():
        if len(prices) > lookback_period + 10:
            # 计算价格动量 = (当前价格 - N日前价格) / N日前价格
            momentum = prices.pct_change(lookback_period)
            momentum_data[symbol] = momentum.dropna()
    
    print(f"✅ 成功计算 {len(momentum_data)} 只股票的动量因子")
    return momentum_data

def create_momentum_portfolios(price_data, momentum_data, n_groups=5):
    """创建动量投资组合"""
    print(f"🔄 创建 {n_groups} 分组动量投资组合...")
    
    # 创建价格和动量数据矩阵
    common_symbols = set(price_data.keys()) & set(momentum_data.keys())
    print(f"   共同股票数量: {len(common_symbols)}")
    
    if len(common_symbols) < 10:
        print("❌ 可用股票数量太少")
        return pd.DataFrame()
    
    # 创建数据矩阵
    price_matrix = pd.DataFrame({symbol: price_data[symbol] for symbol in common_symbols})
    momentum_matrix = pd.DataFrame({symbol: momentum_data[symbol] for symbol in common_symbols})
    
    # 对齐时间索引
    common_dates = price_matrix.index.intersection(momentum_matrix.index)
    price_matrix = price_matrix.loc[common_dates]
    momentum_matrix = momentum_matrix.loc[common_dates]
    
    # 计算收益率
    returns_matrix = price_matrix.pct_change().dropna()
    
    # 月度重新平衡
    rebalance_dates = momentum_matrix.resample('M').last().index
    
    # 初始化组合收益率
    portfolio_returns = {f'Group_{i+1}': [] for i in range(n_groups)}
    portfolio_returns['Long_Short'] = []
    
    print(f"   重新平衡次数: {len(rebalance_dates)}")
    
    for i, rebal_date in enumerate(rebalance_dates[:-1]):
        next_rebal_date = rebalance_dates[i+1]
        
        # 获取重新平衡日的动量因子数据
        current_momentum = momentum_matrix.loc[rebal_date].dropna()
        
        if len(current_momentum) < n_groups * 2:  # 确保每组至少有2只股票
            continue
        
        # 按动量因子排序并分组 (动量高的排在前面)
        sorted_momentum = current_momentum.sort_values(ascending=False)
        group_size = len(sorted_momentum) // n_groups
        
        groups = {}
        for j in range(n_groups):
            start_idx = j * group_size
            if j == n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(sorted_momentum)
            else:
                end_idx = (j + 1) * group_size
            
            groups[f'Group_{j+1}'] = sorted_momentum.iloc[start_idx:end_idx].index.tolist()
        
        # 计算各组合在下一个重新平衡期间的收益率
        period_returns = returns_matrix.loc[rebal_date:next_rebal_date]
        
        for group_name, stocks in groups.items():
            # 等权重投资组合收益率
            available_stocks = [s for s in stocks if s in period_returns.columns]
            if available_stocks:
                group_returns = period_returns[available_stocks].mean(axis=1)
                portfolio_returns[group_name].extend(group_returns.tolist())
        
        # 多空组合 (高动量组 - 低动量组)
        high_momentum_stocks = groups['Group_1']
        low_momentum_stocks = groups[f'Group_{n_groups}']
        
        high_available = [s for s in high_momentum_stocks if s in period_returns.columns]
        low_available = [s for s in low_momentum_stocks if s in period_returns.columns]
        
        if high_available and low_available:
            high_returns = period_returns[high_available].mean(axis=1)
            low_returns = period_returns[low_available].mean(axis=1)
            long_short_returns = high_returns - low_returns
            portfolio_returns['Long_Short'].extend(long_short_returns.tolist())
    
    # 转换为DataFrame
    max_length = max(len(returns) for returns in portfolio_returns.values() if len(returns) > 0)
    for key in portfolio_returns:
        current_length = len(portfolio_returns[key])
        if current_length < max_length:
            portfolio_returns[key].extend([np.nan] * (max_length - current_length))
    
    factor_returns = pd.DataFrame(portfolio_returns)
    print(f"✅ 因子组合创建完成，数据长度: {len(factor_returns)}")
    return factor_returns

def calculate_performance_metrics(factor_returns):
    """计算绩效指标"""
    print("📊 计算绩效指标...")
    
    metrics = {}
    
    for col in factor_returns.columns:
        returns = factor_returns[col].dropna()
        
        if len(returns) == 0:
            continue
        
        # 基本统计
        total_return = (1 + returns).prod() - 1
        annual_return = (1 + total_return) ** (252 / len(returns)) - 1
        annual_vol = returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
        
        # 最大回撤
        cumulative = (1 + returns).cumprod()
        rolling_max = cumulative.expanding().max()
        drawdown = (cumulative - rolling_max) / rolling_max
        max_drawdown = drawdown.min()
        
        # 胜率
        win_rate = (returns > 0).mean()
        
        metrics[col] = {
            'Total_Return': total_return * 100,
            'Annual_Return': annual_return * 100,
            'Annual_Volatility': annual_vol * 100,
            'Sharpe_Ratio': sharpe_ratio,
            'Max_Drawdown': max_drawdown * 100,
            'Win_Rate': win_rate * 100,
            'Observations': len(returns)
        }
    
    return pd.DataFrame(metrics).T

def plot_results(factor_returns):
    """绘制结果图表"""
    print("📈 绘制累积收益率图...")
    
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 10))
    
    # 计算累积收益率
    cumulative_returns = (1 + factor_returns.fillna(0)).cumprod()
    
    # 第一个子图：所有分组
    colors = ['red', 'orange', 'green', 'blue', 'purple']
    for i, col in enumerate([c for c in cumulative_returns.columns if c.startswith('Group_')]):
        ax1.plot(cumulative_returns.index, cumulative_returns[col], 
                color=colors[i % len(colors)], label=col, linewidth=2)
    
    ax1.set_title('Momentum Factor - Group Performance', fontsize=14, fontweight='bold')
    ax1.set_ylabel('Cumulative Return', fontsize=12)
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 第二个子图：多空组合
    if 'Long_Short' in cumulative_returns.columns:
        ax2.plot(cumulative_returns.index, cumulative_returns['Long_Short'], 
                color='darkred', label='Long-Short (High - Low Momentum)', linewidth=2)
        ax2.axhline(y=1, color='gray', linestyle='-', alpha=0.5)
    
    ax2.set_title('Momentum Factor - Long-Short Performance', fontsize=14, fontweight='bold')
    ax2.set_ylabel('Cumulative Return', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('momentum_factor_simple_results.png', dpi=300, bbox_inches='tight')
    plt.show()
    print("📊 图表已保存到: momentum_factor_simple_results.png")

def main():
    """主函数"""
    print("🚀 简化版动量因子回测系统")
    print("=" * 60)
    
    # 1. 加载数据
    price_data = load_sample_data()
    
    if len(price_data) < 10:
        print("❌ 数据不足，无法进行回测")
        return
    
    # 2. 计算动量因子
    momentum_data = calculate_momentum(price_data, lookback_period=60)
    
    if len(momentum_data) < 10:
        print("❌ 动量因子计算失败")
        return
    
    # 3. 创建投资组合
    factor_returns = create_momentum_portfolios(price_data, momentum_data)
    
    if factor_returns.empty:
        print("❌ 投资组合创建失败")
        return
    
    # 4. 计算绩效指标
    metrics_df = calculate_performance_metrics(factor_returns)
    
    # 5. 显示结果
    print("\n📊 回测结果:")
    print("=" * 80)
    print(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}")
    print("-" * 80)
    
    for idx, row in metrics_df.iterrows():
        print(f"{idx:>12} "
              f"{row['Total_Return']:>9.2f}% "
              f"{row['Annual_Return']:>9.2f}% "
              f"{row['Annual_Volatility']:>9.2f}% "
              f"{row['Sharpe_Ratio']:>9.2f} "
              f"{row['Max_Drawdown']:>9.2f}% "
              f"{row['Win_Rate']:>7.1f}%")
    
    # 6. 绘制图表
    plot_results(factor_returns)
    
    # 7. 分析因子有效性
    print("\n📈 因子有效性分析:")
    print("-" * 50)
    
    group_returns = [metrics_df.loc[f'Group_{i}', 'Annual_Return'] for i in range(1, 6) 
                   if f'Group_{i}' in metrics_df.index]
    
    if len(group_returns) >= 5:
        monotonic = all(group_returns[i] >= group_returns[i+1] for i in range(len(group_returns)-1))
        print(f"单调性检验: {'通过' if monotonic else '未通过'}")
        print(f"高低分组收益差: {group_returns[0] - group_returns[-1]:.2f}%")
    
    if 'Long_Short' in metrics_df.index:
        ls_return = metrics_df.loc['Long_Short', 'Annual_Return']
        ls_sharpe = metrics_df.loc['Long_Short', 'Sharpe_Ratio']
        print(f"多空组合年化收益: {ls_return:.2f}%")
        print(f"多空组合夏普比率: {ls_sharpe:.2f}")
        
        # 因子显著性
        if ls_return > 5 and ls_sharpe > 0.8:
            print("因子有效性: 强")
        elif ls_return > 2 and ls_sharpe > 0.4:
            print("因子有效性: 中等")
        else:
            print("因子有效性: 弱")
    
    print(f"\n🎯 动量因子回测完成!")

if __name__ == "__main__":
    main()
