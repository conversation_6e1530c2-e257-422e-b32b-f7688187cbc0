#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
茅台5年总回报计算器（简化版）
使用已下载的数据和akshare获取分红信息
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import os

def get_maotai_dividend_data():
    """获取茅台分红数据"""
    print("📊 获取茅台分红数据...")
    
    try:
        # 使用akshare获取茅台分红数据
        df = ak.stock_fhps_detail_em(symbol="600519")
        
        if df is not None and not df.empty:
            print(f"✅ 成功获取分红数据: {len(df)} 条记录")
            
            # 显示最近几年的分红情况
            print("\n🎁 茅台历年分红情况:")
            print("-" * 50)
            
            # 处理分红数据
            df['除权除息日'] = pd.to_datetime(df['除权除息日'], errors='coerce')
            df = df.dropna(subset=['除权除息日'])
            df = df.sort_values('除权除息日', ascending=False)
            
            # 显示最近10年的分红
            recent_dividends = df.head(15)
            total_dividends_5y = 0
            
            five_years_ago = datetime.now() - timedelta(days=5*365)
            
            for idx, row in recent_dividends.iterrows():
                ex_date = row['除权除息日']
                cash_dividend = row.get('现金分红-现金分红比例', 0)
                
                if pd.notna(cash_dividend) and cash_dividend > 0:
                    print(f"{ex_date.strftime('%Y-%m-%d')}: 每股分红 ¥{cash_dividend:.2f}")
                    
                    # 统计过去5年的分红
                    if ex_date >= five_years_ago:
                        total_dividends_5y += cash_dividend
            
            print(f"\n💰 过去5年累计分红: ¥{total_dividends_5y:.2f}/股")
            return total_dividends_5y
            
        else:
            print("⚠️ 未获取到分红数据")
            return 0
            
    except Exception as e:
        print(f"❌ 获取分红数据失败: {str(e)}")
        return 0

def get_maotai_price_data():
    """获取茅台股价数据"""
    print("\n📈 获取茅台股价数据...")
    
    try:
        # 计算5年前的日期
        end_date = datetime.now()
        start_date = end_date - timedelta(days=5*365 + 30)
        
        start_str = start_date.strftime('%Y%m%d')
        end_str = end_date.strftime('%Y%m%d')
        
        # 获取茅台股价数据（前复权）
        df = ak.stock_zh_a_hist(
            symbol="600519", 
            period="daily", 
            start_date=start_str, 
            end_date=end_str, 
            adjust="qfq"
        )
        
        if df is not None and not df.empty:
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.sort_values('日期')
            
            print(f"✅ 成功获取股价数据: {len(df)} 条记录")
            print(f"📅 数据期间: {df['日期'].iloc[0].strftime('%Y-%m-%d')} 至 {df['日期'].iloc[-1].strftime('%Y-%m-%d')}")
            
            return df
        else:
            print("❌ 未获取到股价数据")
            return None
            
    except Exception as e:
        print(f"❌ 获取股价数据失败: {str(e)}")
        return None

def calculate_total_return():
    """计算茅台5年总回报"""
    print("\n" + "="*60)
    print("🍷 贵州茅台(600519) 过去5年总回报分析")
    print("="*60)
    
    # 获取股价数据
    price_data = get_maotai_price_data()
    if price_data is None:
        print("❌ 无法获取股价数据，分析终止")
        return
    
    # 获取分红数据
    total_dividends_5y = get_maotai_dividend_data()
    
    # 计算5年回报
    print(f"\n📊 计算5年总回报...")
    print("-" * 40)
    
    # 找到大约5年前的价格
    five_years_ago = datetime.now() - timedelta(days=5*365)
    
    # 找到最接近5年前的交易日
    price_data['日期'] = pd.to_datetime(price_data['日期'])
    price_data_5y = price_data[price_data['日期'] >= five_years_ago]
    
    if price_data_5y.empty:
        print("❌ 没有足够的历史数据")
        return
    
    start_price = price_data_5y['收盘'].iloc[0]
    end_price = price_data_5y['收盘'].iloc[-1]
    start_date = price_data_5y['日期'].iloc[0]
    end_date = price_data_5y['日期'].iloc[-1]
    
    # 计算实际投资年限
    actual_years = (end_date - start_date).days / 365.25
    
    print(f"📅 实际分析期间: {start_date.strftime('%Y年%m月%d日')} 至 {end_date.strftime('%Y年%m月%d日')}")
    print(f"⏱️  投资年限: {actual_years:.2f} 年")
    print(f"💰 起始价格: ¥{start_price:,.2f}")
    print(f"💰 结束价格: ¥{end_price:,.2f}")
    
    # 计算股价回报
    price_return = (end_price - start_price) / start_price
    print(f"📈 股价涨幅: {price_return:.2%}")
    
    # 计算分红回报
    dividend_return = total_dividends_5y / start_price
    print(f"🎁 分红收益率: {dividend_return:.2%} (累计分红¥{total_dividends_5y:.2f}/股)")
    
    # 计算总回报
    total_return = price_return + dividend_return
    annual_return = (1 + total_return) ** (1/actual_years) - 1
    
    print(f"\n🏆 总回报结果:")
    print(f"   总收益率: {total_return:.2%}")
    print(f"   年化收益率: {annual_return:.2%}")
    
    # 投资示例
    print(f"\n💡 投资收益示例:")
    initial_investment = 100000  # 10万元
    shares = initial_investment / start_price
    final_stock_value = shares * end_price
    total_dividends_received = shares * total_dividends_5y
    final_total_value = final_stock_value + total_dividends_received
    total_profit = final_total_value - initial_investment
    
    print(f"   如果{start_date.strftime('%Y年%m月')}投资 ¥{initial_investment:,}")
    print(f"   可购买股票: {shares:.0f} 股")
    print(f"   现在股票价值: ¥{final_stock_value:,.0f}")
    print(f"   累计分红收入: ¥{total_dividends_received:,.0f}")
    print(f"   总资产价值: ¥{final_total_value:,.0f}")
    print(f"   总盈利: ¥{total_profit:,.0f}")
    
    # 获取沪深300作为基准对比
    try:
        print(f"\n📊 与沪深300指数对比:")
        hs300_data = ak.stock_zh_index_daily(symbol="sh000300")
        if hs300_data is not None and not hs300_data.empty:
            hs300_data['date'] = pd.to_datetime(hs300_data['date'])
            hs300_5y = hs300_data[hs300_data['date'] >= five_years_ago]
            
            if not hs300_5y.empty:
                hs300_start = hs300_5y['close'].iloc[0]
                hs300_end = hs300_5y['close'].iloc[-1]
                hs300_return = (hs300_end - hs300_start) / hs300_start
                hs300_annual = (1 + hs300_return) ** (1/actual_years) - 1
                
                print(f"   沪深300总收益率: {hs300_return:.2%}")
                print(f"   沪深300年化收益率: {hs300_annual:.2%}")
                excess_return = annual_return - hs300_annual
                print(f"   茅台超额年化收益: {excess_return:.2%}")
    except:
        print("   (无法获取沪深300数据进行对比)")
    
    print("="*60)
    
    return {
        'total_return': total_return,
        'annual_return': annual_return,
        'price_return': price_return,
        'dividend_return': dividend_return,
        'years': actual_years
    }

def main():
    """主函数"""
    try:
        results = calculate_total_return()
        if results:
            print(f"\n✅ 分析完成!")
            print(f"茅台过去{results['years']:.1f}年年化收益率: {results['annual_return']:.2%}")
        else:
            print("❌ 分析失败")
    except Exception as e:
        print(f"❌ 程序执行出错: {str(e)}")

if __name__ == "__main__":
    main()
