#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
宏观经济复合指标合成
将房地产复合指标与季节调整后的消费者信心指数、制造业PMI等权合并
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_real_estate_composite():
    """加载房地产复合指标数据"""
    print("正在加载房地产复合指标数据...")

    try:
        # 尝试加载房地产复合指标数据
        real_estate_data = pd.read_csv('data/real_estate_composite_indicator.csv')
        real_estate_data['DATE'] = pd.to_datetime(real_estate_data['DATE'])
        real_estate_data.set_index('DATE', inplace=True)

        # 选择标准化后的复合指标
        if 'Composite_Standardized' in real_estate_data.columns:
            real_estate_series = real_estate_data['Composite_Standardized']
        elif 'Composite_Indicator' in real_estate_data.columns:
            real_estate_series = real_estate_data['Composite_Indicator']
        else:
            # 如果没有现成的复合指标，使用第一个数值列
            numeric_cols = real_estate_data.select_dtypes(include=[np.number]).columns
            real_estate_series = real_estate_data[numeric_cols[0]]

        print(f"房地产复合指标数据: {len(real_estate_series)} 条记录")
        return real_estate_series

    except FileNotFoundError:
        print("警告: 未找到房地产复合指标文件，将创建模拟数据")
        return create_mock_real_estate_data()

def create_mock_real_estate_data():
    """创建模拟房地产数据（如果原文件不存在）"""
    # 创建从1970年开始的月度数据
    dates = pd.date_range('1970-01-01', '2025-05-01', freq='M')

    # 创建模拟的房地产复合指标（标准化数据）
    np.random.seed(42)
    trend = np.linspace(-1, 1, len(dates))
    cycle = 2 * np.sin(2 * np.pi * np.arange(len(dates)) / 120)  # 10年周期
    noise = np.random.normal(0, 0.5, len(dates))

    mock_data = trend + cycle + noise

    return pd.Series(mock_data, index=dates, name='Real_Estate_Composite')

def load_seasonal_adjusted_indicators():
    """加载季节调整后的指标数据"""
    print("正在加载季节调整后的指标数据...")

    try:
        seasonal_data = pd.read_csv('data/seasonal_adjusted_hp_filtered_indicators.csv')
        seasonal_data['DATE'] = pd.to_datetime(seasonal_data['DATE'])
        seasonal_data.set_index('DATE', inplace=True)

        # 提取周期成分（去趋势去噪后的数据）
        umich_cycle = seasonal_data['UMICH_Cycle'].dropna()
        ism_cycle = seasonal_data['ISM_Cycle'].dropna()

        print(f"消费者信心指数周期成分: {len(umich_cycle)} 条记录")
        print(f"制造业PMI周期成分: {len(ism_cycle)} 条记录")

        return umich_cycle, ism_cycle

    except FileNotFoundError:
        print("错误: 未找到季节调整后的指标文件")
        return None, None

def standardize_series(series, name):
    """标准化时间序列"""
    if series is None or len(series) == 0:
        return None

    # Z-Score标准化
    standardized = (series - series.mean()) / series.std()
    standardized.name = f"{name}_Standardized"

    print(f"{name} 标准化完成:")
    print(f"  原始均值: {series.mean():.4f}, 标准差: {series.std():.4f}")
    print(f"  标准化后均值: {standardized.mean():.4f}, 标准差: {standardized.std():.4f}")

    return standardized

def align_time_series(*series_list):
    """对齐多个时间序列的时间索引"""
    # 过滤掉None值
    valid_series = [s for s in series_list if s is not None and len(s) > 0]

    if len(valid_series) == 0:
        return []

    # 找到共同的时间范围
    start_date = max([s.index.min() for s in valid_series])
    end_date = min([s.index.max() for s in valid_series])

    print(f"共同时间范围: {start_date.strftime('%Y-%m')} 至 {end_date.strftime('%Y-%m')}")

    # 创建统一的时间索引
    common_index = pd.date_range(start=start_date, end=end_date, freq='M')

    # 对齐所有序列到共同的时间索引
    aligned_series = []
    for series in valid_series:
        # 重新索引到共同的时间范围
        aligned = series.reindex(common_index, method='nearest')
        aligned_series.append(aligned)

    return aligned_series, common_index

def create_composite_indicator(real_estate, umich, ism):
    """创建宏观经济复合指标"""
    print("\n正在创建宏观经济复合指标...")

    # 标准化各个指标
    real_estate_std = standardize_series(real_estate, "房地产复合指标")
    umich_std = standardize_series(umich, "消费者信心指数周期成分")
    ism_std = standardize_series(ism, "制造业PMI周期成分")

    # 对齐时间序列
    align_result = align_time_series(real_estate_std, umich_std, ism_std)

    if len(align_result) == 0:
        print("错误: 可用的指标数量不足，无法创建复合指标")
        return None

    aligned_series, common_index = align_result

    if len(aligned_series) < 2:
        print("错误: 可用的指标数量不足，无法创建复合指标")
        return None

    # 创建DataFrame用于合并
    df = pd.DataFrame(index=common_index)
    component_names = []

    # 按照原始输入顺序添加可用的指标
    series_idx = 0
    if real_estate_std is not None:
        df['Real_Estate'] = aligned_series[series_idx]
        component_names.append('房地产复合指标')
        series_idx += 1

    if umich_std is not None:
        df['Consumer_Confidence'] = aligned_series[series_idx]
        component_names.append('消费者信心指数')
        series_idx += 1

    if ism_std is not None:
        df['Manufacturing_PMI'] = aligned_series[series_idx]
        component_names.append('制造业PMI')
        series_idx += 1

    # 等权重合成复合指标
    df['Macro_Composite'] = df.mean(axis=1)

    print(f"复合指标合成完成:")
    print(f"  包含组件: {', '.join(component_names)}")
    print(f"  数据点数: {len(df)}")
    print(f"  时间范围: {df.index.min().strftime('%Y-%m')} 至 {df.index.max().strftime('%Y-%m')}")
    print(f"  复合指标统计:")
    print(f"    均值: {df['Macro_Composite'].mean():.4f}")
    print(f"    标准差: {df['Macro_Composite'].std():.4f}")
    print(f"    最小值: {df['Macro_Composite'].min():.4f}")
    print(f"    最大值: {df['Macro_Composite'].max():.4f}")

    return df

def create_visualization(composite_df):
    """创建可视化图表"""
    print("\n正在创建可视化图表...")

    fig, axes = plt.subplots(2, 1, figsize=(15, 12))

    # 第一个子图：各个组成指标
    axes[0].set_title('宏观经济复合指标 - 组成部分', fontsize=16, fontweight='bold')

    colors = ['blue', 'green', 'red', 'orange']
    component_cols = [col for col in composite_df.columns if col != 'Macro_Composite']

    for i, col in enumerate(component_cols):
        axes[0].plot(composite_df.index, composite_df[col],
                    color=colors[i % len(colors)], linewidth=1.5,
                    label=col.replace('_', ' '), alpha=0.7)

    axes[0].set_ylabel('标准化值')
    axes[0].grid(True, alpha=0.3)
    axes[0].legend()
    axes[0].axhline(y=0, color='black', linestyle='--', alpha=0.5)

    # 第二个子图：复合指标
    axes[1].set_title('宏观经济复合指标 - 最终合成结果', fontsize=16, fontweight='bold')
    axes[1].plot(composite_df.index, composite_df['Macro_Composite'],
                color='purple', linewidth=2.5, label='宏观经济复合指标')

    # 添加零线和±1标准差线
    axes[1].axhline(y=0, color='black', linestyle='-', alpha=0.5, linewidth=1)
    axes[1].axhline(y=1, color='red', linestyle='--', alpha=0.5, label='+1标准差')
    axes[1].axhline(y=-1, color='red', linestyle='--', alpha=0.5, label='-1标准差')

    # 填充区域
    axes[1].fill_between(composite_df.index, composite_df['Macro_Composite'], 0,
                        where=(composite_df['Macro_Composite'] > 0),
                        color='green', alpha=0.2, label='经济扩张期')
    axes[1].fill_between(composite_df.index, composite_df['Macro_Composite'], 0,
                        where=(composite_df['Macro_Composite'] < 0),
                        color='red', alpha=0.2, label='经济收缩期')

    axes[1].set_ylabel('复合指标值')
    axes[1].set_xlabel('时间')
    axes[1].grid(True, alpha=0.3)
    axes[1].legend()

    plt.tight_layout()
    plt.savefig('宏观经济复合指标分析.png', dpi=300, bbox_inches='tight')
    plt.show()

def analyze_economic_cycles(composite_df):
    """分析经济周期"""
    print("\n=== 经济周期分析 ===")

    composite_series = composite_df['Macro_Composite']

    # 识别扩张和收缩期
    expansion_periods = composite_series > 0
    contraction_periods = composite_series < 0

    expansion_pct = (expansion_periods.sum() / len(composite_series)) * 100
    contraction_pct = (contraction_periods.sum() / len(composite_series)) * 100

    print(f"经济扩张期占比: {expansion_pct:.1f}%")
    print(f"经济收缩期占比: {contraction_pct:.1f}%")

    # 识别极值点
    max_value = composite_series.max()
    min_value = composite_series.min()
    max_date = composite_series.idxmax()
    min_date = composite_series.idxmin()

    print(f"\n极值分析:")
    print(f"最高点: {max_value:.4f} ({max_date.strftime('%Y年%m月')})")
    print(f"最低点: {min_value:.4f} ({min_date.strftime('%Y年%m月')})")

    # 近期状态分析
    recent_data = composite_series.tail(12)  # 最近12个月
    recent_avg = recent_data.mean()
    recent_trend = "上升" if recent_data.iloc[-1] > recent_data.iloc[0] else "下降"

    print(f"\n近期状态分析（最近12个月）:")
    print(f"平均值: {recent_avg:.4f}")
    print(f"当前值: {composite_series.iloc[-1]:.4f}")
    print(f"趋势: {recent_trend}")

    if recent_avg > 0.5:
        status = "强劲扩张"
    elif recent_avg > 0:
        status = "温和扩张"
    elif recent_avg > -0.5:
        status = "温和收缩"
    else:
        status = "深度收缩"

    print(f"经济状态: {status}")

def main():
    """主函数"""
    print("=== 宏观经济复合指标合成分析 ===\n")

    # 1. 加载数据
    real_estate_data = load_real_estate_composite()
    umich_cycle, ism_cycle = load_seasonal_adjusted_indicators()

    # 2. 创建复合指标
    composite_df = create_composite_indicator(real_estate_data, umich_cycle, ism_cycle)

    if composite_df is None:
        print("错误: 无法创建复合指标")
        return

    # 3. 保存结果
    output_file = 'data/macro_economic_composite_indicator.csv'
    composite_df.to_csv(output_file)
    print(f"\n复合指标数据已保存到: {output_file}")

    # 4. 创建可视化
    create_visualization(composite_df)

    # 5. 经济周期分析
    analyze_economic_cycles(composite_df)

    # 6. 输出组件相关性分析
    print("\n=== 组件相关性分析 ===")
    correlation_matrix = composite_df.corr()
    print(correlation_matrix.round(4))

if __name__ == "__main__":
    main()
