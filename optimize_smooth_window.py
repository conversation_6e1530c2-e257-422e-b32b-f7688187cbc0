#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化择时指标的平滑窗口参数（性能优化版）

测试不同的smooth_window值对择时策略回测结果的影响，
找出最佳的平滑窗口大小。
"""

import pandas as pd
import numpy as np
import yfinance as yf
import os
import warnings
from scipy import stats
from statsmodels.tsa.filters.hp_filter import hpfilter
import matplotlib.pyplot as plt
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

warnings.filterwarnings('ignore')

# 设置数据目录
DATA_DIR = 'data'

def rolling_smooth_and_detrend_fast(series, window_size=252, smooth_window=5):
    """
    优化版：在滚动窗口中进行去噪和去趋势处理
    """
    n = len(series)
    result = np.full(n, np.nan)

    # 预先计算平滑序列
    smoothed_series = series.rolling(window=smooth_window, center=True).mean()
    smoothed_series = smoothed_series.fillna(method='bfill').fillna(method='ffill')

    for i in range(window_size-1, n):  # 从有足够数据的位置开始
        start_idx = i - window_size + 1

        # 获取窗口数据
        window_data = smoothed_series.iloc[start_idx:i+1].dropna()

        if len(window_data) >= 30:
            try:
                # 使用HP滤波去趋势
                cycle, _ = hpfilter(window_data.values, lamb=7000000)
                result[i] = cycle[-1]
            except Exception:
                # 简化的线性去趋势
                x = np.arange(len(window_data))
                slope = (window_data.iloc[-1] - window_data.iloc[0]) / (len(window_data) - 1)
                trend_value = window_data.iloc[0] + slope * (len(window_data) - 1)
                result[i] = window_data.iloc[-1] - trend_value
        else:
            if len(window_data) > 0:
                result[i] = window_data.iloc[-1] - window_data.mean()

    return pd.Series(result, index=series.index)

def calculate_rolling_percentiles(data, window=252, smooth_window=5):
    """计算滚动分位数"""
    indicators = ['HY_OAS', 'TIPS_5Y', 'EPU']
    percentile_data = pd.DataFrame(index=data.index)

    for indicator in indicators:
        if indicator in data.columns:
            # 使用指定的smooth_window进行处理
            processed_series = rolling_smooth_and_detrend_fast(data[indicator],
                                                             window_size=window,
                                                             smooth_window=smooth_window)

            # 计算滚动分位数
            def rolling_percentile(series, window_size):
                result = pd.Series(index=series.index, dtype=float)
                for i in range(len(series)):
                    start_idx = max(0, i - window_size + 1)
                    end_idx = i + 1
                    window_data = series.iloc[start_idx:end_idx].dropna()

                    if len(window_data) >= window_size // 2:
                        current_value = window_data.iloc[-1]
                        percentile = stats.percentileofscore(window_data, current_value) / 100.0
                        result.iloc[i] = percentile
                return result

            percentile_data[f'{indicator}_percentile'] = rolling_percentile(processed_series, window)

    return percentile_data

def create_timing_indicator(percentile_data):
    """创建择时指标"""
    timing_data = percentile_data.copy()

    # 调整指标方向
    timing_data['HY_OAS_adjusted'] = 1 - timing_data['HY_OAS_percentile']
    timing_data['TIPS_5Y_adjusted'] = timing_data['TIPS_5Y_percentile']
    timing_data['EPU_adjusted'] = 1 - timing_data['EPU_percentile']

    # 计算最终择时指标
    timing_data['Market_Timing_Indicator'] = (
        timing_data['HY_OAS_adjusted'] +
        timing_data['TIPS_5Y_adjusted'] +
        timing_data['EPU_adjusted']
    ) / 3 * 100

    return timing_data.dropna()

def backtest_strategy(timing_indicator, sp500_prices, start_date='2003-05-12'):
    """回测择时策略"""
    # 对齐数据
    start_date = pd.to_datetime(start_date)
    common_dates = sp500_prices.index.intersection(timing_indicator.index)
    common_dates = common_dates[common_dates >= start_date]

    if len(common_dates) == 0:
        return None

    indicator_aligned = timing_indicator.reindex(common_dates, method='ffill')
    prices_aligned = sp500_prices.reindex(common_dates, method='ffill')

    # 生成交易信号
    signals = pd.Series(index=common_dates, dtype=float)
    current_position = 1.0

    for date in common_dates:
        indicator_value = indicator_aligned[date]
        if pd.isna(indicator_value):
            signals[date] = current_position
            continue

        if indicator_value < 10:
            current_position = 0.0
        elif indicator_value > 25:
            current_position = 1.0

        signals[date] = current_position

    # 计算收益率
    returns = prices_aligned.pct_change().fillna(0)
    strategy_returns = signals.shift(1).fillna(1.0) * returns
    buy_hold_returns = returns

    # 计算绩效指标
    def calc_metrics(ret_series):
        total_days = len(ret_series)
        total_years = total_days / 252
        total_return = (1 + ret_series).prod() - 1
        annual_return = (1 + total_return) ** (1/total_years) - 1
        annual_vol = ret_series.std() * np.sqrt(252)
        sharpe = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0

        cumret = (1 + ret_series).cumprod()
        drawdown = (cumret / cumret.expanding().max() - 1)
        max_dd = drawdown.min()

        return {
            'annual_return': annual_return,
            'annual_volatility': annual_vol,
            'sharpe_ratio': sharpe,
            'max_drawdown': max_dd,
            'cash_days': (signals == 0).sum(),
            'cash_ratio': (signals == 0).sum() / len(signals)
        }

    strategy_metrics = calc_metrics(strategy_returns)
    buy_hold_metrics = calc_metrics(buy_hold_returns)

    return {
        'strategy': strategy_metrics,
        'buy_hold': buy_hold_metrics,
        'excess_return': strategy_metrics['annual_return'] - buy_hold_metrics['annual_return'],
        'excess_sharpe': strategy_metrics['sharpe_ratio'] - buy_hold_metrics['sharpe_ratio']
    }

def test_single_window(args):
    """测试单个窗口值的函数，用于并行化"""
    smooth_window, combined_data, sp500_prices = args

    try:
        # 计算择时指标
        percentile_data = calculate_rolling_percentiles(combined_data, smooth_window=smooth_window)
        timing_data = create_timing_indicator(percentile_data)
        timing_indicator = timing_data['Market_Timing_Indicator']

        # 回测策略
        backtest_result = backtest_strategy(timing_indicator, sp500_prices)

        if backtest_result is not None:
            result = {
                'smooth_window': smooth_window,
                'strategy_annual_return': backtest_result['strategy']['annual_return'],
                'strategy_volatility': backtest_result['strategy']['annual_volatility'],
                'strategy_sharpe': backtest_result['strategy']['sharpe_ratio'],
                'strategy_max_dd': backtest_result['strategy']['max_drawdown'],
                'buy_hold_annual_return': backtest_result['buy_hold']['annual_return'],
                'buy_hold_sharpe': backtest_result['buy_hold']['sharpe_ratio'],
                'excess_return': backtest_result['excess_return'],
                'excess_sharpe': backtest_result['excess_sharpe'],
                'cash_days': backtest_result['strategy']['cash_days'],
                'cash_ratio': backtest_result['strategy']['cash_ratio']
            }
            return result
        else:
            return None
    except Exception as e:
        print(f"窗口 {smooth_window} 测试失败: {e}")
        return None

def optimize_smooth_window():
    """优化平滑窗口参数"""
    print("开始优化平滑窗口参数...")

    # 1. 加载基础数据
    print("1. 加载数据...")

    # 加载原始数据
    hy_oas = pd.read_csv(os.path.join(DATA_DIR, 'hy_oas.csv'), index_col=0, parse_dates=True)
    tips_inflation = pd.read_csv(os.path.join(DATA_DIR, 'tips_5y_inflation.csv'), index_col=0, parse_dates=True)
    epu_index = pd.read_csv(os.path.join(DATA_DIR, 'daily_epu_index.csv'), index_col=0, parse_dates=True)

    # 数据预处理
    hy_oas_clean = hy_oas[['HY_OAS']].copy()
    tips_clean = tips_inflation.copy()
    tips_clean.columns = ['TIPS_5Y']
    epu_clean = epu_index.copy()
    epu_clean.columns = ['EPU']

    # 合并数据
    start_date = max(hy_oas_clean.index.min(), tips_clean.index.min(), epu_clean.index.min())
    end_date = min(hy_oas_clean.index.max(), tips_clean.index.max(), epu_clean.index.max())
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')

    combined_data = pd.DataFrame({
        'HY_OAS': hy_oas_clean.reindex(date_range, method='ffill')['HY_OAS'],
        'TIPS_5Y': tips_clean.reindex(date_range, method='ffill')['TIPS_5Y'],
        'EPU': epu_clean.reindex(date_range, method='ffill')['EPU']
    }).dropna()

    # 下载标普500数据
    sp500_data = yf.download('^GSPC', start='2000-01-01', progress=False)
    if isinstance(sp500_data.columns, pd.MultiIndex):
        sp500_data.columns = sp500_data.columns.droplevel(1)
    sp500_prices = sp500_data['Adj Close'] if 'Adj Close' in sp500_data.columns else sp500_data['Close']

    print(f"   数据加载完成，共 {len(combined_data)} 条记录")

    # 2. 测试不同的smooth_window值（并行化）
    print("\n2. 测试不同的平滑窗口（并行化）...")

    # 精确测试范围：12-20天，每个值都测试
    smooth_windows = list(range(12, 21))
    results = []

    print(f"   将测试 {len(smooth_windows)} 个窗口值: {smooth_windows}")

    # 准备并行化参数
    args_list = [(sw, combined_data, sp500_prices) for sw in smooth_windows]

    # 使用进程池并行化
    max_workers = min(mp.cpu_count(), len(smooth_windows))
    print(f"   使用 {max_workers} 个进程并行计算...")

    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_window = {executor.submit(test_single_window, args): args[0]
                           for args in args_list}

        # 收集结果
        completed = 0
        for future in as_completed(future_to_window):
            window = future_to_window[future]
            completed += 1

            try:
                result = future.result()
                if result is not None:
                    results.append(result)
                    print(f"   ✓ 窗口 {window:2d} ({completed:2d}/{len(smooth_windows)}) "
                          f"夏普={result['strategy_sharpe']:.3f}, 超额收益={result['excess_return']:.2%}")
                else:
                    print(f"   ✗ 窗口 {window:2d} ({completed:2d}/{len(smooth_windows)}) 回测失败")
            except Exception as e:
                print(f"   ✗ 窗口 {window:2d} ({completed:2d}/{len(smooth_windows)}) 错误: {str(e)[:30]}")
                continue

    # 3. 分析结果
    print(f"\n3. 分析结果...")

    if not results:
        print("   没有成功的测试结果")
        return None

    results_df = pd.DataFrame(results)

    # 保存详细结果
    output_file = os.path.join(DATA_DIR, 'smooth_window_optimization_results.csv')
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"   详细结果已保存到: {output_file}")

    return results_df

def analyze_optimization_results(results_df):
    """分析优化结果"""
    print("\n" + "="*80)
    print("平滑窗口优化结果分析")
    print("="*80)

    # 找出各项指标的最优值
    best_sharpe = results_df.loc[results_df['strategy_sharpe'].idxmax()]
    best_excess_sharpe = results_df.loc[results_df['excess_sharpe'].idxmax()]
    best_return = results_df.loc[results_df['strategy_annual_return'].idxmax()]
    best_excess_return = results_df.loc[results_df['excess_return'].idxmax()]
    min_drawdown = results_df.loc[results_df['strategy_max_dd'].idxmax()]  # 最大回撤是负值，最大值表示回撤最小

    print(f"最佳夏普比率: smooth_window = {best_sharpe['smooth_window']}, 夏普比率 = {best_sharpe['strategy_sharpe']:.3f}")
    print(f"最佳超额夏普: smooth_window = {best_excess_sharpe['smooth_window']}, 超额夏普 = {best_excess_sharpe['excess_sharpe']:.3f}")
    print(f"最佳收益率: smooth_window = {best_return['smooth_window']}, 年化收益率 = {best_return['strategy_annual_return']:.2%}")
    print(f"最佳超额收益: smooth_window = {best_excess_return['smooth_window']}, 超额收益 = {best_excess_return['excess_return']:.2%}")
    print(f"最小回撤: smooth_window = {min_drawdown['smooth_window']}, 最大回撤 = {min_drawdown['strategy_max_dd']:.2%}")

    # 综合评分（夏普比率权重50%，超额收益权重30%，最大回撤权重20%）
    results_df['composite_score'] = (
        0.5 * (results_df['strategy_sharpe'] / results_df['strategy_sharpe'].max()) +
        0.3 * ((results_df['excess_return'] - results_df['excess_return'].min()) /
               (results_df['excess_return'].max() - results_df['excess_return'].min())) +
        0.2 * ((results_df['strategy_max_dd'] - results_df['strategy_max_dd'].min()) /
               (results_df['strategy_max_dd'].max() - results_df['strategy_max_dd'].min()))
    )

    best_composite = results_df.loc[results_df['composite_score'].idxmax()]
    print(f"\n综合最佳: smooth_window = {best_composite['smooth_window']}")
    print(f"  年化收益率: {best_composite['strategy_annual_return']:.2%}")
    print(f"  夏普比率: {best_composite['strategy_sharpe']:.3f}")
    print(f"  超额收益: {best_composite['excess_return']:.2%}")
    print(f"  最大回撤: {best_composite['strategy_max_dd']:.2%}")
    print(f"  空仓比例: {best_composite['cash_ratio']:.1%}")

    return int(best_composite['smooth_window'])

if __name__ == "__main__":
    # 运行优化
    results_df = optimize_smooth_window()

    if results_df is not None:
        # 分析结果
        best_window = analyze_optimization_results(results_df)
        print(f"\n推荐使用 smooth_window = {best_window}")
    else:
        print("优化失败")
