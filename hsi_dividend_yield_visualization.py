#!/usr/bin/env python3
"""
恒生指数成分股股息率排名可视化
创建多种图表展示股息率排名结果
"""

import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
from datetime import datetime
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIDividendYieldVisualizer:
    """恒生指数成分股股息率可视化器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file
        self.df = None
        self.output_dir = "hsi_dividend_yield_charts"
        
        # 创建输出目录
        os.makedirs(self.output_dir, exist_ok=True)
        
    def load_latest_data(self):
        """加载最新的排名数据"""
        if self.data_file and os.path.exists(self.data_file):
            self.df = pd.read_csv(self.data_file, encoding='utf-8-sig')
        else:
            # 优先查找快速排名文件
            fast_ranking_dir = "hsi_fast_dividend_yield_rankings"
            ranking_dir = "hsi_dividend_yield_rankings"

            # 先尝试快速排名目录
            if os.path.exists(fast_ranking_dir):
                files = [f for f in os.listdir(fast_ranking_dir) if f.startswith('hsi_fast_dividend_yield_ranking_') and f.endswith('.csv') and 'simple' not in f]
                if files:
                    latest_file = sorted(files)[-1]
                    self.data_file = os.path.join(fast_ranking_dir, latest_file)
                    self.df = pd.read_csv(self.data_file, encoding='utf-8-sig')
                    print(f"📊 加载快速排名数据文件: {self.data_file}")

            # 如果没有找到，再尝试原始排名目录
            if self.df is None and os.path.exists(ranking_dir):
                files = [f for f in os.listdir(ranking_dir) if f.startswith('hsi_dividend_yield_ranking_') and f.endswith('.csv') and 'simple' not in f]
                if files:
                    latest_file = sorted(files)[-1]
                    self.data_file = os.path.join(ranking_dir, latest_file)
                    self.df = pd.read_csv(self.data_file, encoding='utf-8-sig')
                    print(f"📊 加载排名数据文件: {self.data_file}")

        if self.df is not None:
            print(f"✅ 成功加载 {len(self.df)} 只股票的排名数据")
            return True
        else:
            print("❌ 无法加载数据文件")
            return False
    
    def create_top_dividend_yield_chart(self, top_n: int = 20):
        """创建前N名股息率排名柱状图"""
        if self.df is None:
            return None
        
        # 获取前N名有分红的股票
        top_stocks = self.df[self.df['dividend_yield'] > 0].head(top_n)
        
        plt.figure(figsize=(14, 10))
        
        # 创建柱状图
        bars = plt.barh(range(len(top_stocks)), top_stocks['dividend_yield'], 
                       color=plt.cm.RdYlGn_r(np.linspace(0.2, 0.8, len(top_stocks))))
        
        # 设置y轴标签
        plt.yticks(range(len(top_stocks)), 
                  [f"{row['stock_code']} {row['stock_name']}" for _, row in top_stocks.iterrows()])
        
        # 添加数值标签
        for i, (_, row) in enumerate(top_stocks.iterrows()):
            freq_type = row.get('frequency_type', '未知')
            plt.text(row['dividend_yield'] + 0.1, i, f"{row['dividend_yield']:.2f}% ({freq_type})",
                    va='center', fontsize=8)
        
        plt.xlabel('股息率 (%)', fontsize=12)
        plt.title(f'恒生指数成分股股息率排名前{top_n}名\n(基于最新价格和智能分红频率计算)', fontsize=14, fontweight='bold')
        plt.grid(axis='x', alpha=0.3)
        
        # 反转y轴，使排名第一的在顶部
        plt.gca().invert_yaxis()
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.output_dir, f"top_{top_n}_dividend_yield_{timestamp}.png")
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 前{top_n}名股息率图表已保存: {filename}")
        
        plt.show()
        return filename
    
    def create_dividend_yield_distribution(self):
        """创建股息率分布图"""
        if self.df is None:
            return None
        
        # 过滤有分红的股票
        dividend_stocks = self.df[self.df['dividend_yield'] > 0]
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 6))
        
        # 直方图
        ax1.hist(dividend_stocks['dividend_yield'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
        ax1.set_xlabel('股息率 (%)', fontsize=12)
        ax1.set_ylabel('股票数量', fontsize=12)
        ax1.set_title('恒生指数成分股股息率分布', fontsize=14, fontweight='bold')
        ax1.grid(alpha=0.3)
        
        # 添加统计信息
        mean_yield = dividend_stocks['dividend_yield'].mean()
        median_yield = dividend_stocks['dividend_yield'].median()
        ax1.axvline(mean_yield, color='red', linestyle='--', label=f'平均值: {mean_yield:.2f}%')
        ax1.axvline(median_yield, color='orange', linestyle='--', label=f'中位数: {median_yield:.2f}%')
        ax1.legend()
        
        # 箱线图
        ax2.boxplot(dividend_stocks['dividend_yield'], vert=True, patch_artist=True,
                   boxprops=dict(facecolor='lightgreen', alpha=0.7))
        ax2.set_ylabel('股息率 (%)', fontsize=12)
        ax2.set_title('股息率箱线图', fontsize=14, fontweight='bold')
        ax2.grid(alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.output_dir, f"dividend_yield_distribution_{timestamp}.png")
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 股息率分布图已保存: {filename}")
        
        plt.show()
        return filename
    
    def create_sector_analysis_chart(self):
        """创建行业分析图表（基于股票名称推断行业）"""
        if self.df is None:
            return None
        
        # 简单的行业分类（基于股票名称关键词）
        def classify_sector(name):
            if any(keyword in name for keyword in ['银行', '建设银行', '工商银行', '中国银行', '招商银行', '中银']):
                return '银行业'
            elif any(keyword in name for keyword in ['保险', '人寿', '平安']):
                return '保险业'
            elif any(keyword in name for keyword in ['地产', '置业', '基建', '长实']):
                return '房地产'
            elif any(keyword in name for keyword in ['石油', '石化', '海洋石油', '神华', '能源']):
                return '能源'
            elif any(keyword in name for keyword in ['电力', '电能', '煤气']):
                return '公用事业'
            elif any(keyword in name for keyword in ['汽车', '比亚迪']):
                return '汽车'
            elif any(keyword in name for keyword in ['科技', '腾讯', '小米', '网易', '阿里', '京东', '美团']):
                return '科技'
            elif any(keyword in name for keyword in ['制药', '生物', '药明']):
                return '医药'
            else:
                return '其他'
        
        # 添加行业分类
        dividend_stocks = self.df[self.df['dividend_yield'] > 0].copy()
        dividend_stocks['sector'] = dividend_stocks['stock_name'].apply(classify_sector)

        # 计算各行业平均股息率和分红频率分布
        sector_stats = dividend_stocks.groupby('sector').agg({
            'dividend_yield': ['mean', 'count'],
            'frequency_type': lambda x: x.mode().iloc[0] if len(x.mode()) > 0 else '未知'
        }).round(2)

        sector_stats.columns = ['平均股息率', '股票数量', '主要分红频率']
        sector_stats = sector_stats.sort_values('平均股息率', ascending=False)
        
        fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(16, 8))
        
        # 各行业平均股息率
        bars1 = ax1.bar(sector_stats.index, sector_stats['平均股息率'], 
                       color=plt.cm.Set3(np.linspace(0, 1, len(sector_stats))))
        ax1.set_ylabel('平均股息率 (%)', fontsize=12)
        ax1.set_title('各行业平均股息率', fontsize=14, fontweight='bold')
        ax1.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars1, sector_stats['平均股息率']):
            ax1.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    f'{value:.2f}%', ha='center', va='bottom', fontsize=10)
        
        # 各行业股票数量
        bars2 = ax2.bar(sector_stats.index, sector_stats['股票数量'], 
                       color=plt.cm.Set2(np.linspace(0, 1, len(sector_stats))))
        ax2.set_ylabel('股票数量', fontsize=12)
        ax2.set_title('各行业股票数量', fontsize=14, fontweight='bold')
        ax2.tick_params(axis='x', rotation=45)
        
        # 添加数值标签
        for bar, value in zip(bars2, sector_stats['股票数量']):
            ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    f'{int(value)}', ha='center', va='bottom', fontsize=10)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.output_dir, f"sector_analysis_{timestamp}.png")
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 行业分析图表已保存: {filename}")
        
        plt.show()
        return filename
    
    def create_price_vs_yield_scatter(self):
        """创建股价与股息率散点图"""
        if self.df is None:
            return None
        
        # 过滤有分红的股票
        dividend_stocks = self.df[self.df['dividend_yield'] > 0]
        
        plt.figure(figsize=(12, 8))
        
        # 创建散点图
        scatter = plt.scatter(dividend_stocks['latest_price'], dividend_stocks['dividend_yield'], 
                            alpha=0.6, s=60, c=dividend_stocks['dividend_yield'], 
                            cmap='RdYlGn', edgecolors='black', linewidth=0.5)
        
        # 添加颜色条
        cbar = plt.colorbar(scatter)
        cbar.set_label('股息率 (%)', fontsize=12)
        
        plt.xlabel('最新股价 (港元)', fontsize=12)
        plt.ylabel('股息率 (%)', fontsize=12)
        plt.title('恒生指数成分股：股价 vs 股息率', fontsize=14, fontweight='bold')
        plt.grid(alpha=0.3)
        
        # 标注一些特殊点
        high_yield_stocks = dividend_stocks[dividend_stocks['dividend_yield'] > 8]
        for _, row in high_yield_stocks.iterrows():
            plt.annotate(f"{row['stock_code']}\n{row['stock_name']}", 
                        (row['latest_price'], row['dividend_yield']),
                        xytext=(5, 5), textcoords='offset points', 
                        fontsize=8, alpha=0.8)
        
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.output_dir, f"price_vs_yield_scatter_{timestamp}.png")
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 股价vs股息率散点图已保存: {filename}")
        
        plt.show()
        return filename
    
    def create_comprehensive_dashboard(self):
        """创建综合仪表板"""
        if self.df is None:
            return None
        
        fig = plt.figure(figsize=(20, 12))
        
        # 创建子图布局
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        # 1. 前10名股息率
        ax1 = fig.add_subplot(gs[0, :2])
        top_10 = self.df[self.df['dividend_yield'] > 0].head(10)
        bars = ax1.barh(range(len(top_10)), top_10['dividend_yield'], 
                       color=plt.cm.RdYlGn_r(np.linspace(0.2, 0.8, len(top_10))))
        ax1.set_yticks(range(len(top_10)))
        ax1.set_yticklabels([f"{row['stock_code']} {row['stock_name']}" for _, row in top_10.iterrows()])
        ax1.set_xlabel('股息率 (%)')
        ax1.set_title('股息率前10名', fontweight='bold')
        ax1.invert_yaxis()
        
        # 2. 股息率分布
        ax2 = fig.add_subplot(gs[0, 2])
        dividend_stocks = self.df[self.df['dividend_yield'] > 0]
        ax2.hist(dividend_stocks['dividend_yield'], bins=15, alpha=0.7, color='skyblue')
        ax2.set_xlabel('股息率 (%)')
        ax2.set_ylabel('股票数量')
        ax2.set_title('股息率分布', fontweight='bold')
        
        # 3. 股价vs股息率散点图
        ax3 = fig.add_subplot(gs[1, :2])
        scatter = ax3.scatter(dividend_stocks['latest_price'], dividend_stocks['dividend_yield'], 
                            alpha=0.6, c=dividend_stocks['dividend_yield'], cmap='RdYlGn')
        ax3.set_xlabel('股价 (港元)')
        ax3.set_ylabel('股息率 (%)')
        ax3.set_title('股价 vs 股息率', fontweight='bold')
        
        # 4. 统计信息
        ax4 = fig.add_subplot(gs[1, 2])
        ax4.axis('off')
        stats_text = f"""
        统计信息
        ────────────
        总股票数: {len(self.df)}
        有分红股票: {len(dividend_stocks)}
        
        股息率统计:
        最高: {dividend_stocks['dividend_yield'].max():.2f}%
        最低: {dividend_stocks['dividend_yield'].min():.2f}%
        平均: {dividend_stocks['dividend_yield'].mean():.2f}%
        中位数: {dividend_stocks['dividend_yield'].median():.2f}%
        
        股价统计:
        最高: {dividend_stocks['latest_price'].max():.2f} 港元
        最低: {dividend_stocks['latest_price'].min():.2f} 港元
        平均: {dividend_stocks['latest_price'].mean():.2f} 港元
        """
        ax4.text(0.1, 0.9, stats_text, transform=ax4.transAxes, fontsize=11, 
                verticalalignment='top', fontfamily='monospace')
        
        # 5. 股息率区间分布
        ax5 = fig.add_subplot(gs[2, :])
        bins = [0, 2, 4, 6, 8, 10, float('inf')]
        labels = ['0-2%', '2-4%', '4-6%', '6-8%', '8-10%', '10%+']
        dividend_stocks['yield_range'] = pd.cut(dividend_stocks['dividend_yield'], bins=bins, labels=labels)
        range_counts = dividend_stocks['yield_range'].value_counts().sort_index()
        
        bars = ax5.bar(range_counts.index, range_counts.values, 
                      color=plt.cm.RdYlGn_r(np.linspace(0.2, 0.8, len(range_counts))))
        ax5.set_xlabel('股息率区间')
        ax5.set_ylabel('股票数量')
        ax5.set_title('股息率区间分布', fontweight='bold')
        
        # 添加数值标签
        for bar, value in zip(bars, range_counts.values):
            ax5.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.1, 
                    f'{int(value)}', ha='center', va='bottom')
        
        plt.suptitle('恒生指数成分股股息率分析仪表板', fontsize=16, fontweight='bold', y=0.98)
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = os.path.join(self.output_dir, f"comprehensive_dashboard_{timestamp}.png")
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 综合仪表板已保存: {filename}")
        
        plt.show()
        return filename

def main():
    """主函数"""
    print("🎨 恒生指数成分股股息率可视化")
    print("=" * 50)
    
    # 创建可视化器
    visualizer = HSIDividendYieldVisualizer()
    
    # 加载数据
    if not visualizer.load_latest_data():
        print("❌ 无法加载数据，请先运行 hsi_dividend_yield_ranking.py")
        return
    
    print(f"\n📊 开始创建可视化图表...")
    print(f"📁 输出目录: {visualizer.output_dir}")
    
    try:
        # 创建各种图表
        print("\n1. 创建前20名股息率排名图...")
        visualizer.create_top_dividend_yield_chart(20)
        
        print("\n2. 创建股息率分布图...")
        visualizer.create_dividend_yield_distribution()
        
        print("\n3. 创建行业分析图...")
        visualizer.create_sector_analysis_chart()
        
        print("\n4. 创建股价vs股息率散点图...")
        visualizer.create_price_vs_yield_scatter()
        
        print("\n5. 创建综合仪表板...")
        visualizer.create_comprehensive_dashboard()
        
        print(f"\n✅ 所有可视化图表创建完成!")
        print(f"📁 图表保存在: {visualizer.output_dir}")
        
    except Exception as e:
        print(f"\n❌ 创建图表时出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
