"""
恒生科技指数 vs 恒生高股息指数轮动策略

策略类型：
1. 动量轮动策略：选择近期表现更好的指数
2. 均值回归轮动策略：选择相对被低估的指数
3. RSI轮动策略：基于RSI指标进行轮动
4. 买入持有对比策略

作者：AI Assistant
创建时间：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import akshare as ak
from datetime import datetime, timedelta
import warnings
import os
import pickle
import hashlib
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class RotationStrategyDataFetcher:
    def __init__(self, cache_dir="rotation_cache"):
        """初始化数据获取器"""
        self.hstech_data = None   # 恒生科技指数
        self.hshdyi_data = None   # 恒生高股息指数
        
        # 缓存设置
        self.cache_dir = cache_dir
        self.cache_expiry_hours = 24
        
        # 创建缓存目录
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)
        
        # 计算最近2年的日期范围（用于轮动策略）
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=2*365 + 30)
        
        print(f"数据获取时间范围：{self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        print(f"缓存目录：{self.cache_dir}")
    
    def _get_cache_key(self, symbol, start_date, end_date):
        """生成缓存键"""
        key_string = f"{symbol}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        return hashlib.md5(key_string.encode()).hexdigest()
    
    def _get_cache_path(self, cache_key):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{cache_key}.pkl")
    
    def _is_cache_valid(self, cache_path):
        """检查缓存是否有效"""
        if not os.path.exists(cache_path):
            return False
        
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_path))
        expiry_time = datetime.now() - timedelta(hours=self.cache_expiry_hours)
        
        return file_time > expiry_time
    
    def _load_from_cache(self, cache_key):
        """从缓存加载数据"""
        cache_path = self._get_cache_path(cache_key)
        
        if self._is_cache_valid(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    data = pickle.load(f)
                print(f"  ✅ 从缓存加载数据，数据点数：{len(data)}")
                return data
            except Exception as e:
                print(f"  ⚠️ 缓存加载失败：{e}")
                return None
        
        return None
    
    def _save_to_cache(self, cache_key, data):
        """保存数据到缓存"""
        cache_path = self._get_cache_path(cache_key)
        
        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            print(f"  💾 数据已保存到缓存")
        except Exception as e:
            print(f"  ⚠️ 缓存保存失败：{e}")
    
    def get_index_data(self, symbol, name):
        """获取指数数据"""
        print(f"正在获取{name}({symbol})数据...")
        
        # 检查缓存
        cache_key = self._get_cache_key(symbol, self.start_date, self.end_date)
        cached_data = self._load_from_cache(cache_key)
        if cached_data is not None:
            return cached_data
        
        try:
            # 使用akshare获取数据
            print(f"  尝试使用akshare获取{symbol}数据...")
            data = ak.stock_hk_index_daily_em(symbol=symbol)
            
            if data is not None and not data.empty:
                print(f"  原始数据形状: {data.shape}")
                
                # 处理列名映射
                column_mapping = {}
                for col in data.columns:
                    col_lower = str(col).lower()
                    if '日期' in str(col) or 'date' in col_lower:
                        column_mapping[col] = 'date'
                    elif '开盘' in str(col) or 'open' in col_lower:
                        column_mapping[col] = 'open'
                    elif '收盘' in str(col) or 'close' in col_lower or 'latest' in col_lower:
                        column_mapping[col] = 'close'
                    elif '最高' in str(col) or 'high' in col_lower:
                        column_mapping[col] = 'high'
                    elif '最低' in str(col) or 'low' in col_lower:
                        column_mapping[col] = 'low'
                    elif '成交量' in str(col) or 'volume' in col_lower:
                        column_mapping[col] = 'volume'
                
                # 重命名列
                data = data.rename(columns=column_mapping)
                
                # 处理日期
                if 'date' in data.columns:
                    data['date'] = pd.to_datetime(data['date'])
                    data.set_index('date', inplace=True)
                    data = data.sort_index()
                    
                    # 筛选时间范围
                    data = data[data.index >= self.start_date]
                    
                    # 检查必要的列
                    required_cols = ['open', 'high', 'low', 'close']
                    available_cols = [col for col in required_cols if col in data.columns]
                    
                    if len(available_cols) >= 4 and len(data) >= 100:
                        print(f"  ✅ 获取成功，数据点数：{len(data)}")
                        
                        # 添加volume列如果不存在
                        if 'volume' not in data.columns:
                            data['volume'] = 0
                        
                        # 确保数据类型正确
                        for col in ['open', 'high', 'low', 'close']:
                            if col in data.columns:
                                data[col] = pd.to_numeric(data[col], errors='coerce')
                        
                        # 去除空值
                        data = data.dropna(subset=['close'])
                        
                        if len(data) >= 100:
                            result_data = data[['open', 'high', 'low', 'close', 'volume']].copy()
                            # 保存到缓存
                            self._save_to_cache(cache_key, result_data)
                            return result_data
                        else:
                            print(f"  ❌ 清理后数据量不足：{len(data)}")
                    else:
                        print(f"  ❌ 数据列不完整，可用列：{available_cols}")
                else:
                    print("  ❌ 未找到日期列")
            else:
                print("  ❌ 获取的数据为空")
                
        except Exception as e:
            print(f"  ❌ 获取失败：{str(e)}")
        
        return None
    
    def get_all_data(self):
        """获取所有数据"""
        print("🚀 开始获取轮动策略数据")
        print("="*60)
        
        self.hstech_data = self.get_index_data("HSTECH", "恒生科技指数")
        self.hshdyi_data = self.get_index_data("HSHDYI", "恒生高股息指数")
        
        success_count = sum([self.hstech_data is not None, self.hshdyi_data is not None])
        
        if success_count == 2:
            print(f"\n✅ 所有数据获取成功！({success_count}/2)")
            return True
        else:
            print(f"\n❌ 数据获取失败 ({success_count}/2)")
            return False

class RotationStrategy:
    def __init__(self, hstech_data, hshdyi_data):
        """初始化轮动策略"""
        self.hstech_data = hstech_data
        self.hshdyi_data = hshdyi_data
        
        # 对齐数据日期
        self.align_data()
        
        # 策略参数（改回每日调仓进行成本分析）
        self.momentum_window = 5   # 动量窗口期（5个交易日）
        self.rebalance_freq = 1   # 调仓频率（每日调仓）
        
        print(f"数据对齐完成，共{len(self.aligned_data)}个交易日")
    
    def align_data(self):
        """对齐两个指数的数据"""
        # 找到共同的日期范围
        common_dates = self.hstech_data.index.intersection(self.hshdyi_data.index)
        
        # 创建对齐的数据
        self.aligned_data = pd.DataFrame(index=common_dates)
        self.aligned_data['hstech_close'] = self.hstech_data.loc[common_dates, 'close']
        self.aligned_data['hshdyi_close'] = self.hshdyi_data.loc[common_dates, 'close']
        
        # 去除空值
        self.aligned_data = self.aligned_data.dropna()
        
        # 计算收益率
        self.aligned_data['hstech_return'] = self.aligned_data['hstech_close'].pct_change()
        self.aligned_data['hshdyi_return'] = self.aligned_data['hshdyi_close'].pct_change()
    
    def momentum_rotation_strategy(self):
        """动量轮动策略：选择近期表现更好的指数"""
        print("🚀 执行动量轮动策略...")
        print(f"📊 参数设置：{self.momentum_window}日动量，每{self.rebalance_freq}日调仓")

        # 计算滚动收益率
        hstech_momentum = self.aligned_data['hstech_close'].pct_change(self.momentum_window)
        hshdyi_momentum = self.aligned_data['hshdyi_close'].pct_change(self.momentum_window)

        # 生成原始信号：选择动量更强的指数
        raw_signals = pd.Series(index=self.aligned_data.index, dtype=str)
        raw_signals[hstech_momentum > hshdyi_momentum] = 'HSTECH'
        raw_signals[hshdyi_momentum > hstech_momentum] = 'HSHDYI'
        raw_signals = raw_signals.fillna(method='ffill')  # 前向填充

        # 应用调仓频率限制
        signals = self.apply_rebalance_frequency(raw_signals)

        return self.backtest_strategy(signals, "动量轮动策略")

    def apply_rebalance_frequency(self, raw_signals):
        """应用调仓频率限制"""
        if self.rebalance_freq == 1:
            return raw_signals  # 每日调仓

        # 创建调仓信号
        rebalanced_signals = pd.Series(index=raw_signals.index, dtype=str)

        # 初始信号
        if len(raw_signals) > 0:
            rebalanced_signals.iloc[0] = raw_signals.iloc[0]

        # 按调仓频率更新信号
        for i in range(self.rebalance_freq, len(raw_signals), self.rebalance_freq):
            if i < len(raw_signals):
                # 在调仓日，使用当日的信号，并持续到下一个调仓日
                current_signal = raw_signals.iloc[i]
                end_idx = min(i + self.rebalance_freq, len(raw_signals))
                rebalanced_signals.iloc[i:end_idx] = current_signal

        # 前向填充剩余的空值
        rebalanced_signals = rebalanced_signals.fillna(method='ffill')

        return rebalanced_signals

    def backtest_strategy(self, signals, strategy_name):
        """回测策略（收盘前5分钟调仓模式）"""
        portfolio_value = [1.0]  # 初始资金为1
        positions = []
        position_changes = 0
        transaction_costs = 0

        # 初始持仓
        current_position = signals.iloc[0] if len(signals) > 0 else 'CASH'
        positions.append(current_position)

        for i in range(1, len(self.aligned_data)):
            # 收盘前5分钟调仓模式：
            # 1. 基于当日数据生成信号
            # 2. 当日收盘前执行调仓
            # 3. 获得当日剩余时间的收益（约占全日收益的很小比例）

            new_signal = signals.iloc[i]  # 当日信号
            prev_value = portfolio_value[-1]

            # 计算换仓成本
            transaction_cost = 0
            execution_slippage = 0  # 收盘前执行的额外滑点

            if current_position != new_signal:
                transaction_cost = 0.0012  # 0.12%（略高于正常交易，但不会太高）
                execution_slippage = 0.0003  # 0.03%额外滑点（收盘前流动性略差）
                position_changes += 1
                transaction_costs += transaction_cost

            # 修正逻辑：收盘前5分钟调仓应该能获得接近完整的当日收益
            # 关键理解：收盘价就是当日的最终价格，收盘前5分钟调仓相当于以接近收盘价执行

            if new_signal == 'HSTECH':
                daily_return = self.aligned_data['hstech_return'].iloc[i]
                if current_position == 'HSTECH':
                    # 无需调仓，获得全日收益，无交易成本
                    new_value = prev_value * (1 + daily_return)
                else:
                    # 需要调仓：能获得当日目标资产的完整收益，但需承担交易成本
                    # 这里的关键是：我们在收盘前调仓，相当于以当日收盘价买入
                    new_value = prev_value * (1 + daily_return) * (1 - transaction_cost - execution_slippage)
                positions.append('HSTECH')

            elif new_signal == 'HSHDYI':
                daily_return = self.aligned_data['hshdyi_return'].iloc[i]
                if current_position == 'HSHDYI':
                    # 无需调仓，获得全日收益，无交易成本
                    new_value = prev_value * (1 + daily_return)
                else:
                    # 需要调仓：获得目标资产当日完整收益，承担交易成本
                    new_value = prev_value * (1 + daily_return) * (1 - transaction_cost - execution_slippage)
                positions.append('HSHDYI')
            else:
                new_value = prev_value * (1 - transaction_cost)
                positions.append('CASH')

            portfolio_value.append(new_value)
            current_position = new_signal

        print(f"总共调仓 {position_changes} 次（收盘前5分钟执行）")

        # 创建结果DataFrame
        results = pd.DataFrame(index=self.aligned_data.index)
        results['portfolio_value'] = portfolio_value
        results['signals'] = positions  # positions已经包含了所有交易日的信号
        results['daily_return'] = results['portfolio_value'].pct_change()

        # 计算策略统计
        total_return = (results['portfolio_value'].iloc[-1] - 1) * 100
        trading_days = len(results)
        annual_return = ((results['portfolio_value'].iloc[-1] ** (252 / trading_days)) - 1) * 100
        volatility = results['daily_return'].std() * np.sqrt(252) * 100
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        max_drawdown = self.calculate_max_drawdown(results['portfolio_value']) * 100

        # 计算年化换仓次数
        annual_turnover = position_changes * 252 / trading_days

        strategy_stats = {
            'strategy_name': strategy_name,
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'final_value': results['portfolio_value'].iloc[-1],
            'position_changes': position_changes,
            'annual_turnover': annual_turnover,
            'total_transaction_costs': transaction_costs * 100
        }

        return results, strategy_stats

    def calculate_max_drawdown(self, portfolio_values):
        """计算最大回撤"""
        peak = portfolio_values.expanding().max()
        drawdown = (portfolio_values - peak) / peak
        return drawdown.min()

    def run_momentum_strategy(self):
        """运行动量轮动策略"""
        print("🎯 开始运行动量轮动策略")
        print("="*80)

        # 运行动量轮动策略
        momentum_results, momentum_stats = self.momentum_rotation_strategy()

        return momentum_results, momentum_stats

    def get_current_position(self):
        """获取当前最新持仓建议"""
        print("\n🔍 计算当前最新持仓建议...")

        # 计算最新的动量信号
        hstech_momentum = self.aligned_data['hstech_close'].pct_change(self.momentum_window)
        hshdyi_momentum = self.aligned_data['hshdyi_close'].pct_change(self.momentum_window)

        # 获取最新的动量值
        latest_hstech_momentum = hstech_momentum.iloc[-1]
        latest_hshdyi_momentum = hshdyi_momentum.iloc[-1]

        # 获取最新价格
        latest_hstech_price = self.aligned_data['hstech_close'].iloc[-1]
        latest_hshdyi_price = self.aligned_data['hshdyi_close'].iloc[-1]
        latest_date = self.aligned_data.index[-1]

        # 确定持仓建议
        if latest_hstech_momentum > latest_hshdyi_momentum:
            current_position = 'HSTECH'
            position_name = '恒生科技指数'
            momentum_diff = latest_hstech_momentum - latest_hshdyi_momentum
        else:
            current_position = 'HSHDYI'
            position_name = '恒生高股息指数'
            momentum_diff = latest_hshdyi_momentum - latest_hstech_momentum

        print("\n" + "="*80)
        print("📊 当前最新持仓建议")
        print("="*80)
        print(f"📅 分析日期: {latest_date.strftime('%Y-%m-%d')}")
        print(f"🎯 建议持仓: {position_name} ({current_position})")
        print(f"📈 {self.momentum_window}日动量:")
        print(f"   恒生科技指数: {latest_hstech_momentum:+.2%}")
        print(f"   恒生高股息指数: {latest_hshdyi_momentum:+.2%}")
        print(f"   动量差值: {momentum_diff:+.2%}")
        print(f"💰 最新价格:")
        print(f"   恒生科技指数: {latest_hstech_price:.2f}")
        print(f"   恒生高股息指数: {latest_hshdyi_price:.2f}")

        return {
            'date': latest_date,
            'position': current_position,
            'position_name': position_name,
            'hstech_momentum': latest_hstech_momentum,
            'hshdyi_momentum': latest_hshdyi_momentum,
            'momentum_diff': momentum_diff,
            'hstech_price': latest_hstech_price,
            'hshdyi_price': latest_hshdyi_price
        }

class MomentumAnalyzer:
    def __init__(self, results, stats):
        """初始化分析器"""
        self.results = results
        self.stats = stats

    def print_performance_summary(self):
        """打印策略表现总结"""
        print("\n" + "="*80)
        print("📊 动量轮动策略表现总结")
        print("="*80)

        print(f"\n🎯 策略名称: {self.stats['strategy_name']}")
        print(f"📈 总收益率: {self.stats['total_return']:.2f}%")
        print(f"📊 年化收益率: {self.stats['annual_return']:.2f}%")
        print(f"📉 年化波动率: {self.stats['volatility']:.2f}%")
        print(f"⚡ 夏普比率: {self.stats['sharpe_ratio']:.3f}")
        print(f"🛡️ 最大回撤: {self.stats['max_drawdown']:.2f}%")
        print(f"💰 最终净值: {self.stats['final_value']:.3f}")
        print(f"🔄 换仓次数: {self.stats['position_changes']}次")
        print(f"📅 年化换仓: {self.stats['annual_turnover']:.1f}次/年")
        print(f"💸 交易成本: {self.stats['total_transaction_costs']:.2f}%")

        # 计算额外统计信息
        daily_returns = self.results['daily_return'].dropna()
        win_rate = (daily_returns > 0).mean() * 100
        avg_win = daily_returns[daily_returns > 0].mean() * 100
        avg_loss = daily_returns[daily_returns < 0].mean() * 100

        print(f"\n📊 详细统计:")
        print(f"   胜率: {win_rate:.1f}%")
        print(f"   平均盈利: {avg_win:.2f}%")
        print(f"   平均亏损: {avg_loss:.2f}%")
        print(f"   盈亏比: {abs(avg_win/avg_loss):.2f}" if avg_loss != 0 else "   盈亏比: N/A")

        # 持仓分布
        position_counts = self.results['signals'].value_counts()
        print(f"\n🔄 持仓分布:")
        for position, count in position_counts.items():
            percentage = count / len(self.results) * 100
            position_name = "恒生科技指数" if position == "HSTECH" else "恒生高股息指数" if position == "HSHDYI" else position
            print(f"   {position_name}: {count}天 ({percentage:.1f}%)")

    def plot_performance_chart(self):
        """绘制策略表现图"""
        fig, axes = plt.subplots(2, 1, figsize=(12, 10))
        fig.suptitle('动量轮动策略表现分析', fontsize=16, fontweight='bold')

        # 1. 净值曲线
        ax1 = axes[0]
        ax1.plot(self.results.index, self.results['portfolio_value'],
                color='red', linewidth=2, label='动量轮动策略')
        ax1.set_title('净值曲线')
        ax1.set_ylabel('净值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 持仓分布
        ax2 = axes[1]
        position_counts = self.results['signals'].value_counts()
        colors = ['red' if pos == 'HSTECH' else 'orange' if pos == 'HSHDYI' else 'gray'
                 for pos in position_counts.index]
        labels = ['恒生科技指数' if pos == 'HSTECH' else '恒生高股息指数' if pos == 'HSHDYI' else pos
                 for pos in position_counts.index]

        ax2.pie(position_counts.values, labels=labels, colors=colors, autopct='%1.1f%%')
        ax2.set_title('持仓分布')

        # 调整布局
        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"momentum_strategy_performance_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 策略表现图表已保存至: {filename}")

        plt.show()

def main():
    """主函数"""
    print("🔄 恒生科技指数 vs 恒生高股息指数动量轮动策略分析（收盘前5分钟调仓）")
    print("="*80)

    # 1. 获取数据
    fetcher = RotationStrategyDataFetcher()
    if not fetcher.get_all_data():
        print("❌ 数据获取失败，程序退出")
        return

    # 2. 运行动量轮动策略
    strategy = RotationStrategy(fetcher.hstech_data, fetcher.hshdyi_data)
    results, stats = strategy.run_momentum_strategy()

    # 3. 分析结果
    analyzer = MomentumAnalyzer(results, stats)
    analyzer.print_performance_summary()
    analyzer.plot_performance_chart()

    # 4. 输出当前最新持仓
    current_position = strategy.get_current_position()

    print("\n🎉 动量轮动策略分析完成！")
    print("📊 请查看生成的图表文件")
    print(f"🎯 当前建议持仓: {current_position['position_name']}")

if __name__ == "__main__":
    main()
