"""
港股主要指数最近一个月行情图表脚本（使用akshare）

功能：
- 使用akshare获取恒生指数(HSI)、恒生科技指数(HSTECH)、恒生国企指数(HSCEI)、恒生高股息指数(HSHDYI)最近一个月的行情数据
- 绘制价格走势图和对比分析图
- 显示涨跌幅统计信息

作者：AI Assistant
创建时间：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import akshare as ak
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HKIndicesAkShareFetcher:
    def __init__(self):
        """初始化数据获取器"""
        self.data = {}
        
        # 指数配置
        self.indices_config = {
            'HSI': {'symbol': 'HSI', 'name': '恒生指数', 'type': 'growth'},
            'HSTECH': {'symbol': 'HSTECH', 'name': '恒生科技指数', 'type': 'growth'},
            'HSCEI': {'symbol': 'HSCEI', 'name': '恒生国企指数', 'type': 'value'},
            'HSHDYI': {'symbol': 'HSHDYI', 'name': '恒生高股息指数', 'type': 'value'}
        }
        
        # 计算最近一个月的日期范围
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=35)
        
        print(f"数据获取时间范围：{self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
    
    def get_index_data(self, index_code, symbol, name):
        """获取单个指数数据"""
        print(f"正在获取{name}({index_code})数据...")
        
        try:
            # 使用akshare获取港股指数数据
            print(f"  尝试使用akshare获取{symbol}数据...")
            data = ak.stock_hk_index_daily_em(symbol=symbol)
            
            if data is not None and not data.empty:
                print(f"  原始数据形状: {data.shape}")
                print(f"  原始数据列名: {list(data.columns)}")
                
                # 处理列名映射
                column_mapping = {}
                for col in data.columns:
                    col_lower = str(col).lower()
                    if '日期' in str(col) or 'date' in col_lower:
                        column_mapping[col] = 'date'
                    elif '开盘' in str(col) or 'open' in col_lower:
                        column_mapping[col] = 'open'
                    elif '收盘' in str(col) or 'close' in col_lower or 'latest' in col_lower:
                        column_mapping[col] = 'close'
                    elif '最高' in str(col) or 'high' in col_lower:
                        column_mapping[col] = 'high'
                    elif '最低' in str(col) or 'low' in col_lower:
                        column_mapping[col] = 'low'
                    elif '成交量' in str(col) or 'volume' in col_lower:
                        column_mapping[col] = 'volume'
                
                print(f"  列名映射: {column_mapping}")
                
                # 重命名列
                data = data.rename(columns=column_mapping)
                
                # 处理日期
                if 'date' in data.columns:
                    data['date'] = pd.to_datetime(data['date'])
                    data.set_index('date', inplace=True)
                    data = data.sort_index()
                    
                    # 筛选最近一个月的数据
                    data = data[data.index >= self.start_date]
                    
                    # 检查必要的列
                    required_cols = ['open', 'high', 'low', 'close']
                    available_cols = [col for col in required_cols if col in data.columns]
                    
                    if len(available_cols) >= 4 and len(data) >= 5:
                        print(f"  ✅ 获取成功，数据点数：{len(data)}")
                        
                        # 添加volume列如果不存在
                        if 'volume' not in data.columns:
                            data['volume'] = 0
                        
                        # 确保数据类型正确
                        for col in ['open', 'high', 'low', 'close']:
                            if col in data.columns:
                                data[col] = pd.to_numeric(data[col], errors='coerce')
                        
                        # 去除空值
                        data = data.dropna(subset=['close'])
                        
                        if len(data) >= 5:
                            self.data[index_code] = data[['open', 'high', 'low', 'close', 'volume']].copy()
                            return True
                        else:
                            print(f"  ❌ 清理后数据量不足：{len(data)}")
                    else:
                        print(f"  ❌ 数据列不完整，可用列：{available_cols}")
                else:
                    print("  ❌ 未找到日期列")
            else:
                print("  ❌ 获取的数据为空")
                
        except Exception as e:
            print(f"  ❌ 获取失败：{str(e)}")
        
        return False
    
    def get_all_data(self):
        """获取所有数据"""
        print("🚀 开始获取港股主要指数数据（使用akshare）")
        print("="*60)
        
        success_count = 0
        for index_code, config in self.indices_config.items():
            if self.get_index_data(index_code, config['symbol'], config['name']):
                success_count += 1
            print()  # 添加空行分隔
        
        print(f"📊 数据获取完成：{success_count}/{len(self.indices_config)} 个指数获取成功")
        
        if success_count >= 1:
            return True
        else:
            print("❌ 没有成功获取任何指数数据")
            return False

class HKIndicesAkShareAnalyzer:
    def __init__(self, fetcher):
        """初始化分析器"""
        self.fetcher = fetcher
        self.data = fetcher.data
        self.indices_config = fetcher.indices_config
    
    def calculate_statistics(self):
        """计算统计信息"""
        stats = {}
        
        for index_code, data in self.data.items():
            if data is not None and len(data) > 0:
                start_price = data['close'].iloc[0]
                end_price = data['close'].iloc[-1]
                return_pct = (end_price - start_price) / start_price * 100
                volatility = data['close'].pct_change().std() * np.sqrt(252) * 100
                
                stats[index_code] = {
                    'name': self.indices_config[index_code]['name'],
                    'type': self.indices_config[index_code]['type'],
                    'start_price': start_price,
                    'end_price': end_price,
                    'return': return_pct,
                    'volatility': volatility,
                    'max_price': data['high'].max(),
                    'min_price': data['low'].min(),
                    'data_points': len(data)
                }
        
        return stats
    
    def print_statistics(self):
        """打印统计信息"""
        stats = self.calculate_statistics()
        
        print("\n" + "="*80)
        print("📊 港股主要指数最近一个月行情统计")
        print("="*80)
        
        # 按类型分组显示
        growth_indices = [k for k, v in stats.items() if v['type'] == 'growth']
        value_indices = [k for k, v in stats.items() if v['type'] == 'value']
        
        if growth_indices:
            print("\n🚀 成长类指数:")
            print("-" * 50)
            for index_code in growth_indices:
                data = stats[index_code]
                print(f"\n{data['name']} ({index_code}):")
                print(f"  期初价格: {data['start_price']:.2f}")
                print(f"  期末价格: {data['end_price']:.2f}")
                print(f"  涨跌幅: {data['return']:+.2f}%")
                print(f"  最高价: {data['max_price']:.2f}")
                print(f"  最低价: {data['min_price']:.2f}")
                print(f"  年化波动率: {data['volatility']:.2f}%")
                print(f"  数据点数: {data['data_points']}")
        
        if value_indices:
            print("\n💰 价值类指数:")
            print("-" * 50)
            for index_code in value_indices:
                data = stats[index_code]
                print(f"\n{data['name']} ({index_code}):")
                print(f"  期初价格: {data['start_price']:.2f}")
                print(f"  期末价格: {data['end_price']:.2f}")
                print(f"  涨跌幅: {data['return']:+.2f}%")
                print(f"  最高价: {data['max_price']:.2f}")
                print(f"  最低价: {data['min_price']:.2f}")
                print(f"  年化波动率: {data['volatility']:.2f}%")
                print(f"  数据点数: {data['data_points']}")
        
        # 显示排名
        if len(stats) > 1:
            print("\n🏆 涨跌幅排名:")
            print("-" * 50)
            sorted_stats = sorted(stats.items(), key=lambda x: x[1]['return'], reverse=True)
            for i, (index_code, data) in enumerate(sorted_stats, 1):
                emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
                print(f"  {emoji} {i}. {data['name']}: {data['return']:+.2f}%")
        
        return stats
    
    def plot_charts(self):
        """绘制图表"""
        if not self.data:
            print("❌ 没有可用数据进行绘图")
            return
        
        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('港股主要指数 - 最近一个月行情对比（akshare数据）', fontsize=16, fontweight='bold')
        
        # 颜色配置
        colors = {'HSI': 'red', 'HSTECH': 'blue', 'HSCEI': 'green', 'HSHDYI': 'orange'}
        
        # 1. 标准化价格对比图（以期初价格为100）
        ax1 = axes[0, 0]
        for index_code, data in self.data.items():
            if data is not None and len(data) > 0:
                normalized = (data['close'] / data['close'].iloc[0]) * 100
                ax1.plot(data.index, normalized, 
                        label=self.indices_config[index_code]['name'], 
                        color=colors.get(index_code, 'gray'), 
                        linewidth=2)
        
        ax1.set_title('标准化价格对比 (期初=100)')
        ax1.set_ylabel('标准化价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=100, color='gray', linestyle='--', alpha=0.5)
        
        # 2. 价格走势图
        ax2 = axes[0, 1]
        for index_code, data in self.data.items():
            if data is not None and len(data) > 0:
                ax2.plot(data.index, data['close'], 
                        label=self.indices_config[index_code]['name'], 
                        color=colors.get(index_code, 'gray'), 
                        linewidth=2)
        
        ax2.set_title('价格走势对比')
        ax2.set_ylabel('价格')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 3. 涨跌幅对比
        ax3 = axes[1, 0]
        stats = self.calculate_statistics()
        if stats:
            indices = list(stats.keys())
            returns = [stats[idx]['return'] for idx in indices]
            names = [stats[idx]['name'] for idx in indices]
            colors_list = [colors.get(idx, 'gray') for idx in indices]
            
            bars = ax3.bar(range(len(indices)), returns, color=colors_list, alpha=0.7)
            ax3.set_title('涨跌幅对比')
            ax3.set_ylabel('涨跌幅 (%)')
            ax3.set_xticks(range(len(indices)))
            ax3.set_xticklabels([name.replace('指数', '') for name in names], rotation=45)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)
            
            # 添加数值标签
            for bar, ret in zip(bars, returns):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2., 
                        height + (0.1 if height >= 0 else -0.3),
                        f'{ret:+.1f}%', ha='center', va='bottom' if height >= 0 else 'top')
        
        ax3.grid(True, alpha=0.3)
        
        # 4. 波动率对比
        ax4 = axes[1, 1]
        if stats:
            indices = list(stats.keys())
            volatilities = [stats[idx]['volatility'] for idx in indices]
            names = [stats[idx]['name'] for idx in indices]
            colors_list = [colors.get(idx, 'gray') for idx in indices]
            
            bars = ax4.bar(range(len(indices)), volatilities, color=colors_list, alpha=0.7)
            ax4.set_title('年化波动率对比')
            ax4.set_ylabel('年化波动率 (%)')
            ax4.set_xticks(range(len(indices)))
            ax4.set_xticklabels([name.replace('指数', '') for name in names], rotation=45)
            
            # 添加数值标签
            for bar, vol in zip(bars, volatilities):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{vol:.1f}%', ha='center', va='bottom')
        
        ax4.grid(True, alpha=0.3)
        
        # 调整布局
        plt.tight_layout()
        
        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hk_indices_akshare_chart_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 图表已保存至: {filename}")
        
        plt.show()

def main():
    """主函数"""
    print("🚀 港股主要指数最近一个月行情分析（akshare版本）")
    print("="*80)
    
    # 创建数据获取器
    fetcher = HKIndicesAkShareFetcher()
    
    # 获取数据
    if not fetcher.get_all_data():
        print("❌ 数据获取失败，程序退出")
        return
    
    # 创建分析器
    analyzer = HKIndicesAkShareAnalyzer(fetcher)
    
    # 打印统计信息
    analyzer.print_statistics()
    
    # 绘制图表
    print("\n正在绘制图表...")
    analyzer.plot_charts()
    
    print("\n🎉 分析完成！")
    print("📊 请查看生成的图表文件")

if __name__ == "__main__":
    main()
