"""
获取沪深300成分股的季度财务数据（使用AKShare API的财务指标接口）

This script retrieves quarterly financial data (revenue and profit) for CSI 300 constituent stocks
over the past three years using AKShare's financial indicator API.
"""

import akshare as ak
import pandas as pd
import os
import time
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_financial_data_akshare.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_financial_data"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def get_stock_financial_data(stock_code, stock_name):
    """
    获取单个股票的季度财务数据
    
    Args:
        stock_code: 股票代码
        stock_name: 股票名称
    
    Returns:
        pandas.DataFrame: 包含季度财务数据的DataFrame，如果获取失败则返回None
    """
    try:
        logger.info(f"获取 {stock_code} ({stock_name}) 的财务数据...")
        
        # 使用AKShare的stock_financial_analysis_indicator接口获取财务指标
        # 这个接口返回的数据包含了多个季度的财务指标
        try:
            # 对股票代码进行处理
            if stock_code.startswith('6') or stock_code.startswith('688'):
                formatted_code = f"sh{stock_code}"
            else:
                formatted_code = f"sz{stock_code}"
            
            # 获取财务指标数据
            financial_data = ak.stock_financial_analysis_indicator(symbol=formatted_code)
            
            if financial_data is None or financial_data.empty:
                logger.warning(f"{stock_code} ({stock_name}) 使用财务指标接口获取数据失败，尝试使用财务报表接口...")
                # 尝试使用另一个接口
                financial_data = ak.stock_financial_report_indicator(symbol=stock_code)
        except Exception as e:
            logger.warning(f"{stock_code} ({stock_name}) 使用财务指标接口获取数据失败: {str(e)}，尝试使用财务报表接口...")
            try:
                # 尝试使用另一个接口
                financial_data = ak.stock_financial_report_indicator(symbol=stock_code)
            except Exception as e2:
                logger.error(f"{stock_code} ({stock_name}) 使用财务报表接口获取数据也失败: {str(e2)}")
                return None
        
        # 如果数据为空，记录错误并返回None
        if financial_data is None or financial_data.empty:
            logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
            return None
        
        # 打印列名以便调试
        logger.info(f"{stock_code} ({stock_name}) 数据列名: {financial_data.columns.tolist()}")
        
        # 处理数据，提取关键财务指标
        # 根据实际返回的数据结构进行调整
        result_df = pd.DataFrame()
        
        # 检查是否包含日期列
        date_col = None
        for col in financial_data.columns:
            if '日期' in col or '报告期' in col or '年度' in col:
                date_col = col
                break
        
        if date_col is None:
            logger.error(f"{stock_code} ({stock_name}) 数据中未找到日期列")
            return None
        
        # 设置日期列
        result_df['报告期'] = financial_data[date_col]
        
        # 转换日期格式
        result_df['报告期'] = pd.to_datetime(result_df['报告期'], errors='coerce')
        
        # 过滤最近3年的数据
        three_years_ago = datetime.now() - timedelta(days=365*3)
        result_df = result_df[result_df['报告期'] >= three_years_ago]
        
        if result_df.empty:
            logger.warning(f"{stock_code} ({stock_name}) 过滤后没有最近3年的数据")
            return None
        
        # 添加股票信息
        result_df['股票代码'] = stock_code
        result_df['股票名称'] = stock_name
        
        # 查找并添加营业收入列
        revenue_col = None
        for col in financial_data.columns:
            if '营业收入' in col or '营业总收入' in col:
                revenue_col = col
                break
        
        if revenue_col:
            result_df['营业收入'] = financial_data[revenue_col]
        else:
            logger.warning(f"{stock_code} ({stock_name}) 未找到营业收入列")
            result_df['营业收入'] = None
        
        # 查找并添加净利润列
        profit_col = None
        for col in financial_data.columns:
            if '净利润' in col:
                profit_col = col
                break
        
        if profit_col:
            result_df['净利润'] = financial_data[profit_col]
        else:
            logger.warning(f"{stock_code} ({stock_name}) 未找到净利润列")
            result_df['净利润'] = None
        
        # 查找并添加归属于母公司股东的净利润列
        parent_profit_col = None
        for col in financial_data.columns:
            if '归属于母公司' in col and '净利润' in col:
                parent_profit_col = col
                break
        
        if parent_profit_col:
            result_df['归属于母公司股东的净利润'] = financial_data[parent_profit_col]
        else:
            logger.warning(f"{stock_code} ({stock_name}) 未找到归属于母公司股东的净利润列")
            result_df['归属于母公司股东的净利润'] = None
        
        # 查找并添加总资产列
        assets_col = None
        for col in financial_data.columns:
            if '总资产' in col:
                assets_col = col
                break
        
        if assets_col:
            result_df['总资产'] = financial_data[assets_col]
        else:
            logger.warning(f"{stock_code} ({stock_name}) 未找到总资产列")
            result_df['总资产'] = None
        
        # 查找并添加净资产收益率列
        roe_col = None
        for col in financial_data.columns:
            if '净资产收益率' in col:
                roe_col = col
                break
        
        if roe_col:
            result_df['净资产收益率'] = financial_data[roe_col]
        else:
            logger.warning(f"{stock_code} ({stock_name}) 未找到净资产收益率列")
            result_df['净资产收益率'] = None
        
        # 确保数值列是数值类型
        for col in ['营业收入', '净利润', '归属于母公司股东的净利润', '总资产', '净资产收益率']:
            if col in result_df.columns:
                result_df[col] = pd.to_numeric(result_df[col], errors='coerce')
        
        # 计算同比增长率（如果有足够的历史数据）
        if len(result_df) > 4:
            # 按报告期排序
            result_df = result_df.sort_values('报告期')
            
            # 计算同比增长率
            for col in ['营业收入', '净利润']:
                if col in result_df.columns and not result_df[col].isna().all():
                    result_df[f'{col}同比增长率'] = result_df[col].pct_change(4) * 100
            
            # 重新按报告期降序排序
            result_df = result_df.sort_values('报告期', ascending=False)
        
        return result_df
    
    except Exception as e:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据时出错: {str(e)}")
        return None

def process_stock(stock_info):
    """
    处理单个股票的财务数据并保存
    
    Args:
        stock_info: 包含股票代码和名称的元组 (code, name)
    
    Returns:
        tuple: (股票代码, 成功/失败)
    """
    stock_code, stock_name = stock_info
    
    # 检查是否已经处理过
    output_file = os.path.join(OUTPUT_DIR, f"{stock_code}_financial_data.csv")
    if os.path.exists(output_file):
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已存在，跳过处理")
        return stock_code, True
    
    # 获取财务数据
    df = get_stock_financial_data(stock_code, stock_name)
    
    if df is not None and not df.empty:
        # 保存到CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已保存到 {output_file}")
        return stock_code, True
    else:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
        return stock_code, False

def main():
    # 读取沪深300成分股列表
    try:
        # 查找最新的格式化文件
        data_dir = "data"
        files = [f for f in os.listdir(data_dir) if f.startswith("csi300_constituents_formatted_") and f.endswith(".csv")]
        if not files:
            logger.error("未找到沪深300成分股数据文件")
            return
        
        # 按文件名排序，获取最新的文件
        files.sort(reverse=True)
        input_file = os.path.join(data_dir, files[0])
        
        # 读取CSV文件
        df = pd.read_csv(input_file, encoding="utf-8-sig")
        logger.info(f"从 {input_file} 读取了 {len(df)} 只沪深300成分股")
        
        # 准备股票列表
        stock_list = list(zip(df["成分券代码"].astype(str), df["成分券名称"]))
        
        # 使用线程池并行处理
        success_count = 0
        fail_count = 0
        
        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = {executor.submit(process_stock, stock_info): stock_info for stock_info in stock_list}
            
            # 使用tqdm显示进度
            for future in tqdm(as_completed(futures), total=len(futures), desc="处理进度"):
                stock_info = futures[future]
                try:
                    code, success = future.result()
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    logger.error(f"处理 {stock_info[0]} ({stock_info[1]}) 时发生异常: {str(e)}")
                    fail_count += 1
                
                # 添加延迟以避免请求过于频繁
                time.sleep(1)
        
        logger.info(f"处理完成: 成功 {success_count} 只, 失败 {fail_count} 只")
        
        # 创建汇总文件
        create_summary_file()
        
    except Exception as e:
        logger.error(f"处理沪深300成分股财务数据时出错: {str(e)}")

def create_summary_file():
    """创建汇总文件，包含所有公司的关键财务指标"""
    try:
        # 获取所有已处理的文件
        all_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith("_financial_data.csv")]
        
        if not all_files:
            logger.warning("没有找到已处理的财务数据文件，无法创建汇总")
            return
        
        # 读取并合并所有数据
        all_data = []
        for file in all_files:
            try:
                file_path = os.path.join(OUTPUT_DIR, file)
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                
                # 只保留最新一期的数据
                if not df.empty:
                    latest_data = df.iloc[0].to_dict()
                    all_data.append(latest_data)
            except Exception as e:
                logger.error(f"读取文件 {file} 时出错: {str(e)}")
        
        if all_data:
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(all_data)
            
            # 保存汇总文件
            summary_file = os.path.join(OUTPUT_DIR, "csi300_financial_summary.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            logger.info(f"汇总文件已保存到 {summary_file}")
        else:
            logger.warning("没有有效的财务数据，无法创建汇总")
    
    except Exception as e:
        logger.error(f"创建汇总文件时出错: {str(e)}")

if __name__ == "__main__":
    main()
