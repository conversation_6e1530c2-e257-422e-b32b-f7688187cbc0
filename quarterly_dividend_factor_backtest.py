#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
季度重新平衡股息率因子回测系统
对比月度和季度重新平衡的效果差异
"""

import pandas as pd
import numpy as np
import os
import pickle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

class QuarterlyDividendFactorBacktest:
    """季度重新平衡股息率因子回测器"""
    
    def __init__(self, 
                 dividend_data_dir='sp500_dividend_yield_data_5y',
                 price_cache_dir='sp500_price_cache'):
        self.dividend_data_dir = dividend_data_dir
        self.price_cache_dir = price_cache_dir
        self.dividend_data = {}
        self.price_data = {}
        self.monthly_results = {}
        self.quarterly_results = {}
        
    def load_data(self):
        """加载所有数据"""
        print("📊 加载股息率数据...")
        
        # 加载股息率数据
        csv_files = [f for f in os.listdir(self.dividend_data_dir) 
                    if f.endswith('_5y_daily_dividend_yield.csv')]
        
        for file in csv_files:
            symbol = file.split('_')[0]
            file_path = os.path.join(self.dividend_data_dir, file)
            
            try:
                df = pd.read_csv(file_path)
                df['date'] = pd.to_datetime(df['date'], utc=True)
                df.set_index('date', inplace=True)
                self.dividend_data[symbol] = df['dividend_yield']
            except Exception as e:
                continue
        
        print(f"✅ 成功加载 {len(self.dividend_data)} 只股票的股息率数据")
        
        # 从缓存加载价格数据
        print("📈 从缓存加载价格数据...")
        
        cache_files = [f for f in os.listdir(self.price_cache_dir) 
                      if f.endswith('_price.pkl')]
        
        for file in cache_files:
            symbol = file.split('_')[0]
            file_path = os.path.join(self.price_cache_dir, file)
            
            try:
                with open(file_path, 'rb') as f:
                    cached_data = pickle.load(f)
                    price_data = cached_data['price_data']
                    
                    if len(price_data) > 500:
                        self.price_data[symbol] = price_data
            except Exception as e:
                continue
        
        print(f"✅ 成功加载 {len(self.price_data)} 只股票的价格数据")
        
        # 对齐数据
        common_symbols = set(self.dividend_data.keys()) & set(self.price_data.keys())
        print(f"🔄 共同股票数量: {len(common_symbols)}")
        
        dividend_matrix = pd.DataFrame()
        price_matrix = pd.DataFrame()
        
        for symbol in common_symbols:
            dividend_matrix[symbol] = self.dividend_data[symbol]
            price_matrix[symbol] = self.price_data[symbol]
        
        common_dates = dividend_matrix.index.intersection(price_matrix.index)
        self.dividend_matrix = dividend_matrix.loc[common_dates].fillna(method='ffill')
        self.price_matrix = price_matrix.loc[common_dates].fillna(method='ffill')
        
        print(f"✅ 数据对齐完成: {self.dividend_matrix.shape}")
        
    def run_backtest(self, rebalance_freq='M', n_groups=5):
        """运行回测"""
        print(f"🔄 运行 {rebalance_freq} 重新平衡回测...")
        
        # 计算收益率
        returns_matrix = self.price_matrix.pct_change().dropna()
        
        # 重新平衡日期
        rebalance_dates = self.dividend_matrix.resample(rebalance_freq).last().index
        
        # 初始化结果
        portfolio_returns = {f'Group_{i+1}': [] for i in range(n_groups)}
        portfolio_returns['Long_Short'] = []
        
        print(f"   重新平衡次数: {len(rebalance_dates)-1}")
        
        valid_periods = 0
        
        for i in range(len(rebalance_dates)-1):
            rebal_date = rebalance_dates[i]
            next_rebal_date = rebalance_dates[i+1]
            
            # 获取重新平衡日的股息率
            try:
                current_dividends = self.dividend_matrix.loc[rebal_date].dropna()
            except KeyError:
                idx = self.dividend_matrix.index.get_indexer([rebal_date], method='nearest')[0]
                actual_date = self.dividend_matrix.index[idx]
                current_dividends = self.dividend_matrix.loc[actual_date].dropna()
            
            if len(current_dividends) < n_groups * 10:
                continue
            
            # 按股息率排序分组
            sorted_dividends = current_dividends.sort_values(ascending=False)
            group_size = len(sorted_dividends) // n_groups
            
            groups = {}
            for j in range(n_groups):
                start_idx = j * group_size
                if j == n_groups - 1:
                    end_idx = len(sorted_dividends)
                else:
                    end_idx = (j + 1) * group_size
                
                groups[f'Group_{j+1}'] = sorted_dividends.iloc[start_idx:end_idx].index.tolist()
            
            # 计算期间收益率
            period_mask = (returns_matrix.index > rebal_date) & (returns_matrix.index <= next_rebal_date)
            period_returns = returns_matrix.loc[period_mask]
            
            if period_returns.empty:
                continue
            
            # 计算各组合收益率
            period_valid = True
            for group_name, stocks in groups.items():
                available_stocks = [s for s in stocks if s in period_returns.columns]
                if len(available_stocks) >= 5:
                    group_ret = period_returns[available_stocks].mean(axis=1)
                    portfolio_returns[group_name].extend(group_ret.tolist())
                else:
                    period_valid = False
                    break
            
            if not period_valid:
                continue
            
            # 多空组合
            high_stocks = groups['Group_1']
            low_stocks = groups[f'Group_{n_groups}']
            
            high_available = [s for s in high_stocks if s in period_returns.columns]
            low_available = [s for s in low_stocks if s in period_returns.columns]
            
            if len(high_available) >= 5 and len(low_available) >= 5:
                high_ret = period_returns[high_available].mean(axis=1)
                low_ret = period_returns[low_available].mean(axis=1)
                ls_ret = high_ret - low_ret
                portfolio_returns['Long_Short'].extend(ls_ret.tolist())
            
            valid_periods += 1
        
        # 创建DataFrame
        min_length = min(len(returns) for returns in portfolio_returns.values() if returns)
        
        if min_length > 0:
            for key in portfolio_returns:
                portfolio_returns[key] = portfolio_returns[key][:min_length]
            
            factor_returns = pd.DataFrame(portfolio_returns)
            print(f"✅ {rebalance_freq} 回测完成: {len(factor_returns)} 个数据点, {valid_periods} 个有效期间")
            return factor_returns
        else:
            print(f"❌ {rebalance_freq} 回测失败")
            return None
    
    def calculate_metrics(self, factor_returns):
        """计算绩效指标"""
        metrics = {}
        
        for col in factor_returns.columns:
            returns = factor_returns[col].dropna()
            
            if len(returns) == 0:
                continue
            
            # 基本统计
            total_return = (1 + returns).prod() - 1
            annual_return = (1 + total_return) ** (252 / len(returns)) - 1
            annual_vol = returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0
            
            # 最大回撤
            cumulative = (1 + returns).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 胜率
            win_rate = (returns > 0).mean()
            
            metrics[col] = {
                'Total_Return': total_return * 100,
                'Annual_Return': annual_return * 100,
                'Annual_Volatility': annual_vol * 100,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown * 100,
                'Win_Rate': win_rate * 100,
                'Observations': len(returns)
            }
        
        return pd.DataFrame(metrics).T
    
    def compare_rebalancing_frequencies(self):
        """对比不同重新平衡频率"""
        print("🔄 对比月度 vs 季度重新平衡...")
        
        # 月度重新平衡
        monthly_returns = self.run_backtest('M')
        if monthly_returns is not None:
            self.monthly_results = self.calculate_metrics(monthly_returns)
        
        # 季度重新平衡
        quarterly_returns = self.run_backtest('Q')
        if quarterly_returns is not None:
            self.quarterly_results = self.calculate_metrics(quarterly_returns)
    
    def display_comparison(self):
        """显示对比结果"""
        print("\n📊 月度 vs 季度重新平衡对比结果")
        print("=" * 100)
        
        if self.monthly_results.empty or self.quarterly_results.empty:
            print("❌ 缺少对比数据")
            return
        
        # 显示月度结果
        print("\n📅 月度重新平衡结果:")
        print("-" * 80)
        print(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}")
        print("-" * 80)
        
        for idx, row in self.monthly_results.iterrows():
            print(f"{idx:>12} "
                  f"{row['Total_Return']:>9.2f}% "
                  f"{row['Annual_Return']:>9.2f}% "
                  f"{row['Annual_Volatility']:>9.2f}% "
                  f"{row['Sharpe_Ratio']:>9.2f} "
                  f"{row['Max_Drawdown']:>9.2f}% "
                  f"{row['Win_Rate']:>7.1f}%")
        
        # 显示季度结果
        print("\n📅 季度重新平衡结果:")
        print("-" * 80)
        print(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}")
        print("-" * 80)
        
        for idx, row in self.quarterly_results.iterrows():
            print(f"{idx:>12} "
                  f"{row['Total_Return']:>9.2f}% "
                  f"{row['Annual_Return']:>9.2f}% "
                  f"{row['Annual_Volatility']:>9.2f}% "
                  f"{row['Sharpe_Ratio']:>9.2f} "
                  f"{row['Max_Drawdown']:>9.2f}% "
                  f"{row['Win_Rate']:>7.1f}%")
        
        # 对比分析
        print("\n📊 关键指标对比 (季度 vs 月度):")
        print("-" * 60)
        
        for group in ['Group_1', 'Group_5', 'Long_Short']:
            if group in self.monthly_results.index and group in self.quarterly_results.index:
                monthly_return = self.monthly_results.loc[group, 'Annual_Return']
                quarterly_return = self.quarterly_results.loc[group, 'Annual_Return']
                monthly_sharpe = self.monthly_results.loc[group, 'Sharpe_Ratio']
                quarterly_sharpe = self.quarterly_results.loc[group, 'Sharpe_Ratio']
                
                return_diff = quarterly_return - monthly_return
                sharpe_diff = quarterly_sharpe - monthly_sharpe
                
                print(f"{group:>12}:")
                print(f"   年化收益: {quarterly_return:6.2f}% vs {monthly_return:6.2f}% (差异: {return_diff:+6.2f}%)")
                print(f"   夏普比率: {quarterly_sharpe:6.2f} vs {monthly_sharpe:6.2f} (差异: {sharpe_diff:+6.2f})")
    
    def generate_comparison_report(self, output_file='quarterly_vs_monthly_backtest_report.txt'):
        """生成对比报告"""
        print("📋 生成对比报告...")
        
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("股息率因子回测: 季度 vs 月度重新平衡对比报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"股票池: 标普500成分股\n")
            f.write(f"可用股票数量: {len(set(self.dividend_data.keys()) & set(self.price_data.keys()))}\n")
            f.write(f"数据期间: {self.dividend_matrix.index.min().strftime('%Y-%m-%d')} 至 {self.dividend_matrix.index.max().strftime('%Y-%m-%d')}\n\n")
            
            # 月度结果
            f.write("📅 月度重新平衡结果:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}\n")
            f.write("-" * 80 + "\n")
            
            for idx, row in self.monthly_results.iterrows():
                f.write(f"{idx:>12} "
                       f"{row['Total_Return']:>9.2f}% "
                       f"{row['Annual_Return']:>9.2f}% "
                       f"{row['Annual_Volatility']:>9.2f}% "
                       f"{row['Sharpe_Ratio']:>9.2f} "
                       f"{row['Max_Drawdown']:>9.2f}% "
                       f"{row['Win_Rate']:>7.1f}%\n")
            
            # 季度结果
            f.write("\n📅 季度重新平衡结果:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}\n")
            f.write("-" * 80 + "\n")
            
            for idx, row in self.quarterly_results.iterrows():
                f.write(f"{idx:>12} "
                       f"{row['Total_Return']:>9.2f}% "
                       f"{row['Annual_Return']:>9.2f}% "
                       f"{row['Annual_Volatility']:>9.2f}% "
                       f"{row['Sharpe_Ratio']:>9.2f} "
                       f"{row['Max_Drawdown']:>9.2f}% "
                       f"{row['Win_Rate']:>7.1f}%\n")
            
            # 对比分析
            f.write("\n📊 关键发现:\n")
            f.write("-" * 40 + "\n")
            
            # 多空组合对比
            if 'Long_Short' in self.monthly_results.index and 'Long_Short' in self.quarterly_results.index:
                monthly_ls = self.monthly_results.loc['Long_Short', 'Annual_Return']
                quarterly_ls = self.quarterly_results.loc['Long_Short', 'Annual_Return']
                
                f.write(f"多空组合年化收益:\n")
                f.write(f"   月度重新平衡: {monthly_ls:.2f}%\n")
                f.write(f"   季度重新平衡: {quarterly_ls:.2f}%\n")
                f.write(f"   差异: {quarterly_ls - monthly_ls:+.2f}%\n\n")
                
                if quarterly_ls > monthly_ls:
                    f.write("结论: 季度重新平衡表现更好\n")
                    f.write("可能原因: 减少交易频率，降低交易成本影响\n")
                else:
                    f.write("结论: 月度重新平衡表现更好\n")
                    f.write("可能原因: 更及时捕捉市场变化\n")
        
        print(f"📋 对比报告已保存: {output_file}")

def main():
    """主函数"""
    print("🚀 股息率因子回测: 季度 vs 月度重新平衡对比")
    print("=" * 70)
    
    # 创建回测器
    backtest = QuarterlyDividendFactorBacktest()
    
    # 加载数据
    backtest.load_data()
    
    # 运行对比回测
    backtest.compare_rebalancing_frequencies()
    
    # 显示结果
    backtest.display_comparison()
    
    # 生成报告
    backtest.generate_comparison_report()
    
    print(f"\n🎯 季度 vs 月度重新平衡对比完成!")

if __name__ == "__main__":
    main()
