"""
获取沪深300成分股的季度财务数据（使用AKShare东方财富接口 - 改进版）

This script retrieves quarterly financial data (revenue and profit) for CSI 300 constituent stocks
over the past three years using AKShare's Eastmoney API with improved reliability.
"""

import akshare as ak
import pandas as pd
import os
import time
import random
import json
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import logging
import requests
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry
import traceback

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_financial_data_improved.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_financial_data"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 创建进度记录目录
PROGRESS_DIR = "data/progress"
if not os.path.exists(PROGRESS_DIR):
    os.makedirs(PROGRESS_DIR)

# 进度记录文件
PROGRESS_FILE = os.path.join(PROGRESS_DIR, "financial_data_progress.json")

# 设置请求会话，添加重试机制
def get_session():
    session = requests.Session()
    retry = Retry(
        total=5,
        backoff_factor=0.5,
        status_forcelist=[500, 502, 503, 504],
        allowed_methods=["HEAD", "GET", "POST"]
    )
    adapter = HTTPAdapter(max_retries=retry)
    session.mount("http://", adapter)
    session.mount("https://", adapter)
    return session

# 替换AKShare的请求会话
try:
    import akshare.utils as ak_utils
    ak_utils.session = get_session()
except:
    logger.warning("无法替换AKShare的请求会话，将使用默认会话")

def load_progress():
    """
    加载进度记录
    
    Returns:
        dict: 包含已处理股票的进度记录
    """
    if os.path.exists(PROGRESS_FILE):
        try:
            with open(PROGRESS_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            logger.error(f"加载进度记录失败: {str(e)}")
    return {"processed": [], "failed": [], "last_update": None}

def save_progress(progress):
    """
    保存进度记录
    
    Args:
        progress: 进度记录字典
    """
    progress["last_update"] = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    try:
        with open(PROGRESS_FILE, 'w', encoding='utf-8') as f:
            json.dump(progress, f, ensure_ascii=False, indent=2)
    except Exception as e:
        logger.error(f"保存进度记录失败: {str(e)}")

def get_stock_financial_data(stock_code, stock_name, max_retries=3, retry_delay=5):
    """
    获取单个股票的季度财务数据，带重试机制
    
    Args:
        stock_code: 股票代码
        stock_name: 股票名称
        max_retries: 最大重试次数
        retry_delay: 重试延迟（秒）
    
    Returns:
        pandas.DataFrame: 包含季度财务数据的DataFrame，如果获取失败则返回None
    """
    for retry in range(max_retries + 1):
        try:
            # 将股票代码转换为东方财富格式（带市场标识）
            if stock_code.startswith('6') or stock_code.startswith('688'):
                em_code = f"SH{stock_code}"
            else:
                em_code = f"SZ{stock_code}"
            
            if retry > 0:
                logger.info(f"第{retry}次重试获取 {em_code} ({stock_name}) 的财务数据...")
            else:
                logger.info(f"获取 {em_code} ({stock_name}) 的财务数据...")
            
            # 添加随机延迟，避免请求过于频繁
            time.sleep(random.uniform(1.0, 3.0))
            
            try:
                # 获取利润表数据
                profit_df = ak.stock_profit_sheet_by_report_em(symbol=em_code)
                
                # 添加随机延迟
                time.sleep(random.uniform(1.0, 2.0))
                
                # 获取资产负债表数据
                balance_df = ak.stock_balance_sheet_by_report_em(symbol=em_code)
                
                # 添加随机延迟
                time.sleep(random.uniform(1.0, 2.0))
                
                # 获取现金流量表数据
                cash_flow_df = ak.stock_cash_flow_sheet_by_report_em(symbol=em_code)
                
                # 如果所有数据都为空，记录错误并返回None
                if (profit_df is None or profit_df.empty) and \
                   (balance_df is None or balance_df.empty) and \
                   (cash_flow_df is None or cash_flow_df.empty):
                    if retry < max_retries:
                        logger.warning(f"{em_code} ({stock_name}) 获取所有财务数据失败，将在{retry_delay}秒后重试...")
                        time.sleep(retry_delay * (retry + 1))  # 指数退避
                        continue
                    else:
                        logger.error(f"{em_code} ({stock_name}) 获取所有财务数据失败，已达到最大重试次数")
                        return None
                
                # 创建结果DataFrame
                result_df = pd.DataFrame()
                
                # 处理利润表数据
                if profit_df is not None and not profit_df.empty:
                    logger.info(f"{em_code} ({stock_name}) 利润表数据列: {profit_df.columns.tolist()}")
                    
                    # 设置报告期为索引
                    result_df['报告期'] = profit_df['REPORT_DATE']
                    result_df['股票代码'] = stock_code
                    result_df['股票名称'] = stock_name
                    
                    # 提取关键财务指标
                    if 'TOTAL_OPERATE_INCOME' in profit_df.columns:
                        result_df['营业总收入'] = profit_df['TOTAL_OPERATE_INCOME']
                    
                    if 'OPERATE_INCOME' in profit_df.columns:
                        result_df['营业收入'] = profit_df['OPERATE_INCOME']
                    
                    if 'OPERATE_PROFIT' in profit_df.columns:
                        result_df['营业利润'] = profit_df['OPERATE_PROFIT']
                    
                    if 'TOTAL_PROFIT' in profit_df.columns:
                        result_df['利润总额'] = profit_df['TOTAL_PROFIT']
                    
                    if 'NETPROFIT' in profit_df.columns:
                        result_df['净利润'] = profit_df['NETPROFIT']
                    
                    if 'PARENT_NETPROFIT' in profit_df.columns:
                        result_df['归属于母公司股东的净利润'] = profit_df['PARENT_NETPROFIT']
                    
                    if 'BASIC_EPS' in profit_df.columns:
                        result_df['基本每股收益'] = profit_df['BASIC_EPS']
                    
                    if 'DILUTED_EPS' in profit_df.columns:
                        result_df['稀释每股收益'] = profit_df['DILUTED_EPS']
                
                # 处理资产负债表数据
                if balance_df is not None and not balance_df.empty:
                    logger.info(f"{em_code} ({stock_name}) 资产负债表数据列: {balance_df.columns.tolist()}")
                    
                    # 确保报告期匹配
                    balance_df = balance_df.set_index('REPORT_DATE')
                    
                    if result_df.empty:
                        result_df['报告期'] = balance_df.index
                        result_df['股票代码'] = stock_code
                        result_df['股票名称'] = stock_name
                        result_df = result_df.set_index('报告期')
                    else:
                        result_df = result_df.set_index('报告期')
                    
                    # 提取关键财务指标
                    if 'TOTAL_ASSETS' in balance_df.columns:
                        result_df['总资产'] = balance_df['TOTAL_ASSETS']
                    
                    if 'TOTAL_LIABILITIES' in balance_df.columns:
                        result_df['总负债'] = balance_df['TOTAL_LIABILITIES']
                    
                    if 'TOTAL_SHAREHOLDERS_EQUITY' in balance_df.columns:
                        result_df['股东权益合计'] = balance_df['TOTAL_SHAREHOLDERS_EQUITY']
                    
                    if 'PARENT_SHAREHOLDERS_EQUITY' in balance_df.columns:
                        result_df['归属于母公司股东权益'] = balance_df['PARENT_SHAREHOLDERS_EQUITY']
                    
                    # 重置索引
                    result_df = result_df.reset_index()
                
                # 处理现金流量表数据
                if cash_flow_df is not None and not cash_flow_df.empty:
                    logger.info(f"{em_code} ({stock_name}) 现金流量表数据列: {cash_flow_df.columns.tolist()}")
                    
                    # 确保报告期匹配
                    cash_flow_df = cash_flow_df.set_index('REPORT_DATE')
                    
                    if result_df.empty:
                        result_df['报告期'] = cash_flow_df.index
                        result_df['股票代码'] = stock_code
                        result_df['股票名称'] = stock_name
                        result_df = result_df.set_index('报告期')
                    else:
                        result_df = result_df.set_index('报告期')
                    
                    # 提取关键财务指标
                    if 'NET_OPERATE_CASH_FLOW' in cash_flow_df.columns:
                        result_df['经营活动产生的现金流量净额'] = cash_flow_df['NET_OPERATE_CASH_FLOW']
                    
                    if 'NET_INVEST_CASH_FLOW' in cash_flow_df.columns:
                        result_df['投资活动产生的现金流量净额'] = cash_flow_df['NET_INVEST_CASH_FLOW']
                    
                    if 'NET_FINANCE_CASH_FLOW' in cash_flow_df.columns:
                        result_df['筹资活动产生的现金流量净额'] = cash_flow_df['NET_FINANCE_CASH_FLOW']
                    
                    # 重置索引
                    result_df = result_df.reset_index()
                
                # 如果结果DataFrame为空，记录错误并返回None
                if result_df.empty:
                    if retry < max_retries:
                        logger.warning(f"{em_code} ({stock_name}) 处理后的财务数据为空，将在{retry_delay}秒后重试...")
                        time.sleep(retry_delay * (retry + 1))
                        continue
                    else:
                        logger.error(f"{em_code} ({stock_name}) 处理后的财务数据为空，已达到最大重试次数")
                        return None
                
                # 确保报告期列存在
                if '报告期' not in result_df.columns and 'REPORT_DATE' in result_df.columns:
                    result_df.rename(columns={'REPORT_DATE': '报告期'}, inplace=True)
                
                # 转换报告期为日期格式
                if '报告期' in result_df.columns:
                    result_df['报告期'] = pd.to_datetime(result_df['报告期'], errors='coerce')
                
                # 过滤最近3年的数据
                if '报告期' in result_df.columns:
                    three_years_ago = datetime.now() - timedelta(days=365*3)
                    result_df = result_df[result_df['报告期'] >= three_years_ago]
                
                # 计算同比增长率（如果有足够的历史数据）
                if len(result_df) > 4 and '报告期' in result_df.columns:
                    # 按报告期排序
                    result_df = result_df.sort_values('报告期')
                    
                    # 计算同比增长率
                    for col in ['营业收入', '营业总收入', '净利润', '归属于母公司股东的净利润']:
                        if col in result_df.columns and not result_df[col].isna().all():
                            result_df[f'{col}同比增长率'] = result_df[col].pct_change(4) * 100
                    
                    # 重新按报告期降序排序
                    result_df = result_df.sort_values('报告期', ascending=False)
                
                return result_df
            
            except Exception as e:
                error_msg = f"{em_code} ({stock_name}) 获取财务数据时出错: {str(e)}"
                if retry < max_retries:
                    logger.warning(f"{error_msg}，将在{retry_delay}秒后重试...")
                    logger.debug(traceback.format_exc())
                    time.sleep(retry_delay * (retry + 1))
                else:
                    logger.error(error_msg)
                    logger.debug(traceback.format_exc())
                    return None
        
        except Exception as e:
            error_msg = f"{stock_code} ({stock_name}) 获取财务数据时出错: {str(e)}"
            if retry < max_retries:
                logger.warning(f"{error_msg}，将在{retry_delay}秒后重试...")
                logger.debug(traceback.format_exc())
                time.sleep(retry_delay * (retry + 1))
            else:
                logger.error(error_msg)
                logger.debug(traceback.format_exc())
                return None
    
    return None

def process_stock(stock_info, progress):
    """
    处理单个股票的财务数据并保存
    
    Args:
        stock_info: 包含股票代码和名称的元组 (code, name)
        progress: 进度记录字典
    
    Returns:
        tuple: (股票代码, 成功/失败)
    """
    stock_code, stock_name = stock_info
    
    # 检查是否已经处理过
    if stock_code in progress["processed"]:
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已存在，跳过处理")
        return stock_code, True
    
    # 检查是否已经失败过多次
    if stock_code in progress["failed"] and progress["failed"].count(stock_code) >= 3:
        logger.warning(f"{stock_code} ({stock_name}) 已经失败多次，跳过处理")
        return stock_code, False
    
    output_file = os.path.join(OUTPUT_DIR, f"{stock_code}_financial_data.csv")
    
    # 获取财务数据
    df = get_stock_financial_data(stock_code, stock_name)
    
    if df is not None and not df.empty:
        # 保存到CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已保存到 {output_file}")
        
        # 更新进度
        if stock_code not in progress["processed"]:
            progress["processed"].append(stock_code)
        
        # 如果之前失败过，从失败列表中移除
        while stock_code in progress["failed"]:
            progress["failed"].remove(stock_code)
        
        return stock_code, True
    else:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
        
        # 更新失败记录
        progress["failed"].append(stock_code)
        
        return stock_code, False

def main():
    # 加载进度记录
    progress = load_progress()
    
    # 读取沪深300成分股列表
    try:
        # 查找最新的格式化文件
        data_dir = "data"
        files = [f for f in os.listdir(data_dir) if f.startswith("csi300_constituents_formatted_") and f.endswith(".csv")]
        if not files:
            logger.error("未找到沪深300成分股数据文件")
            return
        
        # 按文件名排序，获取最新的文件
        files.sort(reverse=True)
        input_file = os.path.join(data_dir, files[0])
        
        # 读取CSV文件
        df = pd.read_csv(input_file, encoding="utf-8-sig")
        logger.info(f"从 {input_file} 读取了 {len(df)} 只沪深300成分股")
        
        # 准备股票列表
        stock_list = list(zip(df["成分券代码"].astype(str), df["成分券名称"]))
        
        # 计算待处理的股票
        to_process = []
        for stock_info in stock_list:
            stock_code = stock_info[0]
            if stock_code not in progress["processed"] and progress["failed"].count(stock_code) < 3:
                to_process.append(stock_info)
        
        logger.info(f"已处理 {len(progress['processed'])} 只股票，待处理 {len(to_process)} 只股票")
        
        if not to_process:
            logger.info("所有股票已处理完毕或已达到最大失败次数")
            return
        
        # 使用线程池并行处理，但减少并发数
        success_count = 0
        fail_count = 0
        
        with ThreadPoolExecutor(max_workers=3) as executor:
            futures = {executor.submit(process_stock, stock_info, progress): stock_info for stock_info in to_process}
            
            # 使用tqdm显示进度
            for future in tqdm(as_completed(futures), total=len(futures), desc="处理进度"):
                stock_info = futures[future]
                try:
                    code, success = future.result()
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    logger.error(f"处理 {stock_info[0]} ({stock_info[1]}) 时发生异常: {str(e)}")
                    logger.debug(traceback.format_exc())
                    fail_count += 1
                    # 更新失败记录
                    progress["failed"].append(stock_info[0])
                
                # 每处理一定数量的股票，保存一次进度
                if (success_count + fail_count) % 5 == 0:
                    save_progress(progress)
                
                # 添加延迟以避免请求过于频繁
                time.sleep(random.uniform(2.0, 4.0))
        
        logger.info(f"处理完成: 成功 {success_count} 只, 失败 {fail_count} 只")
        logger.info(f"总计: 已处理 {len(progress['processed'])} 只, 失败 {len(set(progress['failed']))} 只")
        
        # 保存最终进度
        save_progress(progress)
        
        # 创建汇总文件
        create_summary_file()
        
    except Exception as e:
        logger.error(f"处理沪深300成分股财务数据时出错: {str(e)}")
        logger.debug(traceback.format_exc())
        # 保存进度
        save_progress(progress)

def create_summary_file():
    """创建汇总文件，包含所有公司的关键财务指标"""
    try:
        # 获取所有已处理的文件
        all_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith("_financial_data.csv")]
        
        if not all_files:
            logger.warning("没有找到已处理的财务数据文件，无法创建汇总")
            return
        
        # 读取并合并所有数据
        all_data = []
        for file in all_files:
            try:
                file_path = os.path.join(OUTPUT_DIR, file)
                df = pd.read_csv(file_path, encoding='utf-8-sig')
                
                # 只保留最新一期的数据
                if not df.empty:
                    latest_data = df.iloc[0].to_dict()
                    all_data.append(latest_data)
            except Exception as e:
                logger.error(f"读取文件 {file} 时出错: {str(e)}")
        
        if all_data:
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(all_data)
            
            # 保存汇总文件
            summary_file = os.path.join(OUTPUT_DIR, "csi300_financial_summary.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            logger.info(f"汇总文件已保存到 {summary_file}")
        else:
            logger.warning("没有有效的财务数据，无法创建汇总")
    
    except Exception as e:
        logger.error(f"创建汇总文件时出错: {str(e)}")
        logger.debug(traceback.format_exc())

if __name__ == "__main__":
    main()
