#!/usr/bin/env python3
"""
多资产轮动策略动量窗口参数优化
优化目标：找到最佳的momentum_window参数值
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from strategies.multi_asset_rotation_strategy import MultiAssetRotationStrategy
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def optimize_momentum_window():
    """优化动量窗口参数"""
    
    print("="*80)
    print("多资产轮动策略 - 动量窗口参数优化")
    print("="*80)
    print("测试目标: 找到最佳的momentum_window参数")
    print("测试范围: 5天到60天，步长5天")
    print("评估指标: 年化收益率、夏普比率、最大回撤、卡玛比率")
    print("="*80)
    
    # 定义测试参数范围
    momentum_windows = list(range(5, 61, 5))  # 5, 10, 15, ..., 60
    
    # 存储结果
    results = []
    
    # 固定其他参数
    start_date = '2020-01-01'
    end_date = None
    initial_capital = 100000
    
    print(f"开始测试 {len(momentum_windows)} 个参数组合...")
    
    # 先获取一次数据，后续复用
    base_strategy = MultiAssetRotationStrategy(
        start_date=start_date,
        end_date=end_date,
        initial_capital=initial_capital
    )
    print("获取基础数据...")
    base_strategy.fetch_data()
    base_price_data = base_strategy.price_data.copy()
    
    for i, momentum_window in enumerate(momentum_windows):
        print(f"\n进度: {i+1}/{len(momentum_windows)} - 测试 momentum_window={momentum_window}")
        
        try:
            # 创建策略实例
            strategy = MultiAssetRotationStrategy(
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital
            )
            
            # 设置动量窗口参数
            strategy.momentum_window = momentum_window
            
            # 复用数据
            strategy.price_data = base_price_data.copy()
            
            # 运行回测
            print("  运行回测...")
            backtest_results = strategy.run_backtest()
            
            if backtest_results is not None and len(backtest_results) > 0:
                # 计算性能指标
                metrics = strategy.calculate_performance_metrics()
                
                if metrics is not None:
                    result = {
                        'momentum_window': momentum_window,
                        'total_return': metrics['strategy']['total_return'],
                        'annual_return': metrics['strategy']['annual_return'],
                        'volatility': metrics['strategy']['volatility'],
                        'max_drawdown': metrics['strategy']['max_drawdown'],
                        'sharpe_ratio': metrics['strategy']['sharpe_ratio'],
                        'calmar_ratio': metrics['strategy']['calmar_ratio']
                    }
                    
                    # 计算综合评分 (可以根据需要调整权重)
                    # 综合评分 = 年化收益率 * 0.3 + 夏普比率 * 0.3 + 卡玛比率 * 0.2 - |最大回撤|/100 * 0.2
                    composite_score = (result['annual_return'] * 0.003 +  # 年化收益率权重
                                     result['sharpe_ratio'] * 0.3 +      # 夏普比率权重
                                     result['calmar_ratio'] * 0.2 +      # 卡玛比率权重
                                     result['max_drawdown'] * 0.005)     # 最大回撤权重(负值)
                    
                    result['composite_score'] = composite_score
                    results.append(result)
                    
                    print(f"  年化收益: {result['annual_return']:.2f}%, 夏普比率: {result['sharpe_ratio']:.2f}")
                    print(f"  最大回撤: {result['max_drawdown']:.2f}%, 卡玛比率: {result['calmar_ratio']:.2f}")
                    print(f"  综合评分: {composite_score:.4f}")
                
            else:
                print("  回测失败，跳过此参数")
                
        except Exception as e:
            print(f"  测试 momentum_window={momentum_window} 时出错: {str(e)}")
            continue
    
    # 转换为DataFrame
    results_df = pd.DataFrame(results)
    
    if len(results_df) == 0:
        print("错误：没有成功的测试结果")
        return None
    
    print(f"\n成功测试了 {len(results_df)} 个参数组合")
    return results_df

def analyze_results(results_df):
    """分析优化结果"""
    
    print("\n" + "="*80)
    print("参数优化结果分析")
    print("="*80)
    
    # 按不同指标排序
    best_return = results_df.loc[results_df['annual_return'].idxmax()]
    best_sharpe = results_df.loc[results_df['sharpe_ratio'].idxmax()]
    best_calmar = results_df.loc[results_df['calmar_ratio'].idxmax()]
    best_composite = results_df.loc[results_df['composite_score'].idxmax()]
    min_drawdown = results_df.loc[results_df['max_drawdown'].idxmax()]  # 最大回撤是负值，最大值表示回撤最小
    
    print(f"\n📈 最高年化收益率:")
    print(f"   动量窗口: {best_return['momentum_window']} 天")
    print(f"   年化收益率: {best_return['annual_return']:.2f}%")
    print(f"   夏普比率: {best_return['sharpe_ratio']:.2f}")
    print(f"   最大回撤: {best_return['max_drawdown']:.2f}%")
    
    print(f"\n📊 最高夏普比率:")
    print(f"   动量窗口: {best_sharpe['momentum_window']} 天")
    print(f"   年化收益率: {best_sharpe['annual_return']:.2f}%")
    print(f"   夏普比率: {best_sharpe['sharpe_ratio']:.2f}")
    print(f"   最大回撤: {best_sharpe['max_drawdown']:.2f}%")
    
    print(f"\n🛡️ 最高卡玛比率:")
    print(f"   动量窗口: {best_calmar['momentum_window']} 天")
    print(f"   年化收益率: {best_calmar['annual_return']:.2f}%")
    print(f"   卡玛比率: {best_calmar['calmar_ratio']:.2f}")
    print(f"   最大回撤: {best_calmar['max_drawdown']:.2f}%")
    
    print(f"\n🎯 最高综合评分:")
    print(f"   动量窗口: {best_composite['momentum_window']} 天")
    print(f"   年化收益率: {best_composite['annual_return']:.2f}%")
    print(f"   夏普比率: {best_composite['sharpe_ratio']:.2f}")
    print(f"   卡玛比率: {best_composite['calmar_ratio']:.2f}")
    print(f"   最大回撤: {best_composite['max_drawdown']:.2f}%")
    print(f"   综合评分: {best_composite['composite_score']:.4f}")
    
    print(f"\n🔒 最小回撤:")
    print(f"   动量窗口: {min_drawdown['momentum_window']} 天")
    print(f"   年化收益率: {min_drawdown['annual_return']:.2f}%")
    print(f"   最大回撤: {min_drawdown['max_drawdown']:.2f}%")
    
    return best_composite

def plot_optimization_results(results_df):
    """绘制优化结果图表"""
    
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('多资产轮动策略 - 动量窗口参数优化结果', fontsize=16, fontweight='bold')
    
    # 1. 年化收益率
    ax1 = axes[0, 0]
    ax1.plot(results_df['momentum_window'], results_df['annual_return'], 'b-o', linewidth=2, markersize=6)
    ax1.set_title('年化收益率 vs 动量窗口')
    ax1.set_xlabel('动量窗口 (天)')
    ax1.set_ylabel('年化收益率 (%)')
    ax1.grid(True, alpha=0.3)
    
    # 标记最优点
    best_return_idx = results_df['annual_return'].idxmax()
    ax1.scatter(results_df.loc[best_return_idx, 'momentum_window'], 
               results_df.loc[best_return_idx, 'annual_return'], 
               color='red', s=100, zorder=5)
    
    # 2. 夏普比率
    ax2 = axes[0, 1]
    ax2.plot(results_df['momentum_window'], results_df['sharpe_ratio'], 'g-o', linewidth=2, markersize=6)
    ax2.set_title('夏普比率 vs 动量窗口')
    ax2.set_xlabel('动量窗口 (天)')
    ax2.set_ylabel('夏普比率')
    ax2.grid(True, alpha=0.3)
    
    # 标记最优点
    best_sharpe_idx = results_df['sharpe_ratio'].idxmax()
    ax2.scatter(results_df.loc[best_sharpe_idx, 'momentum_window'], 
               results_df.loc[best_sharpe_idx, 'sharpe_ratio'], 
               color='red', s=100, zorder=5)
    
    # 3. 最大回撤
    ax3 = axes[0, 2]
    ax3.plot(results_df['momentum_window'], results_df['max_drawdown'], 'r-o', linewidth=2, markersize=6)
    ax3.set_title('最大回撤 vs 动量窗口')
    ax3.set_xlabel('动量窗口 (天)')
    ax3.set_ylabel('最大回撤 (%)')
    ax3.grid(True, alpha=0.3)
    
    # 标记最优点（回撤最小）
    min_drawdown_idx = results_df['max_drawdown'].idxmax()
    ax3.scatter(results_df.loc[min_drawdown_idx, 'momentum_window'], 
               results_df.loc[min_drawdown_idx, 'max_drawdown'], 
               color='green', s=100, zorder=5)
    
    # 4. 卡玛比率
    ax4 = axes[1, 0]
    ax4.plot(results_df['momentum_window'], results_df['calmar_ratio'], 'm-o', linewidth=2, markersize=6)
    ax4.set_title('卡玛比率 vs 动量窗口')
    ax4.set_xlabel('动量窗口 (天)')
    ax4.set_ylabel('卡玛比率')
    ax4.grid(True, alpha=0.3)
    
    # 标记最优点
    best_calmar_idx = results_df['calmar_ratio'].idxmax()
    ax4.scatter(results_df.loc[best_calmar_idx, 'momentum_window'], 
               results_df.loc[best_calmar_idx, 'calmar_ratio'], 
               color='red', s=100, zorder=5)
    
    # 5. 综合评分
    ax5 = axes[1, 1]
    ax5.plot(results_df['momentum_window'], results_df['composite_score'], 'orange', marker='o', linewidth=2, markersize=6)
    ax5.set_title('综合评分 vs 动量窗口')
    ax5.set_xlabel('动量窗口 (天)')
    ax5.set_ylabel('综合评分')
    ax5.grid(True, alpha=0.3)
    
    # 标记最优点
    best_composite_idx = results_df['composite_score'].idxmax()
    ax5.scatter(results_df.loc[best_composite_idx, 'momentum_window'], 
               results_df.loc[best_composite_idx, 'composite_score'], 
               color='red', s=100, zorder=5)
    
    # 6. 收益风险散点图
    ax6 = axes[1, 2]
    scatter = ax6.scatter(results_df['volatility'], results_df['annual_return'], 
                         c=results_df['momentum_window'], cmap='viridis', s=80, alpha=0.7)
    ax6.set_title('收益-风险散点图')
    ax6.set_xlabel('年化波动率 (%)')
    ax6.set_ylabel('年化收益率 (%)')
    ax6.grid(True, alpha=0.3)
    
    # 添加颜色条
    cbar = plt.colorbar(scatter, ax=ax6)
    cbar.set_label('动量窗口 (天)')
    
    plt.tight_layout()
    plt.savefig('multi_asset_momentum_optimization.png', dpi=300, bbox_inches='tight')
    plt.close()
    print("\n优化结果图表已保存为: multi_asset_momentum_optimization.png")

def main():
    """主函数"""
    
    # 运行参数优化
    results_df = optimize_momentum_window()
    
    if results_df is not None:
        # 保存详细结果
        results_df.to_csv('multi_asset_momentum_optimization_results.csv', index=False, encoding='utf-8-sig')
        print(f"\n详细结果已保存至: multi_asset_momentum_optimization_results.csv")
        
        # 分析结果
        best_params = analyze_results(results_df)
        
        # 绘制图表
        plot_optimization_results(results_df)
        
        # 推荐最佳参数
        print(f"\n🎯 推荐参数:")
        print(f"最佳动量窗口: {best_params['momentum_window']} 天")
        print(f"预期年化收益率: {best_params['annual_return']:.2f}%")
        print(f"预期夏普比率: {best_params['sharpe_ratio']:.2f}")
        print(f"预期卡玛比率: {best_params['calmar_ratio']:.2f}")
        print(f"预期最大回撤: {best_params['max_drawdown']:.2f}%")
        
        return best_params
    
    return None

if __name__ == "__main__":
    best_params = main()
