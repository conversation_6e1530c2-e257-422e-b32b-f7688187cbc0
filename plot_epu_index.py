#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
经济政策不确定性指数数据可视化

该脚本读取收集的经济政策不确定性指数数据并生成可视化图表。
"""

import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import os
from datetime import datetime, timedelta
import warnings

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
CHARTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'charts')
os.makedirs(CHARTS_DIR, exist_ok=True)

def load_epu_data():
    """加载经济政策不确定性指数数据"""
    file_path = os.path.join(DATA_DIR, 'epu_index.csv')
    
    if not os.path.exists(file_path):
        print(f"数据文件不存在: {file_path}")
        print("请先运行 collect_epu_index.py 收集数据")
        return None
    
    try:
        df = pd.read_csv(file_path, index_col=0, parse_dates=True)
        print(f"成功加载数据，共 {len(df)} 条记录")
        return df
    except Exception as e:
        print(f"加载数据时发生错误: {str(e)}")
        return None

def plot_full_history(df):
    """绘制完整历史数据图表"""
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 绘制主线
    ax.plot(df.index, df['EPU指数'], linewidth=1.5, color='#2E86AB', alpha=0.8)
    
    # 添加重要事件标注
    events = [
        ('1987-10-19', 'Black Monday', 150),
        ('1991-01-17', 'Gulf War', 180),
        ('2001-09-11', '9/11 Attacks', 200),
        ('2008-09-15', 'Lehman Brothers', 200),
        ('2016-11-08', '2016 US Election', 180),
        ('2020-03-11', 'COVID-19 Pandemic', 300),
    ]
    
    for date_str, event, y_pos in events:
        try:
            event_date = pd.to_datetime(date_str)
            if event_date >= df.index.min() and event_date <= df.index.max():
                ax.axvline(x=event_date, color='red', linestyle='--', alpha=0.7)
                ax.text(event_date, y_pos, event, rotation=90, 
                       verticalalignment='bottom', fontsize=9, color='red')
        except:
            continue
    
    # 添加平均线和高不确定性阈值
    mean_value = df['EPU指数'].mean()
    high_threshold = df['EPU指数'].quantile(0.75)
    
    ax.axhline(y=mean_value, color='orange', linestyle='-', alpha=0.7, 
               label=f'Historical Average: {mean_value:.1f}')
    ax.axhline(y=high_threshold, color='red', linestyle=':', alpha=0.7, 
               label=f'High Uncertainty Threshold (75th percentile): {high_threshold:.1f}')
    
    # 设置标题和标签
    ax.set_title('US Economic Policy Uncertainty Index (1985-2025)', fontsize=16, fontweight='bold')
    ax.set_xlabel('Year', fontsize=12)
    ax.set_ylabel('EPU Index', fontsize=12)
    
    # 格式化x轴
    ax.xaxis.set_major_locator(mdates.YearLocator(5))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax.xaxis.set_minor_locator(mdates.YearLocator())
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, 'epu_index_full_history.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"完整历史图表已保存到: {chart_path}")
    
    plt.show()

def plot_recent_years(df, years=10):
    """绘制最近几年的数据"""
    # 获取最近几年的数据
    cutoff_date = df.index.max() - timedelta(days=365*years)
    recent_df = df[df.index >= cutoff_date]
    
    fig, ax = plt.subplots(figsize=(14, 8))
    
    # 绘制线图
    ax.plot(recent_df.index, recent_df['EPU指数'], 
            linewidth=2, color='#A23B72', alpha=0.8)
    
    # 添加最新值标注
    latest_value = recent_df['EPU指数'].iloc[-1]
    latest_date = recent_df.index[-1]
    ax.scatter([latest_date], [latest_value], color='red', s=50, zorder=5)
    ax.text(latest_date, latest_value + 10, f'{latest_value:.1f}', 
            ha='center', va='bottom', fontweight='bold', fontsize=10)
    
    # 添加平均线和高不确定性阈值
    mean_value = recent_df['EPU指数'].mean()
    high_threshold = df['EPU指数'].quantile(0.75)  # 使用全历史数据的75分位数
    
    ax.axhline(y=mean_value, color='green', linestyle='--', alpha=0.7, 
               label=f'{years}-Year Average: {mean_value:.1f}')
    ax.axhline(y=high_threshold, color='red', linestyle=':', alpha=0.7, 
               label=f'Historical High Threshold: {high_threshold:.1f}')
    
    # 标注重要事件
    if years >= 5:
        events = [
            ('2020-03-01', 'COVID-19', 300),
            ('2016-11-01', '2016 Election', 180),
        ]
        
        for date_str, event, y_pos in events:
            try:
                event_date = pd.to_datetime(date_str)
                if event_date >= recent_df.index.min() and event_date <= recent_df.index.max():
                    ax.axvline(x=event_date, color='red', linestyle='--', alpha=0.7)
                    ax.text(event_date, y_pos, event, rotation=90, 
                           verticalalignment='bottom', fontsize=9, color='red')
            except:
                continue
    
    # 设置标题和标签
    ax.set_title(f'US Economic Policy Uncertainty Index (Recent {years} Years)', 
                fontsize=14, fontweight='bold')
    ax.set_xlabel('Date', fontsize=12)
    ax.set_ylabel('EPU Index', fontsize=12)
    
    # 格式化x轴
    ax.xaxis.set_major_locator(mdates.YearLocator())
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax.xaxis.set_minor_locator(mdates.MonthLocator(interval=6))
    
    # 旋转x轴标签
    plt.xticks(rotation=45)
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    ax.legend()
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, f'epu_index_recent_{years}years.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"最近{years}年图表已保存到: {chart_path}")
    
    plt.show()

def plot_presidential_terms(df):
    """按总统任期绘制EPU指数"""
    # 定义总统任期
    presidential_terms = [
        ('1985-01-01', '1989-01-20', 'Reagan (2nd)', '#FF6B6B'),
        ('1989-01-20', '1993-01-20', 'Bush Sr.', '#4ECDC4'),
        ('1993-01-20', '2001-01-20', 'Clinton', '#45B7D1'),
        ('2001-01-20', '2009-01-20', 'Bush Jr.', '#96CEB4'),
        ('2009-01-20', '2017-01-20', 'Obama', '#FFEAA7'),
        ('2017-01-20', '2021-01-20', 'Trump', '#DDA0DD'),
        ('2021-01-20', '2025-03-01', 'Biden', '#98D8C8'),
    ]
    
    fig, ax = plt.subplots(figsize=(16, 10))
    
    # 绘制主线
    ax.plot(df.index, df['EPU指数'], linewidth=2, color='black', alpha=0.8, zorder=3)
    
    # 为每个总统任期添加背景色
    for start_date, end_date, president, color in presidential_terms:
        start = pd.to_datetime(start_date)
        end = pd.to_datetime(end_date)
        
        # 确保日期在数据范围内
        start = max(start, df.index.min())
        end = min(end, df.index.max())
        
        if start < end:
            ax.axvspan(start, end, alpha=0.2, color=color, label=president)
    
    # 添加平均线
    mean_value = df['EPU指数'].mean()
    ax.axhline(y=mean_value, color='red', linestyle='--', alpha=0.7, 
               label=f'Overall Average: {mean_value:.1f}')
    
    # 设置标题和标签
    ax.set_title('US Economic Policy Uncertainty Index by Presidential Terms', 
                fontsize=16, fontweight='bold')
    ax.set_xlabel('Year', fontsize=12)
    ax.set_ylabel('EPU Index', fontsize=12)
    
    # 格式化x轴
    ax.xaxis.set_major_locator(mdates.YearLocator(4))
    ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax.xaxis.set_minor_locator(mdates.YearLocator())
    
    # 添加网格
    ax.grid(True, alpha=0.3)
    ax.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, 'epu_index_presidential_terms.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"总统任期图表已保存到: {chart_path}")
    
    plt.show()

def plot_distribution_analysis(df):
    """绘制EPU指数分布分析"""
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    
    # 1. 直方图
    ax1.hist(df['EPU指数'], bins=30, alpha=0.7, color='skyblue', edgecolor='black')
    ax1.axvline(df['EPU指数'].mean(), color='red', linestyle='--', 
                label=f'Mean: {df["EPU指数"].mean():.1f}')
    ax1.axvline(df['EPU指数'].median(), color='green', linestyle='--', 
                label=f'Median: {df["EPU指数"].median():.1f}')
    ax1.set_title('Distribution of EPU Index Values')
    ax1.set_xlabel('EPU Index')
    ax1.set_ylabel('Frequency')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 箱线图按年代
    decades = []
    values = []
    for year in range(1980, 2030, 10):
        decade_data = df[(df.index.year >= year) & (df.index.year < year + 10)]
        if len(decade_data) > 0:
            decades.append(f'{year}s')
            values.append(decade_data['EPU指数'].values)
    
    ax2.boxplot(values, labels=decades)
    ax2.set_title('EPU Index Distribution by Decade')
    ax2.set_ylabel('EPU Index')
    ax2.grid(True, alpha=0.3)
    
    # 3. 年度平均值
    yearly_avg = df.groupby(df.index.year)['EPU指数'].mean()
    ax3.bar(yearly_avg.index, yearly_avg.values, alpha=0.7, color='lightcoral')
    ax3.set_title('Annual Average EPU Index')
    ax3.set_xlabel('Year')
    ax3.set_ylabel('Average EPU Index')
    ax3.grid(True, alpha=0.3)
    
    # 4. 滚动标准差 (波动性)
    rolling_std = df['EPU指数'].rolling(window=12).std()
    ax4.plot(rolling_std.index, rolling_std.values, color='purple', linewidth=2)
    ax4.set_title('EPU Index Volatility (12-Month Rolling Std)')
    ax4.set_xlabel('Year')
    ax4.set_ylabel('Rolling Standard Deviation')
    ax4.grid(True, alpha=0.3)
    
    # 调整布局
    plt.tight_layout()
    
    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, 'epu_index_distribution_analysis.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"分布分析图表已保存到: {chart_path}")
    
    plt.show()

def main():
    """主函数"""
    print("经济政策不确定性指数数据可视化")
    print("="*50)
    
    # 加载数据
    df = load_epu_data()
    if df is None:
        return
    
    print(f"数据时间范围: {df.index.min().strftime('%Y-%m-%d')} 到 {df.index.max().strftime('%Y-%m-%d')}")
    print(f"最新EPU指数: {df['EPU指数'].iloc[-1]:.1f}")
    print()
    
    # 生成图表
    print("正在生成图表...")
    
    # 1. 完整历史数据
    plot_full_history(df)
    
    # 2. 最近10年数据
    plot_recent_years(df, years=10)
    
    # 3. 按总统任期分析
    plot_presidential_terms(df)
    
    # 4. 分布分析
    plot_distribution_analysis(df)
    
    print("\n所有图表已生成完成！")
    print(f"图表保存目录: {CHARTS_DIR}")

if __name__ == "__main__":
    main()
