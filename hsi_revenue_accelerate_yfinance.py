#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从恒生指数成分股中筛选出营收加速增长的公司

使用yfinance获取财务数据，分析营收增长趋势，筛选出营收加速增长的公司
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import time
import logging
from tqdm import tqdm
from concurrent.futures import ThreadPoolExecutor, as_completed
import yfinance as yf

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hsi_revenue_accelerate.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 创建输出目录
OUTPUT_DIR = "output/hsi_revenue_accelerate"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def load_hsi_constituents(file_path="data_files/hsi_constituents.csv"):
    """从CSV文件加载恒生指数成分股"""
    try:
        # 确保代码列被读取为字符串类型
        df = pd.read_csv(file_path, dtype={'代码': str})

        # 确保代码列的格式正确（5位数字，前导零）
        if '代码' in df.columns:
            df['代码'] = df['代码'].apply(lambda x: str(x).zfill(5))

        logger.info(f"从{file_path}加载了{len(df)}只恒生指数成分股")
        return df
    except Exception as e:
        logger.error(f"加载恒生指数成分股失败: {str(e)}")
        return None

def format_yahoo_stock_code(code, stock_name):
    """格式化股票代码，用于Yahoo Finance API"""
    # 对于港股，需要将代码转换为Yahoo Finance格式 (例如: 0700.HK)
    if code.startswith('0') and len(code) <= 5:
        # 去掉前导零
        numeric_code = code.lstrip('0')
        yahoo_code = f"{numeric_code}.HK"
    else:
        # 处理特殊情况，如美股ADR等
        if '-S' in stock_name or '-SW' in stock_name or '-W' in stock_name:
            # 可能是美股
            if code.startswith('9'):
                # 尝试映射一些常见的股票
                mapping = {
                    '09999': 'NTES',  # 网易
                    '09618': 'JD',    # 京东
                    '09988': 'BABA',  # 阿里巴巴
                    '09888': 'BIDU',  # 百度
                    '09961': 'TCOM'   # 携程
                }
                yahoo_code = mapping.get(code, f"{code}.HK")
            else:
                yahoo_code = f"{code}.HK"
        else:
            yahoo_code = f"{code}.HK"

    return yahoo_code

def get_financial_data(stock_code, stock_name):
    """使用yfinance获取单只股票的财务数据"""
    try:
        # 格式化股票代码
        yahoo_code = format_yahoo_stock_code(stock_code, stock_name)
        logger.info(f"获取 {yahoo_code} ({stock_name}) 的财务数据")

        # 使用yfinance获取股票信息
        stock = yf.Ticker(yahoo_code)

        # 获取季度财务报表
        quarterly_financials = stock.quarterly_financials

        # 获取年度财务报表
        annual_financials = stock.financials

        # 处理数据
        return process_yfinance_data(quarterly_financials, annual_financials, stock_code, stock_name)

    except Exception as e:
        logger.error(f"获取 {stock_code} 的财务数据时出错: {str(e)}")
        return None

def process_yfinance_data(quarterly_financials, annual_financials, stock_code, stock_name):
    """处理yfinance财务数据，提取营收和利润"""
    try:
        # 创建结果列表
        result_rows = []

        # 优先使用季度数据
        if quarterly_financials is not None and not quarterly_financials.empty:
            # 尝试获取总收入行
            if 'Total Revenue' in quarterly_financials.index:
                revenue_data = quarterly_financials.loc['Total Revenue']
                logger.debug(f"找到季度总收入数据: {len(revenue_data)} 个季度")

                # 尝试获取净利润行
                net_income_data = None
                for income_row in ['Net Income', 'Net Income Common Stockholders']:
                    if income_row in quarterly_financials.index:
                        net_income_data = quarterly_financials.loc[income_row]
                        logger.debug(f"找到季度净利润数据: {len(net_income_data)} 个季度")
                        break

                # 处理每个季度的数据
                for date in revenue_data.index:
                    revenue = revenue_data[date]
                    net_profit = net_income_data[date] if net_income_data is not None else None

                    # 创建结果行
                    result_row = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'report_date': date,
                        'revenue': float(revenue) if not pd.isna(revenue) else None,
                        'net_profit': float(net_profit) if net_profit is not None and not pd.isna(net_profit) else None
                    }

                    result_rows.append(result_row)

        # 如果没有季度数据，使用年度数据
        if (not result_rows) and annual_financials is not None and not annual_financials.empty:
            # 尝试获取总收入行
            if 'Total Revenue' in annual_financials.index:
                revenue_data = annual_financials.loc['Total Revenue']
                logger.debug(f"找到年度总收入数据: {len(revenue_data)} 个年度")

                # 尝试获取净利润行
                net_income_data = None
                for income_row in ['Net Income', 'Net Income Common Stockholders']:
                    if income_row in annual_financials.index:
                        net_income_data = annual_financials.loc[income_row]
                        logger.debug(f"找到年度净利润数据: {len(net_income_data)} 个年度")
                        break

                # 处理每个年度的数据
                for date in revenue_data.index:
                    revenue = revenue_data[date]
                    net_profit = net_income_data[date] if net_income_data is not None else None

                    # 创建结果行
                    result_row = {
                        'stock_code': stock_code,
                        'stock_name': stock_name,
                        'report_date': date,
                        'revenue': float(revenue) if not pd.isna(revenue) else None,
                        'net_profit': float(net_profit) if net_profit is not None and not pd.isna(net_profit) else None
                    }

                    result_rows.append(result_row)

        # 检查是否成功提取到数据
        if not result_rows:
            logger.warning(f"未能提取 {stock_code} 的财务数据")
            return None

        # 创建DataFrame
        df = pd.DataFrame(result_rows)

        # 添加年份和季度信息
        df['year'] = df['report_date'].dt.year
        df['month'] = df['report_date'].dt.month
        df['quarter'] = df['month'].apply(lambda x: (x-1)//3 + 1)
        df['year_quarter'] = df['year'].astype(str) + '-Q' + df['quarter'].astype(str)

        # 按日期排序
        df = df.sort_values('report_date')

        return df

    except Exception as e:
        logger.error(f"处理 {stock_code} 的财务数据时出错: {str(e)}")
        return None

def calculate_growth_rates(df):
    """计算同比增长率和加速度"""
    try:
        # 确保数据按日期排序
        df = df.sort_values('report_date')

        # 初始化增长率列
        df['revenue_yoy'] = np.nan
        df['revenue_acceleration'] = np.nan

        # 按季度分组
        for quarter in range(1, 5):
            quarter_data = df[df['quarter'] == quarter].copy()
            quarter_data = quarter_data.sort_values('year')

            # 计算同比增长率
            for i in range(1, len(quarter_data)):
                current_year = quarter_data.iloc[i]['year']
                prev_year = quarter_data.iloc[i-1]['year']

                # 确保是相邻年份
                if current_year == prev_year + 1:
                    # 营收同比增长率
                    current_revenue = quarter_data.iloc[i]['revenue']
                    prev_revenue = quarter_data.iloc[i-1]['revenue']

                    if prev_revenue and prev_revenue != 0:
                        revenue_yoy = (current_revenue - prev_revenue) / prev_revenue * 100
                        df.loc[quarter_data.iloc[i].name, 'revenue_yoy'] = revenue_yoy

        # 计算增长率的加速度（增长率的变化）
        for quarter in range(1, 5):
            quarter_data = df[df['quarter'] == quarter].copy()
            quarter_data = quarter_data.sort_values('year')

            # 需要至少两个增长率数据点才能计算加速度
            if 'revenue_yoy' in quarter_data.columns and len(quarter_data.dropna(subset=['revenue_yoy'])) >= 2:
                growth_rates = quarter_data['revenue_yoy'].dropna()

                for i in range(1, len(growth_rates)):
                    current_growth = growth_rates.iloc[i]
                    prev_growth = growth_rates.iloc[i-1]

                    # 计算加速度（增长率的变化）
                    acceleration = current_growth - prev_growth
                    df.loc[growth_rates.index[i], 'revenue_acceleration'] = acceleration

        return df

    except Exception as e:
        logger.error(f"计算增长率时出错: {str(e)}")
        return df

def is_accelerating(df, consecutive_periods=2):
    """判断是否连续加速增长"""
    try:
        # 确保数据按日期排序
        df = df.sort_values('report_date')

        # 获取加速度数据
        acceleration_data = df['revenue_acceleration'].dropna()

        # 检查是否有足够的数据点
        if len(acceleration_data) < consecutive_periods:
            return False

        # 检查最近几期是否连续为正（增长加速）
        recent_acceleration = acceleration_data.tail(consecutive_periods)
        is_accelerating = all(recent_acceleration > 0)

        return is_accelerating

    except Exception as e:
        logger.error(f"判断是否加速增长时出错: {str(e)}")
        return False

def analyze_stock(stock_code, stock_name):
    """分析单只股票的营收增长情况"""
    try:
        # 获取财务数据
        financial_data = get_financial_data(stock_code, stock_name)

        if financial_data is None or financial_data.empty:
            logger.warning(f"无法获取 {stock_code} 的财务数据")
            return None

        # 计算增长率和加速度
        growth_data = calculate_growth_rates(financial_data)

        # 判断是否加速增长
        accelerating = is_accelerating(growth_data)

        # 获取最新的增长率和加速度
        latest_data = growth_data.sort_values('report_date').tail(1)
        latest_growth = latest_data['revenue_yoy'].iloc[0] if 'revenue_yoy' in latest_data.columns and not latest_data['revenue_yoy'].isna().all() else None
        latest_acceleration = latest_data['revenue_acceleration'].iloc[0] if 'revenue_acceleration' in latest_data.columns and not latest_data['revenue_acceleration'].isna().all() else None

        # 返回分析结果
        result = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'latest_growth': latest_growth,
            'latest_acceleration': latest_acceleration,
            'is_accelerating': accelerating,
            'data': growth_data
        }

        return result

    except Exception as e:
        logger.error(f"分析 {stock_code} 时出错: {str(e)}")
        return None

def plot_results(result_df):
    """绘制结果图表"""
    try:
        # 取增长率最高的前10家公司
        top_companies = result_df.head(10)

        # 创建图表
        plt.figure(figsize=(12, 8))

        # 按增长率排序
        sorted_companies = top_companies.sort_values(by='最新增长率', ascending=True)

        # 创建水平条形图
        bars = plt.barh(sorted_companies['名称'], sorted_companies['最新增长率'], color='skyblue')

        # 添加数据标签
        for bar in bars:
            width = bar.get_width()
            plt.text(width + 1, bar.get_y() + bar.get_height()/2, f'{width:.2f}%',
                    ha='left', va='center', fontsize=10)

        plt.xlabel('最新季度同比增长率 (%)')
        plt.title('恒生指数成分股中营收加速增长的公司 (Top 10)')
        plt.grid(axis='x', linestyle='--', alpha=0.7)
        plt.tight_layout()

        # 保存图表
        output_file = os.path.join(OUTPUT_DIR, "hsi_accelerating_companies.png")
        plt.savefig(output_file, dpi=300, bbox_inches='tight')
        logger.info(f"已保存图表到 {output_file}")

    except Exception as e:
        logger.error(f"绘制图表时出错: {str(e)}")

def main():
    """主函数"""
    logger.info("开始分析恒生指数成分股的营收增长情况")

    # 加载恒生指数成分股
    constituents_df = load_hsi_constituents()
    if constituents_df is None:
        logger.error("加载恒生指数成分股失败，退出程序")
        return

    # 分析结果
    results = []

    # 使用ThreadPoolExecutor进行并行处理
    with ThreadPoolExecutor(max_workers=5) as executor:
        # 创建一个字典，映射future到股票代码
        future_to_stock = {
            executor.submit(analyze_stock, row['代码'], row['名称']): row['代码']
            for _, row in constituents_df.iterrows()
        }

        # 处理完成的任务
        for future in tqdm(as_completed(future_to_stock), total=len(future_to_stock), desc="分析进度"):
            stock_code = future_to_stock[future]
            stock_name = constituents_df[constituents_df['代码'] == stock_code]['名称'].iloc[0]

            try:
                result = future.result()
                if result is not None:
                    results.append(result)
            except Exception as e:
                logger.error(f"处理 {stock_code} ({stock_name}) 时出错: {str(e)}")

    # 筛选出营收加速增长的公司
    accelerating_companies = [r for r in results if r['is_accelerating']]

    # 按最新增长率排序
    accelerating_companies = sorted(accelerating_companies, key=lambda x: x['latest_growth'] if x['latest_growth'] is not None else -float('inf'), reverse=True)

    # 输出结果
    logger.info(f"共分析了 {len(results)} 只股票，其中 {len(accelerating_companies)} 只股票营收呈现加速增长趋势")

    # 创建结果DataFrame
    result_df = pd.DataFrame([
        {
            '代码': r['stock_code'],
            '名称': r['stock_name'],
            '最新增长率': r['latest_growth'],
            '增长加速度': r['latest_acceleration'],
            '连续加速增长': r['is_accelerating']
        }
        for r in accelerating_companies
    ])

    # 保存结果
    result_file = os.path.join(OUTPUT_DIR, "hsi_accelerating_companies.csv")
    result_df.to_csv(result_file, index=False, encoding='utf-8-sig')
    logger.info(f"已保存结果到 {result_file}")

    # 打印结果
    if not result_df.empty:
        pd.set_option('display.max_rows', None)
        pd.set_option('display.width', 1000)
        pd.set_option('display.float_format', '{:.2f}%'.format)
        print("\n营收加速增长的公司:")
        print(result_df)
    else:
        print("没有找到营收加速增长的公司")

    # 绘制图表
    if not result_df.empty:
        plot_results(result_df)

if __name__ == "__main__":
    main()
