#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
房地产复合指标合成 - 最佳版本
基于深度分析的最优处理流程：
1. 选择性去噪（住房销量和NAHB指数）
2. 跳过季节调整（无显著季节性）
3. HP滤波去趋势（所有指标都有显著趋势）
4. 滚动Min-Max标准化（保证动态更新稳定性）
"""

import pandas as pd
import numpy as np
import warnings
warnings.filterwarnings('ignore')

# 导入必要的库
import os

# 尝试导入HP滤波
try:
    from statsmodels.tsa.filters.hp_filter import hpfilter
    HP_FILTER_AVAILABLE = True
except ImportError:
    HP_FILTER_AVAILABLE = False
    print("警告: statsmodels HP滤波不可用，将使用替代方法")

# 尝试导入去噪所需的库
try:
    from scipy import signal
    SCIPY_AVAILABLE = True
except ImportError:
    SCIPY_AVAILABLE = False
    print("警告: scipy不可用，将跳过去噪处理")



def load_data(use_release_adjusted=True):
    """加载三个数据文件

    Args:
        use_release_adjusted: 是否使用发布时间调整后的数据
    """
    if use_release_adjusted:
        print("正在加载发布时间调整后的数据...")

        # 检查是否存在发布时间调整后的文件
        adjusted_files = {
            'new_home_sales': 'data/new_home_sales_release_adjusted.csv',
            'nahb_hmi': 'data/nahb_hmi_release_adjusted.csv',
            'mortgage_rates': 'data/mortgage_rates_release_adjusted.csv'
        }

        missing_files = []
        for name, file_path in adjusted_files.items():
            if not os.path.exists(file_path):
                missing_files.append(file_path)

        if missing_files:
            print(f"警告: 以下发布时间调整后的文件不存在:")
            for file in missing_files:
                print(f"  - {file}")
            print("请先运行 adjust_release_dates.py 生成调整后的数据")
            print("现在使用原始数据...")
            use_release_adjusted = False

    if use_release_adjusted:
        # 加载发布时间调整后的数据
        new_home_sales = pd.read_csv('data/new_home_sales_release_adjusted.csv')
        new_home_sales['DATE'] = pd.to_datetime(new_home_sales['DATE'])

        nahb_hmi = pd.read_csv('data/nahb_hmi_release_adjusted.csv')
        nahb_hmi['DATE'] = pd.to_datetime(nahb_hmi['DATE'])

        mortgage_rates = pd.read_csv('data/mortgage_rates_release_adjusted.csv')
        mortgage_rates['DATE'] = pd.to_datetime(mortgage_rates['DATE'])

        print("✅ 已加载发布时间调整后的数据")
    else:
        print("正在加载原始数据...")

        # 加载原始数据
        new_home_sales = pd.read_csv('data/new_home_sales.csv')
        new_home_sales['DATE'] = pd.to_datetime(new_home_sales['DATE'])
        new_home_sales = new_home_sales[['DATE', 'New_Home_Sales']].copy()

        nahb_hmi = pd.read_csv('data/nahb_hmi.csv')
        nahb_hmi['Date'] = pd.to_datetime(nahb_hmi['Date'])
        nahb_hmi = nahb_hmi[['Date', 'NAHB_HMI']].copy()
        nahb_hmi.rename(columns={'Date': 'DATE'}, inplace=True)

        mortgage_rates = pd.read_csv('data/mortgage_rates.csv')
        mortgage_rates['DATE'] = pd.to_datetime(mortgage_rates['DATE'])
        mortgage_rates = mortgage_rates[['DATE', 'Mortgage_Rate']].copy()

        print("⚠️  使用原始数据（数据所属时间，非发布时间）")

    print(f"新建住房销量数据: {len(new_home_sales)} 条记录")
    print(f"NAHB住房市场指数数据: {len(nahb_hmi)} 条记录")
    print(f"抵押贷款利率数据: {len(mortgage_rates)} 条记录")

    return new_home_sales, nahb_hmi, mortgage_rates

def prepare_data_for_merge(new_home_sales, nahb_hmi, mortgage_rates):
    """准备数据用于按发布时间合并"""
    print("正在准备数据用于按发布时间合并...")

    # 保持原始发布日期，不转换为月度
    sales_data = new_home_sales[['DATE', 'New_Home_Sales']].copy()
    nahb_data = nahb_hmi[['DATE', 'NAHB_HMI']].copy()
    mortgage_data = mortgage_rates[['DATE', 'Mortgage_Rate']].copy()

    print(f"新建住房销量数据: {len(sales_data)} 条记录")
    print(f"NAHB指数数据: {len(nahb_data)} 条记录")
    print(f"抵押贷款利率数据: {len(mortgage_data)} 条记录")

    return sales_data, nahb_data, mortgage_data

def merge_data_by_release_date(sales_data, nahb_data, mortgage_data):
    """按发布时间合并数据，复合指标日期取三个指标日期的最大值"""
    print("正在按发布时间合并数据...")

    # 创建所有唯一日期的列表
    all_dates = set()
    all_dates.update(sales_data['DATE'])
    all_dates.update(nahb_data['DATE'])
    all_dates.update(mortgage_data['DATE'])
    all_dates = sorted(list(all_dates))

    # 创建基础DataFrame
    merged = pd.DataFrame({'DATE': all_dates})

    # 合并各个数据源
    merged = merged.merge(sales_data, on='DATE', how='left')
    merged = merged.merge(nahb_data, on='DATE', how='left')
    merged = merged.merge(mortgage_data, on='DATE', how='left')

    # 前向填充数据（使用最近可用的数据）
    merged['New_Home_Sales'] = merged['New_Home_Sales'].ffill()
    merged['NAHB_HMI'] = merged['NAHB_HMI'].ffill()
    merged['Mortgage_Rate'] = merged['Mortgage_Rate'].ffill()

    # 删除所有指标都为空的行（通常是最开始的行）
    merged = merged.dropna(subset=['New_Home_Sales', 'NAHB_HMI', 'Mortgage_Rate'], how='all')

    print(f"合并后数据: {len(merged)} 条记录")
    print(f"数据时间范围: {merged['DATE'].min()} 到 {merged['DATE'].max()}")

    return merged

def apply_denoising(series, column_name):
    """选择性去噪处理"""
    # 需要去噪的指标
    denoising_indicators = ['New_Home_Sales', 'NAHB_HMI', 'nahb', '住房销量', '住房市场指数']

    # 检查是否需要去噪
    needs_denoising = any(indicator in column_name for indicator in denoising_indicators)

    if not needs_denoising:
        print(f"    {column_name}无需去噪处理")
        return series

    if not SCIPY_AVAILABLE:
        print(f"    scipy不可用，跳过{column_name}去噪处理")
        return series

    try:
        print(f"    对{column_name}应用低通滤波去噪")
        # 低通滤波，截止频率为2年周期
        nyquist = 0.5
        cutoff = 1/24  # 2年周期
        b, a = signal.butter(4, cutoff / nyquist, btype='low')
        denoised = signal.filtfilt(b, a, series.values)
        return pd.Series(denoised, index=series.index)
    except Exception as e:
        print(f"    去噪失败: {e}，使用原始数据")
        return series

def hp_filter_detrend(series, lambda_param=1600):
    """HP滤波去趋势"""
    if not HP_FILTER_AVAILABLE:
        print(f"    HP滤波不可用，使用简单去趋势方法")
        return simple_detrend(series)

    try:
        # 使用statsmodels的HP滤波
        cycle, trend = hpfilter(series.dropna(), lamb=lambda_param)
        print(f"    ✓ HP滤波完成")
        return cycle
    except Exception as e:
        print(f"    HP滤波失败: {str(e)}")
        print(f"    使用简单去趋势方法")
        return simple_detrend(series)

def simple_detrend(series):
    """简单去趋势方法"""
    try:
        # 使用移动平均去趋势
        window = max(12, int(len(series) / 10))
        trend = series.rolling(window=window, center=True).mean()
        detrended = series - trend
        return detrended.fillna(0)
    except:
        # 如果失败，使用线性去趋势
        return series - series.mean()

def rolling_min_max_standardization(series, window=120):
    """滚动窗口Min-Max标准化（保证动态更新稳定性）"""
    result = pd.Series(index=series.index, dtype=float)

    for i in range(len(series)):
        if i < window - 1:
            # 使用从开始到当前的所有数据
            subset = series.iloc[:i+1]
        else:
            # 使用滚动窗口（默认10年）
            subset = series.iloc[i-window+1:i+1]

        if len(subset) > 1:
            min_val = subset.min()
            max_val = subset.max()
            if max_val > min_val:
                result.iloc[i] = (series.iloc[i] - min_val) / (max_val - min_val)
            else:
                result.iloc[i] = 0.5  # 如果窗口内数据相同
        else:
            result.iloc[i] = 0.5

    return result

def preprocess_indicator(data, column_name, lambda_param=1600):
    """预处理单个指标 - 最佳版本

    处理顺序：
    1. 选择性去噪（住房销量和NAHB指数）
    2. 跳过季节调整（无显著季节性）
    3. HP滤波去趋势（所有指标都有显著趋势）
    4. 滚动Min-Max标准化（保证动态更新稳定性）
    """
    print(f"\n正在预处理 {column_name}...")

    # 提取非空数据
    series = data[column_name].dropna()

    if len(series) < 12:  # 至少需要1年数据
        print(f"  警告: {column_name} 数据不足，跳过处理")
        return pd.Series(index=data.index, dtype=float)

    print(f"  数据长度: {len(series)}")
    print(f"  数据范围: {series.min():.2f} - {series.max():.2f}")

    # 步骤1: 选择性去噪
    print(f"  步骤1: 去噪处理")
    series_denoised = apply_denoising(series, column_name)

    # 步骤2: 跳过季节调整
    print(f"  步骤2: 跳过季节调整 - {column_name}无显著季节性")

    # 步骤3: HP滤波去趋势
    print(f"  步骤3: HP滤波去趋势")
    try:
        series_detrended = hp_filter_detrend(series_denoised, lambda_param)
        print(f"    ✓ HP滤波去趋势完成 (λ={lambda_param})")
    except Exception as e:
        print(f"    HP滤波失败: {e}")
        series_detrended = simple_detrend(series_denoised)
        print(f"    ✓ 简单去趋势完成")

    # 步骤4: 滚动Min-Max标准化
    print(f"  步骤4: 滚动Min-Max标准化")
    series_standardized = rolling_min_max_standardization(series_detrended, window=120)
    print(f"    ✓ 滚动Min-Max标准化完成 (10年窗口)")

    # 将结果映射回原始索引
    result = pd.Series(index=data.index, dtype=float)

    # 确保索引匹配
    if len(series_standardized) == len(series):
        # 如果长度匹配，直接映射
        result.loc[series.index] = series_standardized.values
    else:
        # 如果长度不匹配，尝试按位置映射
        min_len = min(len(series_standardized), len(series))
        result.iloc[:min_len] = series_standardized.iloc[:min_len].values

    print(f"  ✓ {column_name}预处理完成，有效值: {result.notna().sum()}")

    return result

def create_composite_indicator(data):
    """创建房地产复合指标 - 最佳版本"""
    print("\n" + "="*60)
    print("正在创建房地产复合指标 - 最佳版本")
    print("="*60)

    # 预处理三个指标
    print("\n🔄 开始预处理各个指标...")
    processed_sales = preprocess_indicator(data, 'New_Home_Sales')
    processed_nahb = preprocess_indicator(data, 'NAHB_HMI')
    processed_mortgage = preprocess_indicator(data, 'Mortgage_Rate')

    # 统计信息
    print(f"\n📊 预处理结果统计:")
    print(f"  新建住房销量有效值: {processed_sales.notna().sum()}")
    print(f"  NAHB住房市场指数有效值: {processed_nahb.notna().sum()}")
    print(f"  抵押贷款利率有效值: {processed_mortgage.notna().sum()}")

    # 抵押贷款利率需要取负值（利率越高，房地产市场越不利）
    print(f"\n🔄 调整抵押贷款利率方向（取负值）...")
    processed_mortgage = -processed_mortgage

    # 等权重合成
    print(f"\n🔄 构建复合指标...")
    composite_data = pd.DataFrame({
        'DATE': data['DATE'],
        'Processed_Sales': processed_sales,
        'Processed_NAHB': processed_nahb,
        'Processed_Mortgage_Rate': processed_mortgage
    })

    # 计算复合指标（等权重平均）
    indicator_columns = ['Processed_Sales', 'Processed_NAHB', 'Processed_Mortgage_Rate']
    valid_indicators = composite_data[indicator_columns].notna()

    # 只有当至少有一个指标有效时才计算复合指标
    composite_data['Real_Estate_Composite'] = composite_data[indicator_columns].mean(axis=1, skipna=True)

    # 只保留至少有一个指标有效的记录
    composite_data = composite_data[valid_indicators.any(axis=1)].copy()

    # 最终统计
    print(f"\n✅ 房地产复合指标创建完成!")
    print(f"  总记录数: {len(composite_data)}")
    print(f"  时间范围: {composite_data['DATE'].min().strftime('%Y-%m')} 到 {composite_data['DATE'].max().strftime('%Y-%m')}")

    if len(composite_data) > 0:
        composite_stats = composite_data['Real_Estate_Composite'].describe()
        print(f"  复合指标统计:")
        print(f"    均值: {composite_stats['mean']:.4f}")
        print(f"    标准差: {composite_stats['std']:.4f}")
        print(f"    最小值: {composite_stats['min']:.4f}")
        print(f"    最大值: {composite_stats['max']:.4f}")

    return composite_data

def main():
    """主函数 - 最佳版本"""
    print("🏠 房地产复合指标合成 - 最佳版本")
    print("="*80)
    print("基于深度分析的最优处理流程:")
    print("  1. 选择性去噪（住房销量和NAHB指数）")
    print("  2. 跳过季节调整（无显著季节性）")
    print("  3. HP滤波去趋势（所有指标都有显著趋势）")
    print("  4. 滚动Min-Max标准化（保证动态更新稳定性）")
    print("="*80)

    try:
        # 1. 加载数据
        print("\n📂 步骤1: 加载数据")
        new_home_sales, nahb_hmi, mortgage_rates = load_data(use_release_adjusted=True)

        # 2. 准备数据用于合并
        print("\n📅 步骤2: 准备数据用于合并")
        sales_data, nahb_data, mortgage_data = prepare_data_for_merge(
            new_home_sales, nahb_hmi, mortgage_rates
        )

        # 3. 按发布时间合并数据
        print("\n🔗 步骤3: 按发布时间合并数据")
        merged_data = merge_data_by_release_date(sales_data, nahb_data, mortgage_data)

        # 4. 创建复合指标
        print("\n🏗️ 步骤4: 创建复合指标")
        composite_indicator = create_composite_indicator(merged_data)

        # 5. 保存结果
        print("\n💾 步骤5: 保存结果")
        output_file = 'data/real_estate_composite_indicator.csv'

        if len(composite_indicator) > 0:
            composite_indicator.to_csv(output_file, index=False)
            print(f"✅ 结果已保存到: {output_file}")

            # 显示数据预览
            print(f"\n📋 数据预览:")
            print(f"  数据形状: {composite_indicator.shape}")
            print(f"  列名: {composite_indicator.columns.tolist()}")
            print(f"\n前5行数据:")
            print(composite_indicator.head().to_string(index=False))

            print(f"\n后5行数据:")
            print(composite_indicator.tail().to_string(index=False))

        else:
            print("❌ 警告: 没有生成有效的复合指标数据")

    except Exception as e:
        print(f"❌ 程序执行出错: {e}")
        import traceback
        traceback.print_exc()

    print(f"\n🎉 房地产复合指标合成完成!")
    print("="*80)

if __name__ == "__main__":
    main()
