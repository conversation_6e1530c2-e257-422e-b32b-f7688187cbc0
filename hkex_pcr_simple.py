#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
香港交易所恒生指数PCR数据获取工具（简化版）

从香港交易所官网获取恒生指数期权的Put/Call Ratio数据
数据源：https://www.hkex.com.hk/eng/sorc/market_data/statistics_putcall_ratio.aspx

使用说明：
1. 运行脚本自动获取最近3个月的PCR数据
2. 生成图表和CSV数据文件
3. 显示详细的分析报告

作者：AI Assistant
创建时间：2025年1月31日
"""

import requests
import pandas as pd
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import numpy as np
import datetime
import time
import os
from bs4 import BeautifulSoup
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def get_hkex_pcr_data(start_date, end_date):
    """
    从香港交易所获取真实PCR数据

    Args:
        start_date (str): 开始日期，格式：YYYY-MM-DD
        end_date (str): 结束日期，格式：YYYY-MM-DD

    Returns:
        pd.DataFrame: PCR数据
    """
    print(f"🚀 正在获取恒生指数PCR数据：{start_date} 到 {end_date}")

    # 香港交易所PCR数据URL
    url = "https://www.hkex.com.hk/eng/sorc/market_data/statistics_putcall_ratio.aspx"

    # 设置更真实的请求头
    headers = {
        'User-Agent': 'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
        'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'Accept-Language': 'en-US,en;q=0.9,zh-CN;q=0.8,zh;q=0.7',
        'Accept-Encoding': 'gzip, deflate, br',
        'Connection': 'keep-alive',
        'Upgrade-Insecure-Requests': '1',
        'Sec-Fetch-Dest': 'document',
        'Sec-Fetch-Mode': 'navigate',
        'Sec-Fetch-Site': 'none',
        'Cache-Control': 'max-age=0',
    }

    try:
        # 创建会话
        session = requests.Session()
        session.headers.update(headers)

        # 第一步：获取页面和ViewState
        print("📡 正在访问香港交易所网站...")
        response = session.get(url, timeout=30)
        response.raise_for_status()

        print("🔍 正在解析页面结构...")
        soup = BeautifulSoup(response.text, 'html.parser')

        # 提取所有隐藏字段
        form_data = {}

        # 查找ViewState等ASP.NET必需字段
        viewstate = soup.find('input', {'name': '__VIEWSTATE'})
        if viewstate:
            form_data['__VIEWSTATE'] = viewstate.get('value', '')
            print("✅ 找到 __VIEWSTATE")

        viewstategenerator = soup.find('input', {'name': '__VIEWSTATEGENERATOR'})
        if viewstategenerator:
            form_data['__VIEWSTATEGENERATOR'] = viewstategenerator.get('value', '')
            print("✅ 找到 __VIEWSTATEGENERATOR")

        eventvalidation = soup.find('input', {'name': '__EVENTVALIDATION'})
        if eventvalidation:
            form_data['__EVENTVALIDATION'] = eventvalidation.get('value', '')
            print("✅ 找到 __EVENTVALIDATION")

        # 查找所有其他隐藏字段
        for hidden_input in soup.find_all('input', {'type': 'hidden'}):
            name = hidden_input.get('name')
            value = hidden_input.get('value', '')
            if name and name not in form_data:
                form_data[name] = value

        print(f"📋 提取到 {len(form_data)} 个表单字段")

        # 设置查询参数 - 使用恒生指数期权代码
        form_data.update({
            'ctl00$ContentPlaceHolder1$ddlUnderlying': '00388',  # 恒生指数期权
            'ctl00$ContentPlaceHolder1$txtDateFrom': datetime.datetime.strptime(start_date, '%Y-%m-%d').strftime('%d/%m/%Y'),
            'ctl00$ContentPlaceHolder1$txtDateTo': datetime.datetime.strptime(end_date, '%Y-%m-%d').strftime('%d/%m/%Y'),
            'ctl00$ContentPlaceHolder1$btnSearch': 'Search'
        })

        print(f"📊 查询参数：")
        print(f"   标的：恒生指数期权 (00388)")
        print(f"   开始日期：{form_data['ctl00$ContentPlaceHolder1$txtDateFrom']}")
        print(f"   结束日期：{form_data['ctl00$ContentPlaceHolder1$txtDateTo']}")

        # 更新请求头为POST请求
        session.headers.update({
            'Content-Type': 'application/x-www-form-urlencoded',
            'Origin': 'https://www.hkex.com.hk',
            'Referer': url,
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'same-origin',
        })

        print("� 正在提交查询请求...")

        # 发送POST请求
        response = session.post(url, data=form_data, timeout=30)
        response.raise_for_status()

        print("🔍 正在解析返回数据...")

        # 解析数据
        data = parse_pcr_data(response.text)

        if data is not None and len(data) > 0:
            print(f"✅ 成功获取 {len(data)} 条真实PCR数据")
            return data
        else:
            print("❌ 未获取到有效数据")
            print("💡 可能的原因：")
            print("   1. 选择的日期范围内没有交易数据")
            print("   2. 网站结构发生变化")
            print("   3. 访问被限制")
            print("\n🔄 尝试其他方法获取数据...")
            return try_alternative_methods(start_date, end_date)

    except Exception as e:
        print(f"❌ 获取数据失败：{e}")
        print("🔄 尝试其他方法获取数据...")
        return try_alternative_methods(start_date, end_date)

def parse_pcr_data(html_content):
    """解析HTML中的PCR数据"""
    try:
        soup = BeautifulSoup(html_content, 'html.parser')

        # 查找数据表格
        table = soup.find('table', {'id': 'ctl00_ContentPlaceHolder1_gvPCR'})
        if not table:
            table = soup.find('table', class_='table')
            if not table:
                return None

        # 解析表格数据
        rows = table.find_all('tr')
        data = []

        for row in rows[1:]:  # 跳过表头
            cells = row.find_all(['td', 'th'])
            if len(cells) >= 4:
                try:
                    date_str = cells[0].get_text().strip()
                    call_volume = float(cells[1].get_text().strip().replace(',', ''))
                    put_volume = float(cells[2].get_text().strip().replace(',', ''))
                    pcr_ratio = float(cells[3].get_text().strip())

                    # 转换日期格式
                    date = datetime.datetime.strptime(date_str, '%d/%m/%Y')

                    data.append({
                        'date': date,
                        'call_volume': call_volume,
                        'put_volume': put_volume,
                        'pcr_ratio': pcr_ratio
                    })
                except (ValueError, IndexError):
                    continue

        if data:
            df = pd.DataFrame(data)
            df = df.sort_values('date')
            return df
        else:
            return None

    except Exception as e:
        print(f"❌ 解析数据失败：{e}")
        return None

def try_alternative_methods(start_date, end_date):
    """尝试其他方法获取PCR数据"""
    print("🔄 尝试备用数据源...")

    # 尝试其他可能的数据源
    print("💡 建议的数据获取方法：")
    print("   1. 直接访问香港交易所网站手动下载数据")
    print("   2. 使用专业金融数据API（如Bloomberg、Wind等）")
    print("   3. 联系券商获取期权交易数据")
    print("   4. 使用第三方金融数据提供商")

    print("❌ 当前无法获取真实PCR数据，请尝试以上方法获取数据")
    return None



def plot_pcr_chart(df, save_path='hkex_hsi_pcr_chart.png'):
    """绘制PCR图表"""
    if df.empty:
        print("❌ 没有数据可以绘制")
        return

    # 计算移动平均线
    df['pcr_ma5'] = df['pcr_ratio'].rolling(window=5, min_periods=1).mean()
    df['pcr_ma10'] = df['pcr_ratio'].rolling(window=10, min_periods=1).mean()
    df['pcr_ma20'] = df['pcr_ratio'].rolling(window=20, min_periods=1).mean()

    # 创建图表
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

    # 第一个子图：PCR比率和移动平均线
    ax1.plot(df['date'], df['pcr_ratio'],
            color='#2E86AB', linewidth=1.5, alpha=0.7, label='PCR比率')
    ax1.plot(df['date'], df['pcr_ma5'],
            color='#A23B72', linewidth=2, label='5日移动平均')
    ax1.plot(df['date'], df['pcr_ma10'],
            color='#F18F01', linewidth=2, label='10日移动平均')
    ax1.plot(df['date'], df['pcr_ma20'],
            color='#C73E1D', linewidth=2, label='20日移动平均')

    # 添加水平参考线
    ax1.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7, label='PCR=1.0')
    ax1.axhline(y=0.8, color='green', linestyle=':', alpha=0.5, label='PCR=0.8 (偏乐观)')
    ax1.axhline(y=1.2, color='red', linestyle=':', alpha=0.5, label='PCR=1.2 (偏悲观)')

    ax1.set_title('恒生指数期权Put/Call比率 (PCR)', fontsize=16, fontweight='bold', pad=20)
    ax1.set_ylabel('PCR比率', fontsize=12)
    ax1.legend(loc='upper left', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # 第二个子图：成交量
    ax2.bar(df['date'], df['call_volume'],
           color='#2E8B57', alpha=0.7, label='看涨期权成交量')
    ax2.bar(df['date'], df['put_volume'],
           bottom=df['call_volume'], color='#CD5C5C', alpha=0.7,
           label='看跌期权成交量')

    ax2.set_title('期权成交量分布', fontsize=14, fontweight='bold')
    ax2.set_ylabel('成交量', fontsize=12)
    ax2.set_xlabel('日期', fontsize=12)
    ax2.legend(loc='upper left', fontsize=10)
    ax2.grid(True, alpha=0.3)

    # 设置日期格式
    for ax in [ax1, ax2]:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.MonthLocator(interval=1))
        plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)

    # 添加最新数据标注
    if len(df) > 0:
        latest = df.iloc[-1]
        latest_date = latest['date']
        latest_pcr = latest['pcr_ratio']

        ax1.scatter(latest_date, latest_pcr, color='red', s=100, zorder=5)
        ax1.annotate(f'最新PCR: {latest_pcr:.3f}\n{latest_date.strftime("%Y-%m-%d")}',
                    xy=(latest_date, latest_pcr),
                    xytext=(20, 20),
                    textcoords='offset points',
                    bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.8),
                    arrowprops=dict(arrowstyle='->', color='red'),
                    fontsize=10,
                    fontweight='bold')

    plt.tight_layout()
    plt.savefig(save_path, dpi=300, bbox_inches='tight')
    plt.close()

    print(f"✅ 图表已保存到：{save_path}")

def generate_report(df):
    """生成分析报告"""
    if df.empty:
        print("❌ 没有数据可以分析")
        return

    print("\n" + "="*60)
    print("📊 恒生指数期权PCR数据分析报告")
    print("="*60)

    # 基本统计信息
    print(f"📅 数据时间范围：{df['date'].min().strftime('%Y-%m-%d')} 到 {df['date'].max().strftime('%Y-%m-%d')}")
    print(f"📈 数据点数量：{len(df)} 个交易日")

    # PCR统计
    pcr_stats = df['pcr_ratio'].describe()
    print(f"\n🎯 PCR比率统计：")
    print(f"   平均值：{pcr_stats['mean']:.4f}")
    print(f"   中位数：{pcr_stats['50%']:.4f}")
    print(f"   标准差：{pcr_stats['std']:.4f}")
    print(f"   最小值：{pcr_stats['min']:.4f}")
    print(f"   最大值：{pcr_stats['max']:.4f}")

    # 最新数据
    latest = df.iloc[-1]
    print(f"\n📊 最新数据 ({latest['date'].strftime('%Y-%m-%d')})：")
    print(f"   PCR比率：{latest['pcr_ratio']:.4f}")
    print(f"   看涨期权成交量：{latest['call_volume']:,}")
    print(f"   看跌期权成交量：{latest['put_volume']:,}")

    # 市场情绪分析
    latest_pcr = latest['pcr_ratio']
    if latest_pcr < 0.8:
        sentiment = "偏乐观 (看涨情绪较强)"
    elif latest_pcr > 1.2:
        sentiment = "偏悲观 (看跌情绪较强)"
    else:
        sentiment = "相对平衡"

    print(f"\n💭 市场情绪分析：{sentiment}")

    # 趋势分析
    if len(df) >= 5:
        recent_trend = df['pcr_ratio'].tail(5).mean()
        overall_mean = df['pcr_ratio'].mean()

        if recent_trend > overall_mean * 1.1:
            trend = "近期PCR上升，看跌情绪增强"
        elif recent_trend < overall_mean * 0.9:
            trend = "近期PCR下降，看涨情绪增强"
        else:
            trend = "近期PCR相对稳定"

        print(f"📈 趋势分析：{trend}")

    print("="*60)

def main():
    """主函数"""
    print("🚀 恒生指数期权PCR数据收集器")
    print("="*50)

    # 设置日期范围（最近3个月）
    end_date = datetime.datetime.now()
    start_date = end_date - datetime.timedelta(days=90)

    start_date_str = start_date.strftime('%Y-%m-%d')
    end_date_str = end_date.strftime('%Y-%m-%d')

    try:
        # 获取PCR数据
        pcr_data = get_hkex_pcr_data(start_date_str, end_date_str)

        if pcr_data is not None and len(pcr_data) > 0:
            # 生成分析报告
            generate_report(pcr_data)

            # 绘制图表
            print("\n📈 正在生成图表...")
            plot_pcr_chart(pcr_data)

            # 保存数据到CSV
            csv_filename = f"hkex_hsi_pcr_data_{start_date_str}_{end_date_str}.csv"
            pcr_data.to_csv(csv_filename, index=False, encoding='utf-8-sig')
            print(f"✅ 数据已保存到：{csv_filename}")

            print("\n🎉 任务完成！")
            print("📁 生成的文件：")
            print(f"   - 图表：hkex_hsi_pcr_chart.png")
            print(f"   - 数据：{csv_filename}")

        else:
            print("❌ 未能获取到有效的PCR数据")

    except Exception as e:
        print(f"❌ 程序执行出错：{e}")

if __name__ == "__main__":
    main()
