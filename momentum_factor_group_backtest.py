#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动量因子分组回测分析
使用最佳参数组合：160天回看期，季度调仓，只做多策略
"""

import pandas as pd
import numpy as np
import sqlite3
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MomentumFactorGroupBacktest:
    def __init__(self, lookback_days=160, rebalance_freq='Q', n_groups=5):
        """
        初始化动量因子分组回测
        
        Parameters:
        lookback_days: 回看天数
        rebalance_freq: 调仓频率 ('Q' = 季度)
        n_groups: 分组数量
        """
        self.lookback_days = lookback_days
        self.rebalance_freq = rebalance_freq
        self.n_groups = n_groups
        self.stock_data = None
        
    def load_stock_data(self):
        """从数据库加载股票数据"""
        try:
            conn = sqlite3.connect('ganggutong_10year_data.db')
            
            # 获取所有股票代码
            stocks_query = "SELECT DISTINCT stock_code FROM stock_prices ORDER BY stock_code"
            stock_codes = pd.read_sql_query(stocks_query, conn)['stock_code'].tolist()
            
            print(f"📊 从数据库加载股票数据 (最多{len(stock_codes)}只)...")
            
            # 加载所有股票的价格数据
            all_data = {}
            valid_stocks = []
            
            for stock_code in stock_codes:
                query = """
                SELECT date, close
                FROM stock_prices
                WHERE stock_code = ?
                ORDER BY date
                """
                df = pd.read_sql_query(query, conn, params=(stock_code,))

                if len(df) >= 500:  # 至少需要500个交易日的数据
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    all_data[stock_code] = df['close']
                    valid_stocks.append(stock_code)
            
            conn.close()
            
            if not all_data:
                print("❌ 没有找到有效的股票数据")
                return False
            
            # 转换为DataFrame
            self.stock_data = pd.DataFrame(all_data)
            self.stock_data = self.stock_data.dropna(how='all')
            
            print(f"   选择 {len(valid_stocks)} 只数据完整的股票")
            print(f"✅ 成功加载 {len(valid_stocks)} 只股票的价格数据")
            print(f"📅 数据时间范围: {self.stock_data.index.min().strftime('%Y-%m-%d')} 到 {self.stock_data.index.max().strftime('%Y-%m-%d')}")
            
            return True
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def calculate_momentum_factor(self, end_date):
        """计算指定日期的动量因子"""
        start_date = end_date - timedelta(days=self.lookback_days + 90)  # 增加缓冲天数

        # 获取时间窗口内的数据
        mask = (self.stock_data.index >= start_date) & (self.stock_data.index <= end_date)
        window_data = self.stock_data[mask]

        print(f"     时间窗口: {start_date.strftime('%Y-%m-%d')} 到 {end_date.strftime('%Y-%m-%d')}")
        print(f"     窗口内交易日数: {len(window_data)}")

        if len(window_data) < 50:  # 降低最小要求
            print(f"     窗口内交易日不足，返回空结果")
            return pd.Series(dtype=float)

        # 计算动量因子（过去lookback_days的累计收益率）
        momentum_scores = {}
        valid_stocks = 0

        for stock in window_data.columns:
            stock_prices = window_data[stock].dropna()

            if len(stock_prices) >= 50:  # 降低最小要求
                # 取最近的数据，但不一定要完整的lookback_days
                if len(stock_prices) >= 2:
                    # 计算累计收益率
                    momentum_score = (stock_prices.iloc[-1] / stock_prices.iloc[0]) - 1
                    momentum_scores[stock] = momentum_score
                    valid_stocks += 1

        print(f"     有效股票数: {valid_stocks}")
        return pd.Series(momentum_scores)
    
    def get_rebalance_dates(self):
        """获取调仓日期"""
        # 从2022年开始回测
        start_date = max(self.stock_data.index.min() + timedelta(days=self.lookback_days + 60),
                        pd.Timestamp('2022-01-01'))
        end_date = self.stock_data.index.max()
        
        rebalance_dates = []
        current_date = start_date
        
        while current_date <= end_date:
            # 找到最接近的交易日
            available_dates = self.stock_data.index[self.stock_data.index >= current_date]
            if len(available_dates) > 0:
                actual_date = available_dates[0]
                if actual_date <= end_date:
                    rebalance_dates.append(actual_date)
            
            # 计算下一个调仓日期
            if self.rebalance_freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_date = current_date.replace(month=6, day=30)
                elif current_date.month <= 6:
                    next_date = current_date.replace(month=9, day=30)
                elif current_date.month <= 9:
                    next_date = current_date.replace(month=12, day=31)
                else:
                    next_date = current_date.replace(year=current_date.year+1, month=3, day=31)
            
            current_date = next_date
        
        return rebalance_dates
    
    def run_group_backtest(self):
        """运行分组回测"""
        print(f"🎯 动量因子分组回测分析")
        print(f"📋 参数设置:")
        print(f"   回看天数: {self.lookback_days}天")
        print(f"   调仓频率: {self.rebalance_freq} (季度)")
        print(f"   分组数量: {self.n_groups}组")
        print("=" * 60)
        
        # 加载数据
        if not self.load_stock_data():
            return None
        
        # 获取调仓日期
        rebalance_dates = self.get_rebalance_dates()
        print(f"🗓️  调仓日期: {len(rebalance_dates)}个")
        
        # 存储每组的收益率
        group_returns = {f'Group_{i+1}': [] for i in range(self.n_groups)}
        group_holdings = {f'Group_{i+1}': [] for i in range(self.n_groups)}
        
        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance_date = rebalance_dates[i+1]
            
            print(f"\n🔄 第{i+1}期调仓: {rebalance_date.strftime('%Y-%m-%d')} -> {next_rebalance_date.strftime('%Y-%m-%d')}")
            
            # 计算动量因子
            momentum_scores = self.calculate_momentum_factor(rebalance_date)

            print(f"   计算得到 {len(momentum_scores)} 只股票的动量因子")

            if len(momentum_scores) < self.n_groups * 3:  # 至少需要足够的股票进行分组
                print(f"   ⚠️  股票数量不足({len(momentum_scores)}只)，需要至少{self.n_groups * 3}只，跳过此期")
                continue
            
            # 分组
            momentum_scores = momentum_scores.sort_values(ascending=False)
            group_size = len(momentum_scores) // self.n_groups
            
            groups = {}
            for j in range(self.n_groups):
                start_idx = j * group_size
                if j == self.n_groups - 1:  # 最后一组包含剩余所有股票
                    end_idx = len(momentum_scores)
                else:
                    end_idx = (j + 1) * group_size
                
                group_stocks = momentum_scores.iloc[start_idx:end_idx].index.tolist()
                groups[f'Group_{j+1}'] = group_stocks
                group_holdings[f'Group_{j+1}'].append(group_stocks)
            
            # 计算各组在持有期间的收益率
            period_mask = (self.stock_data.index > rebalance_date) & (self.stock_data.index <= next_rebalance_date)
            period_data = self.stock_data[period_mask]
            
            if len(period_data) < 2:
                print(f"   ⚠️  持有期数据不足，跳过此期")
                continue
            
            for group_name, stocks in groups.items():
                # 计算组合收益率（等权重）
                group_returns_period = []
                
                for stock in stocks:
                    if stock in period_data.columns:
                        stock_data_period = period_data[stock].dropna()
                        if len(stock_data_period) >= 2:
                            stock_return = (stock_data_period.iloc[-1] / stock_data_period.iloc[0]) - 1
                            group_returns_period.append(stock_return)
                
                if group_returns_period:
                    avg_return = np.mean(group_returns_period)
                    group_returns[group_name].append(avg_return)
                    print(f"   {group_name}: {len(stocks)}只股票, 收益率{avg_return*100:.2f}%")
                else:
                    group_returns[group_name].append(0)
                    print(f"   {group_name}: 无有效数据")
        
        return group_returns, group_holdings, rebalance_dates
    
    def analyze_results(self, group_returns, group_holdings, rebalance_dates):
        """分析回测结果"""
        print("\n" + "="*80)
        print("📊 分组回测结果分析")
        print("="*80)
        
        # 转换为DataFrame
        returns_df = pd.DataFrame(group_returns)
        
        # 计算累计收益
        cumulative_returns = (1 + returns_df).cumprod()
        
        # 计算绩效指标
        results_summary = {}
        
        for group in returns_df.columns:
            returns_series = returns_df[group]
            
            # 年化收益率 (季度调仓，一年4期)
            annual_return = returns_series.mean() * 4 * 100
            
            # 年化波动率
            annual_volatility = returns_series.std() * np.sqrt(4) * 100
            
            # 夏普比率
            sharpe_ratio = (returns_series.mean() / returns_series.std() * np.sqrt(4)) if returns_series.std() > 0 else 0
            
            # 胜率
            win_rate = (returns_series > 0).mean() * 100
            
            # 最大回撤
            cumulative = (1 + returns_series).cumprod()
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max
            max_drawdown = drawdown.min() * 100
            
            # 累计收益
            total_return = cumulative.iloc[-1] - 1 if len(cumulative) > 0 else 0
            
            results_summary[group] = {
                '年化收益率(%)': annual_return,
                '年化波动率(%)': annual_volatility,
                '夏普比率': sharpe_ratio,
                '胜率(%)': win_rate,
                '最大回撤(%)': max_drawdown,
                '累计收益率(%)': total_return * 100,
                '调仓次数': len(returns_series)
            }
        
        # 显示结果
        summary_df = pd.DataFrame(results_summary).T
        print(summary_df.round(2))
        
        # 分析动量效应
        print(f"\n📈 动量效应分析:")
        group1_return = summary_df.loc['Group_1', '年化收益率(%)']
        group5_return = summary_df.loc[f'Group_{self.n_groups}', '年化收益率(%)']
        momentum_premium = group1_return - group5_return
        
        print(f"   最强动量组(Group_1)年化收益: {group1_return:.2f}%")
        print(f"   最弱动量组(Group_{self.n_groups})年化收益: {group5_return:.2f}%")
        print(f"   动量溢价: {momentum_premium:.2f}%")
        
        if momentum_premium > 0:
            print(f"   ✅ 存在显著的动量效应")
        else:
            print(f"   ❌ 不存在动量效应")
        
        return returns_df, summary_df
    
    def plot_results(self, returns_df, summary_df, rebalance_dates):
        """绘制结果图表"""
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

        # 1. 累计收益曲线
        cumulative_returns = (1 + returns_df).cumprod()

        # 创建时间轴（使用调仓日期）
        time_axis = rebalance_dates[1:len(cumulative_returns)+1]  # 从第二个调仓日开始

        for group in cumulative_returns.columns:
            ax1.plot(time_axis, cumulative_returns[group],
                    label=group, linewidth=2, marker='o', markersize=3)

        ax1.set_title('各组累计收益曲线', fontsize=14, fontweight='bold')
        ax1.set_xlabel('时间')
        ax1.set_ylabel('累计收益倍数')
        ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        ax1.grid(True, alpha=0.3)

        # 设置时间轴格式
        import matplotlib.dates as mdates
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.YearLocator())
        plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
        
        # 2. 年化收益率对比
        annual_returns = summary_df['年化收益率(%)']
        colors = plt.cm.RdYlGn(np.linspace(0.2, 0.8, len(annual_returns)))
        bars = ax2.bar(annual_returns.index, annual_returns.values, color=colors)
        ax2.set_title('各组年化收益率对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('年化收益率 (%)')
        ax2.set_xticklabels(annual_returns.index, rotation=45)
        
        # 添加数值标注
        for bar, value in zip(bars, annual_returns.values):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                    f'{value:.1f}%', ha='center', va='bottom', fontweight='bold')
        
        # 3. 夏普比率对比
        sharpe_ratios = summary_df['夏普比率']
        bars = ax3.bar(sharpe_ratios.index, sharpe_ratios.values, color='skyblue')
        ax3.set_title('各组夏普比率对比', fontsize=14, fontweight='bold')
        ax3.set_ylabel('夏普比率')
        ax3.set_xticklabels(sharpe_ratios.index, rotation=45)
        
        # 添加数值标注
        for bar, value in zip(bars, sharpe_ratios.values):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.02,
                    f'{value:.3f}', ha='center', va='bottom', fontweight='bold')
        
        # 4. 风险收益散点图
        returns = summary_df['年化收益率(%)']
        volatilities = summary_df['年化波动率(%)']
        
        scatter = ax4.scatter(volatilities, returns, s=100, alpha=0.7, c=range(len(returns)), cmap='viridis')
        
        for i, group in enumerate(returns.index):
            ax4.annotate(group, (volatilities.iloc[i], returns.iloc[i]), 
                        xytext=(5, 5), textcoords='offset points', fontweight='bold')
        
        ax4.set_title('风险收益散点图', fontsize=14, fontweight='bold')
        ax4.set_xlabel('年化波动率 (%)')
        ax4.set_ylabel('年化收益率 (%)')
        ax4.grid(True, alpha=0.3)
        
        plt.tight_layout()
        
        # 保存图表
        filename = f'momentum_factor_group_backtest_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 分组回测图表已保存到: {filename}")
        plt.show()
        
        return filename

def main():
    """主函数"""
    start_time = datetime.now()
    
    try:
        # 使用最佳参数组合
        backtest = MomentumFactorGroupBacktest(
            lookback_days=160,
            rebalance_freq='Q',
            n_groups=10
        )
        
        # 运行分组回测
        group_returns, group_holdings, rebalance_dates = backtest.run_group_backtest()
        
        if group_returns:
            # 分析结果
            returns_df, summary_df = backtest.analyze_results(group_returns, group_holdings, rebalance_dates)
            
            # 绘制图表
            chart_filename = backtest.plot_results(returns_df, summary_df, rebalance_dates)
            
            # 保存详细结果
            results_filename = f'momentum_factor_group_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
            summary_df.to_csv(results_filename)
            print(f"📁 详细结果已保存到: {results_filename}")
            
            end_time = datetime.now()
            duration = end_time - start_time
            
            print(f"\n🎉 分组回测完成！")
            print(f"⏱️  总耗时: {duration}")
            
        else:
            print("❌ 分组回测失败")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断回测")
    except Exception as e:
        print(f"\n❌ 回测过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
