#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HP滤波参数λ的选择和优化分析
测试不同λ值对债券投资策略效果的影响
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.filters.hp_filter import hpfilter
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def analyze_hp_parameters():
    """分析不同HP滤波参数的效果"""
    
    # 加载数据
    data = pd.read_csv('data/china_bond_composite_index_5years.csv')
    data['日期'] = pd.to_datetime(data['日期'])
    data.set_index('日期', inplace=True)
    price = data['中债综合全价指数']
    
    print("🔍 HP滤波参数λ选择和优化分析")
    print("="*80)
    
    # 测试不同的λ值
    lambda_values = [
        100000,      # 较小λ：趋势更贴近数据
        1000000,     # 
        7000000,     # 标准日度λ
        14000000,    # 2倍标准值
        28000000,    # 4倍标准值
        50000000     # 较大λ：趋势更平滑
    ]
    
    results = {}
    
    print(f"\n📊 测试不同λ值的效果:")
    
    for lambda_val in lambda_values:
        print(f"\n🔄 测试λ = {lambda_val:,}")
        
        try:
            # HP滤波
            cycle, trend = hpfilter(price.dropna(), lamb=lambda_val)
            
            # 计算统计指标
            trend_var_ratio = np.var(trend) / np.var(price.dropna())
            cycle_var_ratio = np.var(cycle) / np.var(price.dropna())
            
            # 创建买入信号（10%分位数）
            percentile = cycle.rolling(252).rank(pct=True)
            buy_signals = percentile <= 0.10
            
            # 简单回测（60天持有）
            buy_dates = buy_signals[buy_signals].index
            returns = []
            
            for buy_date in buy_dates:
                try:
                    buy_price = price.loc[buy_date]
                    sell_date_idx = price.index.get_loc(buy_date) + 60
                    if sell_date_idx < len(price):
                        sell_date = price.index[sell_date_idx]
                        sell_price = price.loc[sell_date]
                        period_return = (sell_price - buy_price) / buy_price
                        returns.append(period_return)
                except:
                    continue
            
            if returns:
                avg_return = np.mean(returns)
                win_rate = sum(1 for r in returns if r > 0) / len(returns)
                trade_count = len(returns)
                sharpe = avg_return / np.std(returns) if np.std(returns) > 0 else 0
            else:
                avg_return = win_rate = trade_count = sharpe = 0
            
            # 计算趋势平滑度（二阶差分的方差）
            trend_smoothness = np.var(np.diff(trend.values, n=2))
            
            results[lambda_val] = {
                'trend_var_ratio': trend_var_ratio,
                'cycle_var_ratio': cycle_var_ratio,
                'avg_return': avg_return,
                'win_rate': win_rate,
                'trade_count': trade_count,
                'sharpe': sharpe,
                'trend_smoothness': trend_smoothness,
                'signal_frequency': buy_signals.sum() / len(buy_signals) * 100
            }
            
            print(f"   趋势方差占比: {trend_var_ratio:.1%}")
            print(f"   周期方差占比: {cycle_var_ratio:.1%}")
            print(f"   信号频率: {results[lambda_val]['signal_frequency']:.1f}%")
            print(f"   平均收益: {avg_return*100:+.2f}%")
            print(f"   胜率: {win_rate*100:.1f}%")
            print(f"   夏普比率: {sharpe:.2f}")
            print(f"   交易次数: {trade_count}")
            
        except Exception as e:
            print(f"   ❌ λ={lambda_val:,} 计算失败: {e}")
    
    # 分析最优参数
    print(f"\n🏆 参数优化结果:")
    
    # 按不同指标排序
    metrics = ['avg_return', 'win_rate', 'sharpe']
    metric_names = ['平均收益', '胜率', '夏普比率']
    
    for metric, name in zip(metrics, metric_names):
        valid_results = {k: v for k, v in results.items() if v[metric] > 0}
        if valid_results:
            best_lambda = max(valid_results.keys(), key=lambda x: valid_results[x][metric])
            best_value = valid_results[best_lambda][metric]
            
            if metric == 'avg_return':
                print(f"   {name}最优: λ={best_lambda:,} (收益{best_value*100:+.2f}%)")
            elif metric == 'win_rate':
                print(f"   {name}最优: λ={best_lambda:,} (胜率{best_value*100:.1f}%)")
            else:
                print(f"   {name}最优: λ={best_lambda:,} (夏普{best_value:.2f})")
    
    # 综合评分
    print(f"\n📊 综合评分分析:")
    composite_scores = {}
    
    for lambda_val, result in results.items():
        if result['avg_return'] > 0:
            # 综合评分：收益40% + 胜率30% + 夏普30%
            score = (
                result['avg_return'] * 100 * 0.4 +
                result['win_rate'] * 100 * 0.3 +
                result['sharpe'] * 10 * 0.3  # 夏普比率放大10倍参与评分
            )
            composite_scores[lambda_val] = score
            print(f"   λ={lambda_val:,}: 综合评分{score:.2f}")
    
    if composite_scores:
        best_overall_lambda = max(composite_scores.keys(), key=lambda x: composite_scores[x])
        print(f"\n🎯 综合最优参数: λ={best_overall_lambda:,}")
    
    # 理论分析
    print(f"\n📚 理论分析:")
    print(f"   标准日度λ = 7,000,000 的理论依据:")
    print(f"   • 基于季度λ=1600的频率调整公式")
    print(f"   • λ = 1600 × (252/4)⁴ = 1600 × 63⁴ ≈ 7,000,000")
    print(f"   • 适用于大多数宏观经济时间序列")
    
    # 实际建议
    print(f"\n💡 实际应用建议:")
    
    # 检查7,000,000的表现
    if 7000000 in results:
        standard_result = results[7000000]
        print(f"   标准λ=7,000,000表现:")
        print(f"   • 平均收益: {standard_result['avg_return']*100:+.2f}%")
        print(f"   • 胜率: {standard_result['win_rate']*100:.1f}%")
        print(f"   • 夏普比率: {standard_result['sharpe']:.2f}")
        print(f"   • 信号频率: {standard_result['signal_frequency']:.1f}%")
    
    # 稳健性建议
    print(f"\n🛡️ 稳健性考虑:")
    print(f"   • 参数过度优化可能导致过拟合")
    print(f"   • 建议使用理论标准值λ=7,000,000")
    print(f"   • 除非有明确证据表明其他值显著更优")
    
    # 创建可视化
    create_lambda_comparison_chart(price, lambda_values, results)
    
    return results

def create_lambda_comparison_chart(price, lambda_values, results):
    """创建λ值对比图表"""
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('HP滤波参数λ对比分析', fontsize=16, fontweight='bold')
    
    # 选择几个代表性的λ值进行可视化
    selected_lambdas = [1000000, 7000000, 28000000]
    colors = ['blue', 'red', 'green']
    
    # 图1: 不同λ值的趋势分解
    ax1 = axes[0, 0]
    ax1.plot(price.index, price.values, linewidth=1, color='black', alpha=0.7, label='原始价格')
    
    for i, lambda_val in enumerate(selected_lambdas):
        try:
            cycle, trend = hpfilter(price.dropna(), lamb=lambda_val)
            ax1.plot(trend.index, trend.values, linewidth=1.5, 
                    color=colors[i], label=f'趋势(λ={lambda_val:,})')
        except:
            continue
    
    ax1.set_title('不同λ值的趋势提取效果')
    ax1.set_ylabel('指数值')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 图2: 周期成分对比
    ax2 = axes[0, 1]
    
    for i, lambda_val in enumerate(selected_lambdas):
        try:
            cycle, trend = hpfilter(price.dropna(), lamb=lambda_val)
            ax2.plot(cycle.index, cycle.values, linewidth=1, 
                    color=colors[i], alpha=0.8, label=f'周期(λ={lambda_val:,})')
        except:
            continue
    
    ax2.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax2.set_title('不同λ值的周期成分')
    ax2.set_ylabel('周期偏离')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 图3: 策略表现对比
    ax3 = axes[1, 0]
    
    lambdas = []
    returns = []
    win_rates = []
    sharpes = []
    
    for lambda_val in sorted(results.keys()):
        if results[lambda_val]['avg_return'] > 0:
            lambdas.append(lambda_val)
            returns.append(results[lambda_val]['avg_return'] * 100)
            win_rates.append(results[lambda_val]['win_rate'] * 100)
            sharpes.append(results[lambda_val]['sharpe'])
    
    x = range(len(lambdas))
    width = 0.25
    
    ax3.bar([i - width for i in x], returns, width, label='平均收益(%)', alpha=0.8)
    ax3.bar(x, win_rates, width, label='胜率(%)', alpha=0.8)
    ax3.bar([i + width for i in x], [s*10 for s in sharpes], width, label='夏普×10', alpha=0.8)
    
    ax3.set_title('不同λ值的策略表现')
    ax3.set_ylabel('指标值')
    ax3.set_xticks(x)
    ax3.set_xticklabels([f'{l:,}' for l in lambdas], rotation=45)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 图4: 方差分解
    ax4 = axes[1, 1]
    
    trend_ratios = []
    cycle_ratios = []
    
    for lambda_val in sorted(results.keys()):
        trend_ratios.append(results[lambda_val]['trend_var_ratio'] * 100)
        cycle_ratios.append(results[lambda_val]['cycle_var_ratio'] * 100)
    
    ax4.plot(range(len(lambdas)), trend_ratios, 'o-', label='趋势方差占比(%)', linewidth=2)
    ax4.plot(range(len(lambdas)), cycle_ratios, 's-', label='周期方差占比(%)', linewidth=2)
    
    ax4.set_title('方差分解随λ变化')
    ax4.set_ylabel('方差占比(%)')
    ax4.set_xticks(range(len(lambdas)))
    ax4.set_xticklabels([f'{l:,}' for l in lambdas], rotation=45)
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # 保存图表
    chart_path = 'data/hp_filter_lambda_optimization.png'
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"\n✅ λ参数优化图表已保存: {chart_path}")
    
    plt.show()

if __name__ == "__main__":
    results = analyze_hp_parameters()
