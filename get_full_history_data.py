"""
获取中证2000和恒生科技指数完整历史数据

This script retrieves complete historical data for CSI 2000 and Hang Seng Tech Index
from their launch dates to present.
"""

from get_csi2000_data import get_csi2000_data, get_hstech_data, IndexDataFetcher
import pandas as pd
from datetime import datetime
import os

def get_full_history_data():
    """
    获取两个指数的完整历史数据
    
    Returns:
        dict: 包含两个指数数据的字典
    """
    print("🚀 获取中证2000和恒生科技指数完整历史数据")
    print("="*60)
    
    results = {}
    
    # 指数配置信息
    indices_info = {
        'CSI2000': {
            'name': '中证2000指数',
            'launch_date': '2023-08-11',
            'description': '反映A股市场中小盘股票的整体表现'
        },
        'HSTECH': {
            'name': '恒生科技指数',
            'launch_date': '2020-07-27',
            'description': '反映香港上市最大型科技公司股价表现'
        }
    }
    
    for index_type, info in indices_info.items():
        print(f"\n{'='*20} {index_type} {'='*20}")
        print(f"📊 指数名称: {info['name']}")
        print(f"🕐 上市日期: {info['launch_date']}")
        print(f"📝 描述: {info['description']}")
        
        # 计算数据期间
        launch_date = pd.to_datetime(info['launch_date'])
        current_date = pd.to_datetime(datetime.now().strftime('%Y-%m-%d'))
        days_since_launch = (current_date - launch_date).days
        
        print(f"📅 上市至今: {days_since_launch} 天")
        print()
        
        try:
            # 获取完整历史数据
            print(f"🌐 开始获取{info['name']}完整历史数据...")
            
            if index_type == 'CSI2000':
                data = get_csi2000_data(full_history=True, force_refresh=True)
            else:  # HSTECH
                data = get_hstech_data(full_history=True, force_refresh=True)
            
            if data is not None and not data.empty:
                print(f"✅ {info['name']}数据获取成功！")
                
                # 数据统计
                print(f"📊 数据点数: {len(data):,}")
                print(f"📅 实际数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                print(f"💰 价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
                
                # 计算总收益率
                total_return = (data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100
                print(f"📈 总收益率: {total_return:.2f}%")
                
                # 计算年化收益率
                years = len(data) / 252  # 假设每年252个交易日
                if years > 0:
                    annualized_return = ((data['close'].iloc[-1] / data['close'].iloc[0]) ** (1/years) - 1) * 100
                    print(f"📊 年化收益率: {annualized_return:.2f}%")
                
                # 计算波动率
                daily_returns = data['close'].pct_change().dropna()
                volatility = daily_returns.std() * (252**0.5) * 100  # 年化波动率
                print(f"📉 年化波动率: {volatility:.2f}%")
                
                # 保存数据
                output_file = f"{index_type.lower()}_full_history_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                data.to_csv(output_file)
                print(f"💾 完整历史数据已保存到: {output_file}")
                
                results[index_type] = {
                    'data': data,
                    'file': output_file,
                    'stats': {
                        'total_return': total_return,
                        'annualized_return': annualized_return if years > 0 else 0,
                        'volatility': volatility,
                        'data_points': len(data),
                        'start_date': data.index.min().strftime('%Y-%m-%d'),
                        'end_date': data.index.max().strftime('%Y-%m-%d')
                    }
                }
                
            else:
                print(f"❌ {info['name']}数据获取失败")
                results[index_type] = None
                
        except Exception as e:
            print(f"❌ 获取{info['name']}数据时发生错误: {e}")
            results[index_type] = None
    
    return results

def compare_indices(results):
    """
    比较两个指数的表现
    
    Args:
        results: 包含指数数据的字典
    """
    print(f"\n{'='*60}")
    print("📊 指数对比分析")
    print("="*60)
    
    if results['CSI2000'] and results['HSTECH']:
        csi_stats = results['CSI2000']['stats']
        hs_stats = results['HSTECH']['stats']
        
        print("📈 收益率对比:")
        print(f"   中证2000总收益率: {csi_stats['total_return']:.2f}%")
        print(f"   恒生科技总收益率: {hs_stats['total_return']:.2f}%")
        print(f"   中证2000年化收益率: {csi_stats['annualized_return']:.2f}%")
        print(f"   恒生科技年化收益率: {hs_stats['annualized_return']:.2f}%")
        
        print("\n📉 风险对比:")
        print(f"   中证2000年化波动率: {csi_stats['volatility']:.2f}%")
        print(f"   恒生科技年化波动率: {hs_stats['volatility']:.2f}%")
        
        print("\n📊 数据量对比:")
        print(f"   中证2000数据点数: {csi_stats['data_points']:,}")
        print(f"   恒生科技数据点数: {hs_stats['data_points']:,}")
        print(f"   中证2000数据期间: {csi_stats['start_date']} 至 {csi_stats['end_date']}")
        print(f"   恒生科技数据期间: {hs_stats['start_date']} 至 {hs_stats['end_date']}")
        
        # 风险调整收益率 (夏普比率的简化版本，假设无风险利率为0)
        csi_sharpe = csi_stats['annualized_return'] / csi_stats['volatility'] if csi_stats['volatility'] > 0 else 0
        hs_sharpe = hs_stats['annualized_return'] / hs_stats['volatility'] if hs_stats['volatility'] > 0 else 0
        
        print("\n⚖️ 风险调整收益率:")
        print(f"   中证2000: {csi_sharpe:.3f}")
        print(f"   恒生科技: {hs_sharpe:.3f}")
        
        if csi_sharpe > hs_sharpe:
            print(f"   💡 中证2000的风险调整收益率更优")
        elif hs_sharpe > csi_sharpe:
            print(f"   💡 恒生科技的风险调整收益率更优")
        else:
            print(f"   💡 两指数风险调整收益率相当")
    
    else:
        print("❌ 无法进行对比分析，部分数据获取失败")

def save_summary_report(results):
    """
    保存汇总报告
    
    Args:
        results: 包含指数数据的字典
    """
    report_file = f"index_full_history_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("中证2000和恒生科技指数完整历史数据分析报告\n")
        f.write("="*60 + "\n")
        f.write(f"报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        for index_type, result in results.items():
            if result:
                stats = result['stats']
                f.write(f"{index_type} 指数分析:\n")
                f.write(f"  数据文件: {result['file']}\n")
                f.write(f"  数据期间: {stats['start_date']} 至 {stats['end_date']}\n")
                f.write(f"  数据点数: {stats['data_points']:,}\n")
                f.write(f"  总收益率: {stats['total_return']:.2f}%\n")
                f.write(f"  年化收益率: {stats['annualized_return']:.2f}%\n")
                f.write(f"  年化波动率: {stats['volatility']:.2f}%\n")
                f.write(f"  风险调整收益率: {stats['annualized_return']/stats['volatility']:.3f}\n\n")
            else:
                f.write(f"{index_type} 指数: 数据获取失败\n\n")
    
    print(f"📄 汇总报告已保存到: {report_file}")

def main():
    """
    主函数
    """
    # 获取完整历史数据
    results = get_full_history_data()
    
    # 比较分析
    compare_indices(results)
    
    # 保存汇总报告
    save_summary_report(results)
    
    print(f"\n🎯 完整历史数据获取完成！")
    
    successful_count = sum(1 for result in results.values() if result is not None)
    print(f"✅ 成功获取 {successful_count}/2 个指数的完整历史数据")
    
    if successful_count > 0:
        print("\n💡 后续分析建议:")
        print("   - 可以使用这些完整历史数据进行长期趋势分析")
        print("   - 进行技术指标计算和回测分析")
        print("   - 研究两个指数的相关性和协整关系")
        print("   - 分析不同市场周期下的表现差异")

if __name__ == "__main__":
    main()
