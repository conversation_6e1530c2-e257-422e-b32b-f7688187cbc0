#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标普500数据查询工具
用于查询和分析已下载的标普500成分股数据
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# 设置字体
plt.rcParams['font.sans-serif'] = ['Arial', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

DATABASE_NAME = 'sp500_10year_data.db'

class SP500DataQuery:
    def __init__(self, db_name=DATABASE_NAME):
        self.db_name = db_name
    
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_name)
    
    def get_stock_list(self):
        """获取所有股票列表"""
        query = """
        SELECT stock_code, stock_name, industry,
               (SELECT COUNT(*) FROM stock_prices WHERE stock_code = si.stock_code) as data_count,
               (SELECT MIN(date) FROM stock_prices WHERE stock_code = si.stock_code) as start_date,
               (SELECT MAX(date) FROM stock_prices WHERE stock_code = si.stock_code) as end_date
        FROM stock_info si
        ORDER BY stock_code
        """
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn)
        
        return df
    
    def get_stock_data(self, stock_code, start_date=None, end_date=None):
        """获取指定股票的价格数据"""
        query = """
        SELECT sp.*, si.stock_name, si.industry
        FROM stock_prices sp
        JOIN stock_info si ON sp.stock_code = si.stock_code
        WHERE sp.stock_code = ?
        """
        params = [stock_code]
        
        if start_date:
            query += " AND sp.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND sp.date <= ?"
            params.append(end_date)
        
        query += " ORDER BY sp.date"
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=params)
        
        if not df.empty:
            df['date'] = pd.to_datetime(df['date'])
        
        return df
    
    def get_industry_summary(self):
        """获取行业汇总信息"""
        query = """
        SELECT si.industry,
               COUNT(DISTINCT si.stock_code) as stock_count,
               AVG(data_counts.data_count) as avg_data_count
        FROM stock_info si
        LEFT JOIN (
            SELECT stock_code, COUNT(*) as data_count
            FROM stock_prices
            GROUP BY stock_code
        ) data_counts ON si.stock_code = data_counts.stock_code
        GROUP BY si.industry
        ORDER BY stock_count DESC
        """
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn)
        
        return df
    
    def get_data_summary(self):
        """获取数据库整体统计信息"""
        with self.get_connection() as conn:
            # 股票总数
            stock_count = pd.read_sql_query("SELECT COUNT(*) as count FROM stock_info", conn).iloc[0]['count']
            
            # 价格记录总数
            price_count = pd.read_sql_query("SELECT COUNT(*) as count FROM stock_prices", conn).iloc[0]['count']
            
            # 日期范围
            date_range = pd.read_sql_query("""
                SELECT MIN(date) as min_date, MAX(date) as max_date 
                FROM stock_prices
            """, conn).iloc[0]
            
            # 有数据的股票数量
            stocks_with_data = pd.read_sql_query("""
                SELECT COUNT(DISTINCT stock_code) as count FROM stock_prices
            """, conn).iloc[0]['count']
        
        return {
            'total_stocks': stock_count,
            'stocks_with_data': stocks_with_data,
            'total_price_records': price_count,
            'date_range': (date_range['min_date'], date_range['max_date']),
            'avg_records_per_stock': price_count / stocks_with_data if stocks_with_data > 0 else 0
        }
    
    def search_stocks(self, keyword):
        """搜索股票（按代码或名称）"""
        query = """
        SELECT stock_code, stock_name, industry,
               (SELECT COUNT(*) FROM stock_prices WHERE stock_code = si.stock_code) as data_count
        FROM stock_info si
        WHERE stock_code LIKE ? OR stock_name LIKE ?
        ORDER BY stock_code
        """
        
        search_pattern = f"%{keyword}%"
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=[search_pattern, search_pattern])
        
        return df
    
    def get_top_stocks_by_volume(self, top_n=20, start_date=None, end_date=None):
        """获取成交量最大的股票"""
        query = """
        SELECT sp.stock_code, si.stock_name, si.industry,
               AVG(sp.volume) as avg_volume,
               SUM(sp.volume) as total_volume,
               COUNT(*) as trading_days
        FROM stock_prices sp
        JOIN stock_info si ON sp.stock_code = si.stock_code
        WHERE sp.volume IS NOT NULL
        """
        params = []
        
        if start_date:
            query += " AND sp.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND sp.date <= ?"
            params.append(end_date)
        
        query += """
        GROUP BY sp.stock_code, si.stock_name, si.industry
        ORDER BY avg_volume DESC
        LIMIT ?
        """
        params.append(top_n)
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=params)
        
        return df
    
    def plot_stock_price(self, stock_code, start_date=None, end_date=None):
        """绘制股票价格走势图"""
        df = self.get_stock_data(stock_code, start_date, end_date)
        
        if df.empty:
            print(f"No data found for stock {stock_code}")
            return
        
        stock_name = df.iloc[0]['stock_name']
        
        plt.figure(figsize=(12, 8))
        
        # 绘制价格走势
        plt.subplot(2, 1, 1)
        plt.plot(df['date'], df['close'], label='Close Price', linewidth=1)
        plt.plot(df['date'], df['adj_close'], label='Adj Close Price', linewidth=1, alpha=0.7)
        plt.plot(df['date'], df['high'], alpha=0.3, label='High', linewidth=0.5)
        plt.plot(df['date'], df['low'], alpha=0.3, label='Low', linewidth=0.5)
        plt.title(f'{stock_code} ({stock_name}) Price Chart')
        plt.ylabel('Price ($)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 绘制成交量
        plt.subplot(2, 1, 2)
        plt.bar(df['date'], df['volume'], alpha=0.7, width=1)
        plt.title('Volume')
        plt.ylabel('Volume')
        plt.xlabel('Date')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def export_stock_data(self, stock_code, filename=None):
        """导出股票数据到CSV文件"""
        df = self.get_stock_data(stock_code)
        
        if df.empty:
            print(f"No data found for stock {stock_code}")
            return
        
        if filename is None:
            stock_name = df.iloc[0]['stock_name'].replace(' ', '_')
            filename = f"{stock_code}_{stock_name}_data.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"Data exported to: {filename}")

    def get_performance_summary(self, start_date=None, end_date=None):
        """获取股票表现汇总"""
        query = """
        SELECT sp.stock_code, si.stock_name, si.industry,
               MIN(sp.close) as min_price,
               MAX(sp.close) as max_price,
               (SELECT close FROM stock_prices WHERE stock_code = sp.stock_code ORDER BY date LIMIT 1) as first_price,
               (SELECT close FROM stock_prices WHERE stock_code = sp.stock_code ORDER BY date DESC LIMIT 1) as last_price
        FROM stock_prices sp
        JOIN stock_info si ON sp.stock_code = si.stock_code
        WHERE sp.close IS NOT NULL
        """
        params = []
        
        if start_date:
            query += " AND sp.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND sp.date <= ?"
            params.append(end_date)
        
        query += " GROUP BY sp.stock_code, si.stock_name, si.industry"
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=params)
        
        if not df.empty:
            df['total_return'] = ((df['last_price'] - df['first_price']) / df['first_price'] * 100).round(2)
            df['max_gain'] = ((df['max_price'] - df['first_price']) / df['first_price'] * 100).round(2)
            df['max_loss'] = ((df['min_price'] - df['first_price']) / df['first_price'] * 100).round(2)
        
        return df

def main():
    """主函数 - 提供交互式查询界面"""
    query_tool = SP500DataQuery()

    print("=" * 60)
    print("S&P 500 Data Query Tool")
    print("=" * 60)

    try:
        # 显示数据库概况
        summary = query_tool.get_data_summary()
        print(f"\nDatabase Summary:")
        print(f"  Total stocks: {summary['total_stocks']}")
        print(f"  Stocks with data: {summary['stocks_with_data']}")
        print(f"  Total price records: {summary['total_price_records']:,}")
        print(f"  Date range: {summary['date_range'][0]} to {summary['date_range'][1]}")
        print(f"  Average records per stock: {summary['avg_records_per_stock']:.0f}")

        while True:
            print("\n" + "=" * 60)
            print("Please select an option:")
            print("1. View all stock list")
            print("2. Search stocks")
            print("3. View stock detailed data")
            print("4. View industry summary")
            print("5. View volume ranking")
            print("6. Plot stock price chart")
            print("7. Export stock data")
            print("8. View performance summary")
            print("0. Exit")

            choice = input("\nPlease enter your choice (0-8): ").strip()

            if choice == '0':
                print("Goodbye!")
                break
            elif choice == '1':
                df = query_tool.get_stock_list()
                print(f"\nStock List (Total: {len(df)} stocks):")
                print(df.to_string(index=False))
            elif choice == '2':
                keyword = input("Enter search keyword (stock code or name): ").strip()
                if keyword:
                    df = query_tool.search_stocks(keyword)
                    if not df.empty:
                        print(f"\nSearch Results:")
                        print(df.to_string(index=False))
                    else:
                        print("No matching stocks found")
            elif choice == '3':
                stock_code = input("Enter stock code: ").strip().upper()
                if stock_code:
                    df = query_tool.get_stock_data(stock_code)
                    if not df.empty:
                        print(f"\n{stock_code} Data (Total: {len(df)} records):")
                        print(df.head(10).to_string(index=False))
                        if len(df) > 10:
                            print("... (showing first 10 records only)")
                    else:
                        print("No data found for this stock")
            elif choice == '4':
                df = query_tool.get_industry_summary()
                print(f"\nIndustry Summary:")
                print(df.to_string(index=False))
            elif choice == '5':
                try:
                    top_n = int(input("Enter number of stocks to display (default 20): ").strip() or "20")
                    df = query_tool.get_top_stocks_by_volume(top_n)
                    print(f"\nVolume Ranking (Top {top_n}):")
                    print(df.to_string(index=False))
                except ValueError:
                    print("Please enter a valid number")
            elif choice == '6':
                stock_code = input("Enter stock code: ").strip().upper()
                if stock_code:
                    query_tool.plot_stock_price(stock_code)
            elif choice == '7':
                stock_code = input("Enter stock code: ").strip().upper()
                if stock_code:
                    filename = input("Enter filename (leave empty for default): ").strip() or None
                    query_tool.export_stock_data(stock_code, filename)
            elif choice == '8':
                df = query_tool.get_performance_summary()
                if not df.empty:
                    print(f"\nPerformance Summary:")
                    # 按总回报排序
                    df_sorted = df.sort_values('total_return', ascending=False)
                    print(df_sorted[['stock_code', 'stock_name', 'total_return', 'max_gain', 'max_loss']].head(20).to_string(index=False))
                else:
                    print("No performance data available")
            else:
                print("Invalid choice, please try again")

    except KeyboardInterrupt:
        print("\n\nProgram interrupted by user")
    except Exception as e:
        print(f"\nError occurred: {e}")

if __name__ == "__main__":
    main()
