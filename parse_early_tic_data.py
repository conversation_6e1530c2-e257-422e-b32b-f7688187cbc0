#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
早期TIC数据解析器（2002-2023）
解析早期TIC数据中的股票净流入信息

作者: AI助手
日期: 2025年1月
"""

import os
import pandas as pd
import numpy as np
import zipfile
import re
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 数据目录
ARCHIVES_DIR = "data/tic_archives_complete"
OUTPUT_DIR = "data/tic_analysis"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def extract_period_from_filename(filename):
    """从文件名提取数据期间"""
    if "2002dec31" in filename:
        return "2002-10"

    match = re.search(r'ticrel_(\d{8})\.zip', filename)
    if match:
        date_code = match.group(1)
        year = int(date_code[:4])
        month = int(date_code[4:6])

        if month == 1:
            data_year = year - 1
            data_month = 12
        else:
            data_year = year
            data_month = month - 1

        return f"{data_year}-{data_month:02d}"

    return "unknown"

def parse_stksect_file(content, period):
    """
    解析stksect.txt文件中的股票净流入数据

    Args:
        content (str): 文件内容
        period (str): 数据期间

    Returns:
        dict: 包含总净流入、官方机构、其他外国投资者的数据
    """
    try:
        lines = content.split('\n')

        # 查找数据开始行
        data_start = -1
        for i, line in enumerate(lines):
            if 'MONTH' in line and 'PURCHASES' in line:
                data_start = i + 2  # 跳过标题行和分隔线
                break

        if data_start == -1:
            return None

        # 解析数据行
        for line in lines[data_start:]:
            if line.strip():
                # 使用正则表达式解析数据行
                # 格式: YYYY-MM  总净流入  官方机构  其他外国投资者  国际组织
                match = re.search(r'(\d{4}-\d{2})\s+(-?\d+(?:,\d{3})*)\s+(-?\d+(?:,\d{3})*)\s+(-?\d+(?:,\d{3})*)\s+(-?\d+(?:,\d{3})*)', line)

                if match:
                    file_period = match.group(1)
                    # 检查是否匹配当前期间
                    if file_period == period:
                        total_net = int(match.group(2).replace(',', ''))
                        official = int(match.group(3).replace(',', ''))
                        other = int(match.group(4).replace(',', ''))
                        intl_org = int(match.group(5).replace(',', ''))

                        return {
                            'period': period,
                            'total_net_purchases': total_net,
                            'foreign_official': official,
                            'other_foreigners': other,
                            'international_orgs': intl_org
                        }

        # 如果没有找到精确匹配，尝试找到最新的数据
        latest_data = None
        for line in lines[data_start:]:
            if line.strip():
                match = re.search(r'(\d{4}-\d{2})\s+(-?\d+(?:,\d{3})*)\s+(-?\d+(?:,\d{3})*)\s+(-?\d+(?:,\d{3})*)\s+(-?\d+(?:,\d{3})*)', line)
                if match:
                    file_period = match.group(1)
                    total_net = int(match.group(2).replace(',', ''))
                    official = int(match.group(3).replace(',', ''))
                    other = int(match.group(4).replace(',', ''))
                    intl_org = int(match.group(5).replace(',', ''))

                    latest_data = {
                        'period': file_period,
                        'total_net_purchases': total_net,
                        'foreign_official': official,
                        'other_foreigners': other,
                        'international_orgs': intl_org
                    }

        # 返回最新的数据（如果有的话）
        if latest_data:
            print(f"    使用最新数据: {latest_data['period']}")
            return latest_data

        return None

    except Exception as e:
        print(f"    解析stksect.txt时出错: {str(e)}")
        return None

def parse_s1_global_file(content, period):
    """
    解析S1_GLOBL.TIC文件中的股票数据

    Args:
        content (str): 文件内容
        period (str): 数据期间

    Returns:
        pd.DataFrame: 按国家的股票净流入数据
    """
    try:
        lines = content.split('\n')

        # 查找数据开始行
        data_start = -1
        for i, line in enumerate(lines):
            if 'Cntry' in line and 'Code' in line and 'Year-Mo' in line:
                data_start = i + 2  # 跳过标题行和分隔线
                break

        if data_start == -1:
            return pd.DataFrame()

        # 解析数据
        country_data = []
        for line in lines[data_start:]:
            if line.strip():
                # 分割数据行
                parts = line.split()
                if len(parts) >= 13:  # 确保有足够的列
                    try:
                        country_code = parts[0]
                        year_month = parts[1]

                        # 检查是否匹配当前期间
                        if year_month == period:
                            # 股票相关列：购买和销售
                            us_stocks_purchases = float(parts[5]) if parts[5] != '0' else 0
                            us_stocks_sales = float(parts[11]) if parts[11] != '0' else 0

                            # 计算净流入
                            net_flow = us_stocks_purchases - us_stocks_sales

                            if abs(net_flow) > 0:  # 只保留有交易的记录
                                country_data.append({
                                    'country_code': country_code,
                                    'period': period,
                                    'us_stocks_purchases': us_stocks_purchases,
                                    'us_stocks_sales': us_stocks_sales,
                                    'net_flow': net_flow
                                })
                    except (ValueError, IndexError):
                        continue

        if country_data:
            df = pd.DataFrame(country_data)
            print(f"    从S1_GLOBL.TIC解析到 {len(df)} 条国家级股票数据")
            return df
        else:
            return pd.DataFrame()

    except Exception as e:
        print(f"    解析S1_GLOBL.TIC时出错: {str(e)}")
        return pd.DataFrame()

def extract_and_analyze_early_zip_file(zip_path):
    """
    解压并分析早期格式的ZIP文件

    Args:
        zip_path (str): ZIP文件路径

    Returns:
        tuple: (汇总数据, 国家级数据)
    """
    filename = os.path.basename(zip_path)
    period = extract_period_from_filename(filename)

    try:
        with zipfile.ZipFile(zip_path, 'r') as zip_file:
            summary_data = None
            country_data = pd.DataFrame()

            # 查找stksect.txt文件（股票部门汇总数据）
            stksect_files = [f for f in zip_file.namelist()
                           if 'stksect.txt' in f.lower()]

            if stksect_files:
                with zip_file.open(stksect_files[0]) as f:
                    content = f.read().decode('utf-8', errors='ignore')
                    summary_data = parse_stksect_file(content, period)

            # 查找S1_GLOBL.TIC文件（国家级详细数据）
            s1_files = [f for f in zip_file.namelist()
                       if 'S1_GLOBL.TIC' in f or 's1_globl.tic' in f.lower()]

            if s1_files:
                with zip_file.open(s1_files[0]) as f:
                    content = f.read().decode('utf-8', errors='ignore')
                    country_data = parse_s1_global_file(content, period)

            if summary_data or not country_data.empty:
                print(f"  ✅ {period} - 成功提取早期数据")
                if summary_data:
                    print(f"    总净流入: {summary_data['total_net_purchases']} 百万美元")
                return summary_data, country_data
            else:
                print(f"  ⚠️  {period} - 未找到可解析的股票数据")
                return None, pd.DataFrame()

    except Exception as e:
        print(f"  ❌ {period} - 处理失败: {str(e)}")
        return None, pd.DataFrame()

def analyze_early_tic_data(start_year=2002, end_year=2023):
    """
    分析早期TIC数据（2002-2023）

    Args:
        start_year (int): 开始年份
        end_year (int): 结束年份

    Returns:
        tuple: (汇总数据, 国家级数据)
    """
    print("早期TIC数据分析器（2002-2023）")
    print("="*60)

    # 获取所有ZIP文件
    zip_files = []
    for filename in os.listdir(ARCHIVES_DIR):
        if filename.endswith('.zip'):
            zip_path = os.path.join(ARCHIVES_DIR, filename)
            # 检查文件大小
            if os.path.getsize(zip_path) > 100000:  # 大于100KB
                period = extract_period_from_filename(filename)
                if period != "unknown":
                    year = int(period.split('-')[0])
                    if start_year <= year <= end_year:
                        zip_files.append(zip_path)

    # 按文件名排序
    zip_files.sort()

    print(f"找到 {len(zip_files)} 个早期数据文件")
    print()

    all_summary_data = []
    all_country_data = []
    successful = 0
    failed = 0

    for i, zip_path in enumerate(zip_files):
        filename = os.path.basename(zip_path)
        period = extract_period_from_filename(filename)

        print(f"进度: {i+1}/{len(zip_files)} - 处理 {period}")

        summary_data, country_data = extract_and_analyze_early_zip_file(zip_path)

        if summary_data or not country_data.empty:
            if summary_data:
                all_summary_data.append(summary_data)
            if not country_data.empty:
                all_country_data.append(country_data)
            successful += 1
        else:
            failed += 1

        # 每50个文件显示一次进度
        if (i + 1) % 50 == 0:
            print(f"    已处理 {i+1} 个文件，成功 {successful} 个")

    print()
    print("="*60)
    print(f"早期数据处理完成: 成功 {successful} 个，失败 {failed} 个")

    # 合并数据
    if all_summary_data:
        summary_df = pd.DataFrame(all_summary_data)
        summary_df = summary_df.sort_values('period').reset_index(drop=True)
        print(f"汇总数据: {len(summary_df)} 个月")
    else:
        summary_df = pd.DataFrame()

    if all_country_data:
        country_df = pd.concat(all_country_data, ignore_index=True)
        country_df = country_df.sort_values(['period', 'country_code']).reset_index(drop=True)
        print(f"国家级数据: {len(country_df)} 条记录")
    else:
        country_df = pd.DataFrame()

    # 保存数据
    if not summary_df.empty:
        summary_file = os.path.join(OUTPUT_DIR, 'tic_early_summary_data.csv')
        summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        print(f"早期汇总数据已保存到: {summary_file}")

    if not country_df.empty:
        country_file = os.path.join(OUTPUT_DIR, 'tic_early_country_data.csv')
        country_df.to_csv(country_file, index=False, encoding='utf-8-sig')
        print(f"早期国家级数据已保存到: {country_file}")

    return summary_df, country_df

def main():
    """主函数"""
    # 分析早期数据（2002-2023）
    summary_data, country_data = analyze_early_tic_data(2002, 2023)

    if not summary_data.empty:
        print(f"\n早期数据统计:")
        print(f"时间范围: {summary_data['period'].min()} - {summary_data['period'].max()}")
        print(f"总月数: {len(summary_data)}")
        print(f"平均月度净流入: {summary_data['total_net_purchases'].mean():.0f} 百万美元")
        print(f"累计净流入: {summary_data['total_net_purchases'].sum():.0f} 百万美元")

if __name__ == "__main__":
    main()
