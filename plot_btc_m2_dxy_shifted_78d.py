import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime
import os
import numpy as np

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 设置代理
def setup_proxy():
    """设置代理，使用config目录下的配置"""
    try:
        # 从config目录导入代理设置
        from config.btc_data_config import PROXY_SETTINGS

        # 设置环境变量
        for protocol, proxy in PROXY_SETTINGS.items():
            os.environ[f"{protocol.upper()}_PROXY"] = proxy

        print(f"Using proxy settings from config: {PROXY_SETTINGS}")
    except Exception as e:
        print(f"Error setting up proxy: {e}")
        # 使用默认代理
        os.environ['HTTP_PROXY'] = 'socks5h://127.0.0.1:13659'
        os.environ['HTTPS_PROXY'] = 'socks5h://127.0.0.1:13659'
        print("Using default proxy settings")

# 读取M2SL数据
def load_m2sl_data():
    try:
        # 尝试从本地文件读取
        m2sl_df = pd.read_csv('M2SL.csv')

        # 检查列名
        print(f"M2SL.csv columns: {m2sl_df.columns.tolist()}")

        # 找出日期列
        date_cols = [col for col in m2sl_df.columns if 'date' in col.lower()]
        if date_cols:
            date_col = date_cols[0]
            print(f"Using {date_col} as date column")
            m2sl_df[date_col] = pd.to_datetime(m2sl_df[date_col])
            m2sl_df.set_index(date_col, inplace=True)
        else:
            # 如果找不到日期列，假设第一列是日期
            date_col = m2sl_df.columns[0]
            print(f"No date column found, using first column {date_col} as date")
            m2sl_df[date_col] = pd.to_datetime(m2sl_df[date_col])
            m2sl_df.set_index(date_col, inplace=True)

        print("Loaded M2SL data from local file")
    except (FileNotFoundError, KeyError) as e:
        # 如果本地文件不存在或读取出错，从FRED API获取
        print(f"Error loading M2SL.csv: {e}, downloading from FRED API...")
        import pandas_datareader.data as web
        m2sl_df = web.DataReader('M2SL', 'fred', '2010-01-01')
        # 保存到本地文件
        m2sl_df.to_csv('M2SL.csv')

    # 创建日期范围到最新的一天
    today = pd.Timestamp.today()
    full_date_range = pd.date_range(start=m2sl_df.index.min(), end=today, freq='D')

    # 重新索引并进行线性插值
    m2sl_df = m2sl_df.reindex(full_date_range)
    m2sl_df = m2sl_df.interpolate(method='linear')

    return m2sl_df

# 获取DXY数据
def get_dxy_data(start_date):
    print(f"Getting DXY data from {start_date}...")
    dxy = yf.download('DX-Y.NYB', start=start_date)
    print(f"Successfully retrieved {len(dxy)} records of DXY data")
    return dxy['Close']

# 获取比特币数据
def get_btc_data(start_date):
    print(f"Getting Bitcoin data from {start_date}...")
    btc = yf.download('BTC-USD', start=start_date)
    print(f"Successfully retrieved {len(btc)} records of Bitcoin data")
    return btc['Close']

# 主函数
def main():
    # 设置代理
    setup_proxy()

    # 加载M2SL数据
    print("Loading M2SL data...")
    m2sl_data = load_m2sl_data()
    print(f"M2SL data range: {m2sl_data.index.min()} to {m2sl_data.index.max()}")

    # 获取DXY数据和比特币数据
    start_date = m2sl_data.index.min()
    dxy_data = get_dxy_data(start_date)
    btc_data = get_btc_data(start_date)

    # 创建日期范围内的数据框
    df = pd.DataFrame(index=pd.date_range(start=start_date, end=m2sl_data.index.max(), freq='D'))
    df['M2SL'] = m2sl_data['M2SL']
    df['DXY'] = dxy_data
    df['BTC'] = btc_data

    # 对齐数据并删除缺失值
    df = df.dropna()

    # 计算M2/DXY比率
    df['M2_DXY_Ratio'] = df['M2SL'] / df['DXY']

    # 删除包含NaN的行
    df = df.dropna()

    print(f"Final data range: {df.index.min()} to {df.index.max()}")
    print(f"Number of data points: {len(df)}")

    # 获取最近1年的数据
    one_year_ago = df.index.max() - pd.DateOffset(years=1)
    recent_df = df[df.index >= one_year_ago]

    print(f"Recent 1 year data range: {recent_df.index.min()} to {recent_df.index.max()}")
    print(f"Number of recent data points: {len(recent_df)}")

    # 提取最近1年的 M2/DXY 比率和比特币数据
    recent_m2_dxy = recent_df['M2_DXY_Ratio']
    recent_btc = recent_df['BTC']

    # 平移78天
    shifted_m2_dxy = recent_m2_dxy.copy()

    # 日期加78天
    shifted_index = recent_df.index + pd.DateOffset(days=78)

    # 计算数据的相关系数
    correlation = recent_m2_dxy.corr(recent_btc)
    print(f"Correlation between M2/DXY ratio and BTC price (last 1 year): {correlation:.4f}")

    # 创建图表
    fig, ax1 = plt.subplots(figsize=(15, 8))

    # 绘制平移后的M2/DXY比率（最近1年）
    ax1.plot(shifted_index, recent_m2_dxy, color='blue', linewidth=2, label='M2/DXY Ratio (Shifted 78 days)')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('M2/DXY Ratio', color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')

    # 创建第二个Y轴
    ax2 = ax1.twinx()
    ax2.plot(recent_df.index, recent_btc, color='orange', linewidth=2, label='BTC-USD')
    ax2.set_ylabel('BTC Price (USD)', color='orange')
    ax2.tick_params(axis='y', labelcolor='orange')

    # 添加标题
    plt.title('M2/DXY Ratio (Shifted 78 days) vs BTC-USD (Last 1 Year)')

    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.grid(True)
    plt.tight_layout()

    # 保存图表
    plt.savefig('m2_dxy_ratio_shifted_78d.png', dpi=300)
    plt.show()

    # 计算平移后的相关系数
    # 创建一个新的DataFrame，包含平移后的M2/DXY比率和比特币价格
    # 注意：由于平移，我们需要调整日期范围
    extended_range = pd.date_range(start=recent_df.index.min(), end=recent_df.index.max() + pd.DateOffset(days=78), freq='D')
    extended_df = pd.DataFrame(index=extended_range)

    # 将原始数据复制到扩展的DataFrame中
    extended_df.loc[recent_df.index, 'BTC'] = recent_btc.values

    # 将平移后的M2/DXY比率添加到扩展的DataFrame中
    for i, date in enumerate(recent_df.index):
        shifted_date = date + pd.DateOffset(days=78)
        if shifted_date in extended_df.index:
            extended_df.loc[shifted_date, 'M2_DXY_Ratio_Shifted'] = recent_m2_dxy.iloc[i]

    # 删除缺失值
    aligned_df = extended_df.dropna()

    # 计算平移后的相关系数
    if not aligned_df.empty:
        shifted_correlation = aligned_df['BTC'].corr(aligned_df['M2_DXY_Ratio_Shifted'])
        print(f"Correlation between BTC price and M2/DXY ratio shifted by 78 days: {shifted_correlation:.4f}")
    else:
        print("Not enough data to calculate correlation after shifting")

if __name__ == "__main__":
    main()
