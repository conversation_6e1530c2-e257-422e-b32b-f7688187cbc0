#!/usr/bin/env python3
"""
恒生指数成分股股息率排名器 - 基于最新价格
使用最新股价计算股息率并对恒生指数成分股进行排名
"""

import os
import json
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
from hsi_daily_dividend_yield_calculator import HSIDailyDividendYieldCalculator
import time
from collections import Counter

class HSIDividendYieldRanking:
    """
    恒生指数成分股股息率排名器
    基于最新股价计算股息率并进行排名
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841", 
                 cache_dir: str = "cache", 
                 output_dir: str = "hsi_dividend_yield_rankings"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.output_dir = output_dir
        self.calculator = HSIDailyDividendYieldCalculator(dividend_data_dir, cache_dir)
        self.ranking_results = []
        self.failed_stocks = []
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        
    def get_stock_list(self):
        """获取所有股票列表"""
        print("📁 获取股票列表...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        stock_list = []
        
        for file in dividend_files:
            stock_code = file.split('_')[0]
            stock_name = file.split('_')[1].replace('.json', '')
            stock_list.append({
                'code': stock_code,
                'name': stock_name,
                'file': file
            })
        
        # 按股票代码排序
        stock_list.sort(key=lambda x: x['code'])
        
        print(f"✅ 找到 {len(stock_list)} 只股票")
        return stock_list
    
    def get_latest_stock_price(self, stock_code: str) -> float:
        """获取股票的最新价格"""
        try:
            print(f"🌐 获取 {stock_code} 最新价格...")
            
            # 使用akshare获取最新价格数据
            df = ak.stock_hk_daily(symbol=stock_code)
            
            if not df.empty:
                # 获取最新的收盘价
                latest_price = df.iloc[-1]['close']
                print(f"   💰 最新价格: {latest_price:.2f} 港元")
                return float(latest_price)
            else:
                print(f"   ❌ 无法获取价格数据")
                return None
                
        except Exception as e:
            print(f"   ❌ 获取价格失败: {e}")
            return None
    
    def analyze_dividend_frequency(self, dividend_history):
        """改进的分红频率分析"""
        if len(dividend_history) < 2:
            return "年度", 1, "单次分红"

        # 分析分红类型
        report_types = [div.get('report_type', '') for div in dividend_history[:8]]

        # 统计各种分红类型
        annual_count = sum(1 for rt in report_types if '年度' in rt)
        interim_count = sum(1 for rt in report_types if '中期' in rt)
        quarterly_count = sum(1 for rt in report_types if '季' in rt or 'Q' in rt.upper())
        special_count = sum(1 for rt in report_types if '特别' in rt)

        # 计算相邻分红之间的间隔（月数）
        intervals = []
        for i in range(min(len(dividend_history) - 1, 8)):  # 看最近8次间隔
            date1 = dividend_history[i]['ex_date']
            date2 = dividend_history[i + 1]['ex_date']
            months_diff = (date1.year - date2.year) * 12 + (date1.month - date2.month)
            intervals.append(abs(months_diff))

        # 判断分红频率
        if intervals:
            avg_interval = np.mean(intervals)

            # 统计不同间隔的出现次数
            quarterly_intervals = sum(1 for interval in intervals if 2 <= interval <= 4)
            semi_annual_intervals = sum(1 for interval in intervals if 5 <= interval <= 8)
            annual_intervals = sum(1 for interval in intervals if 10 <= interval <= 14)

            # 改进的决策逻辑
            if quarterly_count >= 3:
                return "季度", 4, f"季度分红({quarterly_count}次)"
            elif interim_count >= 1 and annual_count >= 1:
                # 有中期分配和年度分配，按半年分红处理，取最近2次
                return "半年", 2, f"年度+中期({annual_count}+{interim_count})"
            elif quarterly_intervals >= 3:
                # 间隔主要是3-4个月，可能是季度分红
                return "季度", 4, f"间隔季度({quarterly_intervals}次)"
            elif semi_annual_intervals >= 2:
                return "半年", 2, f"半年分红({semi_annual_intervals}次)"
            elif avg_interval >= 10:
                # 平均间隔超过10个月，年度分红
                return "年度", 1, f"年度分红(间隔{avg_interval:.1f}月)"
            else:
                # 默认判断为半年分红
                return "半年", 2, f"混合模式(间隔{avg_interval:.1f}月)"

        return "年度", 1, "默认年度"

    def calculate_smart_annual_dividend(self, stock_code: str):
        """改进的智能年度分红计算"""
        if stock_code not in self.calculator.dividend_data:
            return 0.0, "无数据", 0, ""

        dividend_history = self.calculator.dividend_data[stock_code]['dividend_history']

        if not dividend_history:
            return 0.0, "无分红", 0, ""

        # 转换为适合分析的格式
        formatted_history = []
        for div in dividend_history:
            try:
                # 处理日期格式
                ex_date = div['ex_date']
                if isinstance(ex_date, str):
                    if '/' in ex_date:
                        ex_date = datetime.strptime(ex_date, '%Y/%m/%d')
                    else:
                        ex_date = datetime.strptime(ex_date, '%Y-%m-%d')
                elif isinstance(ex_date, datetime):
                    pass  # 已经是datetime对象
                else:
                    continue  # 跳过无法处理的日期格式

                formatted_history.append({
                    'year': div['year'],
                    'ex_date': ex_date,
                    'amount': float(div['amount']),
                    'report_type': div.get('report_type', ''),
                    'plan': div.get('plan', '')
                })
            except Exception as e:
                continue

        # 按除权日期排序（最新的在前）
        formatted_history.sort(key=lambda x: x['ex_date'], reverse=True)

        if not formatted_history:
            return 0.0, "无有效分红", 0, ""

        # 分析分红频率
        frequency_type, take_count, analysis_detail = self.analyze_dividend_frequency(formatted_history)

        # 取最近N次分红
        recent_dividends = formatted_history[:take_count]
        total_dividend = sum(div['amount'] for div in recent_dividends)
        return total_dividend, frequency_type, len(recent_dividends), analysis_detail

    def calculate_latest_dividend_yield(self, stock_code: str, latest_price: float) -> tuple:
        """计算基于最新价格的股息率，返回股息率和详细信息"""
        try:
            # 使用智能年度分红计算
            annual_dividend, frequency_type, dividend_count, analysis_detail = self.calculate_smart_annual_dividend(stock_code)

            if annual_dividend > 0 and latest_price > 0:
                dividend_yield = (annual_dividend / latest_price) * 100
                return dividend_yield, annual_dividend, frequency_type, dividend_count, analysis_detail
            else:
                return 0.0, 0.0, frequency_type, dividend_count, analysis_detail

        except Exception as e:
            print(f"   ❌ 计算股息率失败: {e}")
            return 0.0, 0.0, "错误", 0, str(e)
    
    def calculate_single_stock_ranking(self, stock_info):
        """计算单只股票的排名信息"""
        stock_code = stock_info['code']
        stock_name = stock_info['name']

        try:
            print(f"📈 处理 {stock_code} ({stock_name})...")

            # 获取最新价格
            latest_price = self.get_latest_stock_price(stock_code)

            if latest_price is None:
                self.failed_stocks.append({
                    'code': stock_code,
                    'name': stock_name,
                    'reason': '无法获取最新价格'
                })
                return False

            # 计算股息率（使用新的智能计算方法）
            dividend_yield, annual_dividend, frequency_type, dividend_count, analysis_detail = self.calculate_latest_dividend_yield(stock_code, latest_price)

            # 获取分红历史统计
            dividend_history = []
            if stock_code in self.calculator.dividend_data:
                dividend_history = self.calculator.dividend_data[stock_code]['dividend_history']

            # 计算分红频率（过去3年）
            recent_dividends = [d for d in dividend_history
                              if d['ex_date'] >= datetime.now() - timedelta(days=1095)]
            dividend_frequency = len(recent_dividends)

            # 记录结果
            result = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'latest_price': latest_price,
                'dividend_yield': dividend_yield,
                'annual_dividend': annual_dividend,
                'frequency_type': frequency_type,
                'dividend_count_used': dividend_count,
                'analysis_detail': analysis_detail,
                'dividend_frequency_3y': dividend_frequency,
                'last_dividend_date': max([d['ex_date'] for d in dividend_history]) if dividend_history else None,
                'last_dividend_amount': dividend_history[0]['amount'] if dividend_history else 0,
                'calculation_date': datetime.now()
            }

            self.ranking_results.append(result)

            print(f"   ✅ 股息率: {dividend_yield:.4f}%, 年度分红: {annual_dividend:.2f} 港元 ({frequency_type}: {analysis_detail})")

            # 添加延迟避免API限制
            time.sleep(0.5)
            return True

        except Exception as e:
            print(f"   ❌ 处理失败: {e}")
            self.failed_stocks.append({
                'code': stock_code,
                'name': stock_name,
                'reason': str(e)
            })
            return False
    
    def calculate_all_rankings(self):
        """计算所有股票的排名"""
        print("🚀 开始计算恒生指数成分股股息率排名")
        print("=" * 60)
        
        # 加载分红数据
        self.calculator.load_dividend_data()
        
        # 获取股票列表
        stock_list = self.get_stock_list()
        
        print(f"\n🔄 开始批量计算...")
        start_time = datetime.now()
        
        successful_count = 0
        total_count = len(stock_list)
        
        for i, stock_info in enumerate(stock_list, 1):
            print(f"\n[{i}/{total_count}] ", end="")
            
            if self.calculate_single_stock_ranking(stock_info):
                successful_count += 1
            
            # 每10只股票显示一次进度
            if i % 10 == 0:
                elapsed = datetime.now() - start_time
                avg_time = elapsed.total_seconds() / i
                remaining = (total_count - i) * avg_time
                print(f"\n📊 进度: {i}/{total_count} ({i/total_count*100:.1f}%), "
                      f"成功: {successful_count}, "
                      f"预计剩余: {remaining/60:.1f}分钟")
        
        end_time = datetime.now()
        total_time = end_time - start_time
        
        print(f"\n🎉 计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {successful_count}/{total_count} ({successful_count/total_count*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")
        
        return self.ranking_results
    
    def create_ranking_report(self):
        """创建排名报告"""
        if not self.ranking_results:
            print("❌ 没有排名数据可生成报告")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 按股息率排序
        sorted_results = sorted(self.ranking_results, key=lambda x: x['dividend_yield'], reverse=True)
        
        # 创建DataFrame
        df = pd.DataFrame(sorted_results)
        df['ranking'] = range(1, len(df) + 1)
        
        # 重新排列列顺序
        columns_order = [
            'ranking', 'stock_code', 'stock_name', 'latest_price',
            'dividend_yield', 'annual_dividend', 'frequency_type', 'analysis_detail', 'dividend_count_used',
            'dividend_frequency_3y', 'last_dividend_date', 'last_dividend_amount', 'calculation_date'
        ]
        df = df[columns_order]
        
        # 保存详细排名文件
        ranking_file = os.path.join(self.output_dir, f"hsi_dividend_yield_ranking_{timestamp}.csv")
        df.to_csv(ranking_file, index=False, encoding='utf-8-sig')
        
        # 创建简化版排名文件
        simple_df = df[['ranking', 'stock_code', 'stock_name', 'latest_price', 'dividend_yield', 'frequency_type']].copy()
        simple_df.columns = ['排名', '股票代码', '股票名称', '最新价格(港元)', '股息率(%)', '分红频率']
        
        simple_file = os.path.join(self.output_dir, f"hsi_dividend_yield_ranking_simple_{timestamp}.csv")
        simple_df.to_csv(simple_file, index=False, encoding='utf-8-sig')
        
        print(f"📊 排名报告已生成:")
        print(f"   详细版: {ranking_file}")
        print(f"   简化版: {simple_file}")
        
        return ranking_file, simple_file, df
    
    def print_top_rankings(self, top_n: int = 20):
        """打印前N名股票排名"""
        if not self.ranking_results:
            print("❌ 没有排名数据")
            return
        
        # 按股息率排序
        sorted_results = sorted(self.ranking_results, key=lambda x: x['dividend_yield'], reverse=True)
        
        print(f"\n🏆 股息率最高的前{top_n}只恒生指数成分股:")
        print("=" * 100)
        print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<20} {'最新价格':<12} {'股息率':<10} {'年度分红':<10} {'分红频率':<8}")
        print("-" * 100)

        for i, result in enumerate(sorted_results[:top_n], 1):
            print(f"{i:<4} {result['stock_code']:<8} {result['stock_name']:<20} "
                  f"{result['latest_price']:<12.2f} {result['dividend_yield']:<10.4f}% "
                  f"{result['annual_dividend']:<10.2f} {result['frequency_type']:<8}")
        
        # 显示统计信息
        valid_yields = [r['dividend_yield'] for r in sorted_results if r['dividend_yield'] > 0]
        if valid_yields:
            print(f"\n📈 统计信息:")
            print(f"   有分红股票数: {len(valid_yields)}/{len(sorted_results)}")
            print(f"   股息率范围: {min(valid_yields):.4f}% - {max(valid_yields):.4f}%")
            print(f"   平均股息率: {np.mean(valid_yields):.4f}%")
            print(f"   中位数股息率: {np.median(valid_yields):.4f}%")

            # 分红频率统计
            frequency_stats = Counter([r['frequency_type'] for r in sorted_results if r['dividend_yield'] > 0])
            print(f"\n📊 分红频率分布:")
            for freq, count in frequency_stats.items():
                print(f"   {freq}分红: {count} 只股票")

def main():
    """主函数"""
    print("🚀 恒生指数成分股股息率排名器")
    print("=" * 50)
    
    # 创建排名器
    ranker = HSIDividendYieldRanking(
        dividend_data_dir="hsi_dividend_data_20250603_173841",
        output_dir="hsi_dividend_yield_rankings"
    )
    
    try:
        # 计算所有股票排名
        results = ranker.calculate_all_rankings()
        
        if results:
            # 打印前20名
            ranker.print_top_rankings(20)
            
            # 生成排名报告
            report_files = ranker.create_ranking_report()
            
            if report_files:
                print(f"\n📁 报告文件已生成完成!")
            
            print(f"\n✅ 排名计算完成!")
        else:
            print("❌ 没有成功计算任何股票排名")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
