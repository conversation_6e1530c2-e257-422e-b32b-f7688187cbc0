#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
使用Optuna进行择时策略参数联合优化

同时优化smooth_window、exit_threshold和enter_threshold三个参数，
使用贝叶斯优化找出最佳的参数组合。
"""

import pandas as pd
import numpy as np
import yfinance as yf
import os
import warnings
from scipy import stats
from statsmodels.tsa.filters.hp_filter import hpfilter
import optuna
from optuna.samplers import TPESampler
import sqlite3

warnings.filterwarnings('ignore')

# 设置数据目录
DATA_DIR = 'data'

# 全局变量存储数据，避免重复加载
COMBINED_DATA = None
SP500_PRICES = None

def load_data():
    """加载和预处理数据"""
    global COMBINED_DATA, SP500_PRICES

    if COMBINED_DATA is not None and SP500_PRICES is not None:
        return COMBINED_DATA, SP500_PRICES

    print("加载数据...")

    # 加载原始数据
    hy_oas = pd.read_csv(os.path.join(DATA_DIR, 'hy_oas.csv'), index_col=0, parse_dates=True)
    tips_inflation = pd.read_csv(os.path.join(DATA_DIR, 'tips_5y_inflation.csv'), index_col=0, parse_dates=True)
    epu_index = pd.read_csv(os.path.join(DATA_DIR, 'daily_epu_index.csv'), index_col=0, parse_dates=True)

    # 数据预处理
    hy_oas_clean = hy_oas[['HY_OAS']].copy()
    tips_clean = tips_inflation.copy()
    tips_clean.columns = ['TIPS_5Y']
    epu_clean = epu_index.copy()
    epu_clean.columns = ['EPU']

    # 合并数据
    start_date = max(hy_oas_clean.index.min(), tips_clean.index.min(), epu_clean.index.min())
    end_date = min(hy_oas_clean.index.max(), tips_clean.index.max(), epu_clean.index.max())
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')

    COMBINED_DATA = pd.DataFrame({
        'HY_OAS': hy_oas_clean.reindex(date_range, method='ffill')['HY_OAS'],
        'TIPS_5Y': tips_clean.reindex(date_range, method='ffill')['TIPS_5Y'],
        'EPU': epu_clean.reindex(date_range, method='ffill')['EPU']
    }).dropna()

    # 下载标普500数据
    sp500_data = yf.download('^GSPC', start='2000-01-01', progress=False)
    if isinstance(sp500_data.columns, pd.MultiIndex):
        sp500_data.columns = sp500_data.columns.droplevel(1)
    SP500_PRICES = sp500_data['Adj Close'] if 'Adj Close' in sp500_data.columns else sp500_data['Close']

    print(f"数据加载完成，共 {len(COMBINED_DATA)} 条记录")
    return COMBINED_DATA, SP500_PRICES

def rolling_smooth_and_detrend_fast(series, window_size=252, smooth_window=18):
    """优化版：在滚动窗口中进行去噪和去趋势处理"""
    n = len(series)
    result = np.full(n, np.nan)

    # 预先计算平滑序列
    smoothed_series = series.rolling(window=smooth_window, center=True).mean()
    smoothed_series = smoothed_series.fillna(method='bfill').fillna(method='ffill')

    # 只处理有足够数据的部分，跳过早期数据
    start_idx = max(window_size, 100)  # 跳过前100天

    for i in range(start_idx, n, 5):  # 每5天计算一次，减少计算量
        window_start = i - window_size + 1
        window_data = smoothed_series.iloc[window_start:i+1].dropna()

        if len(window_data) >= 50:  # 降低最小数据要求
            try:
                # 简化的线性去趋势，避免HP滤波
                x = np.arange(len(window_data))
                slope = (window_data.iloc[-1] - window_data.iloc[0]) / (len(window_data) - 1)
                trend_value = window_data.iloc[0] + slope * (len(window_data) - 1)
                result[i] = window_data.iloc[-1] - trend_value
            except Exception:
                result[i] = window_data.iloc[-1] - window_data.mean()

    # 前向填充缺失值
    result_series = pd.Series(result, index=series.index)
    result_series = result_series.fillna(method='ffill').fillna(method='bfill')

    return result_series

def calculate_rolling_percentiles(data, window=252, smooth_window=18):
    """计算滚动分位数"""
    indicators = ['HY_OAS', 'TIPS_5Y', 'EPU']
    percentile_data = pd.DataFrame(index=data.index)

    for indicator in indicators:
        if indicator in data.columns:
            processed_series = rolling_smooth_and_detrend_fast(data[indicator],
                                                             window_size=window,
                                                             smooth_window=smooth_window)

            def rolling_percentile_fast(series, window_size):
                result = pd.Series(index=series.index, dtype=float)
                # 跳过早期数据，每10天计算一次
                start_idx = max(window_size, 100)
                for i in range(start_idx, len(series), 10):
                    window_start = max(0, i - window_size + 1)
                    window_data = series.iloc[window_start:i+1].dropna()

                    if len(window_data) >= window_size // 3:  # 降低要求
                        current_value = window_data.iloc[-1]
                        percentile = stats.percentileofscore(window_data, current_value) / 100.0
                        result.iloc[i] = percentile

                # 前向填充
                result = result.fillna(method='ffill').fillna(method='bfill')
                return result

            percentile_data[f'{indicator}_percentile'] = rolling_percentile_fast(processed_series, window)

    return percentile_data

def create_timing_indicator(percentile_data):
    """创建择时指标"""
    timing_data = percentile_data.copy()

    timing_data['HY_OAS_adjusted'] = 1 - timing_data['HY_OAS_percentile']
    timing_data['TIPS_5Y_adjusted'] = timing_data['TIPS_5Y_percentile']
    timing_data['EPU_adjusted'] = 1 - timing_data['EPU_percentile']

    timing_data['Market_Timing_Indicator'] = (
        timing_data['HY_OAS_adjusted'] +
        timing_data['TIPS_5Y_adjusted'] +
        timing_data['EPU_adjusted']
    ) / 3 * 100

    return timing_data.dropna()

def objective(trial):
    """Optuna优化目标函数"""
    # 缩小参数范围，基于之前的优化结果
    smooth_window = trial.suggest_int('smooth_window', 15, 22)  # 重点在18附近
    exit_threshold = trial.suggest_int('exit_threshold', 5, 12)   # 重点在8附近
    enter_threshold = trial.suggest_int('enter_threshold', 30, 45) # 重点在40附近

    # 确保逻辑正确
    if exit_threshold >= enter_threshold:
        return -999  # 返回很差的分数

    try:
        combined_data, sp500_prices = load_data()

        # 计算择时指标
        percentile_data = calculate_rolling_percentiles(combined_data, smooth_window=smooth_window)
        timing_data = create_timing_indicator(percentile_data)
        timing_indicator = timing_data['Market_Timing_Indicator']

        # 对齐数据 - 从2000年开始
        start_date = pd.to_datetime('2000-01-01')
        common_dates = sp500_prices.index.intersection(timing_indicator.index)
        common_dates = common_dates[common_dates >= start_date]

        if len(common_dates) < 500:  # 至少需要500个交易日
            return -999

        indicator_aligned = timing_indicator.reindex(common_dates, method='ffill')
        prices_aligned = sp500_prices.reindex(common_dates, method='ffill')

        # 生成交易信号
        signals = pd.Series(index=common_dates, dtype=float)
        current_position = 1.0

        for date in common_dates:
            indicator_value = indicator_aligned[date]
            if pd.isna(indicator_value):
                signals[date] = current_position
                continue

            if indicator_value < exit_threshold:
                current_position = 0.0
            elif indicator_value > enter_threshold:
                current_position = 1.0

            signals[date] = current_position

        # 计算收益率
        returns = prices_aligned.pct_change().fillna(0)
        strategy_returns = signals.shift(1).fillna(1.0) * returns
        buy_hold_returns = returns

        # 计算绩效指标
        def calc_metrics(ret_series):
            total_days = len(ret_series)
            total_years = total_days / 252
            total_return = (1 + ret_series).prod() - 1
            annual_return = (1 + total_return) ** (1/total_years) - 1
            annual_vol = ret_series.std() * np.sqrt(252)
            sharpe = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0

            cumret = (1 + ret_series).cumprod()
            drawdown = (cumret / cumret.expanding().max() - 1)
            max_dd = drawdown.min()

            return {
                'annual_return': annual_return,
                'sharpe_ratio': sharpe,
                'max_drawdown': max_dd,
                'cash_ratio': (signals == 0).sum() / len(signals)
            }

        strategy_metrics = calc_metrics(strategy_returns)
        buy_hold_metrics = calc_metrics(buy_hold_returns)

        # 计算综合评分作为优化目标
        excess_return = strategy_metrics['annual_return'] - buy_hold_metrics['annual_return']
        excess_sharpe = strategy_metrics['sharpe_ratio'] - buy_hold_metrics['sharpe_ratio']

        # 空仓比例惩罚（太高或太低都不好）
        optimal_cash_ratio = 0.08
        cash_penalty = abs(strategy_metrics['cash_ratio'] - optimal_cash_ratio) / optimal_cash_ratio

        # 综合评分：夏普比率40% + 超额收益30% + 回撤改善20% + 空仓比例惩罚10%
        score = (
            0.4 * strategy_metrics['sharpe_ratio'] +
            0.3 * excess_return * 10 +  # 放大超额收益的权重
            0.2 * (buy_hold_metrics['max_drawdown'] - strategy_metrics['max_drawdown']) * 10 +  # 回撤改善
            0.1 * (1 - cash_penalty)  # 空仓比例惩罚
        )

        # 记录关键指标供分析
        trial.set_user_attr('strategy_sharpe', strategy_metrics['sharpe_ratio'])
        trial.set_user_attr('excess_return', excess_return)
        trial.set_user_attr('strategy_max_dd', strategy_metrics['max_drawdown'])
        trial.set_user_attr('cash_ratio', strategy_metrics['cash_ratio'])
        trial.set_user_attr('strategy_annual_return', strategy_metrics['annual_return'])

        return score

    except Exception as e:
        print(f"参数组合 ({smooth_window}, {exit_threshold}, {enter_threshold}) 测试失败: {e}")
        return -999

def run_optuna_optimization():
    """运行Optuna优化"""
    print("开始使用Optuna进行参数优化...")

    # 创建研究
    study = optuna.create_study(
        direction='maximize',
        sampler=TPESampler(seed=42),
        study_name='timing_strategy_optimization'
    )

    # 运行优化 - 减少试验次数
    print("开始优化过程...")
    study.optimize(objective, n_trials=50, show_progress_bar=True)

    # 分析结果
    print("\n" + "="*80)
    print("Optuna优化结果分析")
    print("="*80)

    best_trial = study.best_trial
    print(f"最佳参数组合:")
    print(f"  smooth_window: {best_trial.params['smooth_window']}")
    print(f"  exit_threshold: {best_trial.params['exit_threshold']}")
    print(f"  enter_threshold: {best_trial.params['enter_threshold']}")
    print(f"  优化得分: {best_trial.value:.4f}")

    print(f"\n最佳参数的绩效指标:")
    print(f"  策略夏普比率: {best_trial.user_attrs['strategy_sharpe']:.3f}")
    print(f"  年化收益率: {best_trial.user_attrs['strategy_annual_return']:.2%}")
    print(f"  超额收益: {best_trial.user_attrs['excess_return']:.2%}")
    print(f"  最大回撤: {best_trial.user_attrs['strategy_max_dd']:.2%}")
    print(f"  空仓比例: {best_trial.user_attrs['cash_ratio']:.1%}")

    # 显示前10名试验
    print(f"\n前10名参数组合:")
    trials_df = study.trials_dataframe()
    top_trials = trials_df.nlargest(10, 'value')

    print(f"{'排名':<4} {'参数(SW,Exit,Enter)':<18} {'得分':<8} {'夏普':<6} {'超额收益':<8} {'最大回撤':<8} {'空仓比例':<8}")
    print("-" * 75)

    for i, (_, row) in enumerate(top_trials.iterrows(), 1):
        sw = int(row['params_smooth_window'])
        exit_th = int(row['params_exit_threshold'])
        enter_th = int(row['params_enter_threshold'])
        score = row['value']
        sharpe = row['user_attrs_strategy_sharpe']
        excess_ret = row['user_attrs_excess_return']
        max_dd = row['user_attrs_strategy_max_dd']
        cash_ratio = row['user_attrs_cash_ratio']

        print(f"{i:<4} ({sw},{exit_th},{enter_th}){'':<10} "
              f"{score:>7.3f} {sharpe:>5.3f} {excess_ret:>7.2%} "
              f"{max_dd:>7.2%} {cash_ratio:>7.1%}")

    # 保存结果
    results_file = os.path.join(DATA_DIR, 'optuna_optimization_results.csv')
    trials_df.to_csv(results_file, index=False, encoding='utf-8-sig')
    print(f"\n详细结果已保存到: {results_file}")

    return best_trial.params['smooth_window'], best_trial.params['exit_threshold'], best_trial.params['enter_threshold']

if __name__ == "__main__":
    try:
        # 运行优化
        best_smooth, best_exit, best_enter = run_optuna_optimization()
        print(f"\n推荐使用参数:")
        print(f"  smooth_window = {best_smooth}")
        print(f"  exit_threshold = {best_exit}")
        print(f"  enter_threshold = {best_enter}")

    except Exception as e:
        print(f"优化过程出错: {e}")
        import traceback
        traceback.print_exc()
