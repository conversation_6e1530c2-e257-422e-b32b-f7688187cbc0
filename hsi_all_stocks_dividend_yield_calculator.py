#!/usr/bin/env python3
"""
恒生指数所有成分股每日股息率计算器
使用滚动12个月分红总额计算股息率，避免未来数据泄露
"""

import os
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from hsi_daily_dividend_yield_calculator import HSIDailyDividendYieldCalculator
import time

class HSIAllStocksDividendYieldCalculator:
    """
    恒生指数所有成分股股息率计算器
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841", 
                 cache_dir: str = "cache", max_stocks: int = None):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.max_stocks = max_stocks
        self.calculator = HSIDailyDividendYieldCalculator(dividend_data_dir, cache_dir)
        self.results = {}
        self.failed_stocks = []
        self.summary_stats = {}
        
    def get_stock_list(self):
        """获取所有股票列表"""
        print("📁 获取股票列表...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        stock_list = []
        
        for file in dividend_files:
            stock_code = file.split('_')[0]
            stock_name = file.split('_')[1].replace('.json', '')
            stock_list.append({
                'code': stock_code,
                'name': stock_name,
                'file': file
            })
        
        # 按股票代码排序
        stock_list.sort(key=lambda x: x['code'])
        
        if self.max_stocks:
            stock_list = stock_list[:self.max_stocks]
            print(f"📊 限制处理前 {self.max_stocks} 只股票")
        
        print(f"✅ 找到 {len(stock_list)} 只股票")
        return stock_list
    
    def calculate_single_stock(self, stock_info):
        """计算单只股票的股息率"""
        stock_code = stock_info['code']
        stock_name = stock_info['name']
        
        try:
            print(f"📈 计算 {stock_code} ({stock_name})...")
            
            # 使用已有的计算器
            yield_series = self.calculator.calculate_daily_dividend_yield(stock_code)
            
            if yield_series is not None and len(yield_series) > 0:
                # 计算基本统计信息
                valid_yields = yield_series.dropna()
                non_zero_yields = valid_yields[valid_yields > 0]
                
                stats = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'total_days': len(valid_yields),
                    'dividend_days': len(non_zero_yields),
                    'coverage_rate': len(non_zero_yields) / len(valid_yields) * 100 if len(valid_yields) > 0 else 0,
                    'avg_yield': non_zero_yields.mean() if len(non_zero_yields) > 0 else 0,
                    'max_yield': non_zero_yields.max() if len(non_zero_yields) > 0 else 0,
                    'min_yield': non_zero_yields.min() if len(non_zero_yields) > 0 else 0,
                    'std_yield': non_zero_yields.std() if len(non_zero_yields) > 0 else 0,
                    'latest_yield': valid_yields.iloc[-1] if len(valid_yields) > 0 else 0,
                    'data_start': valid_yields.index.min() if len(valid_yields) > 0 else None,
                    'data_end': valid_yields.index.max() if len(valid_yields) > 0 else None
                }
                
                self.results[stock_code] = {
                    'yield_series': yield_series,
                    'stats': stats
                }
                
                print(f"   ✅ 成功: 平均股息率 {stats['avg_yield']:.4f}%, 覆盖率 {stats['coverage_rate']:.1f}%")
                return True
                
            else:
                print(f"   ❌ 无有效数据")
                self.failed_stocks.append({
                    'code': stock_code,
                    'name': stock_name,
                    'reason': '无有效股息率数据'
                })
                return False
                
        except Exception as e:
            print(f"   ❌ 计算失败: {e}")
            self.failed_stocks.append({
                'code': stock_code,
                'name': stock_name,
                'reason': str(e)
            })
            return False
    
    def calculate_all_stocks(self):
        """计算所有股票的股息率"""
        print("🚀 开始计算所有恒生指数成分股股息率")
        print("=" * 60)
        
        # 加载分红数据
        self.calculator.load_dividend_data()
        
        # 获取股票列表
        stock_list = self.get_stock_list()
        
        print(f"\n🔄 开始批量计算...")
        start_time = datetime.now()
        
        successful_count = 0
        total_count = len(stock_list)
        
        for i, stock_info in enumerate(stock_list, 1):
            print(f"\n[{i}/{total_count}] ", end="")
            
            if self.calculate_single_stock(stock_info):
                successful_count += 1
            
            # 每10只股票显示一次进度
            if i % 10 == 0:
                elapsed = datetime.now() - start_time
                avg_time = elapsed.total_seconds() / i
                remaining = (total_count - i) * avg_time
                print(f"\n📊 进度: {i}/{total_count} ({i/total_count*100:.1f}%), "
                      f"成功: {successful_count}, "
                      f"预计剩余: {remaining/60:.1f}分钟")
        
        end_time = datetime.now()
        total_time = end_time - start_time
        
        print(f"\n🎉 计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {successful_count}/{total_count} ({successful_count/total_count*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")
        
        return self.results
    
    def save_results(self):
        """保存计算结果"""
        if not self.results:
            print("❌ 没有结果可保存")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 1. 保存详细股息率数据
        print("\n💾 保存详细股息率数据...")
        yield_data = {}
        for stock_code, data in self.results.items():
            stock_name = data['stats']['stock_name']
            yield_data[f"{stock_code}_{stock_name}"] = data['yield_series']
        
        yield_df = pd.DataFrame(yield_data)
        yield_file = f"hsi_all_stocks_dividend_yields_{timestamp}.csv"
        yield_df.to_csv(yield_file, encoding='utf-8-sig')
        print(f"   ✅ 详细数据: {yield_file}")
        print(f"      维度: {yield_df.shape}")
        
        # 2. 保存统计摘要
        print("\n📊 保存统计摘要...")
        stats_data = []
        for stock_code, data in self.results.items():
            stats_data.append(data['stats'])
        
        stats_df = pd.DataFrame(stats_data)
        stats_df = stats_df.sort_values('avg_yield', ascending=False)
        stats_file = f"hsi_dividend_yield_summary_{timestamp}.csv"
        stats_df.to_csv(stats_file, index=False, encoding='utf-8-sig')
        print(f"   ✅ 统计摘要: {stats_file}")
        
        # 3. 保存失败记录
        if self.failed_stocks:
            print("\n⚠️  保存失败记录...")
            failed_df = pd.DataFrame(self.failed_stocks)
            failed_file = f"hsi_dividend_yield_failed_{timestamp}.csv"
            failed_df.to_csv(failed_file, index=False, encoding='utf-8-sig')
            print(f"   📝 失败记录: {failed_file}")
        
        return {
            'yield_file': yield_file,
            'stats_file': stats_file,
            'failed_file': failed_file if self.failed_stocks else None
        }
    
    def print_summary(self):
        """打印汇总信息"""
        if not self.results:
            print("❌ 没有结果可汇总")
            return
        
        print("\n📈 计算结果汇总")
        print("=" * 40)
        
        # 整体统计
        total_stocks = len(self.results)
        avg_yields = [data['stats']['avg_yield'] for data in self.results.values() if data['stats']['avg_yield'] > 0]
        
        print(f"成功计算股票数: {total_stocks}")
        print(f"有分红股票数: {len(avg_yields)}")
        print(f"平均股息率范围: {min(avg_yields):.4f}% - {max(avg_yields):.4f}%")
        print(f"股息率中位数: {np.median(avg_yields):.4f}%")
        
        # 显示前10只高股息率股票
        print(f"\n🏆 股息率最高的前10只股票:")
        stats_list = [data['stats'] for data in self.results.values()]
        stats_df = pd.DataFrame(stats_list)
        top_10 = stats_df[stats_df['avg_yield'] > 0].nlargest(10, 'avg_yield')
        
        for _, row in top_10.iterrows():
            print(f"   {row['stock_code']} ({row['stock_name']}): {row['avg_yield']:.4f}%")
        
        # 显示失败的股票
        if self.failed_stocks:
            print(f"\n❌ 计算失败的股票 ({len(self.failed_stocks)}只):")
            for failed in self.failed_stocks[:5]:  # 只显示前5个
                print(f"   {failed['code']} ({failed['name']}): {failed['reason']}")
            if len(self.failed_stocks) > 5:
                print(f"   ... 还有 {len(self.failed_stocks) - 5} 只")

def main():
    """主函数"""
    print("🚀 恒生指数所有成分股股息率计算器")
    print("=" * 50)
    
    # 创建计算器 (可以设置max_stocks参数来限制处理数量，用于测试)
    calculator = HSIAllStocksDividendYieldCalculator(
        dividend_data_dir="hsi_dividend_data_20250603_173841",
        max_stocks=None  # 设置为None处理所有股票，或设置数字限制处理数量
    )
    
    try:
        # 计算所有股票
        results = calculator.calculate_all_stocks()
        
        if results:
            # 打印汇总
            calculator.print_summary()
            
            # 保存结果
            files = calculator.save_results()
            
            print(f"\n📁 输出文件:")
            for key, file in files.items():
                if file:
                    print(f"   {key}: {file}")
            
            print(f"\n✅ 所有计算完成!")
        else:
            print("❌ 没有成功计算任何股票")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
