#!/usr/bin/env python3
"""
恒生指数成分股智能股息率因子回测
使用修正后的智能分红频率算法进行回测分析
"""

import os
import json
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import time
import pickle
from collections import defaultdict
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSISmartDividendYieldBacktest:
    """
    恒生指数成分股智能股息率因子回测器
    使用智能分红频率识别算法
    """
    
    def __init__(self, 
                 dividend_data_dir: str = "hsi_dividend_data_20250603_173841",
                 cache_dir: str = "hsi_smart_backtest_cache",
                 output_dir: str = "hsi_smart_backtest_results"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.output_dir = output_dir
        
        # 数据存储
        self.dividend_data = {}
        self.price_data = {}
        self.stock_list = []
        
        # 回测参数
        self.start_date = datetime(2022, 1, 1)
        self.end_date = datetime(2024, 12, 31)
        self.rebalance_freq = 'Q'  # 季度再平衡
        self.n_groups = 5  # 分为5组
        
        # 回测结果
        self.backtest_results = {}
        self.portfolio_returns = {}
        
        # 创建目录
        os.makedirs(cache_dir, exist_ok=True)
        os.makedirs(output_dir, exist_ok=True)
        
    def load_dividend_data(self):
        """加载分红数据"""
        print("📁 加载分红数据...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        
        for file in dividend_files:
            try:
                file_path = os.path.join(self.dividend_data_dir, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                stock_code = data['stock_code']
                
                # 转换日期格式
                dividend_history = []
                for div in data['dividend_history']:
                    try:
                        ex_date_str = div['ex_date']
                        if '/' in ex_date_str:
                            ex_date = datetime.strptime(ex_date_str, '%Y/%m/%d')
                        else:
                            ex_date = datetime.strptime(ex_date_str, '%Y-%m-%d')
                        
                        dividend_history.append({
                            'year': div['year'],
                            'ex_date': ex_date,
                            'amount': float(div['amount']),
                            'report_type': div.get('report_type', ''),
                            'plan': div.get('plan', '')
                        })
                    except Exception as e:
                        continue
                
                # 按除权日期排序（最新的在前）
                dividend_history.sort(key=lambda x: x['ex_date'], reverse=True)
                
                self.dividend_data[stock_code] = {
                    'stock_name': data['stock_name'],
                    'dividend_history': dividend_history
                }
                
                self.stock_list.append(stock_code)
                
            except Exception as e:
                continue
        
        print(f"✅ 成功加载 {len(self.dividend_data)} 只股票的分红数据")
        return True
    
    def analyze_dividend_frequency(self, dividend_history):
        """分析分红频率（与智能排名器相同的逻辑）"""
        if len(dividend_history) < 2:
            return "年度", 1
        
        # 分析分红类型
        report_types = [div.get('report_type', '') for div in dividend_history[:8]]
        
        # 统计各种分红类型
        annual_count = sum(1 for rt in report_types if '年度' in rt)
        interim_count = sum(1 for rt in report_types if '中期' in rt)
        quarterly_count = sum(1 for rt in report_types if '季' in rt or 'Q' in rt.upper())
        
        # 计算相邻分红之间的间隔（月数）
        intervals = []
        for i in range(min(len(dividend_history) - 1, 8)):
            date1 = dividend_history[i]['ex_date']
            date2 = dividend_history[i + 1]['ex_date']
            months_diff = (date1.year - date2.year) * 12 + (date1.month - date2.month)
            intervals.append(abs(months_diff))
        
        # 判断分红频率
        if intervals:
            avg_interval = np.mean(intervals)
            
            # 统计不同间隔的出现次数
            quarterly_intervals = sum(1 for interval in intervals if 2 <= interval <= 4)
            semi_annual_intervals = sum(1 for interval in intervals if 5 <= interval <= 8)
            annual_intervals = sum(1 for interval in intervals if 10 <= interval <= 14)
            
            # 决策逻辑（优先考虑报告类型，再考虑间隔）
            if quarterly_count >= 2:
                return "季度", 4
            elif (interim_count >= 2 and annual_count >= 2) or semi_annual_intervals >= 2:
                return "半年", 2
            elif quarterly_intervals >= 3:
                return "季度", 4
            elif avg_interval >= 10:
                return "年度", 1
            else:
                return "半年", 2
        
        return "年度", 1
    
    def calculate_smart_annual_dividend(self, stock_code: str, calc_date: datetime):
        """根据分红频率智能计算年度分红总和"""
        if stock_code not in self.dividend_data:
            return 0.0, "无数据", 0
        
        dividend_history = self.dividend_data[stock_code]['dividend_history']
        
        if not dividend_history:
            return 0.0, "无分红", 0
        
        # 只考虑计算日期之前的分红
        available_dividends = [div for div in dividend_history if div['ex_date'] <= calc_date]
        
        if not available_dividends:
            return 0.0, "无历史分红", 0
        
        # 分析分红频率
        frequency_type, take_count = self.analyze_dividend_frequency(available_dividends)
        
        # 取最近N次分红
        recent_dividends = available_dividends[:take_count]
        total_dividend = sum(div['amount'] for div in recent_dividends)
        
        return total_dividend, frequency_type, len(recent_dividends)
    
    def load_price_data(self):
        """加载价格数据"""
        print("📊 加载价格数据...")
        
        cache_file = os.path.join(self.cache_dir, "price_data_cache.pkl")
        
        # 尝试从缓存加载
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    if datetime.now() - cache_data['timestamp'] < timedelta(hours=6):
                        self.price_data = cache_data['data']
                        print(f"💾 从缓存加载 {len(self.price_data)} 只股票的价格数据")
                        return True
            except:
                pass
        
        # 重新获取价格数据
        print("🌐 从akshare获取价格数据...")
        
        for i, stock_code in enumerate(self.stock_list, 1):
            try:
                print(f"   [{i}/{len(self.stock_list)}] 获取 {stock_code}...")
                
                df = ak.stock_hk_daily(symbol=stock_code)
                if not df.empty:
                    # 转换日期索引
                    df['date'] = pd.to_datetime(df['date'])
                    df.set_index('date', inplace=True)
                    
                    # 筛选回测期间的数据
                    mask = (df.index >= self.start_date) & (df.index <= self.end_date)
                    df = df[mask]
                    
                    if not df.empty:
                        self.price_data[stock_code] = df
                        print(f"      ✅ 获取 {len(df)} 条记录")
                    else:
                        print(f"      ❌ 回测期间无数据")
                else:
                    print(f"      ❌ 无价格数据")
                
                time.sleep(0.2)  # 避免API限制
                
            except Exception as e:
                print(f"      ❌ 获取失败: {e}")
                continue
        
        # 保存缓存
        cache_data = {
            'timestamp': datetime.now(),
            'data': self.price_data
        }
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
        
        print(f"✅ 成功获取 {len(self.price_data)} 只股票的价格数据")
        return True
    
    def generate_rebalance_dates(self):
        """生成再平衡日期"""
        if self.rebalance_freq == 'Q':
            # 季度再平衡：每年1、4、7、10月的第一个交易日
            dates = []
            current = self.start_date
            while current <= self.end_date:
                if current.month in [1, 4, 7, 10]:
                    dates.append(current)
                    current += timedelta(days=90)  # 大约3个月
                else:
                    current += timedelta(days=30)
            return dates
        elif self.rebalance_freq == 'M':
            # 月度再平衡
            return pd.date_range(start=self.start_date, end=self.end_date, freq='MS').tolist()
        else:
            # 年度再平衡
            return pd.date_range(start=self.start_date, end=self.end_date, freq='YS').tolist()
    
    def calculate_dividend_yield_at_date(self, stock_code: str, calc_date: datetime) -> float:
        """计算指定日期的智能股息率"""
        try:
            # 获取股价
            if stock_code not in self.price_data:
                return 0.0
            
            price_df = self.price_data[stock_code]
            
            # 找到最接近的价格
            available_dates = price_df.index[price_df.index <= calc_date]
            if len(available_dates) == 0:
                return 0.0
            
            latest_date = available_dates[-1]
            current_price = price_df.loc[latest_date, 'close']
            
            if pd.isna(current_price) or current_price <= 0:
                return 0.0
            
            # 计算智能年度分红
            annual_dividend, frequency_type, dividend_count = self.calculate_smart_annual_dividend(
                stock_code, calc_date
            )
            
            if annual_dividend > 0:
                dividend_yield = (annual_dividend / current_price) * 100
                return dividend_yield
            else:
                return 0.0
                
        except Exception as e:
            return 0.0

    def run_backtest(self):
        """运行智能股息率因子回测"""
        print("🚀 开始智能股息率因子回测")
        print("=" * 60)

        # 1. 加载数据
        if not self.load_dividend_data():
            return False

        if not self.load_price_data():
            return False

        # 2. 生成再平衡日期
        rebalance_dates = self.generate_rebalance_dates()
        print(f"📅 生成 {len(rebalance_dates)} 个再平衡日期")

        # 3. 执行回测
        portfolio_values = {f'Group_{i+1}': [1.0] for i in range(self.n_groups)}
        portfolio_values['Equal_Weight'] = [1.0]

        all_factor_scores = []

        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            next_rebalance = rebalance_dates[i + 1]

            print(f"\n🔄 再平衡 {i+1}/{len(rebalance_dates)-1}: {rebalance_date.strftime('%Y-%m-%d')}")

            # 计算因子得分
            factor_scores = {}
            valid_stocks = []

            for stock_code in self.stock_list:
                dividend_yield = self.calculate_dividend_yield_at_date(stock_code, rebalance_date)
                if dividend_yield > 0:  # 只考虑有分红的股票
                    factor_scores[stock_code] = dividend_yield
                    valid_stocks.append(stock_code)

            if len(valid_stocks) < self.n_groups:
                print(f"   ⚠️  有效股票数量不足: {len(valid_stocks)}")
                continue

            # 按股息率排序并分组
            sorted_stocks = sorted(factor_scores.items(), key=lambda x: x[1], reverse=True)
            group_size = len(sorted_stocks) // self.n_groups

            groups = {}
            for g in range(self.n_groups):
                start_idx = g * group_size
                if g == self.n_groups - 1:  # 最后一组包含剩余股票
                    end_idx = len(sorted_stocks)
                else:
                    end_idx = (g + 1) * group_size

                group_stocks = [stock for stock, _ in sorted_stocks[start_idx:end_idx]]
                groups[f'Group_{g+1}'] = group_stocks

            # 记录因子得分
            period_scores = {
                'date': rebalance_date,
                'scores': factor_scores,
                'groups': groups
            }
            all_factor_scores.append(period_scores)

            # 计算期间收益
            period_returns = self.calculate_period_returns(groups, rebalance_date, next_rebalance)

            # 更新组合价值
            for group_name, group_return in period_returns.items():
                if group_name in portfolio_values:
                    current_value = portfolio_values[group_name][-1]
                    new_value = current_value * (1 + group_return)
                    portfolio_values[group_name].append(new_value)

            print(f"   📊 期间收益: {', '.join([f'{k}: {v:.2%}' for k, v in period_returns.items()])}")

        # 保存结果
        self.portfolio_returns = portfolio_values
        self.backtest_results = {
            'rebalance_dates': rebalance_dates,
            'factor_scores': all_factor_scores,
            'portfolio_returns': portfolio_values
        }

        print(f"\n✅ 回测完成!")
        return True

    def calculate_period_returns(self, groups, start_date, end_date):
        """计算各组合在指定期间的收益率"""
        period_returns = {}

        # 计算等权重基准
        all_stocks = []
        for group_stocks in groups.values():
            all_stocks.extend(group_stocks)

        if all_stocks:
            equal_weight_return = self.calculate_group_return(all_stocks, start_date, end_date)
            period_returns['Equal_Weight'] = equal_weight_return

        # 计算各分组收益
        for group_name, group_stocks in groups.items():
            if group_stocks:
                group_return = self.calculate_group_return(group_stocks, start_date, end_date)
                period_returns[group_name] = group_return

        return period_returns

    def calculate_group_return(self, stocks, start_date, end_date):
        """计算股票组合的等权重收益率"""
        try:
            returns = []

            for stock_code in stocks:
                if stock_code not in self.price_data:
                    continue

                price_df = self.price_data[stock_code]

                # 获取开始和结束价格
                start_prices = price_df.index[price_df.index >= start_date]
                end_prices = price_df.index[price_df.index <= end_date]

                if len(start_prices) == 0 or len(end_prices) == 0:
                    continue

                start_price = price_df.loc[start_prices[0], 'close']
                end_price = price_df.loc[end_prices[-1], 'close']

                if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                    stock_return = (end_price - start_price) / start_price
                    returns.append(stock_return)

            if returns:
                return np.mean(returns)  # 等权重平均
            else:
                return 0.0

        except Exception as e:
            return 0.0

    def analyze_performance(self):
        """分析回测表现"""
        if not self.portfolio_returns:
            print("❌ 没有回测结果可分析")
            return None

        print("\n📊 回测表现分析")
        print("=" * 50)

        results = {}

        for group_name, values in self.portfolio_returns.items():
            if len(values) > 1:
                # 计算总收益率
                total_return = (values[-1] - values[0]) / values[0]

                # 计算年化收益率
                years = len(values) / 4  # 假设季度再平衡
                annual_return = (values[-1] / values[0]) ** (1/years) - 1 if years > 0 else 0

                # 计算波动率
                returns = [values[i]/values[i-1] - 1 for i in range(1, len(values))]
                volatility = np.std(returns) * np.sqrt(4) if returns else 0  # 年化波动率

                # 计算夏普比率（假设无风险利率为3%）
                risk_free_rate = 0.03
                sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0

                # 计算最大回撤
                max_drawdown = self.calculate_max_drawdown(values)

                results[group_name] = {
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'final_value': values[-1]
                }

                print(f"\n{group_name}:")
                print(f"  总收益率: {total_return:.2%}")
                print(f"  年化收益率: {annual_return:.2%}")
                print(f"  年化波动率: {volatility:.2%}")
                print(f"  夏普比率: {sharpe_ratio:.2f}")
                print(f"  最大回撤: {max_drawdown:.2%}")

        return results

    def calculate_max_drawdown(self, values):
        """计算最大回撤"""
        if len(values) < 2:
            return 0.0

        peak = values[0]
        max_dd = 0.0

        for value in values[1:]:
            if value > peak:
                peak = value
            else:
                drawdown = (peak - value) / peak
                max_dd = max(max_dd, drawdown)

        return max_dd

    def plot_cumulative_returns(self):
        """绘制累积收益率图表"""
        if not self.portfolio_returns:
            print("❌ 没有回测结果可绘制")
            return None

        plt.figure(figsize=(14, 8))

        # 绘制各组合的累积收益率
        colors = ['red', 'orange', 'green', 'blue', 'purple', 'black']

        for i, (group_name, values) in enumerate(self.portfolio_returns.items()):
            if len(values) > 1:
                cumulative_returns = [(v - 1) * 100 for v in values]  # 转换为百分比
                x_axis = range(len(cumulative_returns))

                color = colors[i % len(colors)]
                linewidth = 3 if group_name == 'Group_1' else 2
                linestyle = '--' if group_name == 'Equal_Weight' else '-'

                plt.plot(x_axis, cumulative_returns,
                        color=color, linewidth=linewidth, linestyle=linestyle,
                        label=f'{group_name} ({cumulative_returns[-1]:.1f}%)')

        plt.title('恒生指数成分股智能股息率因子回测 - 累积收益率', fontsize=16, fontweight='bold')
        plt.xlabel('再平衡期数', fontsize=12)
        plt.ylabel('累积收益率 (%)', fontsize=12)
        plt.legend(loc='upper left')
        plt.grid(True, alpha=0.3)

        # 添加说明文字
        plt.text(0.02, 0.98,
                f'回测期间: {self.start_date.strftime("%Y-%m-%d")} 至 {self.end_date.strftime("%Y-%m-%d")}\n'
                f'再平衡频率: {self.rebalance_freq}\n'
                f'分组数量: {self.n_groups}',
                transform=plt.gca().transAxes, fontsize=10,
                verticalalignment='top', bbox=dict(boxstyle='round', facecolor='wheat', alpha=0.5))

        plt.tight_layout()

        # 保存图表
        filename = os.path.join(self.output_dir, f"smart_dividend_yield_backtest_{datetime.now().strftime('%Y%m%d_%H%M%S')}.png")
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {filename}")

        plt.show()
        return filename

    def generate_report(self):
        """生成详细回测报告"""
        if not self.portfolio_returns:
            print("❌ 没有回测结果可生成报告")
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.output_dir, f"smart_dividend_yield_backtest_report_{timestamp}.txt")

        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("恒生指数成分股智能股息率因子回测报告\n")
            f.write("=" * 60 + "\n\n")

            f.write(f"回测参数:\n")
            f.write(f"  回测期间: {self.start_date.strftime('%Y-%m-%d')} 至 {self.end_date.strftime('%Y-%m-%d')}\n")
            f.write(f"  再平衡频率: {self.rebalance_freq}\n")
            f.write(f"  分组数量: {self.n_groups}\n")
            f.write(f"  股票数量: {len(self.stock_list)}\n\n")

            # 分析结果
            results = self.analyze_performance()
            if results:
                f.write("回测结果:\n")
                f.write("-" * 40 + "\n")

                for group_name, metrics in results.items():
                    f.write(f"\n{group_name}:\n")
                    f.write(f"  总收益率: {metrics['total_return']:.2%}\n")
                    f.write(f"  年化收益率: {metrics['annual_return']:.2%}\n")
                    f.write(f"  年化波动率: {metrics['volatility']:.2%}\n")
                    f.write(f"  夏普比率: {metrics['sharpe_ratio']:.2f}\n")
                    f.write(f"  最大回撤: {metrics['max_drawdown']:.2%}\n")
                    f.write(f"  最终价值: {metrics['final_value']:.4f}\n")

                # 因子有效性分析
                if 'Group_1' in results and 'Group_5' in results:
                    high_return = results['Group_1']['annual_return']
                    low_return = results['Group_5']['annual_return']
                    return_spread = high_return - low_return

                    f.write(f"\n因子有效性分析:\n")
                    f.write(f"  高股息率组合年化收益率: {high_return:.2%}\n")
                    f.write(f"  低股息率组合年化收益率: {low_return:.2%}\n")
                    f.write(f"  收益率差异: {return_spread:.2%}\n")

                    if return_spread > 0:
                        f.write(f"  结论: 智能股息率因子显示正向效应\n")
                    else:
                        f.write(f"  结论: 智能股息率因子显示负向效应\n")

            f.write(f"\n报告生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        print(f"📄 报告已保存: {report_file}")
        return report_file

def main():
    """主函数"""
    print("🚀 恒生指数成分股智能股息率因子回测")
    print("=" * 60)

    # 创建回测器
    backtest = HSISmartDividendYieldBacktest(
        dividend_data_dir="hsi_dividend_data_20250603_173841",
        cache_dir="hsi_smart_backtest_cache",
        output_dir="hsi_smart_backtest_results"
    )

    start_time = time.time()

    try:
        # 运行回测
        if not backtest.run_backtest():
            print("❌ 回测失败")
            return

        # 分析表现
        results = backtest.analyze_performance()

        # 生成图表
        print("\n📊 生成可视化图表...")
        backtest.plot_cumulative_returns()

        # 生成报告
        print("\n📄 生成详细报告...")
        backtest.generate_report()

        # 总结
        end_time = time.time()
        total_time = end_time - start_time

        print(f"\n🎉 智能股息率因子回测完成!")
        print(f"   总耗时: {total_time:.1f} 秒")

        if results and 'Group_1' in results and 'Group_5' in results:
            high_return = results['Group_1']['annual_return']
            low_return = results['Group_5']['annual_return']
            return_spread = high_return - low_return

            print(f"\n🎯 关键发现:")
            print(f"   高股息率组合年化收益率: {high_return:.2%}")
            print(f"   低股息率组合年化收益率: {low_return:.2%}")
            print(f"   收益率差异: {return_spread:.2%}")

            if return_spread > 0:
                print("   ✅ 智能股息率因子显示正向效应")
                print("   💡 建议：可以考虑在投资策略中纳入智能股息率因子")
            else:
                print("   ❌ 智能股息率因子显示负向效应")
                print("   💡 建议：需要进一步研究或考虑其他因子")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断回测")
    except Exception as e:
        print(f"\n❌ 回测过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
