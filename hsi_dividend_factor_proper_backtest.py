#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子正确回测分析

正确的回测思路：
1. 每个再平衡日，用当时的股价和过去12个月分红计算股息率
2. 按股息率分组构建投资组合
3. 持有到下个再平衡日，计算收益率
4. 重复上述过程
"""

import csv
import json
import math
from datetime import datetime, timedelta
from collections import defaultdict
import urllib.request
import urllib.parse
import time
import re

class HSIDividendFactorProperBacktest:
    """正确的股息率因子回测分析器"""
    
    def __init__(self, 
                 dividend_data_file: str = "hsi_dividend_quick_20250603_141448.csv",
                 start_date: str = "2022-01-01",
                 end_date: str = "2024-12-31",
                 rebalance_freq: str = "Q",  # Q=季度, M=月度
                 n_groups: int = 5):
        """
        初始化回测分析器
        
        Args:
            dividend_data_file: 分红数据文件
            start_date: 回测开始日期
            end_date: 回测结束日期
            rebalance_freq: 再平衡频率
            n_groups: 分组数量
        """
        self.dividend_data_file = dividend_data_file
        self.start_date = datetime.strptime(start_date, "%Y-%m-%d")
        self.end_date = datetime.strptime(end_date, "%Y-%m-%d")
        self.rebalance_freq = rebalance_freq
        self.n_groups = n_groups
        
        # 数据存储
        self.stock_list = []  # 股票列表
        self.dividend_history = {}  # 历史分红数据 {symbol: [(date, amount), ...]}
        self.price_history = {}     # 历史价格数据 {symbol: [(date, price), ...]}
        
        # 回测结果
        self.rebalance_dates = []
        self.portfolio_holdings = {}  # {date: {group: [symbols]}}
        self.portfolio_returns = {}   # {group: [returns]}
        
        print("🚀 正确的股息率因子回测分析器已初始化")
        print(f"📅 回测期间: {start_date} 至 {end_date}")
        print(f"🔄 再平衡频率: {rebalance_freq}")
    
    def load_stock_list(self) -> bool:
        """加载股票列表"""
        try:
            print(f"📁 加载股票列表...")
            
            with open(self.dividend_data_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                for row in reader:
                    if row['状态'] == 'success':
                        symbol = row['股票代码']
                        name = row['股票名称']
                        self.stock_list.append({'symbol': symbol, 'name': name})
            
            print(f"✅ 加载了 {len(self.stock_list)} 只股票")
            return True
            
        except Exception as e:
            print(f"❌ 加载股票列表失败: {e}")
            return False
    
    def fetch_historical_dividend_data(self, symbol: str) -> list:
        """获取历史分红数据"""
        try:
            print(f"💰 获取 {symbol} 历史分红数据...")
            
            base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
                'Referer': 'https://datacenter.eastmoney.com/',
                'Accept': 'application/json, text/plain, */*',
            }
            
            params = {
                'reportName': 'RPT_HKF10_MAIN_DIVBASIC',
                'columns': 'SECURITY_CODE,EX_DIVIDEND_DATE,DIVIDEND_DATE,YEAR,PLAN_EXPLAIN',
                'quoteColumns': '',
                'filter': f'(SECURITY_CODE="{symbol}")',
                'pageNumber': 1,
                'pageSize': 50,  # 获取更多历史数据
                'sortTypes': '-1,-1',
                'sortColumns': 'NOTICE_DATE,EX_DIVIDEND_DATE',
                'source': 'F10',
                'client': 'PC',
                'v': str(int(time.time() * 1000))
            }
            
            query_string = urllib.parse.urlencode(params)
            full_url = f"{base_url}?{query_string}"
            req = urllib.request.Request(full_url, headers=headers)
            
            with urllib.request.urlopen(req, timeout=15) as response:
                if response.status == 200:
                    content = response.read().decode('utf-8')
                    data = json.loads(content)
                    
                    if 'result' in data and 'data' in data['result']:
                        raw_records = data['result']['data']
                        
                        dividend_records = []
                        for record in raw_records:
                            ex_date_str = record.get('EX_DIVIDEND_DATE')
                            plan_explain = record.get('PLAN_EXPLAIN', '')
                            
                            if ex_date_str and plan_explain:
                                try:
                                    ex_date = datetime.strptime(ex_date_str[:10], '%Y-%m-%d')
                                    amount = self.parse_dividend_amount(plan_explain)
                                    
                                    if amount > 0:
                                        dividend_records.append((ex_date, amount))
                                except:
                                    continue
                        
                        # 按日期排序
                        dividend_records.sort(key=lambda x: x[0])
                        print(f"   ✅ 获取到 {len(dividend_records)} 条分红记录")
                        return dividend_records
                    else:
                        print(f"   ⚠️  无分红数据")
                        return []
                else:
                    print(f"   ❌ HTTP错误: {response.status}")
                    return []
                    
        except Exception as e:
            print(f"   ❌ 获取失败: {e}")
            return []
    
    def parse_dividend_amount(self, plan_explain: str) -> float:
        """解析分红金额"""
        if not plan_explain or plan_explain == "未派发或宣派股息":
            return 0.0

        plan_text = str(plan_explain)

        # 港币金额模式
        hkd_patterns = [
            r'港币(\d+\.?\d*)元',
            r'相当于港币(\d+\.?\d+)元',
            r'港币(\d+\.?\d+)',
            r'每股派港币(\d+\.?\d*)元',
            r'派港币(\d+\.?\d*)元',
            r'派息港币(\d+\.?\d*)元',
        ]

        for pattern in hkd_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue

        # 其他格式
        other_patterns = [
            r'每股派(\d+\.?\d*)港币',
            r'派(\d+\.?\d*)港币',
            r'每股(\d+\.?\d*)港币',
            r'每股派(\d+\.?\d*)元',
            r'派(\d+\.?\d*)元',
            r'每股(\d+\.?\d*)仙',
            r'派(\d+\.?\d*)仙',
            r'(\d+\.?\d*)港币',
        ]

        for pattern in other_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    amount = float(match.group(1))
                    if '仙' in pattern:
                        amount = amount / 100
                    return amount
                except:
                    continue

        # 括号中的港币等值
        bracket_pattern = r'\(相当于港币(\d+\.?\d+)元'
        match = re.search(bracket_pattern, plan_text)
        if match:
            try:
                return float(match.group(1))
            except:
                pass

        return 0.0
    
    def simulate_price_data(self, symbol: str) -> list:
        """模拟价格数据（实际应用中应该获取真实价格数据）"""
        # 生成模拟的日度价格数据
        import hashlib
        import random
        
        # 基于股票代码生成稳定的随机种子
        seed = int(hashlib.md5(symbol.encode()).hexdigest()[:8], 16) % 1000000
        random.seed(seed)
        
        # 生成基础价格
        base_price = random.uniform(50, 500)
        
        # 生成价格序列
        price_data = []
        current_date = self.start_date
        current_price = base_price
        
        while current_date <= self.end_date:
            # 模拟价格波动
            daily_return = random.normalvariate(0.0005, 0.02)  # 日收益率
            current_price *= (1 + daily_return)
            current_price = max(current_price, base_price * 0.3)  # 防止价格过低
            
            price_data.append((current_date, current_price))
            current_date += timedelta(days=1)
        
        return price_data
    
    def calculate_dividend_yield(self, symbol: str, calc_date: datetime) -> float:
        """计算指定日期的股息率"""
        try:
            # 获取过去12个月的分红
            start_period = calc_date - timedelta(days=365)
            
            if symbol not in self.dividend_history:
                return 0.0
            
            # 计算过去12个月的总分红
            total_dividend = 0.0
            for div_date, amount in self.dividend_history[symbol]:
                if start_period <= div_date <= calc_date:
                    total_dividend += amount
            
            # 获取当前股价
            if symbol not in self.price_history:
                return 0.0
            
            # 找到最接近计算日期的价格
            current_price = None
            for price_date, price in self.price_history[symbol]:
                if price_date <= calc_date:
                    current_price = price
                else:
                    break
            
            if current_price is None or current_price <= 0:
                return 0.0
            
            # 计算股息率
            dividend_yield = (total_dividend / current_price) * 100
            return dividend_yield
            
        except Exception as e:
            return 0.0
    
    def generate_rebalance_dates(self) -> list:
        """生成再平衡日期"""
        dates = []
        current_date = self.start_date
        
        if self.rebalance_freq == 'Q':  # 季度
            while current_date <= self.end_date:
                dates.append(current_date)
                # 下一个季度
                if current_date.month <= 3:
                    next_date = current_date.replace(month=4, day=1)
                elif current_date.month <= 6:
                    next_date = current_date.replace(month=7, day=1)
                elif current_date.month <= 9:
                    next_date = current_date.replace(month=10, day=1)
                else:
                    next_date = current_date.replace(year=current_date.year+1, month=1, day=1)
                current_date = next_date
        elif self.rebalance_freq == 'M':  # 月度
            while current_date <= self.end_date:
                dates.append(current_date)
                # 下一个月
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year+1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month+1, day=1)
        
        return dates
    
    def create_portfolios_by_dividend_yield(self, calc_date: datetime) -> dict:
        """基于股息率创建投资组合"""
        # 计算所有股票的股息率
        stock_yields = []
        for stock in self.stock_list:
            symbol = stock['symbol']
            dividend_yield = self.calculate_dividend_yield(symbol, calc_date)
            if dividend_yield >= 0:  # 包括0股息率的股票
                stock_yields.append((symbol, dividend_yield, stock['name']))
        
        if len(stock_yields) < self.n_groups:
            return {}
        
        # 按股息率排序（降序）
        stock_yields.sort(key=lambda x: x[1], reverse=True)
        
        # 分组
        n_stocks_per_group = len(stock_yields) // self.n_groups
        portfolios = {}
        
        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(stock_yields)
            else:
                end_idx = (i + 1) * n_stocks_per_group
            
            group_stocks = stock_yields[start_idx:end_idx]
            portfolios[f'Group_{i+1}'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_performance(self, portfolios: dict, 
                                      start_date: datetime, end_date: datetime) -> dict:
        """计算投资组合在持有期间的表现"""
        performance = {}
        
        for group_name, stocks in portfolios.items():
            if not stocks:
                continue
            
            # 计算组合收益率（等权重）
            stock_returns = []
            
            for symbol, yield_rate, name in stocks:
                if symbol in self.price_history:
                    # 找到开始和结束价格
                    start_price = None
                    end_price = None
                    
                    for price_date, price in self.price_history[symbol]:
                        if price_date <= start_date and start_price is None:
                            start_price = price
                        if price_date <= end_date:
                            end_price = price
                    
                    if start_price and end_price and start_price > 0:
                        stock_return = (end_price - start_price) / start_price
                        stock_returns.append(stock_return)
            
            if stock_returns:
                # 等权重组合收益率
                portfolio_return = sum(stock_returns) / len(stock_returns)
                performance[group_name] = {
                    'return': portfolio_return,
                    'stock_count': len(stocks),
                    'avg_dividend_yield': sum(s[1] for s in stocks) / len(stocks)
                }
        
        return performance
    
    def run_backtest(self) -> bool:
        """运行完整的回测"""
        try:
            print("🚀 开始正确的股息率因子回测...")
            
            # 1. 加载股票列表
            if not self.load_stock_list():
                return False
            
            # 2. 获取历史分红数据（简化版：只获取部分股票）
            print("💰 获取历史分红数据...")
            test_symbols = [stock['symbol'] for stock in self.stock_list[:10]]  # 测试前10只
            
            for symbol in test_symbols:
                dividend_data = self.fetch_historical_dividend_data(symbol)
                self.dividend_history[symbol] = dividend_data
                time.sleep(1)  # 避免API限制
            
            # 3. 生成模拟价格数据
            print("📈 生成模拟价格数据...")
            for symbol in test_symbols:
                price_data = self.simulate_price_data(symbol)
                self.price_history[symbol] = price_data
            
            # 4. 生成再平衡日期
            self.rebalance_dates = self.generate_rebalance_dates()
            print(f"🔄 生成 {len(self.rebalance_dates)} 个再平衡日期")
            
            # 5. 执行回测
            all_performance = []
            
            for i, rebalance_date in enumerate(self.rebalance_dates[:-1]):
                print(f"🔄 再平衡 {i+1}/{len(self.rebalance_dates)-1}: {rebalance_date.strftime('%Y-%m-%d')}")
                
                # 基于当时的股息率创建投资组合
                portfolios = self.create_portfolios_by_dividend_yield(rebalance_date)
                
                if not portfolios:
                    continue
                
                # 计算持有期间表现
                next_rebalance = self.rebalance_dates[i+1]
                performance = self.calculate_portfolio_performance(
                    portfolios, rebalance_date, next_rebalance
                )
                
                if performance:
                    all_performance.append({
                        'date': rebalance_date,
                        'performance': performance,
                        'portfolios': portfolios
                    })
            
            # 6. 汇总结果
            self.summarize_results(all_performance)
            
            print("✅ 回测完成!")
            return True
            
        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def summarize_results(self, all_performance: list):
        """汇总回测结果"""
        if not all_performance:
            print("❌ 没有回测结果")
            return
        
        print("\n📊 回测结果汇总")
        print("=" * 60)
        
        # 计算各组合的平均表现
        group_performance = defaultdict(list)
        
        for period_result in all_performance:
            for group_name, perf in period_result['performance'].items():
                group_performance[group_name].append(perf['return'])
        
        # 显示结果
        print(f"{'组合':<10} {'期数':<6} {'平均收益率':<12} {'累积收益率':<12} {'胜率':<8}")
        print("-" * 60)
        
        for group_name in sorted(group_performance.keys()):
            returns = group_performance[group_name]
            if returns:
                avg_return = sum(returns) / len(returns)
                cumulative_return = 1.0
                for r in returns:
                    cumulative_return *= (1 + r)
                cumulative_return -= 1
                
                win_rate = sum(1 for r in returns if r > 0) / len(returns)
                
                print(f"{group_name:<10} {len(returns):<6} {avg_return*100:<12.2f}% "
                      f"{cumulative_return*100:<12.2f}% {win_rate*100:<8.1f}%")
        
        # 因子有效性分析
        if 'Group_1' in group_performance and 'Group_5' in group_performance:
            high_returns = group_performance['Group_1']
            low_returns = group_performance['Group_5']
            
            if high_returns and low_returns:
                high_avg = sum(high_returns) / len(high_returns)
                low_avg = sum(low_returns) / len(low_returns)
                spread = high_avg - low_avg
                
                print(f"\n📈 因子有效性分析:")
                print(f"高股息率组合平均收益: {high_avg*100:.2f}%")
                print(f"低股息率组合平均收益: {low_avg*100:.2f}%")
                print(f"多空收益差: {spread*100:.2f}%")
                
                if spread > 0:
                    print("✅ 股息率因子表现正向")
                else:
                    print("❌ 股息率因子表现负向")


def main():
    """主函数"""
    print("🎯 恒生指数成分股股息率因子正确回测分析")
    print("=" * 60)
    
    # 创建回测分析器
    backtest = HSIDividendFactorProperBacktest(
        dividend_data_file="hsi_dividend_quick_20250603_141448.csv",
        start_date="2023-01-01",
        end_date="2024-12-31",
        rebalance_freq="Q",  # 季度再平衡
        n_groups=5
    )
    
    # 运行回测
    backtest.run_backtest()


if __name__ == "__main__":
    main()
