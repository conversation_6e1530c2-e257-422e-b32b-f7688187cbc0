#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速预览已获取的ROE数据

功能：
1. 快速查看已获取的ROE数据
2. 显示数据统计
3. 预览部分股票的ROE趋势

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import os
from datetime import datetime

def preview_roe_data():
    """预览ROE数据"""
    print("🔍 快速预览已获取的ROE数据")
    print("=" * 50)
    
    # 检查缓存目录
    cache_dir = "hsi_roe_cache"
    quarterly_dir = os.path.join(cache_dir, "quarterly_data")
    daily_dir = os.path.join(cache_dir, "daily_data")
    
    if not os.path.exists(quarterly_dir):
        print("❌ 未找到季度数据目录")
        return
    
    # 统计已获取的文件
    quarterly_files = [f for f in os.listdir(quarterly_dir) if f.endswith('_quarterly_roe.csv')]
    daily_files = [f for f in os.listdir(daily_dir) if f.endswith('_daily_roe.csv')] if os.path.exists(daily_dir) else []
    
    print(f"📊 数据获取进度:")
    print(f"   - 季度数据: {len(quarterly_files)} 个文件")
    print(f"   - 每日数据: {len(daily_files)} 个文件")
    
    if not quarterly_files:
        print("❌ 暂无数据可预览")
        return
    
    # 随机选择几个文件进行预览
    import random
    preview_files = random.sample(quarterly_files, min(5, len(quarterly_files)))
    
    print(f"\n📈 预览 {len(preview_files)} 只股票的ROE数据:")
    print("-" * 80)
    
    for file in preview_files:
        file_path = os.path.join(quarterly_dir, file)
        try:
            df = pd.read_csv(file_path, parse_dates=['report_date'])
            
            stock_code = df['stock_code'].iloc[0]
            stock_name = df['stock_name'].iloc[0]
            
            print(f"\n🏢 {stock_code} ({stock_name})")
            print(f"   📅 数据期间: {df['report_date'].min().date()} 到 {df['report_date'].max().date()}")
            print(f"   📊 记录数: {len(df)} 条")
            print(f"   💰 最新ROE: {df['roe'].iloc[-1]:.2f}%")
            print(f"   📈 平均ROE: {df['roe'].mean():.2f}%")
            print(f"   📊 最高ROE: {df['roe'].max():.2f}%")
            print(f"   📉 最低ROE: {df['roe'].min():.2f}%")
            
            # 显示最近几期的ROE数据
            print(f"   🔍 最近5期ROE:")
            recent_data = df.tail(5)[['report_date', 'roe']]
            for _, row in recent_data.iterrows():
                print(f"      {row['report_date'].date()}: {row['roe']:.2f}%")
                
        except Exception as e:
            print(f"❌ 读取 {file} 失败: {e}")
    
    # 如果有合并文件，也预览一下
    combined_files = [f for f in os.listdir(cache_dir) if f.startswith('hsi_daily_roe_combined_')]
    if combined_files:
        latest_combined = sorted(combined_files, reverse=True)[0]
        combined_path = os.path.join(cache_dir, latest_combined)
        
        print(f"\n📋 合并数据文件预览: {latest_combined}")
        print("-" * 50)
        
        try:
            df_combined = pd.read_csv(combined_path, parse_dates=['date'])
            
            print(f"📊 总记录数: {len(df_combined):,}")
            print(f"🏢 股票数量: {df_combined['stock_code'].nunique()}")
            print(f"📅 日期范围: {df_combined['date'].min().date()} 到 {df_combined['date'].max().date()}")
            
            # 显示ROE统计
            print(f"\n💰 ROE统计:")
            print(f"   平均ROE: {df_combined['roe'].mean():.2f}%")
            print(f"   中位数ROE: {df_combined['roe'].median():.2f}%")
            print(f"   最高ROE: {df_combined['roe'].max():.2f}%")
            print(f"   最低ROE: {df_combined['roe'].min():.2f}%")
            
            # 显示最新ROE排名前10
            latest_roe = df_combined.groupby('stock_code').last()
            top_10_roe = latest_roe.nlargest(10, 'roe')[['stock_name', 'roe']]
            
            print(f"\n🏆 最新ROE排名前10:")
            for i, (code, row) in enumerate(top_10_roe.iterrows(), 1):
                print(f"   {i:2d}. {code} ({row['stock_name']}): {row['roe']:.2f}%")
                
        except Exception as e:
            print(f"❌ 读取合并文件失败: {e}")
    
    print(f"\n✅ 预览完成！")

if __name__ == "__main__":
    preview_roe_data()
