import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.dates import DateFormatter
import matplotlib.dates as mdates
import os
from pathlib import Path

def calculate_macd(df, fast_period=12, slow_period=26, signal_period=9):
    """
    Calculate MACD, MACD Signal and MACD Histogram
    
    Args:
        df (pandas.DataFrame): DataFrame with price data
        fast_period (int): Fast EMA period
        slow_period (int): Slow EMA period
        signal_period (int): Signal EMA period
        
    Returns:
        pandas.DataFrame: DataFrame with MACD indicators
    """
    # Make a copy of the DataFrame to avoid modifying the original
    df_macd = df.copy()
    
    # Calculate the Fast and Slow EMAs
    df_macd['ema_fast'] = df_macd['Close'].ewm(span=fast_period, adjust=False).mean()
    df_macd['ema_slow'] = df_macd['Close'].ewm(span=slow_period, adjust=False).mean()
    
    # Calculate MACD Line
    df_macd['macd'] = df_macd['ema_fast'] - df_macd['ema_slow']
    
    # Calculate Signal Line
    df_macd['macd_signal'] = df_macd['macd'].ewm(span=signal_period, adjust=False).mean()
    
    # Calculate MACD Histogram
    df_macd['macd_hist'] = df_macd['macd'] - df_macd['macd_signal']
    
    return df_macd

def plot_macd(df_macd, title="Hang Seng Index MACD", start_date=None, end_date=None):
    """
    Plot MACD indicator with price chart
    
    Args:
        df_macd (pandas.DataFrame): DataFrame with MACD indicators
        title (str): Chart title
        start_date (str): Start date for the chart in 'YYYY-MM-DD' format
        end_date (str): End date for the chart in 'YYYY-MM-DD' format
    """
    # Filter data by date range if specified
    if start_date:
        df_macd = df_macd[df_macd.index >= start_date]
    if end_date:
        df_macd = df_macd[df_macd.index <= end_date]
    
    # Create figure and axis
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(14, 10), gridspec_kw={'height_ratios': [3, 1]})
    
    # Plot price chart
    ax1.plot(df_macd.index, df_macd['Close'], color='blue', linewidth=1.5)
    ax1.set_title(title, fontsize=16)
    ax1.set_ylabel('Price', fontsize=12)
    ax1.grid(True, alpha=0.3)
    
    # Format x-axis dates
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # Plot MACD
    ax2.plot(df_macd.index, df_macd['macd'], color='blue', linewidth=1.5, label='MACD')
    ax2.plot(df_macd.index, df_macd['macd_signal'], color='red', linewidth=1, label='Signal')
    
    # Plot MACD Histogram
    for i in range(len(df_macd)):
        if df_macd['macd_hist'].iloc[i] >= 0:
            ax2.bar(df_macd.index[i], df_macd['macd_hist'].iloc[i], color='green', width=0.8, alpha=0.5)
        else:
            ax2.bar(df_macd.index[i], df_macd['macd_hist'].iloc[i], color='red', width=0.8, alpha=0.5)
    
    # Add zero line
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=0.5)
    
    ax2.set_ylabel('MACD', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left')
    
    # Format x-axis dates
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # Save the figure
    output_dir = Path("output")
    if not output_dir.exists():
        output_dir.mkdir(parents=True)
    
    plt.savefig(output_dir / "hsi_macd.png", dpi=300, bbox_inches='tight')
    print(f"Chart saved to {output_dir / 'hsi_macd.png'}")
    
    # Show the plot
    plt.show()

def analyze_macd_signals(df_macd, lookback_period=30):
    """
    Analyze recent MACD signals
    
    Args:
        df_macd (pandas.DataFrame): DataFrame with MACD indicators
        lookback_period (int): Number of days to look back for analysis
        
    Returns:
        dict: Dictionary with analysis results
    """
    # Get the most recent data
    recent_data = df_macd.iloc[-lookback_period:]
    
    # Check for MACD crossovers
    macd_crossover = []
    for i in range(1, len(recent_data)):
        # MACD crosses above Signal (Bullish)
        if (recent_data['macd'].iloc[i-1] < recent_data['macd_signal'].iloc[i-1] and 
            recent_data['macd'].iloc[i] > recent_data['macd_signal'].iloc[i]):
            macd_crossover.append({
                'date': recent_data.index[i].strftime('%Y-%m-%d'),
                'type': 'Bullish',
                'macd': recent_data['macd'].iloc[i],
                'signal': recent_data['macd_signal'].iloc[i]
            })
        
        # MACD crosses below Signal (Bearish)
        elif (recent_data['macd'].iloc[i-1] > recent_data['macd_signal'].iloc[i-1] and 
              recent_data['macd'].iloc[i] < recent_data['macd_signal'].iloc[i]):
            macd_crossover.append({
                'date': recent_data.index[i].strftime('%Y-%m-%d'),
                'type': 'Bearish',
                'macd': recent_data['macd'].iloc[i],
                'signal': recent_data['macd_signal'].iloc[i]
            })
    
    # Check current MACD trend
    latest = recent_data.iloc[-1]
    if latest['macd'] > latest['macd_signal']:
        current_trend = "Bullish (MACD above Signal)"
    else:
        current_trend = "Bearish (MACD below Signal)"
    
    # Check MACD histogram trend
    hist_trend = "Increasing" if latest['macd_hist'] > recent_data['macd_hist'].iloc[-2] else "Decreasing"
    
    # Check if MACD is above or below zero
    macd_position = "Above zero (Bullish)" if latest['macd'] > 0 else "Below zero (Bearish)"
    
    # Prepare analysis results
    analysis = {
        'current_date': latest.name.strftime('%Y-%m-%d'),
        'current_price': latest['Close'],
        'macd': latest['macd'],
        'signal': latest['macd_signal'],
        'histogram': latest['macd_hist'],
        'current_trend': current_trend,
        'histogram_trend': hist_trend,
        'macd_position': macd_position,
        'recent_crossovers': macd_crossover
    }
    
    return analysis

def main():
    # Load HSI data
    data_file = Path("data/hsi_data.csv")
    
    if not data_file.exists():
        print(f"Error: Data file {data_file} not found.")
        return
    
    print(f"Loading data from {data_file}...")
    df = pd.read_csv(data_file)
    
    # Convert Date to datetime and set as index
    df['Date'] = pd.to_datetime(df['Date'])
    df.set_index('Date', inplace=True)
    
    # Sort by date (ascending)
    df.sort_index(inplace=True)
    
    print(f"Loaded {len(df)} records from {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")
    
    # Calculate MACD
    print("Calculating MACD indicators...")
    df_macd = calculate_macd(df)
    
    # Analyze recent MACD signals
    print("Analyzing recent MACD signals...")
    analysis = analyze_macd_signals(df_macd)
    
    # Print analysis results
    print("\n=== MACD Analysis for Hang Seng Index ===")
    print(f"Date: {analysis['current_date']}")
    print(f"Current Price: {analysis['current_price']:.2f}")
    print(f"MACD: {analysis['macd']:.2f}")
    print(f"Signal: {analysis['signal']:.2f}")
    print(f"Histogram: {analysis['histogram']:.2f}")
    print(f"Current Trend: {analysis['current_trend']}")
    print(f"Histogram Trend: {analysis['histogram_trend']}")
    print(f"MACD Position: {analysis['macd_position']}")
    
    print("\nRecent MACD Crossovers:")
    if analysis['recent_crossovers']:
        for crossover in analysis['recent_crossovers']:
            print(f"  {crossover['date']} - {crossover['type']} Crossover (MACD: {crossover['macd']:.2f}, Signal: {crossover['signal']:.2f})")
    else:
        print("  No crossovers in the recent period")
    
    # Plot MACD with price chart
    print("\nGenerating MACD chart...")
    
    # Plot full history
    plot_macd(df_macd, title="Hang Seng Index MACD (Full History)")
    
    # Plot recent 1 year
    one_year_ago = df_macd.index.max() - pd.DateOffset(years=1)
    plot_macd(df_macd, title="Hang Seng Index MACD (Last 1 Year)", 
              start_date=one_year_ago.strftime('%Y-%m-%d'))
    
    print("Analysis complete.")

if __name__ == "__main__":
    main()
