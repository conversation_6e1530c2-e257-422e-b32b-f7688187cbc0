#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ROE数据获取进度监控工具

功能：
1. 监控ROE数据获取进度
2. 显示已完成的股票数量
3. 估算剩余时间

作者: AI Assistant
创建时间: 2025年1月
"""

import os
import time
from datetime import datetime

def monitor_progress():
    """监控ROE数据获取进度"""
    cache_dir = "hsi_roe_cache"
    quarterly_dir = os.path.join(cache_dir, "quarterly_data")
    daily_dir = os.path.join(cache_dir, "daily_data")
    
    total_stocks = 83  # 恒生指数成分股总数
    
    print("🔍 ROE数据获取进度监控")
    print("=" * 50)
    
    while True:
        try:
            # 统计已完成的文件
            quarterly_files = 0
            daily_files = 0
            
            if os.path.exists(quarterly_dir):
                quarterly_files = len([f for f in os.listdir(quarterly_dir) if f.endswith('_quarterly_roe.csv')])
            
            if os.path.exists(daily_dir):
                daily_files = len([f for f in os.listdir(daily_dir) if f.endswith('_daily_roe.csv')])
            
            # 显示进度
            current_time = datetime.now().strftime("%H:%M:%S")
            quarterly_progress = (quarterly_files / total_stocks) * 100
            daily_progress = (daily_files / total_stocks) * 100
            
            print(f"\r[{current_time}] 季度数据: {quarterly_files}/{total_stocks} ({quarterly_progress:.1f}%) | "
                  f"每日数据: {daily_files}/{total_stocks} ({daily_progress:.1f}%)", end="", flush=True)
            
            # 如果完成了，退出
            if daily_files >= total_stocks:
                print(f"\n🎉 数据获取完成！")
                break
            
            time.sleep(5)  # 每5秒更新一次
            
        except KeyboardInterrupt:
            print(f"\n\n⚠️  监控已停止")
            break
        except Exception as e:
            print(f"\n❌ 监控出错: {e}")
            break

if __name__ == "__main__":
    monitor_progress()
