"""
获取中证2000指数和恒生科技指数行情数据

This script retrieves historical price data for the CSI 2000 Index (中证2000)
and Hang Seng Tech Index (恒生科技指数) using multiple data sources (AKShare and yfinance)
with caching support.
"""

import akshare as ak
import yfinance as yf
import pandas as pd
import numpy as np
import os
import json
from datetime import datetime, timedelta
import time

class IndexDataFetcher:
    def __init__(self, index_type="CSI2000", cache_dir="cache_data"):
        """
        初始化指数数据获取器

        Args:
            index_type: 指数类型 ("CSI2000" 或 "HSTECH")
            cache_dir: 缓存目录路径
        """
        self.index_type = index_type
        self.cache_dir = cache_dir

        # 指数配置
        self.index_configs = {
            'CSI2000': {
                'name': '中证2000指数',
                'akshare_symbol': 'sh932000',
                'akshare_symbol_em': '932000',  # 东方财富使用的代码
                'yfinance_symbols': ['932000.SS'],
                'index_code': '932000',
                'market_type': 'CN',
                'launch_date': '2023-08-11'  # 指数发布日期
            },
            'HSTECH': {
                'name': '恒生科技指数',
                'akshare_symbol': 'HSTECH',
                'akshare_symbol_em': 'HSTECH',  # 东方财富使用的代码
                'yfinance_symbols': ['^HSTECH'],
                'index_code': 'HSTECH',
                'market_type': 'HK',
                'launch_date': '2020-07-27'  # 指数发布日期
            }
        }

        if index_type not in self.index_configs:
            raise ValueError(f"不支持的指数类型: {index_type}. 支持的类型: {list(self.index_configs.keys())}")

        self.index_config = self.index_configs[index_type]

        # 设置缓存文件路径
        cache_prefix = index_type.lower()
        self.cache_file = os.path.join(cache_dir, f"{cache_prefix}_data.csv")
        self.cache_info_file = os.path.join(cache_dir, f"{cache_prefix}_cache_info.json")

        # 创建缓存目录
        os.makedirs(cache_dir, exist_ok=True)

    def get_default_start_date(self):
        """
        获取指数的默认开始日期（从上市日期开始）

        Returns:
            str: 默认开始日期 (YYYY-MM-DD)
        """
        return self.index_config['launch_date']

    def get_full_history_date_range(self):
        """
        获取完整历史数据的日期范围

        Returns:
            tuple: (start_date, end_date)
        """
        start_date = self.index_config['launch_date']
        end_date = datetime.now().strftime("%Y-%m-%d")
        return start_date, end_date
        
    def is_cache_valid(self, max_age_hours=24):
        """
        检查缓存是否有效
        
        Args:
            max_age_hours: 缓存最大有效时间（小时）
            
        Returns:
            bool: 缓存是否有效
        """
        if not os.path.exists(self.cache_file) or not os.path.exists(self.cache_info_file):
            return False
            
        try:
            with open(self.cache_info_file, 'r', encoding='utf-8') as f:
                cache_info = json.load(f)
                
            cache_time = datetime.fromisoformat(cache_info['last_update'])
            current_time = datetime.now()
            
            # 检查缓存是否过期
            if (current_time - cache_time).total_seconds() > max_age_hours * 3600:
                return False
                
            # 检查是否需要更新（工作日且当前时间超过收盘时间）
            if current_time.weekday() < 5:  # 周一到周五
                market_close_time = current_time.replace(hour=15, minute=0, second=0, microsecond=0)
                if current_time > market_close_time and cache_time < market_close_time:
                    return False
                    
            return True
            
        except Exception as e:
            print(f"检查缓存状态失败: {e}")
            return False
    
    def load_from_cache(self):
        """
        从缓存加载数据
        
        Returns:
            pandas.DataFrame: 缓存的数据，如果失败返回None
        """
        try:
            if os.path.exists(self.cache_file):
                data = pd.read_csv(self.cache_file, index_col=0, parse_dates=True)
                print(f"✅ 从缓存加载{self.index_config['name']}数据成功")
                print(f"📅 数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                print(f"📊 数据点数: {len(data):,}")
                return data
            return None
        except Exception as e:
            print(f"❌ 从缓存加载数据失败: {e}")
            return None
    
    def save_to_cache(self, data):
        """
        保存数据到缓存
        
        Args:
            data: 要保存的数据
        """
        try:
            # 保存数据
            data.to_csv(self.cache_file)
            
            # 保存缓存信息
            cache_info = {
                'last_update': datetime.now().isoformat(),
                'data_start': data.index.min().isoformat(),
                'data_end': data.index.max().isoformat(),
                'data_points': len(data),
                'index_name': self.index_config['name']
            }
            
            with open(self.cache_info_file, 'w', encoding='utf-8') as f:
                json.dump(cache_info, f, ensure_ascii=False, indent=2)
                
            print(f"💾 数据已保存到缓存")
            
        except Exception as e:
            print(f"❌ 保存缓存失败: {e}")
    
    def fetch_from_akshare(self, start_date=None, end_date=None):
        """
        使用AKShare获取指数数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 获取的数据，如果失败返回None
        """
        print(f"🌐 尝试使用AKShare获取{self.index_config['name']}数据...")

        # 根据市场类型选择不同的方法
        if self.index_config['market_type'] == 'CN':
            # 中国A股指数方法
            methods = [
                ('index_zh_a_hist', self.index_config['akshare_symbol_em']),
                ('stock_zh_index_daily', self.index_config['akshare_symbol']),
                ('stock_zh_index_daily_em', self.index_config['akshare_symbol']),
                ('stock_zh_index_daily_tx', self.index_config['akshare_symbol'])
            ]
        else:
            # 香港指数方法
            methods = [
                ('stock_hk_index_daily_em', self.index_config['akshare_symbol']),
                ('stock_hk_index_daily_sina', self.index_config['akshare_symbol'])
            ]

        for method_name, symbol in methods:
            try:
                print(f"  尝试方法: {method_name} with symbol: {symbol}")

                if method_name == 'index_zh_a_hist':
                    # 使用东方财富的指数历史数据接口
                    # 设置合理的日期范围以提高下载速度
                    api_start_date = start_date.replace('-', '') if start_date else "20200101"
                    api_end_date = end_date.replace('-', '') if end_date else datetime.now().strftime("%Y%m%d")
                    data = ak.index_zh_a_hist(symbol=symbol, period="daily",
                                            start_date=api_start_date, end_date=api_end_date)
                elif method_name == 'stock_zh_index_daily':
                    data = ak.stock_zh_index_daily(symbol=symbol)
                elif method_name == 'stock_zh_index_daily_em':
                    data = ak.stock_zh_index_daily_em(symbol=symbol)
                elif method_name == 'stock_zh_index_daily_tx':
                    data = ak.stock_zh_index_daily_tx(symbol=symbol)
                elif method_name == 'stock_hk_index_daily_em':
                    # 香港指数数据
                    data = ak.stock_hk_index_daily_em(symbol=symbol)
                elif method_name == 'stock_hk_index_daily_sina':
                    # 新浪香港指数数据
                    data = ak.stock_hk_index_daily_sina(symbol=symbol)
                else:
                    continue

                if data is None or data.empty:
                    print(f"  ❌ {method_name} 返回空数据")
                    continue

                print(f"  ✅ {method_name} 获取到数据，列名: {list(data.columns)}")
                print(f"  📊 原始数据形状: {data.shape}")

                # 数据预处理
                data = data.copy()

                # 打印前几行数据以便调试
                print(f"  📋 前3行数据:")
                print(data.head(3))

                # 检查数据结构并重命名列
                if 'date' in data.columns:
                    data['date'] = pd.to_datetime(data['date'])
                    data.set_index('date', inplace=True)
                elif '时间' in data.columns:
                    data['时间'] = pd.to_datetime(data['时间'])
                    data.set_index('时间', inplace=True)
                elif '日期' in data.columns:
                    data['日期'] = pd.to_datetime(data['日期'])
                    data.set_index('日期', inplace=True)
                elif data.index.name in ['date', '时间', '日期'] or isinstance(data.index[0], (pd.Timestamp, str)):
                    data.index = pd.to_datetime(data.index)
                else:
                    print(f"  ❌ 无法识别日期列，列名: {list(data.columns)}")
                    continue

                # 标准化列名
                column_mapping = {}
                for col in data.columns:
                    col_lower = col.lower()
                    if col_lower in ['open', '开盘', '今开']:
                        column_mapping[col] = 'open'
                    elif col_lower in ['high', '最高', 'max']:
                        column_mapping[col] = 'high'
                    elif col_lower in ['low', '最低', 'min']:
                        column_mapping[col] = 'low'
                    elif col_lower in ['close', '收盘', '收盘价']:
                        column_mapping[col] = 'close'
                    elif col_lower in ['volume', '成交量', 'vol']:
                        column_mapping[col] = 'volume'

                # 应用列名映射
                data = data.rename(columns=column_mapping)

                # 检查必需的列是否存在
                required_columns = ['open', 'high', 'low', 'close']
                missing_columns = [col for col in required_columns if col not in data.columns]

                if missing_columns:
                    print(f"  ❌ 缺少必需列: {missing_columns}")
                    continue

                # 如果没有成交量列，创建一个默认的
                if 'volume' not in data.columns:
                    data['volume'] = 1000000  # 默认成交量

                # 排序
                data = data.sort_index()

                # 筛选日期范围
                if start_date:
                    start_dt = pd.to_datetime(start_date)
                    data = data[data.index >= start_dt]

                if end_date:
                    end_dt = pd.to_datetime(end_date)
                    data = data[data.index <= end_dt]

                # 确保数据类型正确
                numeric_columns = ['open', 'high', 'low', 'close', 'volume']
                for col in numeric_columns:
                    if col in data.columns:
                        data[col] = pd.to_numeric(data[col], errors='coerce')

                # 删除包含NaN的行
                data = data.dropna()

                if len(data) > 0:
                    print(f"✅ AKShare获取成功 ({method_name})")
                    print(f"📅 数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                    print(f"📊 数据点数: {len(data):,}")
                    return data[['open', 'high', 'low', 'close', 'volume']]
                else:
                    print(f"  ❌ {method_name} 处理后数据为空")

            except Exception as e:
                print(f"  ❌ {method_name} 获取失败: {e}")
                continue

        print("❌ 所有AKShare方法都获取失败")
        return None

    def fetch_from_yfinance(self, start_date=None, end_date=None):
        """
        使用yfinance获取指数数据

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 获取的数据，如果失败返回None
        """
        print(f"🌐 尝试使用yfinance获取{self.index_config['name']}数据...")

        for ticker in self.index_config['yfinance_symbols']:
            try:
                print(f"  尝试代码: {ticker}")

                # 获取数据
                index_ticker = yf.Ticker(ticker)

                # 设置日期范围
                if not start_date:
                    start_date = "2020-01-01"  # 默认开始日期
                if not end_date:
                    end_date = datetime.now().strftime("%Y-%m-%d")

                data = index_ticker.history(start=start_date, end=end_date)

                if data.empty:
                    print(f"  ❌ {ticker} 返回空数据")
                    continue

                # 重命名列以保持一致性
                data = data.rename(columns={
                    'Open': 'open',
                    'Close': 'close',
                    'High': 'high',
                    'Low': 'low',
                    'Volume': 'volume'
                })

                # 删除包含NaN的行
                data = data.dropna()

                if len(data) > 0:
                    print(f"✅ yfinance获取成功 ({ticker})")
                    print(f"📅 数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                    print(f"📊 数据点数: {len(data):,}")
                    return data[['open', 'high', 'low', 'close', 'volume']]
                else:
                    print(f"  ❌ {ticker} 处理后数据为空")

            except Exception as e:
                print(f"  ❌ {ticker} 获取失败: {e}")
                continue

        print("❌ 所有yfinance代码都获取失败")
        return None

    def get_data(self, start_date=None, end_date=None, force_refresh=False, full_history=False):
        """
        获取指数数据（优先使用缓存）

        Args:
            start_date: 开始日期 (YYYY-MM-DD)
            end_date: 结束日期 (YYYY-MM-DD)
            force_refresh: 是否强制刷新数据
            full_history: 是否获取完整历史数据（从上市日期开始）

        Returns:
            pandas.DataFrame: 指数数据
        """
        print(f"📊 开始获取{self.index_config['name']}数据...")

        # 设置默认日期范围
        if full_history or start_date is None:
            if full_history:
                start_date = self.get_default_start_date()
                print(f"🕐 获取完整历史数据，从指数上市日期开始: {start_date}")
            else:
                start_date = (datetime.now() - timedelta(days=365*2)).strftime("%Y-%m-%d")  # 默认2年
        if end_date is None:
            end_date = datetime.now().strftime("%Y-%m-%d")

        print(f"📅 数据范围: {start_date} 至 {end_date}")

        # 检查缓存
        if not force_refresh and self.is_cache_valid():
            cached_data = self.load_from_cache()
            if cached_data is not None:
                # 如果指定了日期范围，筛选数据
                if start_date or end_date:
                    if start_date:
                        start_dt = pd.to_datetime(start_date)
                        cached_data = cached_data[cached_data.index >= start_dt]
                    if end_date:
                        end_dt = pd.to_datetime(end_date)
                        cached_data = cached_data[cached_data.index <= end_dt]

                if len(cached_data) > 0:
                    return cached_data

        # 尝试从AKShare获取数据
        data = self.fetch_from_akshare(start_date, end_date)

        # 如果AKShare失败，尝试yfinance
        if data is None:
            data = self.fetch_from_yfinance(start_date, end_date)

        # 如果都失败了，生成模拟数据
        if data is None:
            print("⚠️  所有数据源都失败，生成模拟数据用于演示...")
            data = self.generate_demo_data(start_date, end_date)

        # 保存到缓存
        if data is not None and not data.empty:
            self.save_to_cache(data)
            return data
        else:
            print("❌ 无法获取任何数据")
            return None

    def generate_demo_data(self, start_date=None, end_date=None):
        """
        生成模拟的指数数据用于演示

        Args:
            start_date: 开始日期
            end_date: 结束日期

        Returns:
            pandas.DataFrame: 模拟数据
        """
        print(f"🎭 正在生成模拟{self.index_config['name']}数据...")

        # 设置日期范围
        if not start_date:
            start_date = "2020-01-01"
        if not end_date:
            end_date = datetime.now().strftime("%Y-%m-%d")

        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)

        # 生成工作日日期
        date_range = pd.bdate_range(start=start_dt, end=end_dt)

        # 设置随机种子以确保可重复性
        np.random.seed(42)

        # 根据指数类型设置不同的模拟参数
        n_days = len(date_range)
        if self.index_type == 'CSI2000':
            base_price = 4000  # 中证2000基础价格
            daily_return_mean = 0.0003
            daily_return_std = 0.028  # 中证2000波动较大
        else:  # HSTECH
            base_price = 4000  # 恒生科技基础价格
            daily_return_mean = 0.0002
            daily_return_std = 0.035  # 恒生科技波动更大

        # 生成价格走势（带有趋势和波动）
        returns = np.random.normal(daily_return_mean, daily_return_std, n_days)

        # 添加一些趋势和周期性
        trend = np.linspace(-0.05, 0.15, n_days)  # 整体上升趋势
        cycle = 0.12 * np.sin(2 * np.pi * np.arange(n_days) / 252)  # 年度周期

        returns = returns + trend/n_days + cycle/n_days

        # 计算价格
        prices = [base_price]
        for i in range(1, n_days):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(new_price)

        prices = np.array(prices)

        # 生成OHLC数据
        data = pd.DataFrame(index=date_range)
        data['close'] = prices

        # 生成开盘价（基于前一日收盘价加上小幅波动）
        data['open'] = data['close'].shift(1) * (1 + np.random.normal(0, 0.008, n_days))
        data['open'].iloc[0] = base_price

        # 生成最高价和最低价
        daily_volatility = np.random.uniform(0.015, 0.05, n_days)
        data['high'] = np.maximum(data['open'], data['close']) * (1 + daily_volatility/2)
        data['low'] = np.minimum(data['open'], data['close']) * (1 - daily_volatility/2)

        # 生成成交量
        base_volume = 800000
        data['volume'] = np.random.lognormal(np.log(base_volume), 0.6, n_days)

        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close', 'volume']:
            data[col] = pd.to_numeric(data[col], errors='coerce')

        print(f"✅ 模拟数据生成成功，数据点数：{len(data)}")
        print(f"💰 价格范围：{data['close'].min():.2f} - {data['close'].max():.2f}")
        print(f"⚠️  注意：这是{self.index_config['name']}的模拟数据，仅用于演示")

        return data

    def get_basic_info(self):
        """
        获取指数基本信息

        Returns:
            dict: 指数基本信息
        """
        descriptions = {
            'CSI2000': '中证2000指数是由中证指数有限公司编制，反映A股市场中小盘股票的整体表现',
            'HSTECH': '恒生科技指数反映在香港上市的最大型科技公司股价表现，涵盖互联网、软件服务、电子商务等科技领域'
        }

        return {
            'name': self.index_config['name'],
            'code': self.index_config['index_code'],
            'akshare_symbol': self.index_config['akshare_symbol'],
            'yfinance_symbols': self.index_config['yfinance_symbols'],
            'market_type': self.index_config['market_type'],
            'description': descriptions.get(self.index_type, '指数信息')
        }

    def print_data_summary(self, data):
        """
        打印数据摘要信息

        Args:
            data: 数据DataFrame
        """
        if data is None or data.empty:
            print("❌ 无数据可显示")
            return

        print("\n" + "="*60)
        print(f"{self.index_config['name']}数据摘要")
        print("="*60)
        print(f"📅 数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
        print(f"📊 数据点数: {len(data):,}")
        print(f"💰 价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
        print(f"📈 期间涨幅: {((data['close'].iloc[-1] / data['close'].iloc[0]) - 1) * 100:.2f}%")
        print(f"📊 平均成交量: {data['volume'].mean():,.0f}")
        print()

        # 显示最近5天数据
        print("最近5个交易日数据:")
        print(data.tail().round(2))
        print("="*60)


def get_index_data(index_type="CSI2000", start_date=None, end_date=None, force_refresh=False, full_history=False, cache_dir="cache_data"):
    """
    便捷函数：获取指数数据

    Args:
        index_type: 指数类型 ("CSI2000" 或 "HSTECH")
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        force_refresh: 是否强制刷新数据
        full_history: 是否获取完整历史数据（从上市日期开始）
        cache_dir: 缓存目录

    Returns:
        pandas.DataFrame: 指数数据
    """
    fetcher = IndexDataFetcher(index_type=index_type, cache_dir=cache_dir)
    return fetcher.get_data(start_date=start_date, end_date=end_date, force_refresh=force_refresh, full_history=full_history)


def get_csi2000_data(start_date=None, end_date=None, force_refresh=False, full_history=False, cache_dir="cache_data"):
    """
    便捷函数：获取中证2000指数数据（保持向后兼容）

    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        force_refresh: 是否强制刷新数据
        full_history: 是否获取完整历史数据（从上市日期开始）
        cache_dir: 缓存目录

    Returns:
        pandas.DataFrame: 中证2000指数数据
    """
    return get_index_data("CSI2000", start_date, end_date, force_refresh, full_history, cache_dir)


def get_hstech_data(start_date=None, end_date=None, force_refresh=False, full_history=False, cache_dir="cache_data"):
    """
    便捷函数：获取恒生科技指数数据

    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        force_refresh: 是否强制刷新数据
        full_history: 是否获取完整历史数据（从上市日期开始）
        cache_dir: 缓存目录

    Returns:
        pandas.DataFrame: 恒生科技指数数据
    """
    return get_index_data("HSTECH", start_date, end_date, force_refresh, full_history, cache_dir)


def main():
    """
    主函数：演示如何获取中证2000和恒生科技指数完整历史数据
    """
    print("🚀 中证2000指数和恒生科技指数完整历史数据获取工具")
    print("="*60)

    print("📥 获取完整历史数据（从指数上市日期开始）")
    print()

    results = {}

    # 获取两个指数的数据
    for index_type in ['CSI2000', 'HSTECH']:
        print(f"{'='*20} {index_type} {'='*20}")

        # 创建数据获取器
        fetcher = IndexDataFetcher(index_type=index_type)

        # 显示基本信息
        info = fetcher.get_basic_info()
        print(f"📊 指数名称: {info['name']}")
        print(f"🔢 指数代码: {info['code']}")
        print(f"🌍 市场类型: {info['market_type']}")
        print(f"🕐 上市日期: {fetcher.get_default_start_date()}")
        print(f"📝 描述: {info['description']}")
        print()

        # 获取完整历史数据
        data = fetcher.get_data(full_history=True, force_refresh=True)

        if data is not None:
            # 显示数据摘要
            fetcher.print_data_summary(data)

            # 保存到CSV文件
            output_file = f"{index_type.lower()}_data_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
            data.to_csv(output_file)
            print(f"💾 数据已保存到: {output_file}")

            results[index_type] = data
        else:
            print("❌ 数据获取失败")
            results[index_type] = None

        print("\n")

    return results


if __name__ == "__main__":
    # 运行主函数
    results = main()

    # 如果成功获取数据，可以进行进一步分析
    successful_indices = [idx for idx, data in results.items() if data is not None]

    if successful_indices:
        print("\n🎯 数据获取成功！可以进行进一步分析...")
        print(f"✅ 成功获取的指数: {', '.join(successful_indices)}")
        print("\n💡 使用示例:")
        print("   - 计算收益率: data['daily_return'] = data['close'].pct_change()")
        print("   - 计算移动平均: data['ma20'] = data['close'].rolling(20).mean()")
        print("   - 技术分析: 可结合其他技术指标库进行分析")
        print("   - 比较分析: 可以比较两个指数的表现差异")

        # 如果两个指数都获取成功，显示简单的比较信息
        if len(successful_indices) == 2:
            csi2000_data = results['CSI2000']
            hstech_data = results['HSTECH']

            print("\n📈 简单比较分析:")
            if csi2000_data is not None and hstech_data is not None:
                csi2000_return = (csi2000_data['close'].iloc[-1] / csi2000_data['close'].iloc[0] - 1) * 100
                hstech_return = (hstech_data['close'].iloc[-1] / hstech_data['close'].iloc[0] - 1) * 100

                print(f"   - 中证2000期间涨幅: {csi2000_return:.2f}%")
                print(f"   - 恒生科技期间涨幅: {hstech_return:.2f}%")

                if csi2000_return > hstech_return:
                    print(f"   - 中证2000表现更好，超出恒生科技 {csi2000_return - hstech_return:.2f}%")
                else:
                    print(f"   - 恒生科技表现更好，超出中证2000 {hstech_return - csi2000_return:.2f}%")
    else:
        print("\n❌ 所有指数数据获取都失败了")
