"""
港股主要指数最近一个月行情图表脚本

功能：
- 获取恒生指数(HSI)、恒生科技指数(HSTECH)、恒生高股息指数(HSHDYI)最近1年的行情数据
- 绘制价格走势图、成交量图和对比分析图
- 显示涨跌幅统计信息
- 对比成长类指数(HSI、HSTECH)与价值类指数(HSHDYI)的表现

作者：AI Assistant
创建时间：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
import akshare as ak
from datetime import datetime, timedelta
import warnings
import signal
import sys
import os
import pickle
import hashlib
warnings.filterwarnings('ignore')

# 设置超时处理
def timeout_handler(signum, frame):
    raise TimeoutError("操作超时")

def with_timeout(timeout_seconds):
    def decorator(func):
        def wrapper(*args, **kwargs):
            # 设置信号处理器
            signal.signal(signal.SIGALRM, timeout_handler)
            signal.alarm(timeout_seconds)
            try:
                result = func(*args, **kwargs)
                signal.alarm(0)  # 取消超时
                return result
            except TimeoutError:
                print(f"  操作超时 ({timeout_seconds}秒)")
                return None
            finally:
                signal.alarm(0)  # 确保取消超时
        return wrapper
    return decorator

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HKIndicesDataFetcher:
    def __init__(self, cache_dir="cache"):
        """初始化数据获取器"""
        self.hsi_data = None      # 恒生指数
        self.hstech_data = None   # 恒生科技指数
        self.hshdyi_data = None   # 恒生高股息指数

        # 缓存设置
        self.cache_dir = cache_dir
        self.cache_expiry_hours = 24  # 缓存24小时后过期

        # 创建缓存目录
        if not os.path.exists(self.cache_dir):
            os.makedirs(self.cache_dir)

        # 计算最近1年的日期范围
        self.end_date = datetime.now()
        self.start_date = self.end_date - timedelta(days=365 + 30)  # 1年加上一些缓冲天数

        print(f"数据获取时间范围：{self.start_date.strftime('%Y-%m-%d')} 到 {self.end_date.strftime('%Y-%m-%d')}")
        print(f"缓存目录：{self.cache_dir}")

    def _get_cache_key(self, symbol, start_date, end_date):
        """生成缓存键"""
        key_string = f"{symbol}_{start_date.strftime('%Y%m%d')}_{end_date.strftime('%Y%m%d')}"
        return hashlib.md5(key_string.encode()).hexdigest()

    def _get_cache_path(self, cache_key):
        """获取缓存文件路径"""
        return os.path.join(self.cache_dir, f"{cache_key}.pkl")

    def _is_cache_valid(self, cache_path):
        """检查缓存是否有效"""
        if not os.path.exists(cache_path):
            return False

        # 检查文件修改时间
        file_time = datetime.fromtimestamp(os.path.getmtime(cache_path))
        expiry_time = datetime.now() - timedelta(hours=self.cache_expiry_hours)

        return file_time > expiry_time

    def _load_from_cache(self, cache_key):
        """从缓存加载数据"""
        cache_path = self._get_cache_path(cache_key)

        if self._is_cache_valid(cache_path):
            try:
                with open(cache_path, 'rb') as f:
                    data = pickle.load(f)
                print(f"  ✅ 从缓存加载数据，数据点数：{len(data)}")
                return data
            except Exception as e:
                print(f"  ⚠️ 缓存加载失败：{e}")
                return None

        return None

    def _save_to_cache(self, cache_key, data):
        """保存数据到缓存"""
        cache_path = self._get_cache_path(cache_key)

        try:
            with open(cache_path, 'wb') as f:
                pickle.dump(data, f)
            print(f"  💾 数据已保存到缓存")
        except Exception as e:
            print(f"  ⚠️ 缓存保存失败：{e}")

    def get_hsi_data(self):
        """获取恒生指数数据"""
        print("正在获取恒生指数(HSI)数据...")

        # 检查缓存
        cache_key = self._get_cache_key("HSI", self.start_date, self.end_date)
        cached_data = self._load_from_cache(cache_key)
        if cached_data is not None:
            self.hsi_data = cached_data
            return True

        try:
            # 方法1：尝试使用akshare获取恒生指数数据
            print("  尝试使用akshare获取数据...")
            hsi_data = ak.stock_hk_index_daily_em(symbol="HSI")

            if not hsi_data.empty:
                print(f"  原始数据列名: {list(hsi_data.columns)}")

                # 检查并重命名列
                column_mapping = {}
                for col in hsi_data.columns:
                    if '日期' in col or 'date' in col.lower():
                        column_mapping[col] = 'date'
                    elif '开盘' in col or 'open' in col.lower():
                        column_mapping[col] = 'open'
                    elif '收盘' in col or 'close' in col.lower() or 'latest' in col.lower():
                        column_mapping[col] = 'close'
                    elif '最高' in col or 'high' in col.lower():
                        column_mapping[col] = 'high'
                    elif '最低' in col or 'low' in col.lower():
                        column_mapping[col] = 'low'
                    elif '成交量' in col or 'volume' in col.lower():
                        column_mapping[col] = 'volume'

                print(f"  列名映射: {column_mapping}")
                hsi_data = hsi_data.rename(columns=column_mapping)

                # 设置日期索引
                if 'date' in hsi_data.columns:
                    hsi_data['date'] = pd.to_datetime(hsi_data['date'])
                    hsi_data.set_index('date', inplace=True)
                    hsi_data = hsi_data.sort_index()

                    # 筛选最近1年的数据
                    hsi_data = hsi_data[hsi_data.index >= self.start_date]

                    # 检查必要的列是否存在
                    required_cols = ['open', 'high', 'low', 'close']
                    available_cols = [col for col in required_cols if col in hsi_data.columns]

                    if len(available_cols) >= 4 and len(hsi_data) >= 50:
                        print(f"  akshare获取成功，数据点数：{len(hsi_data)}")
                        # 添加volume列如果不存在
                        if 'volume' not in hsi_data.columns:
                            hsi_data['volume'] = 0
                        self.hsi_data = hsi_data[['open', 'high', 'low', 'close', 'volume']].copy()
                        # 保存到缓存
                        self._save_to_cache(cache_key, self.hsi_data)
                        return True
                    else:
                        print(f"  数据列不完整或数据量不足，可用列：{available_cols}")
                else:
                    print("  未找到日期列")

        except Exception as e:
            print(f"  akshare获取失败：{e}")

        try:
            # 方法2：尝试使用yfinance获取恒生指数数据
            print("  尝试使用yfinance获取数据...")
            hsi_ticker = yf.Ticker("^HSI")
            data = hsi_ticker.history(
                start=self.start_date.strftime('%Y-%m-%d'),
                end=self.end_date.strftime('%Y-%m-%d')
            )

            if not data.empty and len(data) >= 10:
                print(f"  yfinance获取成功，数据点数：{len(data)}")
                # 重命名列以保持一致性
                data = data.rename(columns={
                    'Open': 'open',
                    'Close': 'close',
                    'High': 'high',
                    'Low': 'low',
                    'Volume': 'volume'
                })
                self.hsi_data = data[['open', 'high', 'low', 'close', 'volume']].copy()
                # 保存到缓存
                self._save_to_cache(cache_key, self.hsi_data)
                return True
            else:
                print("  yfinance数据量不足")

        except Exception as e:
            print(f"  yfinance获取失败：{e}")

        print("❌ 恒生指数数据获取失败")
        return False

    def get_hstech_data(self):
        """获取恒生科技指数数据"""
        print("正在获取恒生科技指数(HSTECH)数据...")

        # 检查缓存
        cache_key = self._get_cache_key("HSTECH", self.start_date, self.end_date)
        cached_data = self._load_from_cache(cache_key)
        if cached_data is not None:
            self.hstech_data = cached_data
            return True

        try:
            # 方法1：尝试使用akshare获取恒生科技指数数据
            print("  尝试使用akshare获取数据...")
            hstech_data = ak.stock_hk_index_daily_sina(symbol="HSTECH")

            if not hstech_data.empty:
                # 重命名列（sina接口的列名可能不同）
                hstech_data = hstech_data.rename(columns={
                    'date': 'date',
                    'open': 'open',
                    'close': 'close',
                    'high': 'high',
                    'low': 'low',
                    'volume': 'volume'
                })

                # 设置日期索引
                hstech_data['date'] = pd.to_datetime(hstech_data['date'])
                hstech_data.set_index('date', inplace=True)
                hstech_data = hstech_data.sort_index()

                # 筛选最近1年的数据
                hstech_data = hstech_data[hstech_data.index >= self.start_date]

                if len(hstech_data) >= 50:  # 至少50个交易日
                    print(f"  akshare获取成功，数据点数：{len(hstech_data)}")
                    self.hstech_data = hstech_data[['open', 'high', 'low', 'close', 'volume']].copy()
                    # 保存到缓存
                    self._save_to_cache(cache_key, self.hstech_data)
                    return True
                else:
                    print("  akshare数据量不足")

        except Exception as e:
            print(f"  akshare获取失败：{e}")

        try:
            # 方法2：尝试使用yfinance获取恒生科技指数数据
            print("  尝试使用yfinance获取数据...")
            hstech_ticker = yf.Ticker("^HSTECH")
            data = hstech_ticker.history(
                start=self.start_date.strftime('%Y-%m-%d'),
                end=self.end_date.strftime('%Y-%m-%d')
            )

            if not data.empty and len(data) >= 50:
                print(f"  yfinance获取成功，数据点数：{len(data)}")
                # 重命名列以保持一致性
                data = data.rename(columns={
                    'Open': 'open',
                    'Close': 'close',
                    'High': 'high',
                    'Low': 'low',
                    'Volume': 'volume'
                })
                self.hstech_data = data[['open', 'high', 'low', 'close', 'volume']].copy()
                # 保存到缓存
                self._save_to_cache(cache_key, self.hstech_data)
                return True
            else:
                print("  yfinance数据量不足")

        except Exception as e:
            print(f"  yfinance获取失败：{e}")

        print("❌ 恒生科技指数数据获取失败")
        return False


    def get_hshdyi_data(self):
        """获取恒生高股息指数数据"""
        print("正在获取恒生高股息指数(HSHDYI)数据...")

        # 检查缓存
        cache_key = self._get_cache_key("HSHDYI", self.start_date, self.end_date)
        cached_data = self._load_from_cache(cache_key)
        if cached_data is not None:
            self.hshdyi_data = cached_data
            return True

        try:
            # 使用akshare获取恒生高股息指数数据（东方财富）
            print("  尝试使用akshare从东方财富获取数据...")
            hshdyi_data = ak.stock_hk_index_daily_em(symbol="HSHDYI")

            if hshdyi_data is not None and not hshdyi_data.empty:
                print(f"  原始数据形状: {hshdyi_data.shape}")
                print(f"  原始数据列名: {list(hshdyi_data.columns)}")

                # 处理列名映射
                column_mapping = {}
                for col in hshdyi_data.columns:
                    col_lower = str(col).lower()
                    if '日期' in str(col) or 'date' in col_lower:
                        column_mapping[col] = 'date'
                    elif '开盘' in str(col) or 'open' in col_lower:
                        column_mapping[col] = 'open'
                    elif '收盘' in str(col) or 'close' in col_lower or 'latest' in col_lower:
                        column_mapping[col] = 'close'
                    elif '最高' in str(col) or 'high' in col_lower:
                        column_mapping[col] = 'high'
                    elif '最低' in str(col) or 'low' in col_lower:
                        column_mapping[col] = 'low'
                    elif '成交量' in str(col) or 'volume' in col_lower:
                        column_mapping[col] = 'volume'

                print(f"  列名映射: {column_mapping}")

                # 重命名列
                hshdyi_data = hshdyi_data.rename(columns=column_mapping)

                # 处理日期
                if 'date' in hshdyi_data.columns:
                    hshdyi_data['date'] = pd.to_datetime(hshdyi_data['date'])
                    hshdyi_data.set_index('date', inplace=True)
                    hshdyi_data = hshdyi_data.sort_index()

                    # 筛选最近1年的数据
                    hshdyi_data = hshdyi_data[hshdyi_data.index >= self.start_date]

                    # 检查必要的列
                    required_cols = ['open', 'high', 'low', 'close']
                    available_cols = [col for col in required_cols if col in hshdyi_data.columns]

                    if len(available_cols) >= 4 and len(hshdyi_data) >= 50:
                        print(f"  ✅ 获取成功，数据点数：{len(hshdyi_data)}")

                        # 添加volume列如果不存在
                        if 'volume' not in hshdyi_data.columns:
                            hshdyi_data['volume'] = 0

                        # 确保数据类型正确
                        for col in ['open', 'high', 'low', 'close']:
                            if col in hshdyi_data.columns:
                                hshdyi_data[col] = pd.to_numeric(hshdyi_data[col], errors='coerce')

                        # 去除空值
                        hshdyi_data = hshdyi_data.dropna(subset=['close'])

                        if len(hshdyi_data) >= 50:
                            self.hshdyi_data = hshdyi_data[['open', 'high', 'low', 'close', 'volume']].copy()
                            # 保存到缓存
                            self._save_to_cache(cache_key, self.hshdyi_data)
                            return True
                        else:
                            print(f"  ❌ 清理后数据量不足：{len(hshdyi_data)}")
                    else:
                        print(f"  ❌ 数据列不完整，可用列：{available_cols}")
                else:
                    print("  ❌ 未找到日期列")
            else:
                print("  ❌ 获取的数据为空")

        except Exception as e:
            print(f"  ❌ 获取失败：{str(e)}")

        print("❌ 恒生高股息指数数据获取失败")
        return False




    def get_all_data(self):
        """获取所有数据"""
        print("🚀 开始获取港股主要指数数据")
        print("="*60)

        hsi_success = self.get_hsi_data()
        hstech_success = self.get_hstech_data()
        hshdyi_success = self.get_hshdyi_data()

        success_count = sum([hsi_success, hstech_success, hshdyi_success])

        if success_count >= 2:
            print(f"\n✅ 大部分数据获取成功！({success_count}/3)")
            return True
        elif success_count == 1:
            print(f"\n⚠️ 部分数据获取成功 ({success_count}/3)")
            return True
        else:
            print(f"\n❌ 数据获取失败 ({success_count}/3)")
            return False

class HKIndicesChartPlotter:
    def __init__(self, data_fetcher):
        """初始化图表绘制器"""
        self.data_fetcher = data_fetcher
        self.hsi_data = data_fetcher.hsi_data
        self.hstech_data = data_fetcher.hstech_data
        self.hshdyi_data = data_fetcher.hshdyi_data

    def calculate_statistics(self):
        """计算统计信息"""
        stats = {}

        # 定义指数信息
        indices_info = {
            'HSI': {'data': self.hsi_data, 'name': '恒生指数', 'type': 'growth'},
            'HSTECH': {'data': self.hstech_data, 'name': '恒生科技指数', 'type': 'growth'},
            'HSHDYI': {'data': self.hshdyi_data, 'name': '恒生高股息指数', 'type': 'value'}
        }

        for index_code, info in indices_info.items():
            data = info['data']
            if data is not None and len(data) > 0:
                start_price = data['close'].iloc[0]
                end_price = data['close'].iloc[-1]
                return_pct = (end_price - start_price) / start_price * 100
                volatility = data['close'].pct_change().std() * np.sqrt(252) * 100

                stats[index_code] = {
                    'name': info['name'],
                    'type': info['type'],
                    'start_price': start_price,
                    'end_price': end_price,
                    'return': return_pct,
                    'volatility': volatility,
                    'max_price': data['high'].max(),
                    'min_price': data['low'].min()
                }

        return stats

    def print_statistics(self):
        """打印统计信息"""
        stats = self.calculate_statistics()

        print("\n" + "="*80)
        print("📊 港股主要指数最近1年行情统计")
        print("="*80)

        # 按类型分组显示
        growth_indices = [k for k, v in stats.items() if v['type'] == 'growth']
        value_indices = [k for k, v in stats.items() if v['type'] == 'value']

        if growth_indices:
            print("\n🚀 成长类指数:")
            print("-" * 50)
            for index_code in growth_indices:
                data = stats[index_code]
                print(f"\n{data['name']} ({index_code}):")
                print(f"  期初价格: {data['start_price']:.2f}")
                print(f"  期末价格: {data['end_price']:.2f}")
                print(f"  涨跌幅: {data['return']:+.2f}%")
                print(f"  最高价: {data['max_price']:.2f}")
                print(f"  最低价: {data['min_price']:.2f}")
                print(f"  年化波动率: {data['volatility']:.2f}%")

        if value_indices:
            print("\n💰 价值类指数:")
            print("-" * 50)
            for index_code in value_indices:
                data = stats[index_code]
                print(f"\n{data['name']} ({index_code}):")
                print(f"  期初价格: {data['start_price']:.2f}")
                print(f"  期末价格: {data['end_price']:.2f}")
                print(f"  涨跌幅: {data['return']:+.2f}%")
                print(f"  最高价: {data['max_price']:.2f}")
                print(f"  最低价: {data['min_price']:.2f}")
                print(f"  年化波动率: {data['volatility']:.2f}%")

        # 显示排名
        if len(stats) > 1:
            print("\n🏆 涨跌幅排名:")
            print("-" * 50)
            sorted_stats = sorted(stats.items(), key=lambda x: x[1]['return'], reverse=True)
            for i, (index_code, data) in enumerate(sorted_stats, 1):
                emoji = "🥇" if i == 1 else "🥈" if i == 2 else "🥉" if i == 3 else "📊"
                print(f"  {emoji} {i}. {data['name']}: {data['return']:+.2f}%")

    def plot_price_charts(self):
        """绘制价格走势图"""
        # 检查是否有可用数据
        available_data = {}
        if self.hsi_data is not None:
            available_data['HSI'] = {'data': self.hsi_data, 'name': '恒生指数', 'color': 'red'}
        if self.hstech_data is not None:
            available_data['HSTECH'] = {'data': self.hstech_data, 'name': '恒生科技指数', 'color': 'blue'}
        if self.hshdyi_data is not None:
            available_data['HSHDYI'] = {'data': self.hshdyi_data, 'name': '恒生高股息指数', 'color': 'orange'}

        if not available_data:
            print("❌ 没有可用数据进行绘图")
            return

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('港股主要指数 - 最近1年行情对比', fontsize=16, fontweight='bold')

        # 1. 标准化价格对比图（以期初价格为100）
        ax1 = axes[0, 0]
        for index_code, info in available_data.items():
            data = info['data']
            normalized = (data['close'] / data['close'].iloc[0]) * 100
            ax1.plot(data.index, normalized,
                    label=info['name'],
                    color=info['color'],
                    linewidth=2)

        ax1.set_title('标准化价格对比 (期初=100)')
        ax1.set_ylabel('标准化价格')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        ax1.axhline(y=100, color='gray', linestyle='--', alpha=0.5)

        # 2. 价格走势对比图
        ax2 = axes[0, 1]
        for index_code, info in available_data.items():
            data = info['data']
            ax2.plot(data.index, data['close'],
                    label=info['name'],
                    color=info['color'],
                    linewidth=2)

        ax2.set_title('价格走势对比')
        ax2.set_ylabel('价格')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 涨跌幅对比
        ax3 = axes[1, 0]
        stats = self.calculate_statistics()
        if stats:
            indices = list(stats.keys())
            returns = [stats[idx]['return'] for idx in indices]
            names = [stats[idx]['name'] for idx in indices]
            colors_list = [available_data.get(idx, {}).get('color', 'gray') for idx in indices]

            bars = ax3.bar(range(len(indices)), returns, color=colors_list, alpha=0.7)
            ax3.set_title('涨跌幅对比')
            ax3.set_ylabel('涨跌幅 (%)')
            ax3.set_xticks(range(len(indices)))
            ax3.set_xticklabels([name.replace('指数', '') for name in names], rotation=45)
            ax3.axhline(y=0, color='black', linestyle='-', alpha=0.3)

            # 添加数值标签
            for bar, ret in zip(bars, returns):
                height = bar.get_height()
                ax3.text(bar.get_x() + bar.get_width()/2.,
                        height + (0.1 if height >= 0 else -0.3),
                        f'{ret:+.1f}%', ha='center', va='bottom' if height >= 0 else 'top')

        ax3.grid(True, alpha=0.3)

        # 4. 波动率对比
        ax4 = axes[1, 1]
        if stats:
            indices = list(stats.keys())
            volatilities = [stats[idx]['volatility'] for idx in indices]
            names = [stats[idx]['name'] for idx in indices]
            colors_list = [available_data.get(idx, {}).get('color', 'gray') for idx in indices]

            bars = ax4.bar(range(len(indices)), volatilities, color=colors_list, alpha=0.7)
            ax4.set_title('年化波动率对比')
            ax4.set_ylabel('年化波动率 (%)')
            ax4.set_xticks(range(len(indices)))
            ax4.set_xticklabels([name.replace('指数', '') for name in names], rotation=45)

            # 添加数值标签
            for bar, vol in zip(bars, volatilities):
                height = bar.get_height()
                ax4.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                        f'{vol:.1f}%', ha='center', va='bottom')

        ax4.grid(True, alpha=0.3)

        # 调整布局
        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"hk_indices_chart_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 图表已保存至: {filename}")

        plt.show()

    def plot_candlestick_charts(self):
        """绘制K线图"""
        try:
            import mplfinance as mpf
        except ImportError:
            print("⚠️ 需要安装mplfinance库来绘制K线图: pip install mplfinance")
            return

        # 检查是否有可用数据
        available_data = []
        if self.hsi_data is not None:
            available_data.append(('HSI', self.hsi_data, '恒生指数'))
        if self.hstech_data is not None:
            available_data.append(('HSTECH', self.hstech_data, '恒生科技指数'))
        if self.hshdyi_data is not None:
            available_data.append(('HSHDYI', self.hshdyi_data, '恒生高股息指数'))

        if not available_data:
            print("❌ 没有可用数据进行K线图绘制")
            return

        # 绘制每个指数的K线图
        for index_code, data, name in available_data:
            print(f"正在绘制{name}K线图...")
            plot_data = data.copy()
            plot_data.columns = ['Open', 'High', 'Low', 'Close', 'Volume']

            mpf.plot(plot_data,
                    type='candle',
                    style='charles',
                    title=f'{name} - 最近1年K线图',
                    ylabel='价格',
                    volume=True,
                    savefig=f'{index_code.lower()}_candlestick_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png')

def main():
    """主函数"""
    print("🚀 港股主要指数最近1年行情分析")
    print("="*80)

    # 创建数据获取器
    data_fetcher = HKIndicesDataFetcher()

    # 获取数据
    if not data_fetcher.get_all_data():
        print("❌ 数据获取失败，程序退出")
        return

    # 创建图表绘制器
    chart_plotter = HKIndicesChartPlotter(data_fetcher)

    # 打印统计信息
    chart_plotter.print_statistics()

    # 绘制图表
    print("\n正在绘制图表...")
    chart_plotter.plot_price_charts()

    # 尝试绘制K线图
    try:
        chart_plotter.plot_candlestick_charts()
    except Exception as e:
        print(f"K线图绘制失败: {e}")

    print("\n🎉 分析完成！")
    print("📊 请查看生成的图表文件")

if __name__ == "__main__":
    main()
