#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子有效性验证（纯做多版本）

基于 hsi_individual_dividend_yields 目录下的真实股息率和股价数据，
验证股息率因子在纯做多策略中的有效性。
"""

import os
import csv
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIDividendFactorLongOnlyValidator:
    """恒生指数股息率因子验证器（纯做多版本）"""
    
    def __init__(self, data_dir: str = "hsi_individual_dividend_yields", rebalance_freq: str = 'Q'):
        """
        初始化验证器
        
        Args:
            data_dir: 股息率数据目录
            rebalance_freq: 再平衡频率 ('M'=月度, 'Q'=季度, 'H'=半年, 'Y'=年度)
        """
        self.data_dir = data_dir
        self.stock_data = {}
        self.summary_data = {}
        self.backtest_results = {}
        
        # 回测参数
        self.rebalance_freq = rebalance_freq
        self.lookback_days = 252   # 1年回看期
        
        # 根据再平衡频率调整前瞻期
        if rebalance_freq == 'M':
            self.forward_days = 21     # 1个月前瞻期
        elif rebalance_freq == 'Q':
            self.forward_days = 63     # 3个月前瞻期
        elif rebalance_freq == 'H':
            self.forward_days = 126    # 6个月前瞻期
        else:  # 'Y'
            self.forward_days = 252    # 1年前瞻期
        
        freq_names = {'M': '月度', 'Q': '季度', 'H': '半年度', 'Y': '年度'}
        print("🚀 恒生指数股息率因子验证器已初始化（纯做多版本）")
        print(f"📁 数据目录: {data_dir}")
        print(f"🔄 再平衡频率: {freq_names.get(rebalance_freq, rebalance_freq)}")
    
    def load_stock_data(self) -> bool:
        """加载所有股票的股息率和股价数据"""
        try:
            print("📊 加载股票数据...")
            
            # 加载各股票的详细数据
            loaded_count = 0
            for filename in os.listdir(self.data_dir):
                if filename.endswith('_dividend_yield_20250603_175937.csv') or \
                   filename.endswith('_dividend_yield_20250603_175938.csv') or \
                   filename.endswith('_dividend_yield_20250603_175939.csv') or \
                   filename.endswith('_dividend_yield_20250603_175940.csv') or \
                   filename.endswith('_dividend_yield_20250603_175941.csv') or \
                   filename.endswith('_dividend_yield_20250603_175942.csv') or \
                   filename.endswith('_dividend_yield_20250603_175943.csv') or \
                   filename.endswith('_dividend_yield_20250603_175944.csv') or \
                   filename.endswith('_dividend_yield_20250603_175945.csv') or \
                   filename.endswith('_dividend_yield_20250603_175946.csv') or \
                   filename.endswith('_dividend_yield_20250603_175947.csv') or \
                   filename.endswith('_dividend_yield_20250603_175948.csv') or \
                   filename.endswith('_dividend_yield_20250603_175949.csv'):
                    
                    # 提取股票代码和名称
                    parts = filename.split('_')
                    if len(parts) >= 3:
                        stock_code = parts[0]
                        stock_name = parts[1]
                        
                        # 加载股票数据
                        file_path = os.path.join(self.data_dir, filename)
                        stock_df = self._load_stock_file(file_path, stock_code, stock_name)
                        
                        if stock_df is not None and len(stock_df) > 0:
                            self.stock_data[stock_code] = {
                                'name': stock_name,
                                'data': stock_df
                            }
                            loaded_count += 1
            
            print(f"✅ 成功加载 {loaded_count} 只股票的详细数据")
            
            if loaded_count == 0:
                print("❌ 未找到有效的股票数据文件")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 加载股票数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def _load_stock_file(self, file_path: str, stock_code: str, stock_name: str) -> Optional[pd.DataFrame]:
        """加载单个股票文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8')
            
            # 检查必要的列
            required_cols = ['日期', '股价(港元)', '股息率(%)', '滚动12月分红总额(港元)']
            if not all(col in df.columns for col in required_cols):
                print(f"⚠️  {stock_code} 数据格式不正确，跳过")
                return None
            
            # 处理数据
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.sort_values('日期')
            df.set_index('日期', inplace=True)
            
            # 清理数据
            df = df.dropna(subset=['股价(港元)', '股息率(%)'])
            
            # 计算收益率
            df['price'] = df['股价(港元)']
            df['dividend_yield'] = df['股息率(%)']
            df['return_1d'] = df['price'].pct_change()
            df['return_5d'] = df['price'].pct_change(5)
            df['return_20d'] = df['price'].pct_change(20)
            df['return_60d'] = df['price'].pct_change(60)
            
            return df
            
        except Exception as e:
            print(f"⚠️  加载 {stock_code} 数据失败: {e}")
            return None
    
    def calculate_factor_scores(self, date: datetime) -> pd.Series:
        """计算指定日期的股息率因子得分"""
        scores = {}
        
        for stock_code, stock_info in self.stock_data.items():
            df = stock_info['data']
            
            # 找到指定日期之前的最新数据
            available_dates = df.index[df.index <= date]
            if len(available_dates) > 0:
                latest_date = available_dates[-1]
                
                # 获取股息率作为因子得分
                dividend_yield = df.loc[latest_date, 'dividend_yield']
                
                # 确保股息率有效
                if pd.notna(dividend_yield) and dividend_yield > 0:
                    scores[stock_code] = dividend_yield
        
        return pd.Series(scores)
    
    def create_long_only_portfolios(self, factor_scores: pd.Series) -> Dict[str, List[str]]:
        """创建纯做多投资组合"""
        if len(factor_scores) == 0:
            return {}
        
        # 按因子得分排序（降序，高股息率在前）
        sorted_scores = factor_scores.sort_values(ascending=False)
        
        portfolios = {}
        
        # 创建不同的纯做多策略
        total_stocks = len(sorted_scores)
        
        # 1. 高股息率组合（前20%）
        top_20_count = max(1, int(total_stocks * 0.2))
        portfolios['高股息率组合'] = sorted_scores.head(top_20_count).index.tolist()
        
        # 2. 中高股息率组合（前40%）
        top_40_count = max(1, int(total_stocks * 0.4))
        portfolios['中高股息率组合'] = sorted_scores.head(top_40_count).index.tolist()
        
        # 3. 全市场组合（所有股票等权重）
        portfolios['全市场组合'] = sorted_scores.index.tolist()
        
        # 4. 恒生指数基准（模拟，使用所有股票）
        portfolios['恒生指数基准'] = sorted_scores.index.tolist()
        
        return portfolios
    
    def calculate_portfolio_returns(self, portfolios: Dict[str, List[str]], 
                                  start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """计算投资组合在指定期间的收益率"""
        portfolio_returns = {}
        
        for portfolio_name, stocks in portfolios.items():
            if not stocks:
                continue
            
            stock_returns = []
            
            for stock_code in stocks:
                if stock_code in self.stock_data:
                    df = self.stock_data[stock_code]['data']
                    
                    # 获取期间数据
                    period_data = df[(df.index >= start_date) & (df.index <= end_date)]
                    
                    if len(period_data) >= 2:
                        # 计算期间收益率
                        start_price = period_data['price'].iloc[0]
                        end_price = period_data['price'].iloc[-1]
                        
                        if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                            stock_return = (end_price - start_price) / start_price
                            stock_returns.append(stock_return)
            
            # 计算组合平均收益率（等权重）
            if stock_returns:
                portfolio_returns[portfolio_name] = np.mean(stock_returns)
            else:
                portfolio_returns[portfolio_name] = 0.0
        
        return portfolio_returns
    
    def get_rebalance_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取再平衡日期"""
        dates = []
        current_date = start_date
        
        while current_date <= end_date:
            dates.append(current_date)
            
            # 根据再平衡频率确定下一个日期
            if self.rebalance_freq == 'M':  # 月度
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1, day=1)
            elif self.rebalance_freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_month = 6
                elif current_date.month <= 6:
                    next_month = 9
                elif current_date.month <= 9:
                    next_month = 12
                else:
                    next_month = 3
                    current_date = current_date.replace(year=current_date.year + 1)
                
                if next_month <= 12:
                    current_date = current_date.replace(month=next_month, day=1)
            elif self.rebalance_freq == 'H':  # 半年度
                if current_date.month <= 6:
                    current_date = current_date.replace(month=12, day=1)
                else:
                    current_date = current_date.replace(year=current_date.year + 1, month=6, day=1)
            else:  # 年度
                current_date = current_date.replace(year=current_date.year + 1)
        
        return dates

    def run_backtest(self) -> bool:
        """运行股息率因子回测（纯做多）"""
        try:
            print("🔄 开始股息率因子回测（纯做多）...")

            # 确定回测期间
            all_dates = set()
            for stock_info in self.stock_data.values():
                all_dates.update(stock_info['data'].index)

            if not all_dates:
                print("❌ 没有可用的日期数据")
                return False

            start_date = min(all_dates) + timedelta(days=self.lookback_days)
            end_date = max(all_dates) - timedelta(days=self.forward_days)

            print(f"📅 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

            # 获取再平衡日期
            rebalance_dates = self.get_rebalance_dates(start_date, end_date)
            print(f"🔄 再平衡次数: {len(rebalance_dates)}")

            # 存储所有组合的收益率
            all_portfolio_returns = defaultdict(list)
            all_factor_scores = []

            for i, rebalance_date in enumerate(rebalance_dates):
                print(f"🔄 再平衡 {i+1}/{len(rebalance_dates)}: {rebalance_date.strftime('%Y-%m-%d')}")

                # 计算因子得分
                factor_scores = self.calculate_factor_scores(rebalance_date)
                if factor_scores.empty:
                    continue

                all_factor_scores.append(factor_scores)

                # 创建投资组合
                portfolios = self.create_long_only_portfolios(factor_scores)

                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    hold_end = rebalance_dates[i + 1]
                else:
                    hold_end = end_date

                # 计算期间收益
                period_returns = self.calculate_portfolio_returns(
                    portfolios, rebalance_date, hold_end
                )

                # 累积收益
                for portfolio_name, returns in period_returns.items():
                    all_portfolio_returns[portfolio_name].append(returns)

            # 保存回测结果
            self.backtest_results = {
                'portfolio_returns': dict(all_portfolio_returns),
                'factor_scores': all_factor_scores,
                'rebalance_dates': rebalance_dates,
                'start_date': start_date,
                'end_date': end_date
            }

            print("✅ 回测完成")
            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def analyze_factor_performance(self) -> Dict:
        """分析因子表现"""
        if not self.backtest_results:
            print("❌ 请先运行回测")
            return {}

        print("📊 分析因子表现...")

        portfolio_returns = self.backtest_results['portfolio_returns']
        analysis_results = {}

        # 计算各组合的统计指标
        for portfolio_name, returns in portfolio_returns.items():
            if not returns:
                continue

            returns_array = np.array(returns)

            # 基本统计
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array)
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0

            # 累积收益
            cumulative_return = np.prod(1 + returns_array) - 1

            # 年化收益率
            if self.rebalance_freq == 'M':
                periods_per_year = 12
            elif self.rebalance_freq == 'Q':
                periods_per_year = 4
            elif self.rebalance_freq == 'H':
                periods_per_year = 2
            else:  # 'Y'
                periods_per_year = 1

            annual_return = (1 + mean_return) ** periods_per_year - 1

            # 最大回撤
            cumulative_returns = np.cumprod(1 + returns_array)
            running_max = np.maximum.accumulate(cumulative_returns)
            drawdowns = (cumulative_returns - running_max) / running_max
            max_drawdown = np.min(drawdowns)

            # 胜率
            win_rate = np.sum(returns_array > 0) / len(returns_array)

            analysis_results[portfolio_name] = {
                'mean_return': mean_return,
                'std_return': std_return,
                'sharpe_ratio': sharpe_ratio,
                'cumulative_return': cumulative_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_periods': len(returns)
            }

        return analysis_results

    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数（IC）"""
        if not self.backtest_results:
            return {}

        print("📈 计算信息系数...")

        factor_scores = self.backtest_results['factor_scores']
        ic_values = []

        for i, scores in enumerate(factor_scores):
            if i >= len(factor_scores) - 1:
                break

            # 获取下期收益率
            next_period_returns = {}
            rebalance_date = self.backtest_results['rebalance_dates'][i]
            next_date = self.backtest_results['rebalance_dates'][i + 1] if i + 1 < len(self.backtest_results['rebalance_dates']) else self.backtest_results['end_date']

            for stock_code in scores.index:
                if stock_code in self.stock_data:
                    df = self.stock_data[stock_code]['data']
                    period_data = df[(df.index >= rebalance_date) & (df.index <= next_date)]

                    if len(period_data) >= 2:
                        start_price = period_data['price'].iloc[0]
                        end_price = period_data['price'].iloc[-1]

                        if pd.notna(start_price) and pd.notna(end_price) and start_price > 0:
                            stock_return = (end_price - start_price) / start_price
                            next_period_returns[stock_code] = stock_return

            # 计算IC
            if len(next_period_returns) >= 5:  # 至少需要5只股票
                factor_values = []
                return_values = []

                for stock_code in scores.index:
                    if stock_code in next_period_returns:
                        factor_values.append(scores[stock_code])
                        return_values.append(next_period_returns[stock_code])

                if len(factor_values) >= 5:
                    ic = np.corrcoef(factor_values, return_values)[0, 1]
                    if not np.isnan(ic):
                        ic_values.append(ic)

        if ic_values:
            return {
                'ic_mean': np.mean(ic_values),
                'ic_std': np.std(ic_values),
                'ic_ir': np.mean(ic_values) / np.std(ic_values) if np.std(ic_values) > 0 else 0,
                'ic_values': ic_values,
                'ic_positive_rate': np.sum(np.array(ic_values) > 0) / len(ic_values)
            }
        else:
            return {}

    def generate_report(self, analysis_results: Dict, ic_results: Dict):
        """生成分析报告"""
        print("\n" + "="*80)
        print("恒生指数成分股股息率因子有效性验证报告（纯做多版本）")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: {self.data_dir}")
        print(f"分析股票数: {len(self.stock_data)}")
        print(f"投资策略: 纯做多（不允许做空）")
        print()

        # 回测基本信息
        if self.backtest_results:
            print("回测基本信息")
            print("-"*40)
            print(f"回测期间: {self.backtest_results['start_date'].strftime('%Y-%m-%d')} 至 {self.backtest_results['end_date'].strftime('%Y-%m-%d')}")
            print(f"再平衡频率: {self.rebalance_freq}")
            print(f"再平衡次数: {len(self.backtest_results['rebalance_dates'])}")
            print()

        # 组合表现分析
        if analysis_results:
            print("投资组合表现分析")
            print("-"*90)
            print(f"{'组合名称':<15} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'最大回撤':<8} {'胜率':<8}")
            print("-"*90)

            # 按预期顺序显示组合
            portfolio_order = ['高股息率组合', '中高股息率组合', '全市场组合', '恒生指数基准']

            for portfolio_name in portfolio_order:
                if portfolio_name in analysis_results:
                    perf = analysis_results[portfolio_name]
                    print(f"{portfolio_name:<15} {perf['annual_return']*100:<10.2f}% "
                          f"{perf['std_return']*100:<8.2f}% {perf['sharpe_ratio']:<8.2f} "
                          f"{abs(perf['max_drawdown'])*100:<8.2f}% {perf['win_rate']*100:<8.1f}%")
            print()

        # 股息率因子有效性分析
        if '高股息率组合' in analysis_results and '全市场组合' in analysis_results:
            high_div = analysis_results['高股息率组合']
            market = analysis_results['全市场组合']

            return_outperformance = high_div['annual_return'] - market['annual_return']
            sharpe_outperformance = high_div['sharpe_ratio'] - market['sharpe_ratio']

            print("股息率因子有效性分析")
            print("-"*40)
            print(f"高股息率组合年化收益: {high_div['annual_return']*100:.2f}%")
            print(f"全市场组合年化收益: {market['annual_return']*100:.2f}%")
            print(f"超额收益: {return_outperformance*100:.2f}%")
            print(f"夏普比率提升: {sharpe_outperformance:.2f}")
            print()

            if return_outperformance > 0:
                print("✅ 股息率因子在纯做多策略中表现正向：高股息率组合跑赢市场")
            else:
                print("❌ 股息率因子在纯做多策略中表现负向：高股息率组合跑输市场")
            print()

        # 信息系数分析
        if ic_results:
            print("信息系数分析")
            print("-"*40)
            print(f"平均IC: {ic_results['ic_mean']:.4f}")
            print(f"IC标准差: {ic_results['ic_std']:.4f}")
            print(f"IC信息比率: {ic_results['ic_ir']:.4f}")
            print(f"IC正值比例: {ic_results['ic_positive_rate']*100:.1f}%")
            print()

            # IC有效性判断
            if abs(ic_results['ic_mean']) > 0.02 and ic_results['ic_positive_rate'] > 0.5:
                print("✅ 因子具有较强的预测能力")
            elif abs(ic_results['ic_mean']) > 0.01:
                print("⚠️  因子具有一定的预测能力")
            else:
                print("❌ 因子预测能力较弱")
            print()

        # 实际投资建议
        print("实际投资建议（纯做多策略）")
        print("-"*40)

        if '高股息率组合' in analysis_results:
            high_div_perf = analysis_results['高股息率组合']
            print(f"💡 建议构建高股息率投资组合:")
            print(f"   - 选择股息率排名前20%的恒生指数成分股")
            print(f"   - 预期年化收益: {high_div_perf['annual_return']*100:.2f}%")
            print(f"   - 夏普比率: {high_div_perf['sharpe_ratio']:.2f}")
            print(f"   - 最大回撤控制在: {abs(high_div_perf['max_drawdown'])*100:.2f}%以内")
            print(f"   - 胜率: {high_div_perf['win_rate']*100:.1f}%")
            print()

            if high_div_perf['annual_return'] > 0.05:  # 年化收益超过5%
                print("✅ 该策略具有良好的投资价值")
            else:
                print("⚠️  该策略收益有限，需要谨慎考虑")

        print("="*80)

    def create_visualizations(self, analysis_results: Dict, ic_results: Dict):
        """创建可视化图表"""
        try:
            print("📊 生成可视化图表...")

            # 创建图表
            fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
            fig.suptitle('恒生指数股息率因子有效性验证（纯做多策略）', fontsize=16, fontweight='bold')

            # 1. 组合累积收益图
            if self.backtest_results and analysis_results:
                portfolio_returns = self.backtest_results['portfolio_returns']

                portfolio_order = ['高股息率组合', '中高股息率组合', '全市场组合', '恒生指数基准']
                colors = ['red', 'orange', 'blue', 'gray']

                for i, portfolio_name in enumerate(portfolio_order):
                    if portfolio_name in portfolio_returns:
                        returns = portfolio_returns[portfolio_name]
                        cumulative_returns = np.cumprod(1 + np.array(returns))
                        ax1.plot(range(len(cumulative_returns)), cumulative_returns,
                                label=portfolio_name, linewidth=2, color=colors[i])

                ax1.set_title('投资组合累积收益对比', fontweight='bold')
                ax1.set_xlabel('再平衡期数')
                ax1.set_ylabel('累积收益倍数')
                ax1.legend()
                ax1.grid(True, alpha=0.3)

            # 2. 年化收益率对比
            if analysis_results:
                portfolios = []
                annual_returns = []

                portfolio_order = ['高股息率组合', '中高股息率组合', '全市场组合', '恒生指数基准']
                for portfolio_name in portfolio_order:
                    if portfolio_name in analysis_results:
                        portfolios.append(portfolio_name.replace('组合', ''))
                        annual_returns.append(analysis_results[portfolio_name]['annual_return'] * 100)

                bars = ax2.bar(portfolios, annual_returns, color=['red', 'orange', 'blue', 'gray'])
                ax2.set_title('各组合年化收益率对比', fontweight='bold')
                ax2.set_ylabel('年化收益率 (%)')
                ax2.grid(True, alpha=0.3)
                ax2.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars, annual_returns):
                    ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.2,
                            f'{value:.1f}%', ha='center', va='bottom')

            # 3. 夏普比率对比
            if analysis_results:
                portfolios = []
                sharpe_ratios = []

                for portfolio_name in portfolio_order:
                    if portfolio_name in analysis_results:
                        portfolios.append(portfolio_name.replace('组合', ''))
                        sharpe_ratios.append(analysis_results[portfolio_name]['sharpe_ratio'])

                bars = ax3.bar(portfolios, sharpe_ratios, color=['darkred', 'darkorange', 'darkblue', 'darkgray'])
                ax3.set_title('各组合夏普比率对比', fontweight='bold')
                ax3.set_ylabel('夏普比率')
                ax3.grid(True, alpha=0.3)
                ax3.tick_params(axis='x', rotation=45)

                # 添加数值标签
                for bar, value in zip(bars, sharpe_ratios):
                    ax3.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                            f'{value:.2f}', ha='center', va='bottom')

            # 4. IC时间序列
            if ic_results and 'ic_values' in ic_results:
                ic_values = ic_results['ic_values']
                ax4.plot(range(len(ic_values)), ic_values, 'b-', linewidth=1, alpha=0.7)
                ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)
                ax4.axhline(y=ic_results['ic_mean'], color='green', linestyle='-', alpha=0.8,
                           label=f"平均IC: {ic_results['ic_mean']:.3f}")
                ax4.set_title('信息系数时间序列', fontweight='bold')
                ax4.set_xlabel('期数')
                ax4.set_ylabel('IC值')
                ax4.legend()
                ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            output_file = 'hsi_dividend_factor_long_only_validation.png'
            plt.savefig(output_file, dpi=300, bbox_inches='tight')
            print(f"📊 图表已保存: {output_file}")

            plt.show()

        except Exception as e:
            print(f"⚠️  生成图表失败: {e}")

    def save_results(self, analysis_results: Dict, ic_results: Dict):
        """保存分析结果"""
        try:
            print("💾 保存分析结果...")

            # 保存详细结果
            results = {
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'strategy_type': 'long_only',
                'data_source': self.data_dir,
                'total_stocks': len(self.stock_data),
                'backtest_params': {
                    'rebalance_freq': self.rebalance_freq,
                    'lookback_days': self.lookback_days,
                    'forward_days': self.forward_days
                },
                'portfolio_performance': analysis_results,
                'information_coefficient': ic_results
            }

            # 保存JSON文件
            with open('hsi_dividend_factor_long_only_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2, default=str)

            print("💾 详细结果已保存: hsi_dividend_factor_long_only_results.json")

            # 保存CSV汇总
            if analysis_results:
                with open('hsi_dividend_factor_long_only_summary.csv', 'w', newline='', encoding='utf-8') as f:
                    writer = csv.writer(f)
                    writer.writerow(['组合名称', '年化收益(%)', '波动率(%)', '夏普比率', '最大回撤(%)', '胜率(%)', '总期数'])

                    portfolio_order = ['高股息率组合', '中高股息率组合', '全市场组合', '恒生指数基准']
                    for portfolio_name in portfolio_order:
                        if portfolio_name in analysis_results:
                            perf = analysis_results[portfolio_name]
                            writer.writerow([
                                portfolio_name,
                                f"{perf['annual_return']*100:.2f}",
                                f"{perf['std_return']*100:.2f}",
                                f"{perf['sharpe_ratio']:.2f}",
                                f"{abs(perf['max_drawdown'])*100:.2f}",
                                f"{perf['win_rate']*100:.1f}",
                                perf['total_periods']
                            ])

                print("📊 汇总已保存: hsi_dividend_factor_long_only_summary.csv")

        except Exception as e:
            print(f"❌ 保存结果失败: {e}")

    def run_validation(self) -> bool:
        """运行完整的因子验证流程"""
        try:
            print("🎯 开始股息率因子有效性验证（纯做多策略）...")

            # 1. 加载数据
            print("\n📊 第一步：加载股票数据")
            if not self.load_stock_data():
                return False

            # 2. 运行回测
            print("\n🔄 第二步：运行因子回测")
            if not self.run_backtest():
                return False

            # 3. 分析表现
            print("\n📈 第三步：分析因子表现")
            analysis_results = self.analyze_factor_performance()

            # 4. 计算IC
            print("\n📊 第四步：计算信息系数")
            ic_results = self.calculate_information_coefficient()

            # 5. 生成报告
            print("\n📄 第五步：生成分析报告")
            self.generate_report(analysis_results, ic_results)

            # 6. 创建可视化
            print("\n📊 第六步：生成可视化图表")
            self.create_visualizations(analysis_results, ic_results)

            # 7. 保存结果
            print("\n💾 第七步：保存分析结果")
            self.save_results(analysis_results, ic_results)

            print("\n✅ 股息率因子有效性验证完成（纯做多策略）！")

            # 总结
            if '高股息率组合' in analysis_results and '全市场组合' in analysis_results:
                high_div_return = analysis_results['高股息率组合']['annual_return']
                market_return = analysis_results['全市场组合']['annual_return']
                outperformance = high_div_return - market_return

                print(f"\n🎯 验证结果总结（纯做多策略）:")
                print(f"   高股息率组合年化收益: {high_div_return*100:.2f}%")
                print(f"   全市场组合年化收益: {market_return*100:.2f}%")
                print(f"   超额收益: {outperformance*100:.2f}%")

                if outperformance > 0:
                    print("   ✅ 股息率因子在纯做多策略中显示正向效应")
                    print("   💡 建议：可以考虑构建高股息率投资组合")
                else:
                    print("   ❌ 股息率因子在纯做多策略中显示负向效应")
                    print("   💡 建议：高股息率策略可能不适合当前市场环境")

            if ic_results:
                print(f"   📊 平均信息系数: {ic_results['ic_mean']:.4f}")
                if abs(ic_results['ic_mean']) > 0.02:
                    print("   ✅ 因子具有较强的预测能力")
                elif abs(ic_results['ic_mean']) > 0.01:
                    print("   ⚠️  因子具有一定的预测能力")
                else:
                    print("   ❌ 因子预测能力较弱")

            # 实际投资建议
            if '高股息率组合' in analysis_results:
                high_div_perf = analysis_results['高股息率组合']
                print(f"\n💰 实际投资建议:")
                print(f"   📈 预期年化收益: {high_div_perf['annual_return']*100:.2f}%")
                print(f"   📊 夏普比率: {high_div_perf['sharpe_ratio']:.2f}")
                print(f"   📉 最大回撤: {abs(high_div_perf['max_drawdown'])*100:.2f}%")
                print(f"   🎯 胜率: {high_div_perf['win_rate']*100:.1f}%")

                if high_div_perf['annual_return'] > 0.05 and high_div_perf['sharpe_ratio'] > 0.3:
                    print("   ✅ 该策略具有良好的投资价值，建议实施")
                elif high_div_perf['annual_return'] > 0:
                    print("   ⚠️  该策略有一定价值，但需要谨慎考虑风险")
                else:
                    print("   ❌ 该策略表现不佳，不建议实施")

            return True

        except Exception as e:
            print(f"❌ 验证失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎯 恒生指数成分股股息率因子有效性验证（纯做多策略）")
    print("=" * 70)
    print("📝 说明：本验证仅考虑做多策略，不涉及做空操作")
    print("🎯 目标：验证高股息率股票是否能够跑赢市场平均水平")
    print()

    # 创建验证器
    validator = HSIDividendFactorLongOnlyValidator("hsi_individual_dividend_yields")

    # 运行验证
    success = validator.run_validation()

    if success:
        print("\n🎉 股息率因子有效性验证完成（纯做多策略）！")
        print("\n📁 输出文件:")
        print("   📊 图表: hsi_dividend_factor_long_only_validation.png")
        print("   📄 详细结果: hsi_dividend_factor_long_only_results.json")
        print("   📊 汇总: hsi_dividend_factor_long_only_summary.csv")
        print("\n💡 投资提示:")
        print("   - 本分析基于历史数据，不构成投资建议")
        print("   - 实际投资时请考虑交易成本、税收等因素")
        print("   - 建议结合其他因子进行多元化投资")
        print("   - 定期重新评估策略有效性")
    else:
        print("\n💥 验证过程中遇到问题，请检查错误信息。")


if __name__ == "__main__":
    main()
