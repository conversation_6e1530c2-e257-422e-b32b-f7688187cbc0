#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
考虑数据发布时滞的现实回测
避免数据泄漏问题，使用回测时点能够获取到的最新指标数据
"""

import pandas as pd
import numpy as np
import yfinance as yf
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def apply_data_lags():
    """
    应用各指标的数据发布时滞

    数据发布时滞分析：
    1. 消费者信心指数(UMICH): 月中发布，滞后约2周
    2. 制造业PMI(ISM): 月初发布，滞后约1-3天
    3. 房地产指标: 月中发布，滞后约2-3周
    4. 外国投资者净流入(TIC): 月中发布，滞后约7周
    5. OFR金融压力指数: 周度更新，滞后约1周

    基于TIC数据7周延迟：使用1.75个月前的数据进行决策
    """
    print("正在应用数据发布时滞...")

    # 加载改进版择时指标
    timing_df = pd.read_csv('data/improved_us_mid_term_timing_indicator.csv', index_col=0)
    timing_df.index = pd.to_datetime(timing_df.index)

    # 应用7周（约1.75个月）的数据滞后
    # 即：在t月做决策时，只能使用t-2月及之前的数据
    # 使用shift(2)来模拟1.75个月的延迟
    lagged_timing = timing_df['Improved_US_Mid_Term_Timing'].shift(2)

    print(f"原始指标数据点: {len(timing_df)}")
    print(f"应用滞后后数据点: {len(lagged_timing.dropna())}")
    print(f"损失数据点: {len(timing_df) - len(lagged_timing.dropna())}")

    return lagged_timing.dropna()

def realistic_backtest():
    """执行考虑数据滞后的现实回测"""

    print("="*80)
    print("考虑数据发布时滞的现实回测")
    print("策略：使用7周前的择时指标进行决策（基于TIC数据延迟）")
    print("="*80)

    # 1. 获取考虑滞后的择时指标
    lagged_timing = apply_data_lags()

    print(f"滞后择时指标时间范围: {lagged_timing.index.min()} - {lagged_timing.index.max()}")

    # 2. 下载SPY数据
    print("正在下载SPY数据...")
    spy_data = yf.download('SPY', start='2005-01-01', end='2025-02-01', progress=False)
    spy_prices = spy_data['Close']['SPY']
    spy_monthly = spy_prices.resample('ME').last()

    # 3. 对齐数据到月末
    lagged_timing.index = lagged_timing.index.to_period('M').to_timestamp('M')
    spy_monthly.index = spy_monthly.index.to_period('M').to_timestamp('M')

    # 4. 找到共同日期并对齐
    common_dates = lagged_timing.index.intersection(spy_monthly.index)
    timing_aligned = lagged_timing.loc[common_dates]
    spy_aligned = spy_monthly.loc[common_dates]

    # 5. 限制回测时间范围从2000年开始
    start_date = pd.to_datetime('2000-01-01')
    end_date = common_dates.max()

    # 筛选从2000年开始的数据
    date_mask = (common_dates >= start_date) & (common_dates <= end_date)
    common_dates = common_dates[date_mask]
    timing_aligned = timing_aligned.loc[common_dates]
    spy_aligned = spy_aligned.loc[common_dates]

    print(f"对齐后数据: {len(common_dates)}个观测值")
    print(f"回测时间范围: {common_dates.min()} - {common_dates.max()}")
    print(f"回测期间: 从2000年开始")

    # 6. 生成交易信号（使用滞后的指标）
    signals = (timing_aligned >= 25).astype(int)

    print(f"\n交易信号统计（考虑数据滞后）:")
    print(f"持有期数: {signals.sum()}/{len(signals)} ({signals.mean():.1%})")
    print(f"空仓期数: {len(signals) - signals.sum()} ({1-signals.mean():.1%})")

    # 统计信号变化
    signal_changes = signals.diff()
    buy_signals = (signal_changes == 1).sum()
    sell_signals = (signal_changes == -1).sum()
    print(f"买入信号次数: {buy_signals}")
    print(f"卖出信号次数: {sell_signals}")

    # 7. 计算收益率
    spy_returns = spy_aligned.pct_change().dropna()

    # 策略收益率：使用前一期的信号决定当期是否持有
    strategy_returns = spy_returns * signals.shift(1).loc[spy_returns.index]

    # 8. 计算累积收益
    spy_cumulative = (1 + spy_returns).cumprod()
    strategy_cumulative = (1 + strategy_returns).cumprod()

    # 9. 计算绩效指标
    initial_capital = 100000

    # 总收益率
    spy_total_return = spy_cumulative.iloc[-1] - 1
    strategy_total_return = strategy_cumulative.iloc[-1] - 1

    # 年化收益率
    years = len(spy_returns) / 12
    spy_annual_return = (spy_cumulative.iloc[-1] ** (1/years)) - 1
    strategy_annual_return = (strategy_cumulative.iloc[-1] ** (1/years)) - 1

    # 波动率
    spy_volatility = spy_returns.std() * np.sqrt(12)
    strategy_volatility = strategy_returns.std() * np.sqrt(12)

    # 夏普比率
    spy_sharpe = spy_annual_return / spy_volatility
    strategy_sharpe = strategy_annual_return / strategy_volatility

    # 最大回撤
    spy_running_max = spy_cumulative.expanding().max()
    spy_drawdown = (spy_cumulative - spy_running_max) / spy_running_max
    spy_max_drawdown = spy_drawdown.min()

    strategy_running_max = strategy_cumulative.expanding().max()
    strategy_drawdown = (strategy_cumulative - strategy_running_max) / strategy_running_max
    strategy_max_drawdown = strategy_drawdown.min()

    # 胜率
    spy_win_rate = (spy_returns > 0).mean()
    strategy_win_rate = (strategy_returns > 0).mean()

    # 10. 打印结果
    print("\n" + "="*80)
    print("现实回测结果（考虑数据滞后）- 从2000年开始")
    print("="*80)

    print(f"\n📊 绩效对比:")
    print(f"{'指标':<15} {'SPY买入持有':<15} {'择时策略':<15} {'优势':<10}")
    print("-" * 65)

    metrics = [
        ('总收益率', f"{spy_total_return:.1%}", f"{strategy_total_return:.1%}"),
        ('年化收益率', f"{spy_annual_return:.1%}", f"{strategy_annual_return:.1%}"),
        ('年化波动率', f"{spy_volatility:.1%}", f"{strategy_volatility:.1%}"),
        ('夏普比率', f"{spy_sharpe:.2f}", f"{strategy_sharpe:.2f}"),
        ('最大回撤', f"{spy_max_drawdown:.1%}", f"{strategy_max_drawdown:.1%}"),
        ('胜率', f"{spy_win_rate:.1%}", f"{strategy_win_rate:.1%}")
    ]

    for metric, spy_val, strategy_val in metrics:
        # 判断优势
        if metric in ['总收益率', '年化收益率', '夏普比率', '胜率']:
            try:
                spy_num = float(spy_val.rstrip('%'))
                strategy_num = float(strategy_val.rstrip('%'))
                advantage = "择时策略" if strategy_num > spy_num else "SPY"
            except:
                advantage = "择时策略" if strategy_val > spy_val else "SPY"
        else:  # 波动率和回撤越小越好
            try:
                spy_num = abs(float(spy_val.rstrip('%')))
                strategy_num = abs(float(strategy_val.rstrip('%')))
                advantage = "择时策略" if strategy_num < spy_num else "SPY"
            except:
                advantage = "择时策略" if abs(strategy_val) < abs(spy_val) else "SPY"

        print(f"{metric:<15} {spy_val:<15} {strategy_val:<15} {advantage:<10}")

    print(f"\n💰 最终投资组合价值 (初始${initial_capital:,}):")
    print(f"SPY买入持有: ${initial_capital * spy_cumulative.iloc[-1]:,.0f}")
    print(f"择时策略: ${initial_capital * strategy_cumulative.iloc[-1]:,.0f}")

    # 超额收益
    excess_return = strategy_cumulative.iloc[-1] / spy_cumulative.iloc[-1] - 1
    print(f"择时策略超额收益: {excess_return:.1%}")

    # 11. 分析空仓期间的表现
    print(f"\n📈 空仓期间分析:")
    cash_periods = signals == 0
    if cash_periods.any():
        cash_returns = spy_returns[signals.shift(1).loc[spy_returns.index] == 0]
        if len(cash_returns) > 0:
            cash_period_return = cash_returns.mean()
            print(f"空仓期间SPY平均月收益率: {cash_period_return:.2%}")
            print(f"空仓期间数: {len(cash_returns)}个月")
            print(f"空仓期间SPY累积收益: {(1 + cash_returns).prod() - 1:.1%}")
            print(f"成功规避损失: {'是' if cash_period_return < 0 else '否'}")

    # 12. 数据滞后影响分析
    print(f"\n🕐 数据滞后影响分析:")

    # 加载原始（无滞后）指标进行对比
    original_timing = pd.read_csv('data/improved_us_mid_term_timing_indicator.csv', index_col=0)
    original_timing.index = pd.to_datetime(original_timing.index)
    original_timing.index = original_timing.index.to_period('M').to_timestamp('M')
    original_signals = (original_timing['Improved_US_Mid_Term_Timing'] >= 25).astype(int)

    # 计算信号一致性
    common_original_dates = original_signals.index.intersection(signals.index)
    if len(common_original_dates) > 0:
        original_aligned = original_signals.loc[common_original_dates]
        lagged_aligned = signals.loc[common_original_dates]
        signal_consistency = (original_aligned == lagged_aligned).mean()
        print(f"信号一致性: {signal_consistency:.1%}")

        # 计算滞后导致的错失机会
        missed_opportunities = ((original_aligned == 1) & (lagged_aligned == 0)).sum()
        false_signals = ((original_aligned == 0) & (lagged_aligned == 1)).sum()
        print(f"滞后导致错失买入机会: {missed_opportunities}次")
        print(f"滞后导致错误买入: {false_signals}次")

    # 13. 保存结果
    results_df = pd.DataFrame({
        'Date': common_dates,
        'Lagged_Timing_Indicator': timing_aligned.values,
        'SPY_Price': spy_aligned.values,
        'Signal': signals.values,
        'SPY_Return': spy_returns.reindex(common_dates, fill_value=0).values,
        'Strategy_Return': strategy_returns.reindex(common_dates, fill_value=0).values,
        'SPY_Cumulative': spy_cumulative.reindex(common_dates, method='ffill').values,
        'Strategy_Cumulative': strategy_cumulative.reindex(common_dates, method='ffill').values,
        'SPY_Portfolio': (spy_cumulative * initial_capital).reindex(common_dates, method='ffill').values,
        'Strategy_Portfolio': (strategy_cumulative * initial_capital).reindex(common_dates, method='ffill').values
    })

    results_df.to_csv('data/realistic_timing_strategy_backtest_2000.csv', index=False)
    print(f"\n现实回测结果已保存至: data/realistic_timing_strategy_backtest_2000.csv")

    # 14. 当前市场状态（考虑滞后）
    current_available_indicator = timing_aligned.iloc[-1]
    current_signal = "持有SPY" if current_available_indicator >= 25 else "空仓规避"
    available_data_date = timing_aligned.index[-1]

    print(f"\n🎯 当前可用信号 (基于{available_data_date.strftime('%Y-%m')}数据):")
    print(f"可用择时指标值: {current_available_indicator:.1f}")
    print(f"交易信号: {current_signal}")
    print(f"注意: 该信号基于7周前的数据，符合实际投资决策情况")

    return results_df, spy_cumulative, strategy_cumulative, spy_returns, strategy_returns

def create_lag_comparison_chart(results_df):
    """创建滞后影响对比图表"""

    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('数据滞后对择时策略的影响分析 (从2000年开始)', fontsize=16, fontweight='bold')

    # 子图1: 投资组合价值对比
    ax1 = axes[0, 0]
    ax1.plot(results_df['Date'], results_df['SPY_Portfolio'], linewidth=2,
             label='SPY买入持有', color='blue', alpha=0.8)
    ax1.plot(results_df['Date'], results_df['Strategy_Portfolio'], linewidth=2,
             label='择时策略(考虑滞后)', color='red', alpha=0.8)

    ax1.set_title('投资组合价值对比', fontweight='bold')
    ax1.set_ylabel('投资组合价值 ($)')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    ax1.yaxis.set_major_formatter(plt.FuncFormatter(lambda x, p: f'${x/1000:.0f}K'))

    # 子图2: 择时指标和信号
    ax2 = axes[0, 1]
    ax2.plot(results_df['Date'], results_df['Lagged_Timing_Indicator'],
             linewidth=2, color='purple', alpha=0.8, label='滞后择时指标')
    ax2.axhline(y=25, color='red', linestyle='--', linewidth=2, label='空仓阈值 (25)')
    ax2.axhline(y=50, color='gray', linestyle='--', alpha=0.7, label='中位线 (50)')

    # 标记空仓期间
    cash_periods = results_df['Signal'] == 0
    if cash_periods.any():
        ax2.fill_between(results_df['Date'], 0, 100, where=cash_periods,
                        alpha=0.3, color='red', label='空仓期间')

    ax2.set_title('滞后择时指标与交易信号', fontweight='bold')
    ax2.set_ylabel('择时指标值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    ax2.set_ylim(0, 100)

    # 子图3: 累积收益率对比
    ax3 = axes[1, 0]
    spy_returns_pct = (results_df['SPY_Cumulative'] - 1) * 100
    strategy_returns_pct = (results_df['Strategy_Cumulative'] - 1) * 100

    ax3.plot(results_df['Date'], spy_returns_pct, linewidth=2,
             label='SPY买入持有', color='blue', alpha=0.8)
    ax3.plot(results_df['Date'], strategy_returns_pct, linewidth=2,
             label='择时策略(考虑滞后)', color='red', alpha=0.8)

    ax3.set_title('累积收益率对比', fontweight='bold')
    ax3.set_ylabel('累积收益率 (%)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 子图4: 回撤对比
    ax4 = axes[1, 1]

    # 计算回撤
    spy_running_max = results_df['SPY_Cumulative'].expanding().max()
    spy_drawdown = (results_df['SPY_Cumulative'] - spy_running_max) / spy_running_max * 100

    strategy_running_max = results_df['Strategy_Cumulative'].expanding().max()
    strategy_drawdown = (results_df['Strategy_Cumulative'] - strategy_running_max) / strategy_running_max * 100

    ax4.fill_between(results_df['Date'], spy_drawdown, 0, alpha=0.6, color='blue', label='SPY买入持有')
    ax4.fill_between(results_df['Date'], strategy_drawdown, 0, alpha=0.6, color='red', label='择时策略(考虑滞后)')

    ax4.set_title('最大回撤对比', fontweight='bold')
    ax4.set_ylabel('回撤 (%)')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    # 格式化x轴日期
    for ax in axes.flat:
        ax.xaxis.set_major_locator(mdates.YearLocator(2))
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
        ax.tick_params(axis='x', rotation=45)

    plt.tight_layout()
    plt.savefig('data/realistic_timing_strategy_with_lag_2000.png', dpi=300, bbox_inches='tight')
    plt.show()

def main():
    """主函数"""

    # 执行现实回测
    results_df, spy_cumulative, strategy_cumulative, spy_returns, strategy_returns = realistic_backtest()

    # 创建可视化
    print(f"\n📊 正在生成可视化图表...")
    create_lag_comparison_chart(results_df)
    print(f"图表已保存至: data/realistic_timing_strategy_with_lag_2000.png")

    return results_df

if __name__ == "__main__":
    results = main()
