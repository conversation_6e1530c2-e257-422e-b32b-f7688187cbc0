#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plot S&P 500 Relative Strength Lines
Visualize relative strength trends for top performing stocks
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
from sp500_rs_simple import SP500RelativeStrengthAnalyzer

warnings.filterwarnings('ignore')

def plot_relative_strength_lines(analyzer, top_n=10, save_path=None):
    """Plot relative strength lines for top stocks"""
    
    if analyzer.analysis_results.empty:
        print("❌ No analysis results to plot")
        return
    
    print(f"📊 Plotting relative strength lines for top {top_n} stocks...")
    
    # Get top stocks
    top_stocks = analyzer.analysis_results.head(top_n)
    
    # Create figure with subplots
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle(f'S&P 500 Relative Strength Analysis - Top {top_n} Stocks', fontsize=16, fontweight='bold')
    
    # 1. Relative Strength Lines
    ax1 = axes[0, 0]
    colors = plt.cm.tab10(np.linspace(0, 1, min(top_n, 10)))
    
    for i, (_, stock) in enumerate(top_stocks.iterrows()):
        symbol = stock['symbol']
        if symbol in analyzer.relative_strength_data:
            rs_data = analyzer.relative_strength_data[symbol]
            ax1.plot(rs_data.index, rs_data.values, 
                    label=f"{symbol} (Score: {stock['strength_score']:.0f})", 
                    color=colors[i % len(colors)], linewidth=2, alpha=0.8)
    
    ax1.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='Baseline (100)')
    ax1.set_title('Relative Strength Lines', fontsize=14, fontweight='bold')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Relative Strength')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    
    # Format x-axis dates
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 2. Strength Score Bar Chart
    ax2 = axes[0, 1]
    bars = ax2.bar(range(len(top_stocks)), top_stocks['strength_score'], 
                  color=plt.cm.RdYlGn(top_stocks['strength_score']/100))
    ax2.set_title('Strength Score Ranking', fontsize=14, fontweight='bold')
    ax2.set_xlabel('Stock Ranking')
    ax2.set_ylabel('Strength Score')
    ax2.set_xticks(range(len(top_stocks)))
    ax2.set_xticklabels(top_stocks['symbol'], rotation=45, ha='right')
    
    # Add value labels on bars
    for i, bar in enumerate(bars):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                f'{height:.0f}', ha='center', va='bottom', fontsize=8)
    
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. Current RS vs Total Change Scatter
    ax3 = axes[1, 0]
    scatter = ax3.scatter(top_stocks['current_rs'], top_stocks['total_change_pct'], 
                         c=top_stocks['strength_score'], cmap='RdYlGn', 
                         s=100, alpha=0.7, edgecolors='black')
    
    # Add stock labels
    for _, stock in top_stocks.iterrows():
        ax3.annotate(stock['symbol'], 
                   (stock['current_rs'], stock['total_change_pct']),
                   xytext=(5, 5), textcoords='offset points', 
                   fontsize=8, alpha=0.8)
    
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax3.axvline(x=100, color='red', linestyle='--', alpha=0.5)
    ax3.set_title('Current RS vs Total Change', fontsize=14, fontweight='bold')
    ax3.set_xlabel('Current Relative Strength')
    ax3.set_ylabel('Total Change (%)')
    
    # Add colorbar
    cbar = plt.colorbar(scatter, ax=ax3)
    cbar.set_label('Strength Score')
    ax3.grid(True, alpha=0.3)
    
    # 4. Rising Stocks Analysis
    ax4 = axes[1, 1]
    rising_stocks = analyzer.get_rising_stocks(min_score=60)
    
    if not rising_stocks.empty:
        # Plot slopes for rising stocks
        if '1M_slope' in rising_stocks.columns and '3M_slope' in rising_stocks.columns:
            scatter2 = ax4.scatter(rising_stocks['1M_slope'], rising_stocks['3M_slope'], 
                                 c=rising_stocks['strength_score'], cmap='RdYlGn', 
                                 s=100, alpha=0.7, edgecolors='black')
            
            # Add stock labels
            for _, stock in rising_stocks.iterrows():
                ax4.annotate(stock['symbol'], 
                           (stock['1M_slope'], stock['3M_slope']),
                           xytext=(5, 5), textcoords='offset points', 
                           fontsize=8, alpha=0.8)
            
            ax4.axhline(y=0, color='red', linestyle='--', alpha=0.5)
            ax4.axvline(x=0, color='red', linestyle='--', alpha=0.5)
            ax4.set_title('Rising Stocks: 1M vs 3M Slope', fontsize=14, fontweight='bold')
            ax4.set_xlabel('1-Month Slope')
            ax4.set_ylabel('3-Month Slope')
            
            # Add colorbar
            cbar2 = plt.colorbar(scatter2, ax=ax4)
            cbar2.set_label('Strength Score')
        else:
            ax4.text(0.5, 0.5, 'No slope data available', ha='center', va='center', 
                   transform=ax4.transAxes, fontsize=12)
            ax4.set_title('Rising Stocks Analysis', fontsize=14, fontweight='bold')
    else:
        ax4.text(0.5, 0.5, 'No rising stocks found', ha='center', va='center', 
               transform=ax4.transAxes, fontsize=12)
        ax4.set_title('Rising Stocks Analysis', fontsize=14, fontweight='bold')
    
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    
    # Save plot
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📁 Plot saved to: {save_path}")
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_path = f"sp500_relative_strength_{timestamp}.png"
        plt.savefig(default_path, dpi=300, bbox_inches='tight')
        print(f"📁 Plot saved to: {default_path}")
    
    plt.show()

def plot_individual_stocks(analyzer, symbols, save_path=None):
    """Plot individual stock relative strength analysis"""
    
    if not symbols:
        print("❌ No symbols provided")
        return
    
    print(f"📊 Plotting individual analysis for {len(symbols)} stocks...")
    
    # Calculate subplot layout
    n_stocks = len(symbols)
    cols = min(3, n_stocks)
    rows = (n_stocks + cols - 1) // cols
    
    fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 4*rows))
    if n_stocks == 1:
        axes = [axes]
    elif rows == 1:
        axes = axes.reshape(1, -1)
    
    fig.suptitle('Individual Stock Relative Strength Analysis', fontsize=16, fontweight='bold')
    
    for i, symbol in enumerate(symbols):
        row = i // cols
        col = i % cols
        if rows > 1:
            ax = axes[row, col]
        elif cols > 1:
            ax = axes[col]
        else:
            ax = axes
        
        if symbol in analyzer.relative_strength_data:
            rs_data = analyzer.relative_strength_data[symbol]
            
            # Plot relative strength line
            ax.plot(rs_data.index, rs_data.values, 'b-', linewidth=2, label='Relative Strength')
            
            # Add moving averages
            if len(rs_data) >= 20:
                ma20 = rs_data.rolling(20).mean()
                ax.plot(ma20.index, ma20.values, 'orange', linewidth=1, alpha=0.8, label='MA20')
            
            if len(rs_data) >= 50:
                ma50 = rs_data.rolling(50).mean()
                ax.plot(ma50.index, ma50.values, 'red', linewidth=1, alpha=0.8, label='MA50')
            
            # Add baseline
            ax.axhline(y=100, color='gray', linestyle='--', alpha=0.7, label='Baseline')
            
            # Get stock info
            stock_info = analyzer.analysis_results[analyzer.analysis_results['symbol'] == symbol]
            if not stock_info.empty:
                stock_data = stock_info.iloc[0]
                title = f"{symbol}\nScore: {stock_data['strength_score']:.0f} | Current RS: {stock_data['current_rs']:.1f}"
            else:
                title = f"{symbol}\nCurrent RS: {rs_data.iloc[-1]:.1f}"
            
            ax.set_title(title, fontsize=12, fontweight='bold')
            ax.set_xlabel('Date')
            ax.set_ylabel('Relative Strength')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # Format x-axis dates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
        
        else:
            ax.text(0.5, 0.5, f'No data\n{symbol}', ha='center', va='center', 
                   transform=ax.transAxes, fontsize=12)
            ax.set_title(symbol, fontsize=12, fontweight='bold')
    
    # Hide extra subplots
    for i in range(n_stocks, rows * cols):
        row = i // cols
        col = i % cols
        if rows > 1:
            axes[row, col].set_visible(False)
        elif cols > 1:
            axes[col].set_visible(False)
    
    plt.tight_layout()
    
    # Save plot
    if save_path:
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📁 Individual analysis plot saved to: {save_path}")
    else:
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        default_path = f"sp500_individual_analysis_{timestamp}.png"
        plt.savefig(default_path, dpi=300, bbox_inches='tight')
        print(f"📁 Individual analysis plot saved to: {default_path}")
    
    plt.show()

def main():
    """Main function"""
    print("🎯 S&P 500 Relative Strength Visualization")
    print("=" * 50)
    
    # Create and run analyzer
    analyzer = SP500RelativeStrengthAnalyzer(period="1y", benchmark="SPY")
    
    if not analyzer.run_analysis():
        print("❌ Analysis failed")
        return
    
    # Plot overall analysis
    plot_relative_strength_lines(analyzer, top_n=10)
    
    # Plot individual analysis for top rising stocks
    rising_stocks = analyzer.get_rising_stocks(min_score=60)
    if not rising_stocks.empty:
        top_rising = rising_stocks.head(6)['symbol'].tolist()
        plot_individual_stocks(analyzer, top_rising)
    
    print("\n✅ Visualization completed!")

if __name__ == "__main__":
    main()
