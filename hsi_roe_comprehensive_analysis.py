#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股ROE数据综合分析工具

功能：
1. 全面分析ROE数据
2. 生成投资策略建议
3. 识别高质量股票
4. 创建ROE排名和评分系统
5. 生成HTML报告

作者: AI Assistant
创建时间: 2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime, timedelta
import os
import warnings
import plotly.graph_objects as go
import plotly.express as px
from plotly.subplots import make_subplots
import plotly.offline as pyo

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIROEComprehensiveAnalyzer:
    """恒生指数ROE数据综合分析器"""
    
    def __init__(self, data_file: str = None):
        self.data_file = data_file
        self.df = None
        self.analysis_results = {}
        
        # 如果没有指定文件，自动查找最新的合并数据文件
        if data_file is None:
            self.data_file = self.find_latest_combined_file()
    
    def find_latest_combined_file(self) -> str:
        """查找最新的合并数据文件"""
        cache_dir = "hsi_roe_cache"
        if not os.path.exists(cache_dir):
            raise FileNotFoundError("未找到ROE数据缓存目录")
        
        # 查找所有合并文件
        combined_files = [f for f in os.listdir(cache_dir) if f.startswith('hsi_daily_roe_combined_')]
        
        if not combined_files:
            raise FileNotFoundError("未找到ROE合并数据文件")
        
        # 按文件名排序，取最新的
        combined_files.sort(reverse=True)
        latest_file = os.path.join(cache_dir, combined_files[0])
        
        print(f"📁 使用数据文件: {latest_file}")
        return latest_file
    
    def load_data(self) -> bool:
        """加载ROE数据"""
        try:
            print(f"📊 加载ROE数据: {self.data_file}")
            self.df = pd.read_csv(self.data_file, parse_dates=['date'])
            
            print(f"✅ 数据加载成功")
            print(f"   - 总记录数: {len(self.df):,}")
            print(f"   - 股票数量: {self.df['stock_code'].nunique()}")
            print(f"   - 日期范围: {self.df['date'].min().date()} 到 {self.df['date'].max().date()}")
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False
    
    def calculate_roe_metrics(self) -> pd.DataFrame:
        """计算ROE相关指标"""
        print("\n📈 计算ROE相关指标...")
        
        metrics = []
        
        for stock_code in self.df['stock_code'].unique():
            stock_data = self.df[self.df['stock_code'] == stock_code].copy()
            stock_data = stock_data.sort_values('date')
            stock_name = stock_data['stock_name'].iloc[0]
            
            # 基本统计
            latest_roe = stock_data['roe'].iloc[-1]
            avg_roe = stock_data['roe'].mean()
            median_roe = stock_data['roe'].median()
            std_roe = stock_data['roe'].std()
            max_roe = stock_data['roe'].max()
            min_roe = stock_data['roe'].min()
            
            # ROE趋势分析
            recent_1y = stock_data[stock_data['date'] >= (stock_data['date'].max() - timedelta(days=365))]
            recent_3y = stock_data[stock_data['date'] >= (stock_data['date'].max() - timedelta(days=1095))]
            
            avg_roe_1y = recent_1y['roe'].mean() if len(recent_1y) > 0 else np.nan
            avg_roe_3y = recent_3y['roe'].mean() if len(recent_3y) > 0 else np.nan
            
            # ROE稳定性（变异系数）
            cv_roe = (std_roe / avg_roe) if avg_roe != 0 else np.inf
            
            # ROE增长趋势
            if len(stock_data) >= 2:
                # 计算线性回归斜率作为趋势指标
                x = np.arange(len(stock_data))
                y = stock_data['roe'].values
                trend_slope = np.polyfit(x, y, 1)[0] * 365  # 年化趋势
            else:
                trend_slope = 0
            
            # ROE质量评分（综合指标）
            # 考虑：最新ROE、平均ROE、稳定性、趋势
            roe_score = self.calculate_roe_quality_score(
                latest_roe, avg_roe, cv_roe, trend_slope
            )
            
            metrics.append({
                'stock_code': stock_code,
                'stock_name': stock_name,
                'latest_roe': latest_roe,
                'avg_roe': avg_roe,
                'median_roe': median_roe,
                'std_roe': std_roe,
                'max_roe': max_roe,
                'min_roe': min_roe,
                'avg_roe_1y': avg_roe_1y,
                'avg_roe_3y': avg_roe_3y,
                'cv_roe': cv_roe,
                'trend_slope': trend_slope,
                'roe_score': roe_score,
                'records_count': len(stock_data),
                'date_range_start': stock_data['date'].min(),
                'date_range_end': stock_data['date'].max()
            })
        
        metrics_df = pd.DataFrame(metrics)
        
        # 添加排名
        metrics_df['latest_roe_rank'] = metrics_df['latest_roe'].rank(ascending=False)
        metrics_df['avg_roe_rank'] = metrics_df['avg_roe'].rank(ascending=False)
        metrics_df['roe_score_rank'] = metrics_df['roe_score'].rank(ascending=False)
        
        self.analysis_results['metrics'] = metrics_df
        
        print(f"✅ 完成 {len(metrics_df)} 只股票的ROE指标计算")
        return metrics_df
    
    def calculate_roe_quality_score(self, latest_roe: float, avg_roe: float, 
                                   cv_roe: float, trend_slope: float) -> float:
        """计算ROE质量评分"""
        try:
            # 归一化各个指标到0-100分
            
            # 最新ROE得分（0-30分）
            latest_score = min(30, max(0, latest_roe * 1.5))
            
            # 平均ROE得分（0-30分）
            avg_score = min(30, max(0, avg_roe * 1.5))
            
            # 稳定性得分（0-25分，变异系数越小越好）
            if cv_roe == np.inf or cv_roe > 2:
                stability_score = 0
            else:
                stability_score = max(0, 25 - cv_roe * 12.5)
            
            # 趋势得分（0-15分）
            trend_score = min(15, max(-15, trend_slope * 3)) + 15
            
            total_score = latest_score + avg_score + stability_score + trend_score
            return round(total_score, 2)
            
        except:
            return 0
    
    def identify_high_quality_stocks(self, top_n: int = 10) -> pd.DataFrame:
        """识别高质量股票"""
        if 'metrics' not in self.analysis_results:
            self.calculate_roe_metrics()
        
        metrics_df = self.analysis_results['metrics']
        
        print(f"\n🏆 识别前{top_n}只高质量股票...")
        
        # 筛选条件
        high_quality = metrics_df[
            (metrics_df['latest_roe'] > 5) &  # 最新ROE > 5%
            (metrics_df['avg_roe'] > 5) &    # 平均ROE > 5%
            (metrics_df['cv_roe'] < 1.5) &   # 变异系数 < 1.5（相对稳定）
            (metrics_df['records_count'] >= 365)  # 至少有1年数据
        ].copy()
        
        # 按ROE质量评分排序
        high_quality = high_quality.sort_values('roe_score', ascending=False)
        
        top_stocks = high_quality.head(top_n)
        
        print("高质量股票排名:")
        display_cols = ['stock_code', 'stock_name', 'latest_roe', 'avg_roe', 'cv_roe', 'roe_score']
        print(top_stocks[display_cols].to_string(index=False))
        
        self.analysis_results['high_quality_stocks'] = top_stocks
        return top_stocks
    
    def create_interactive_dashboard(self, output_file: str = "hsi_roe_dashboard.html"):
        """创建交互式仪表板"""
        if 'metrics' not in self.analysis_results:
            self.calculate_roe_metrics()
        
        print(f"\n📊 创建交互式仪表板...")
        
        metrics_df = self.analysis_results['metrics']
        
        # 创建子图
        fig = make_subplots(
            rows=2, cols=2,
            subplot_titles=('ROE Distribution', 'ROE vs Stability', 'Top 10 Stocks by ROE Score', 'ROE Trend Analysis'),
            specs=[[{"type": "histogram"}, {"type": "scatter"}],
                   [{"type": "bar"}, {"type": "scatter"}]]
        )
        
        # 1. ROE分布直方图
        fig.add_trace(
            go.Histogram(x=metrics_df['latest_roe'], name='Latest ROE Distribution', nbinsx=20),
            row=1, col=1
        )
        
        # 2. ROE vs 稳定性散点图
        fig.add_trace(
            go.Scatter(
                x=metrics_df['cv_roe'], 
                y=metrics_df['latest_roe'],
                mode='markers',
                text=metrics_df['stock_name'],
                name='ROE vs Stability',
                marker=dict(size=8, opacity=0.7)
            ),
            row=1, col=2
        )
        
        # 3. 前10只股票ROE评分
        top_10 = metrics_df.nlargest(10, 'roe_score')
        fig.add_trace(
            go.Bar(
                x=top_10['stock_name'], 
                y=top_10['roe_score'],
                name='ROE Quality Score',
                text=top_10['roe_score'],
                textposition='auto'
            ),
            row=2, col=1
        )
        
        # 4. ROE趋势分析
        fig.add_trace(
            go.Scatter(
                x=metrics_df['avg_roe'], 
                y=metrics_df['trend_slope'],
                mode='markers',
                text=metrics_df['stock_name'],
                name='Average ROE vs Trend',
                marker=dict(size=8, opacity=0.7, color=metrics_df['roe_score'], 
                           colorscale='Viridis', showscale=True)
            ),
            row=2, col=2
        )
        
        # 更新布局
        fig.update_layout(
            title_text="HSI Stocks ROE Analysis Dashboard",
            showlegend=False,
            height=800
        )
        
        # 保存为HTML文件
        pyo.plot(fig, filename=output_file, auto_open=False)
        print(f"✅ 交互式仪表板已保存: {output_file}")
        
        return output_file
    
    def generate_investment_recommendations(self) -> dict:
        """生成投资建议"""
        if 'metrics' not in self.analysis_results:
            self.calculate_roe_metrics()
        
        print(f"\n💡 生成投资建议...")
        
        metrics_df = self.analysis_results['metrics']
        
        recommendations = {
            'buy_candidates': [],
            'hold_candidates': [],
            'watch_candidates': [],
            'avoid_candidates': []
        }
        
        for _, stock in metrics_df.iterrows():
            stock_info = {
                'code': stock['stock_code'],
                'name': stock['stock_name'],
                'latest_roe': stock['latest_roe'],
                'roe_score': stock['roe_score'],
                'reason': ''
            }
            
            # 买入候选
            if (stock['latest_roe'] > 15 and stock['avg_roe'] > 12 and 
                stock['cv_roe'] < 0.8 and stock['trend_slope'] > 0):
                stock_info['reason'] = '高ROE、稳定、上升趋势'
                recommendations['buy_candidates'].append(stock_info)
            
            # 持有候选
            elif (stock['latest_roe'] > 8 and stock['avg_roe'] > 8 and 
                  stock['cv_roe'] < 1.2):
                stock_info['reason'] = '中等ROE、相对稳定'
                recommendations['hold_candidates'].append(stock_info)
            
            # 观察候选
            elif (stock['latest_roe'] > 5 and stock['trend_slope'] > 1):
                stock_info['reason'] = 'ROE改善趋势明显'
                recommendations['watch_candidates'].append(stock_info)
            
            # 避免候选
            elif (stock['latest_roe'] < 3 or stock['cv_roe'] > 2 or 
                  stock['trend_slope'] < -2):
                stock_info['reason'] = '低ROE或不稳定或下降趋势'
                recommendations['avoid_candidates'].append(stock_info)
        
        # 按ROE评分排序
        for category in recommendations:
            recommendations[category].sort(key=lambda x: x['roe_score'], reverse=True)
        
        # 打印建议
        print("\n🚀 投资建议摘要:")
        print(f"买入候选: {len(recommendations['buy_candidates'])} 只")
        print(f"持有候选: {len(recommendations['hold_candidates'])} 只")
        print(f"观察候选: {len(recommendations['watch_candidates'])} 只")
        print(f"避免候选: {len(recommendations['avoid_candidates'])} 只")
        
        self.analysis_results['recommendations'] = recommendations
        return recommendations
    
    def export_analysis_report(self, output_file: str = None) -> str:
        """导出分析报告"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"hsi_roe_analysis_report_{timestamp}.xlsx"
        
        print(f"\n📋 导出分析报告...")
        
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            # 基本指标
            if 'metrics' in self.analysis_results:
                self.analysis_results['metrics'].to_excel(writer, sheet_name='ROE_Metrics', index=False)
            
            # 高质量股票
            if 'high_quality_stocks' in self.analysis_results:
                self.analysis_results['high_quality_stocks'].to_excel(writer, sheet_name='High_Quality_Stocks', index=False)
            
            # 投资建议
            if 'recommendations' in self.analysis_results:
                recommendations = self.analysis_results['recommendations']
                
                for category, stocks in recommendations.items():
                    if stocks:
                        df = pd.DataFrame(stocks)
                        sheet_name = category.replace('_', ' ').title()
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
        
        print(f"✅ 分析报告已导出: {output_file}")
        return output_file


def main():
    """主函数"""
    print("🚀 恒生指数成分股ROE数据综合分析工具")
    print("=" * 60)
    
    # 创建分析器
    analyzer = HSIROEComprehensiveAnalyzer()
    
    # 加载数据
    if not analyzer.load_data():
        return
    
    # 计算ROE指标
    metrics_df = analyzer.calculate_roe_metrics()
    
    # 识别高质量股票
    high_quality_stocks = analyzer.identify_high_quality_stocks(top_n=15)
    
    # 生成投资建议
    recommendations = analyzer.generate_investment_recommendations()
    
    # 创建交互式仪表板
    dashboard_file = analyzer.create_interactive_dashboard()
    
    # 导出分析报告
    report_file = analyzer.export_analysis_report()
    
    print(f"\n🎉 综合分析完成！")
    print(f"📊 交互式仪表板: {dashboard_file}")
    print(f"📋 分析报告: {report_file}")


if __name__ == "__main__":
    main()
