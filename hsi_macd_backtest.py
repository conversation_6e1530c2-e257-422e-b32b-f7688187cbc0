import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from matplotlib.dates import DateFormatter
import matplotlib.dates as mdates
import os
from pathlib import Path
from datetime import datetime
from scipy.signal import argrelextrema

def calculate_macd(df, fast_period=12, slow_period=26, signal_period=9):
    """
    Calculate MACD, MACD Signal and MACD Histogram

    Args:
        df (pandas.DataFrame): DataFrame with price data
        fast_period (int): Fast EMA period
        slow_period (int): Slow EMA period
        signal_period (int): Signal EMA period

    Returns:
        pandas.DataFrame: DataFrame with MACD indicators
    """
    # Make a copy of the DataFrame to avoid modifying the original
    df_macd = df.copy()

    # Calculate the Fast and Slow EMAs
    df_macd['ema_fast'] = df_macd['Close'].ewm(span=fast_period, adjust=False).mean()
    df_macd['ema_slow'] = df_macd['Close'].ewm(span=slow_period, adjust=False).mean()

    # Calculate MACD Line
    df_macd['macd'] = df_macd['ema_fast'] - df_macd['ema_slow']

    # Calculate Signal Line
    df_macd['macd_signal'] = df_macd['macd'].ewm(span=signal_period, adjust=False).mean()

    # Calculate MACD Histogram
    df_macd['macd_hist'] = df_macd['macd'] - df_macd['macd_signal']

    return df_macd



def backtest_macd_divergence_strategy(df, initial_capital=100000, position_size=1.0, price_window=10, macd_window=10):
    """
    Backtest MACD divergence strategy

    Strategy:
    - Buy when bullish divergence is detected (price makes lower lows, MACD makes higher lows)
    - Sell when bearish divergence is detected (price makes higher highs, MACD makes lower highs)

    Args:
        df (pandas.DataFrame): DataFrame with MACD indicators
        initial_capital (float): Initial capital for the backtest
        position_size (float): Percentage of capital to use for each trade (0.0-1.0)
        price_window (int): Window size for finding local extrema in price
        macd_window (int): Window size for finding local extrema in MACD

    Returns:
        pandas.DataFrame: DataFrame with backtest results
    """
    # Detect MACD divergences
    df_div = detect_macd_divergence(df, price_window=price_window, macd_window=macd_window)

    # Make a copy of the DataFrame
    backtest = df_div.copy()

    # Initialize columns for the backtest
    backtest['signal'] = 0  # 1 for buy, -1 for sell
    backtest['position'] = 0  # 1 for long, 0 for flat
    backtest['entry_price'] = np.nan
    backtest['exit_price'] = np.nan
    backtest['pnl'] = 0.0
    backtest['capital'] = initial_capital

    # Generate signals based on MACD divergences
    for i in range(1, len(backtest)):
        # Bullish divergence (Buy signal)
        if backtest['bullish_divergence'].iloc[i]:
            backtest.loc[backtest.index[i], 'signal'] = 1

        # Bearish divergence (Sell signal)
        elif backtest['bearish_divergence'].iloc[i]:
            backtest.loc[backtest.index[i], 'signal'] = -1

    # Execute trades based on signals
    position = 0
    entry_price = 0

    for i in range(1, len(backtest)):
        # Update position based on previous position and current signal
        if position == 0 and backtest['signal'].iloc[i] == 1:
            # Enter long position
            position = 1
            entry_price = backtest['Close'].iloc[i]
            backtest.loc[backtest.index[i], 'entry_price'] = entry_price
        elif position == 1 and backtest['signal'].iloc[i] == -1:
            # Exit long position
            position = 0
            exit_price = backtest['Close'].iloc[i]
            backtest.loc[backtest.index[i], 'exit_price'] = exit_price

            # Calculate P&L for this trade
            shares = (backtest['capital'].iloc[i-1] * position_size) / entry_price
            trade_pnl = shares * (exit_price - entry_price)
            backtest.loc[backtest.index[i], 'pnl'] = trade_pnl

            # Update capital
            backtest.loc[backtest.index[i], 'capital'] = backtest['capital'].iloc[i-1] + trade_pnl
        else:
            # No change in position
            backtest.loc[backtest.index[i], 'capital'] = backtest['capital'].iloc[i-1]

        # Update position column
        backtest.loc[backtest.index[i], 'position'] = position

    # Calculate cumulative P&L
    backtest['cumulative_pnl'] = backtest['pnl'].cumsum()

    # Calculate returns
    backtest['returns'] = backtest['capital'].pct_change()
    backtest['cumulative_returns'] = (1 + backtest['returns']).cumprod() - 1

    return backtest

def detect_macd_divergence(df, price_window=10, macd_window=10, min_points=3):
    """
    Detect MACD divergences

    Strategy:
    - Bullish divergence: Price makes lower lows while MACD makes higher lows
    - Bearish divergence: Price makes higher highs while MACD makes lower highs

    Args:
        df (pandas.DataFrame): DataFrame with MACD indicators
        price_window (int): Window size for finding local extrema in price
        macd_window (int): Window size for finding local extrema in MACD
        min_points (int): Minimum number of points to confirm a divergence

    Returns:
        pandas.DataFrame: DataFrame with divergence signals
    """
    # Make a copy of the DataFrame
    df_div = df.copy()

    # Initialize divergence columns
    df_div['bullish_divergence'] = False
    df_div['bearish_divergence'] = False

    # Find local minima and maxima in price
    price_minima_idx = argrelextrema(df_div['Close'].values, np.less, order=price_window)[0]
    price_maxima_idx = argrelextrema(df_div['Close'].values, np.greater, order=price_window)[0]

    # Find local minima and maxima in MACD
    macd_minima_idx = argrelextrema(df_div['macd'].values, np.less, order=macd_window)[0]
    macd_maxima_idx = argrelextrema(df_div['macd'].values, np.greater, order=macd_window)[0]

    # Mark the local extrema points
    df_div['price_min'] = False
    df_div['price_max'] = False
    df_div['macd_min'] = False
    df_div['macd_max'] = False

    df_div.iloc[price_minima_idx, df_div.columns.get_loc('price_min')] = True
    df_div.iloc[price_maxima_idx, df_div.columns.get_loc('price_max')] = True
    df_div.iloc[macd_minima_idx, df_div.columns.get_loc('macd_min')] = True
    df_div.iloc[macd_maxima_idx, df_div.columns.get_loc('macd_max')] = True

    # Detect bullish divergences (price makes lower lows, MACD makes higher lows)
    for i in range(len(df_div) - min_points):
        if i in price_minima_idx:  # If this is a price minimum
            # Find the next price minimum
            next_mins = price_minima_idx[price_minima_idx > i]
            if len(next_mins) > 0:
                next_min_idx = next_mins[0]

                # Check if price made a lower low
                if df_div['Close'].iloc[next_min_idx] < df_div['Close'].iloc[i]:
                    # Find corresponding MACD minima
                    macd_min_before = macd_minima_idx[(macd_minima_idx >= i - 3) & (macd_minima_idx <= i + 3)]
                    macd_min_after = macd_minima_idx[(macd_minima_idx >= next_min_idx - 3) & (macd_minima_idx <= next_min_idx + 3)]

                    if len(macd_min_before) > 0 and len(macd_min_after) > 0:
                        macd_min_before_idx = macd_min_before[0]
                        macd_min_after_idx = macd_min_after[0]

                        # Check if MACD made a higher low (bullish divergence)
                        if df_div['macd'].iloc[macd_min_after_idx] > df_div['macd'].iloc[macd_min_before_idx]:
                            df_div.loc[df_div.index[next_min_idx], 'bullish_divergence'] = True

    # Detect bearish divergences (price makes higher highs, MACD makes lower highs)
    for i in range(len(df_div) - min_points):
        if i in price_maxima_idx:  # If this is a price maximum
            # Find the next price maximum
            next_maxs = price_maxima_idx[price_maxima_idx > i]
            if len(next_maxs) > 0:
                next_max_idx = next_maxs[0]

                # Check if price made a higher high
                if df_div['Close'].iloc[next_max_idx] > df_div['Close'].iloc[i]:
                    # Find corresponding MACD maxima
                    macd_max_before = macd_maxima_idx[(macd_maxima_idx >= i - 3) & (macd_maxima_idx <= i + 3)]
                    macd_max_after = macd_maxima_idx[(macd_maxima_idx >= next_max_idx - 3) & (macd_maxima_idx <= next_max_idx + 3)]

                    if len(macd_max_before) > 0 and len(macd_max_after) > 0:
                        macd_max_before_idx = macd_max_before[0]
                        macd_max_after_idx = macd_max_after[0]

                        # Check if MACD made a lower high (bearish divergence)
                        if df_div['macd'].iloc[macd_max_after_idx] < df_div['macd'].iloc[macd_max_before_idx]:
                            df_div.loc[df_div.index[next_max_idx], 'bearish_divergence'] = True

    return df_div

def calculate_performance_metrics(backtest):
    """
    Calculate performance metrics for the backtest

    Args:
        backtest (pandas.DataFrame): DataFrame with backtest results

    Returns:
        dict: Dictionary with performance metrics
    """
    # Filter out rows with no returns
    returns = backtest['returns'].dropna()

    # Calculate total return
    total_return = backtest['capital'].iloc[-1] / backtest['capital'].iloc[0] - 1

    # Calculate annualized return
    days = (backtest.index[-1] - backtest.index[0]).days
    years = days / 365.25
    annualized_return = (1 + total_return) ** (1 / years) - 1

    # Calculate max drawdown
    cumulative_returns = backtest['cumulative_returns'].fillna(0)
    rolling_max = cumulative_returns.cummax()
    drawdown = (cumulative_returns - rolling_max) / (1 + rolling_max)
    max_drawdown = drawdown.min()

    # Calculate Sharpe ratio (assuming risk-free rate of 0)
    sharpe_ratio = np.sqrt(252) * returns.mean() / returns.std() if returns.std() != 0 else 0

    # Calculate win rate
    trades = backtest[backtest['pnl'] != 0]
    winning_trades = trades[trades['pnl'] > 0]
    win_rate = len(winning_trades) / len(trades) if len(trades) > 0 else 0

    # Calculate average profit per trade
    avg_profit = trades['pnl'].mean() if len(trades) > 0 else 0

    # Calculate profit factor
    gross_profit = winning_trades['pnl'].sum() if len(winning_trades) > 0 else 0
    losing_trades = trades[trades['pnl'] < 0]
    gross_loss = abs(losing_trades['pnl'].sum()) if len(losing_trades) > 0 else 0
    profit_factor = gross_profit / gross_loss if gross_loss != 0 else float('inf')

    # Calculate maximum consecutive wins and losses
    consecutive_wins = 0
    max_consecutive_wins = 0
    consecutive_losses = 0
    max_consecutive_losses = 0

    for pnl in trades['pnl']:
        if pnl > 0:
            consecutive_wins += 1
            consecutive_losses = 0
            max_consecutive_wins = max(max_consecutive_wins, consecutive_wins)
        elif pnl < 0:
            consecutive_losses += 1
            consecutive_wins = 0
            max_consecutive_losses = max(max_consecutive_losses, consecutive_losses)

    # Prepare metrics dictionary
    metrics = {
        'total_return': total_return,
        'annualized_return': annualized_return,
        'max_drawdown': max_drawdown,
        'sharpe_ratio': sharpe_ratio,
        'win_rate': win_rate,
        'avg_profit': avg_profit,
        'profit_factor': profit_factor,
        'total_trades': len(trades),
        'winning_trades': len(winning_trades),
        'losing_trades': len(losing_trades),
        'max_consecutive_wins': max_consecutive_wins,
        'max_consecutive_losses': max_consecutive_losses
    }

    return metrics

def plot_backtest_results(backtest, title="MACD Strategy Backtest", start_date=None, end_date=None):
    """
    Plot backtest results

    Args:
        backtest (pandas.DataFrame): DataFrame with backtest results
        title (str): Chart title
        start_date (str): Start date for the chart in 'YYYY-MM-DD' format
        end_date (str): End date for the chart in 'YYYY-MM-DD' format
    """
    # Filter data by date range if specified
    if start_date:
        backtest = backtest[backtest.index >= start_date]
    if end_date:
        backtest = backtest[backtest.index <= end_date]

    # Create figure and axis
    fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(14, 12), gridspec_kw={'height_ratios': [3, 1, 1]})

    # Plot price chart with buy/sell signals
    ax1.plot(backtest.index, backtest['Close'], color='blue', linewidth=1.5)

    # Plot buy signals
    buy_signals = backtest[backtest['signal'] == 1]
    ax1.scatter(buy_signals.index, buy_signals['Close'], color='green', marker='^', s=100, label='Buy Signal')

    # Plot sell signals
    sell_signals = backtest[backtest['signal'] == -1]
    ax1.scatter(sell_signals.index, sell_signals['Close'], color='red', marker='v', s=100, label='Sell Signal')

    ax1.set_title(title, fontsize=16)
    ax1.set_ylabel('Price', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')

    # Format x-axis dates
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)

    # Plot MACD
    ax2.plot(backtest.index, backtest['macd'], color='blue', linewidth=1.5, label='MACD')
    ax2.plot(backtest.index, backtest['macd_signal'], color='red', linewidth=1, label='Signal')

    # Plot MACD Histogram
    for i in range(len(backtest)):
        if backtest['macd_hist'].iloc[i] >= 0:
            ax2.bar(backtest.index[i], backtest['macd_hist'].iloc[i], color='green', width=0.8, alpha=0.5)
        else:
            ax2.bar(backtest.index[i], backtest['macd_hist'].iloc[i], color='red', width=0.8, alpha=0.5)

    # Add zero line
    ax2.axhline(y=0, color='black', linestyle='-', linewidth=0.5)

    ax2.set_ylabel('MACD', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left')

    # Format x-axis dates
    ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax2.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax2.xaxis.get_majorticklabels(), rotation=45)

    # Plot equity curve
    ax3.plot(backtest.index, backtest['capital'], color='green', linewidth=1.5)
    ax3.set_ylabel('Capital', fontsize=12)
    ax3.grid(True, alpha=0.3)

    # Format x-axis dates
    ax3.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m-%d'))
    ax3.xaxis.set_major_locator(mdates.MonthLocator(interval=3))
    plt.setp(ax3.xaxis.get_majorticklabels(), rotation=45)

    plt.tight_layout()

    # Save the figure
    output_dir = Path("output")
    if not output_dir.exists():
        output_dir.mkdir(parents=True)

    filename = f"{title.replace(' ', '_').lower()}.png"
    plt.savefig(output_dir / filename, dpi=300, bbox_inches='tight')
    print(f"Chart saved to {output_dir / filename}")

    # Show the plot
    plt.show()

def resample_to_weekly(df):
    """
    Resample daily data to weekly data

    Args:
        df (pandas.DataFrame): DataFrame with daily data

    Returns:
        pandas.DataFrame: DataFrame with weekly data
    """
    # Make sure the index is datetime
    if not isinstance(df.index, pd.DatetimeIndex):
        df.index = pd.to_datetime(df.index)

    # Resample to weekly (last business day of the week)
    weekly_df = df.resample('W-FRI').agg({
        'Open': 'first',
        'High': 'max',
        'Low': 'min',
        'Close': 'last',
        'Volume': 'sum'
    })

    # Drop any rows with NaN values
    weekly_df.dropna(inplace=True)

    return weekly_df

def main():
    # Load HSI data
    data_file = Path("data/hsi_data.csv")

    if not data_file.exists():
        print(f"Error: Data file {data_file} not found.")
        return

    print(f"Loading data from {data_file}...")
    df = pd.read_csv(data_file)

    # Convert Date to datetime and set as index
    df['Date'] = pd.to_datetime(df['Date'])
    df.set_index('Date', inplace=True)

    # Sort by date (ascending)
    df.sort_index(inplace=True)

    print(f"Loaded {len(df)} records from {df.index.min().strftime('%Y-%m-%d')} to {df.index.max().strftime('%Y-%m-%d')}")

    # Resample to weekly data
    print("Resampling daily data to weekly...")
    weekly_df = resample_to_weekly(df)
    print(f"Resampled to {len(weekly_df)} weekly records")

    # Calculate MACD on weekly data
    print("Calculating MACD indicators on weekly data...")
    weekly_macd = calculate_macd(weekly_df)

    # Define backtest period
    start_date = "2015-01-01"
    end_date = weekly_macd.index.max().strftime('%Y-%m-%d')

    # Filter data for backtest period
    backtest_data = weekly_macd[(weekly_macd.index >= start_date) & (weekly_macd.index <= end_date)]

    print(f"Running backtests for period: {start_date} to {end_date}")

    # Create a new function for MACD histogram trend change strategy on weekly data
    def backtest_macd_histogram_trend_weekly(df, initial_capital=100000, position_size=1.0):
        """
        Backtest MACD histogram trend change strategy on weekly data

        Strategy:
        - Buy when MACD histogram stops decreasing and starts increasing (trend change from down to up)
        - Sell when MACD histogram stops increasing and starts decreasing (trend change from up to down)

        Args:
            df (pandas.DataFrame): DataFrame with MACD indicators
            initial_capital (float): Initial capital for the backtest
            position_size (float): Percentage of capital to use for each trade (0.0-1.0)

        Returns:
            pandas.DataFrame: DataFrame with backtest results
        """
        # Make a copy of the DataFrame
        backtest = df.copy()

        # Initialize columns for the backtest
        backtest['signal'] = 0  # 1 for buy, -1 for sell
        backtest['position'] = 0  # 1 for long, 0 for flat
        backtest['entry_price'] = np.nan
        backtest['exit_price'] = np.nan
        backtest['pnl'] = 0.0
        backtest['capital'] = initial_capital

        # Calculate histogram change (current - previous)
        backtest['hist_change'] = backtest['macd_hist'].diff()

        # Identify trend changes in the histogram
        backtest['trend_up'] = False  # Histogram starts increasing (potential buy)
        backtest['trend_down'] = False  # Histogram starts decreasing (potential sell)

        # Generate signals based on MACD histogram trend changes
        for i in range(2, len(backtest)):
            # Trend change from down to up (Buy signal)
            # Previous change was negative or zero, current change is positive
            if backtest['hist_change'].iloc[i-1] <= 0 and backtest['hist_change'].iloc[i] > 0:
                backtest.loc[backtest.index[i], 'trend_up'] = True
                backtest.loc[backtest.index[i], 'signal'] = 1

            # Trend change from up to down (Sell signal)
            # Previous change was positive or zero, current change is negative
            elif backtest['hist_change'].iloc[i-1] >= 0 and backtest['hist_change'].iloc[i] < 0:
                backtest.loc[backtest.index[i], 'trend_down'] = True
                backtest.loc[backtest.index[i], 'signal'] = -1

        # Execute trades based on signals
        position = 0
        entry_price = 0

        for i in range(1, len(backtest)):
            # Update position based on previous position and current signal
            if position == 0 and backtest['signal'].iloc[i] == 1:
                # Enter long position
                position = 1
                entry_price = backtest['Close'].iloc[i]
                backtest.loc[backtest.index[i], 'entry_price'] = entry_price
            elif position == 1 and backtest['signal'].iloc[i] == -1:
                # Exit long position
                position = 0
                exit_price = backtest['Close'].iloc[i]
                backtest.loc[backtest.index[i], 'exit_price'] = exit_price

                # Calculate P&L for this trade
                shares = (backtest['capital'].iloc[i-1] * position_size) / entry_price
                trade_pnl = shares * (exit_price - entry_price)
                backtest.loc[backtest.index[i], 'pnl'] = trade_pnl

                # Update capital
                backtest.loc[backtest.index[i], 'capital'] = backtest['capital'].iloc[i-1] + trade_pnl
            else:
                # No change in position
                backtest.loc[backtest.index[i], 'capital'] = backtest['capital'].iloc[i-1]

            # Update position column
            backtest.loc[backtest.index[i], 'position'] = position

        # Calculate cumulative P&L
        backtest['cumulative_pnl'] = backtest['pnl'].cumsum()

        # Calculate returns
        backtest['returns'] = backtest['capital'].pct_change()
        backtest['cumulative_returns'] = (1 + backtest['returns']).cumprod() - 1

        return backtest

    # Run backtest for MACD Histogram Trend Change Strategy on weekly data
    print("\nBacktesting MACD Histogram Trend Change Strategy (Weekly)...")
    histogram_results = backtest_macd_histogram_trend_weekly(backtest_data)
    histogram_metrics = calculate_performance_metrics(histogram_results)

    # Print performance metrics
    print("\n=== Performance Metrics ===")

    print("\nMACD Histogram Trend Change Strategy (Weekly):")
    print(f"Total Return: {histogram_metrics['total_return']:.2%}")
    print(f"Annualized Return: {histogram_metrics['annualized_return']:.2%}")
    print(f"Max Drawdown: {histogram_metrics['max_drawdown']:.2%}")
    print(f"Sharpe Ratio: {histogram_metrics['sharpe_ratio']:.2f}")
    print(f"Win Rate: {histogram_metrics['win_rate']:.2%}")
    print(f"Profit Factor: {histogram_metrics['profit_factor']:.2f}")
    print(f"Total Trades: {histogram_metrics['total_trades']}")

    # Plot backtest results
    print("\nGenerating backtest charts...")

    plot_backtest_results(histogram_results, title="MACD Histogram Trend Change Strategy Backtest (Weekly)",
                         start_date=start_date, end_date=end_date)

    print("Backtest complete.")

if __name__ == "__main__":
    main()
