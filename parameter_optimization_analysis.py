#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单分位数+回落卖出策略参数优化分析
优化买入阈值、卖出阈值、回落天数三个参数
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from statsmodels.tsa.filters.hp_filter import hpfilter
import pickle
import itertools
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ParameterOptimizer:
    """参数优化器"""
    
    def __init__(self):
        self.data = None
        self.processed_data = None
        self.optimization_results = []
        
    def load_and_process_data(self):
        """加载和处理数据"""
        try:
            cache_file = 'cache/511260_price_data.pkl'
            with open(cache_file, 'rb') as f:
                self.data = pickle.load(f)
            
            # 确保索引是日期类型
            if not isinstance(self.data.index, pd.DatetimeIndex):
                if 'date' in self.data.columns:
                    self.data['date'] = pd.to_datetime(self.data['date'])
                    self.data.set_index('date', inplace=True)
                else:
                    self.data.index = pd.to_datetime(self.data.index)
            
            # 标准化列名
            if 'close' not in self.data.columns:
                for col in self.data.columns:
                    if 'close' in col.lower() or '收盘' in col:
                        self.data['close'] = self.data[col]
                        break
            
            # 筛选最近10年数据
            end_date = self.data.index[-1]
            start_date = end_date - pd.DateOffset(years=10)
            self.data = self.data[self.data.index >= start_date]

            print(f"✅ 成功加载511260数据")
            print(f"   数据期间: {self.data.index[0].strftime('%Y-%m-%d')} 至 {self.data.index[-1].strftime('%Y-%m-%d')}")
            print(f"   数据点数: {len(self.data)} 个")

            # 数据处理
            price = self.data['close']
            
            # HP滤波去趋势
            cycle, trend = hpfilter(price.dropna(), lamb=7000000)
            
            # 创建处理后的数据
            self.processed_data = pd.DataFrame(index=price.index)
            self.processed_data['价格'] = price
            self.processed_data['趋势成分'] = trend.reindex(price.index)
            self.processed_data['去趋势数据'] = cycle.reindex(price.index)
            
            # 计算百分位数
            self.processed_data['百分位数'] = self.processed_data['去趋势数据'].rolling(252).rank(pct=True)
            
            # 计算价格变化
            self.processed_data['价格变化'] = self.processed_data['价格'].pct_change()
            
            return True
            
        except Exception as e:
            print(f"❌ 数据加载失败: {e}")
            return False

    def calculate_consecutive_days(self, pullback_days):
        """计算连续下跌天数"""
        self.processed_data['连续下跌天数'] = 0
        
        for i in range(1, len(self.processed_data)):
            if self.processed_data['价格变化'].iloc[i] < 0:
                self.processed_data['连续下跌天数'].iloc[i] = self.processed_data['连续下跌天数'].iloc[i-1] + 1
            else:
                self.processed_data['连续下跌天数'].iloc[i] = 0

    def backtest_strategy(self, buy_threshold, sell_threshold, pullback_days):
        """回测单个参数组合"""
        # 计算连续下跌天数
        self.calculate_consecutive_days(pullback_days)
        
        # 生成买卖信号
        buy_signals = self.processed_data['百分位数'] <= buy_threshold
        sell_signals = (
            (self.processed_data['百分位数'] >= sell_threshold) & 
            (self.processed_data['连续下跌天数'] >= pullback_days)
        )
        
        # 执行交易
        trades = []
        position = 0
        buy_date = None
        buy_price = None
        
        for date, row in self.processed_data.iterrows():
            if position == 0 and buy_signals.loc[date] and not pd.isna(row['百分位数']):
                position = 1
                buy_date = date
                buy_price = row['价格']
                
            elif position == 1 and sell_signals.loc[date] and not pd.isna(row['百分位数']):
                sell_date = date
                sell_price = row['价格']
                trade_return = (sell_price - buy_price) / buy_price
                hold_days = (date - buy_date).days
                
                trades.append({
                    'buy_date': buy_date,
                    'sell_date': sell_date,
                    'buy_price': buy_price,
                    'sell_price': sell_price,
                    'hold_days': hold_days,
                    'return': trade_return
                })
                
                position = 0
                buy_date = None
                buy_price = None
        
        # 计算策略表现
        if not trades:
            return None
        
        # 计算每日收益
        daily_returns = pd.Series(0.0, index=self.processed_data.index)
        for trade in trades:
            mask = (self.processed_data.index >= trade['buy_date']) & (self.processed_data.index <= trade['sell_date'])
            price_returns = self.processed_data['价格'].pct_change()
            daily_returns[mask] = price_returns[mask]
        
        # 处理当前持仓（如果有）
        if position == 1:
            mask = self.processed_data.index >= buy_date
            price_returns = self.processed_data['价格'].pct_change()
            daily_returns[mask] = price_returns[mask]
        
        # 计算累积收益
        cumulative_returns = (1 + daily_returns).cumprod()
        
        # 计算回撤
        rolling_max = cumulative_returns.expanding().max()
        drawdowns = (cumulative_returns - rolling_max) / rolling_max
        
        # 策略统计指标
        total_return = cumulative_returns.iloc[-1] - 1
        annual_return = (1 + total_return) ** (252 / len(daily_returns)) - 1
        volatility = daily_returns.std() * np.sqrt(252)
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        max_drawdown = drawdowns.min()
        
        # 交易统计
        trade_returns = [t['return'] for t in trades]
        win_rate = sum(1 for r in trade_returns if r > 0) / len(trade_returns)
        avg_return = np.mean(trade_returns)
        avg_hold_days = np.mean([t['hold_days'] for t in trades])
        
        return {
            'buy_threshold': buy_threshold,
            'sell_threshold': sell_threshold,
            'pullback_days': pullback_days,
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'avg_return': avg_return,
            'avg_hold_days': avg_hold_days,
            'total_trades': len(trades),
            'current_position': position
        }

    def optimize_parameters(self):
        """参数优化"""
        print("\n🔄 开始参数优化...")
        
        # 定义参数范围
        buy_thresholds = [0.05, 0.10, 0.15, 0.20, 0.25]  # 5%-25%
        sell_thresholds = [0.80, 0.85, 0.90, 0.95]       # 80%-95%
        pullback_days_range = [1, 2, 3, 4, 5]            # 1-5天
        
        total_combinations = len(buy_thresholds) * len(sell_thresholds) * len(pullback_days_range)
        print(f"   总共需要测试 {total_combinations} 个参数组合")
        
        self.optimization_results = []
        count = 0
        
        for buy_thresh, sell_thresh, pullback_days in itertools.product(buy_thresholds, sell_thresholds, pullback_days_range):
            count += 1
            if count % 10 == 0:
                print(f"   进度: {count}/{total_combinations} ({count/total_combinations*100:.1f}%)")
            
            result = self.backtest_strategy(buy_thresh, sell_thresh, pullback_days)
            if result:
                self.optimization_results.append(result)
        
        print(f"✅ 参数优化完成，共测试了 {len(self.optimization_results)} 个有效组合")
        return True

    def analyze_optimization_results(self):
        """分析优化结果"""
        if not self.optimization_results:
            print("❌ 没有优化结果")
            return
        
        # 转换为DataFrame
        df = pd.DataFrame(self.optimization_results)
        
        print("\n" + "="*80)
        print("📊 参数优化结果分析")
        print("="*80)
        
        # 按不同指标排序找出最佳参数
        metrics = {
            '年化收益率': 'annual_return',
            '夏普比率': 'sharpe_ratio', 
            '总收益率': 'total_return',
            '最大回撤': 'max_drawdown'
        }
        
        for metric_name, metric_col in metrics.items():
            print(f"\n🏆 按{metric_name}排序的前5名:")
            if metric_col == 'max_drawdown':
                # 回撤越小越好
                top_results = df.nlargest(5, metric_col)  # 因为回撤是负数，nlargest实际是最小回撤
            else:
                top_results = df.nlargest(5, metric_col)
            
            for i, (_, row) in enumerate(top_results.iterrows(), 1):
                print(f"   第{i}名: 买入{row['buy_threshold']*100:.0f}% 卖出{row['sell_threshold']*100:.0f}% 回落{row['pullback_days']:.0f}天")
                print(f"         年化收益{row['annual_return']*100:.2f}% 夏普{row['sharpe_ratio']:.2f} 回撤{row['max_drawdown']*100:.2f}% 交易{row['total_trades']:.0f}次")
        
        # 综合评分
        print(f"\n🎯 综合评分排序 (年化收益率×0.4 + 夏普比率×0.4 + 回撤改善×0.2):")
        df['综合评分'] = (
            df['annual_return'] * 0.4 + 
            df['sharpe_ratio'] * 0.4 + 
            (-df['max_drawdown']) * 0.2  # 回撤越小越好
        )
        
        top_comprehensive = df.nlargest(10, '综合评分')
        for i, (_, row) in enumerate(top_comprehensive.iterrows(), 1):
            print(f"   第{i}名: 买入{row['buy_threshold']*100:.0f}% 卖出{row['sell_threshold']*100:.0f}% 回落{row['pullback_days']:.0f}天")
            print(f"         综合评分{row['综合评分']:.3f} 年化{row['annual_return']*100:.2f}% 夏普{row['sharpe_ratio']:.2f} 回撤{row['max_drawdown']*100:.2f}%")
        
        # 保存最佳参数
        best_params = top_comprehensive.iloc[0]
        print(f"\n🏅 推荐最佳参数组合:")
        print(f"   买入阈值: {best_params['buy_threshold']*100:.0f}%")
        print(f"   卖出阈值: {best_params['sell_threshold']*100:.0f}%") 
        print(f"   回落天数: {best_params['pullback_days']:.0f}天")
        print(f"   预期年化收益率: {best_params['annual_return']*100:.2f}%")
        print(f"   预期夏普比率: {best_params['sharpe_ratio']:.2f}")
        print(f"   预期最大回撤: {best_params['max_drawdown']*100:.2f}%")
        
        return best_params

    def create_optimization_visualization(self):
        """创建优化结果可视化"""
        if not self.optimization_results:
            return
        
        df = pd.DataFrame(self.optimization_results)
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('511260策略参数优化结果分析', fontsize=16, fontweight='bold')
        
        # 1. 年化收益率热力图 (买入阈值 vs 卖出阈值)
        ax1 = axes[0, 0]
        pivot_return = df.groupby(['buy_threshold', 'sell_threshold'])['annual_return'].mean().unstack()
        im1 = ax1.imshow(pivot_return.values, cmap='RdYlGn', aspect='auto')
        ax1.set_xticks(range(len(pivot_return.columns)))
        ax1.set_yticks(range(len(pivot_return.index)))
        ax1.set_xticklabels([f'{x*100:.0f}%' for x in pivot_return.columns])
        ax1.set_yticklabels([f'{x*100:.0f}%' for x in pivot_return.index])
        ax1.set_xlabel('卖出阈值')
        ax1.set_ylabel('买入阈值')
        ax1.set_title('年化收益率热力图')
        plt.colorbar(im1, ax=ax1)
        
        # 2. 夏普比率热力图
        ax2 = axes[0, 1]
        pivot_sharpe = df.groupby(['buy_threshold', 'sell_threshold'])['sharpe_ratio'].mean().unstack()
        im2 = ax2.imshow(pivot_sharpe.values, cmap='RdYlGn', aspect='auto')
        ax2.set_xticks(range(len(pivot_sharpe.columns)))
        ax2.set_yticks(range(len(pivot_sharpe.index)))
        ax2.set_xticklabels([f'{x*100:.0f}%' for x in pivot_sharpe.columns])
        ax2.set_yticklabels([f'{x*100:.0f}%' for x in pivot_sharpe.index])
        ax2.set_xlabel('卖出阈值')
        ax2.set_ylabel('买入阈值')
        ax2.set_title('夏普比率热力图')
        plt.colorbar(im2, ax=ax2)
        
        # 3. 回落天数影响
        ax3 = axes[0, 2]
        pullback_stats = df.groupby('pullback_days').agg({
            'annual_return': 'mean',
            'sharpe_ratio': 'mean',
            'max_drawdown': 'mean'
        })
        
        ax3_twin = ax3.twinx()
        bars1 = ax3.bar(pullback_stats.index - 0.2, pullback_stats['annual_return'] * 100, 
                       width=0.4, label='年化收益率(%)', alpha=0.7, color='green')
        bars2 = ax3_twin.bar(pullback_stats.index + 0.2, pullback_stats['sharpe_ratio'], 
                            width=0.4, label='夏普比率', alpha=0.7, color='blue')
        
        ax3.set_xlabel('回落天数')
        ax3.set_ylabel('年化收益率 (%)', color='green')
        ax3_twin.set_ylabel('夏普比率', color='blue')
        ax3.set_title('回落天数对策略表现的影响')
        ax3.legend(loc='upper left')
        ax3_twin.legend(loc='upper right')
        
        # 4. 收益率 vs 回撤散点图
        ax4 = axes[1, 0]
        scatter = ax4.scatter(df['max_drawdown'] * 100, df['annual_return'] * 100, 
                             c=df['sharpe_ratio'], cmap='viridis', alpha=0.6)
        ax4.set_xlabel('最大回撤 (%)')
        ax4.set_ylabel('年化收益率 (%)')
        ax4.set_title('收益率 vs 回撤关系')
        plt.colorbar(scatter, ax=ax4, label='夏普比率')
        
        # 5. 交易次数分布
        ax5 = axes[1, 1]
        ax5.hist(df['total_trades'], bins=15, alpha=0.7, color='orange', edgecolor='black')
        ax5.set_xlabel('交易次数')
        ax5.set_ylabel('参数组合数量')
        ax5.set_title('交易次数分布')
        ax5.axvline(df['total_trades'].mean(), color='red', linestyle='--', 
                   label=f'平均: {df["total_trades"].mean():.1f}次')
        ax5.legend()
        
        # 6. 综合评分前10名
        ax6 = axes[1, 2]
        df['综合评分'] = (df['annual_return'] * 0.4 + df['sharpe_ratio'] * 0.4 + (-df['max_drawdown']) * 0.2)
        top_10 = df.nlargest(10, '综合评分')
        
        bars = ax6.barh(range(len(top_10)), top_10['综合评分'], alpha=0.7, color='purple')
        ax6.set_yticks(range(len(top_10)))
        ax6.set_yticklabels([f"{row['buy_threshold']*100:.0f}%-{row['sell_threshold']*100:.0f}%-{row['pullback_days']:.0f}天" 
                            for _, row in top_10.iterrows()])
        ax6.set_xlabel('综合评分')
        ax6.set_title('综合评分前10名参数组合')
        
        # 添加数值标签
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax6.text(width + 0.01, bar.get_y() + bar.get_height()/2, 
                    f'{width:.3f}', ha='left', va='center', fontsize=8)
        
        plt.tight_layout()
        
        # 保存图表
        chart_path = 'data/parameter_optimization_results.png'
        plt.savefig(chart_path, dpi=300, bbox_inches='tight')
        print(f"✅ 参数优化结果图表已保存: {chart_path}")
        
        plt.show()
        return chart_path

def main():
    """主函数"""
    print("🚀 511260策略参数优化分析")
    print("="*60)
    print("优化目标：买入阈值、卖出阈值、回落天数")
    print("="*60)
    
    # 创建优化器
    optimizer = ParameterOptimizer()
    
    # 加载数据
    if not optimizer.load_and_process_data():
        return None
    
    # 执行参数优化
    if not optimizer.optimize_parameters():
        return None
    
    # 分析结果
    best_params = optimizer.analyze_optimization_results()
    
    # 创建可视化
    optimizer.create_optimization_visualization()
    
    print("\n" + "="*60)
    print("✅ 参数优化分析完成！")
    print("="*60)
    
    return optimizer, best_params

if __name__ == "__main__":
    result = main()
