#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
择时策略阈值优化

通过网格搜索找出进出市场的最佳阈值组合，
优化目标包括夏普比率、年化收益率、最大回撤等多个指标。
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
from datetime import datetime
import warnings
from itertools import product
import time

# 忽略警告信息
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 设置数据目录
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
CHARTS_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'charts')
os.makedirs(CHARTS_DIR, exist_ok=True)

def load_data():
    """加载择时指标和标普500数据"""
    print("正在加载数据...")

    # 加载择时指标
    timing_file = os.path.join(DATA_DIR, 'us_market_timing_indicator.csv')
    if not os.path.exists(timing_file):
        print(f"择时指标文件不存在: {timing_file}")
        return None, None

    timing_data = pd.read_csv(timing_file, index_col=0, parse_dates=True)
    timing_indicator = timing_data['Market_Timing_Indicator']

    # 加载标普500数据
    sp500_file = os.path.join(DATA_DIR, 'sp500_data.csv')
    if not os.path.exists(sp500_file):
        print(f"标普500数据文件不存在: {sp500_file}")
        return None, None

    sp500_data = pd.read_csv(sp500_file, skiprows=2)
    sp500_data['Date'] = pd.to_datetime(sp500_data['Date'])
    sp500_data.set_index('Date', inplace=True)

    # 使用第一列作为价格数据
    if 'Price' in sp500_data.columns:
        sp500_prices = sp500_data['Price'].dropna()
    else:
        # 如果没有Price列，使用第一个数值列
        sp500_prices = sp500_data.iloc[:, 0].dropna()

    print(f"择时指标数据: {len(timing_indicator)} 条记录")
    print(f"标普500数据: {len(sp500_prices)} 条记录")

    return timing_indicator, sp500_prices

def calculate_signals_optimized(timing_indicator, enter_threshold, exit_threshold):
    """计算交易信号（优化版本，无打印输出）"""
    signals = pd.Series(index=timing_indicator.index, dtype=float)
    signals[:] = 1.0  # 初始化为满仓

    in_cash_position = False

    for i, indicator_value in enumerate(timing_indicator.values):
        if not in_cash_position:
            if indicator_value < enter_threshold:
                in_cash_position = True
                signals.iloc[i] = 0.0
            else:
                signals.iloc[i] = 1.0
        else:
            if indicator_value > exit_threshold:
                in_cash_position = False
                signals.iloc[i] = 1.0
            else:
                signals.iloc[i] = 0.0

    return signals

def backtest_strategy_optimized(sp500_prices, signals, transaction_cost=0.001):
    """回测策略（优化版本）"""
    # 对齐数据
    common_dates = sp500_prices.index.intersection(signals.index)
    sp500_aligned = sp500_prices.loc[common_dates]
    signals_aligned = signals.loc[common_dates]

    if len(common_dates) < 100:  # 数据太少
        return None

    # 计算日收益率
    returns = sp500_aligned.pct_change().fillna(0)

    # 计算仓位变化和交易成本
    position_changes = signals_aligned.diff().fillna(0)
    trading_costs = np.abs(position_changes) * transaction_cost

    # 计算策略收益率
    strategy_returns = signals_aligned.shift(1) * returns - trading_costs
    strategy_returns = strategy_returns.fillna(0)

    # 计算现金收益（年化2%）
    cash_return = 0.02 / 252
    cash_returns = (1 - signals_aligned.shift(1)) * cash_return
    cash_returns = cash_returns.fillna(0)

    # 调整策略收益
    strategy_returns_adjusted = strategy_returns + cash_returns

    # 买入持有收益
    buy_hold_returns = returns

    return {
        'strategy_returns': strategy_returns_adjusted,
        'buy_hold_returns': buy_hold_returns,
        'signals': signals_aligned,
        'num_trades': (position_changes != 0).sum(),
        'total_cost': trading_costs.sum()
    }

def calculate_metrics(returns):
    """计算绩效指标"""
    if len(returns) == 0 or returns.std() == 0:
        return {
            'annual_return': 0,
            'annual_volatility': 0,
            'sharpe_ratio': 0,
            'max_drawdown': 0,
            'calmar_ratio': 0,
            'win_rate': 0
        }

    # 年化收益率
    annual_return = (1 + returns.mean()) ** 252 - 1

    # 年化波动率
    annual_volatility = returns.std() * np.sqrt(252)

    # 夏普比率
    risk_free_rate = 0.02
    sharpe_ratio = (annual_return - risk_free_rate) / annual_volatility if annual_volatility > 0 else 0

    # 最大回撤
    cumret = (1 + returns).cumprod()
    rolling_max = cumret.expanding().max()
    drawdown = (cumret - rolling_max) / rolling_max
    max_drawdown = drawdown.min()

    # 卡尔马比率
    calmar_ratio = annual_return / abs(max_drawdown) if max_drawdown != 0 else 0

    # 胜率
    win_rate = (returns > 0).mean()

    return {
        'annual_return': annual_return,
        'annual_volatility': annual_volatility,
        'sharpe_ratio': sharpe_ratio,
        'max_drawdown': max_drawdown,
        'calmar_ratio': calmar_ratio,
        'win_rate': win_rate
    }

def grid_search_thresholds(timing_indicator, sp500_prices,
                          enter_range=(5, 20), exit_range=(15, 40), step=1):
    """网格搜索最佳阈值"""
    print("开始网格搜索最佳阈值...")
    print(f"进入阈值范围: {enter_range[0]}-{enter_range[1]}")
    print(f"退出阈值范围: {exit_range[0]}-{exit_range[1]}")
    print(f"步长: {step}")

    enter_thresholds = range(enter_range[0], enter_range[1] + 1, step)
    exit_thresholds = range(exit_range[0], exit_range[1] + 1, step)

    results = []
    total_combinations = len(enter_thresholds) * len(exit_thresholds)

    print(f"总共需要测试 {total_combinations} 种组合...")

    start_time = time.time()

    for i, (enter_thresh, exit_thresh) in enumerate(product(enter_thresholds, exit_thresholds)):
        # 确保重新进入阈值大于退出阈值（exit_thresh是重新进入阈值，enter_thresh是退出阈值）
        if exit_thresh <= enter_thresh:
            continue

        # 计算信号
        signals = calculate_signals_optimized(timing_indicator, enter_thresh, exit_thresh)

        # 回测
        backtest_result = backtest_strategy_optimized(sp500_prices, signals)

        if backtest_result is None:
            continue

        # 计算指标
        strategy_metrics = calculate_metrics(backtest_result['strategy_returns'])
        buy_hold_metrics = calculate_metrics(backtest_result['buy_hold_returns'])

        # 计算相对表现
        excess_return = strategy_metrics['annual_return'] - buy_hold_metrics['annual_return']
        volatility_reduction = buy_hold_metrics['annual_volatility'] - strategy_metrics['annual_volatility']
        drawdown_reduction = buy_hold_metrics['max_drawdown'] - strategy_metrics['max_drawdown']

        # 仓位统计
        cash_ratio = (signals == 0).mean()

        results.append({
            'enter_threshold': enter_thresh,
            'exit_threshold': exit_thresh,
            'strategy_annual_return': strategy_metrics['annual_return'],
            'strategy_sharpe_ratio': strategy_metrics['sharpe_ratio'],
            'strategy_max_drawdown': strategy_metrics['max_drawdown'],
            'strategy_calmar_ratio': strategy_metrics['calmar_ratio'],
            'buy_hold_annual_return': buy_hold_metrics['annual_return'],
            'buy_hold_sharpe_ratio': buy_hold_metrics['sharpe_ratio'],
            'buy_hold_max_drawdown': buy_hold_metrics['max_drawdown'],
            'excess_return': excess_return,
            'volatility_reduction': volatility_reduction,
            'drawdown_reduction': drawdown_reduction,
            'num_trades': backtest_result['num_trades'],
            'total_cost': backtest_result['total_cost'],
            'cash_ratio': cash_ratio
        })

        # 进度显示
        if (i + 1) % 50 == 0 or (i + 1) == total_combinations:
            elapsed = time.time() - start_time
            print(f"已完成 {i + 1}/{total_combinations} ({(i+1)/total_combinations*100:.1f}%), "
                  f"用时 {elapsed:.1f}秒")

    results_df = pd.DataFrame(results)
    print(f"网格搜索完成，共测试了 {len(results_df)} 种有效组合")

    return results_df

def analyze_optimization_results(results_df):
    """分析优化结果"""
    print("\n" + "="*80)
    print("阈值优化结果分析")
    print("="*80)

    # 按不同指标排序找出最佳组合
    metrics = {
        'sharpe_ratio': ('夏普比率', 'strategy_sharpe_ratio', False),
        'annual_return': ('年化收益率', 'strategy_annual_return', False),
        'calmar_ratio': ('卡尔马比率', 'strategy_calmar_ratio', False),
        'max_drawdown': ('最大回撤', 'strategy_max_drawdown', True),
        'excess_return': ('超额收益', 'excess_return', False),
        'drawdown_reduction': ('回撤减少', 'drawdown_reduction', False)
    }

    best_combinations = {}

    for metric_key, (metric_name, column, ascending) in metrics.items():
        best_row = results_df.loc[results_df[column].idxmax() if not ascending else results_df[column].idxmin()]
        best_combinations[metric_key] = {
            'name': metric_name,
            'enter_threshold': best_row['enter_threshold'],
            'exit_threshold': best_row['exit_threshold'],
            'value': best_row[column],
            'annual_return': best_row['strategy_annual_return'],
            'sharpe_ratio': best_row['strategy_sharpe_ratio'],
            'max_drawdown': best_row['strategy_max_drawdown'],
            'num_trades': best_row['num_trades'],
            'cash_ratio': best_row['cash_ratio']
        }

    print("各指标最佳阈值组合:")
    print("-" * 80)
    for metric_key, info in best_combinations.items():
        print(f"{info['name']:<12}: 进入={info['enter_threshold']:2.0f}, 退出={info['exit_threshold']:2.0f} | "
              f"年化收益={info['annual_return']:6.2%}, 夏普={info['sharpe_ratio']:5.2f}, "
              f"回撤={info['max_drawdown']:6.2%}, 交易={info['num_trades']:3.0f}次")

    # 综合评分（多目标优化）
    print(f"\n综合评分分析:")
    print("-" * 80)

    # 标准化各指标（0-1范围）
    results_df['sharpe_score'] = (results_df['strategy_sharpe_ratio'] - results_df['strategy_sharpe_ratio'].min()) / \
                                (results_df['strategy_sharpe_ratio'].max() - results_df['strategy_sharpe_ratio'].min())

    results_df['return_score'] = (results_df['strategy_annual_return'] - results_df['strategy_annual_return'].min()) / \
                                (results_df['strategy_annual_return'].max() - results_df['strategy_annual_return'].min())

    results_df['drawdown_score'] = (results_df['strategy_max_drawdown'].max() - results_df['strategy_max_drawdown']) / \
                                  (results_df['strategy_max_drawdown'].max() - results_df['strategy_max_drawdown'].min())

    # 交易频率惩罚（交易次数越少越好）
    results_df['trade_score'] = 1 - (results_df['num_trades'] - results_df['num_trades'].min()) / \
                               (results_df['num_trades'].max() - results_df['num_trades'].min())

    # 综合评分（可调整权重）
    weights = {'sharpe': 0.3, 'return': 0.3, 'drawdown': 0.3, 'trade': 0.1}
    results_df['composite_score'] = (
        weights['sharpe'] * results_df['sharpe_score'] +
        weights['return'] * results_df['return_score'] +
        weights['drawdown'] * results_df['drawdown_score'] +
        weights['trade'] * results_df['trade_score']
    )

    # 找出综合评分最高的组合
    best_overall = results_df.loc[results_df['composite_score'].idxmax()]

    print(f"综合最佳阈值组合:")
    print(f"  进入阈值: {best_overall['enter_threshold']}")
    print(f"  退出阈值: {best_overall['exit_threshold']}")
    print(f"  年化收益率: {best_overall['strategy_annual_return']:.2%}")
    print(f"  夏普比率: {best_overall['strategy_sharpe_ratio']:.2f}")
    print(f"  最大回撤: {best_overall['strategy_max_drawdown']:.2%}")
    print(f"  交易次数: {best_overall['num_trades']:.0f}")
    print(f"  空仓比例: {best_overall['cash_ratio']:.1%}")
    print(f"  综合评分: {best_overall['composite_score']:.3f}")

    return results_df, best_combinations, best_overall

def plot_optimization_results(results_df, best_overall):
    """绘制优化结果图表"""
    print("正在生成优化结果图表...")

    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # 创建数据透视表用于热力图
    pivot_sharpe = results_df.pivot(index='exit_threshold', columns='enter_threshold',
                                   values='strategy_sharpe_ratio')
    pivot_return = results_df.pivot(index='exit_threshold', columns='enter_threshold',
                                   values='strategy_annual_return')
    pivot_drawdown = results_df.pivot(index='exit_threshold', columns='enter_threshold',
                                     values='strategy_max_drawdown')
    pivot_composite = results_df.pivot(index='exit_threshold', columns='enter_threshold',
                                      values='composite_score')

    # 1. 夏普比率热力图
    sns.heatmap(pivot_sharpe, annot=False, cmap='RdYlGn', ax=ax1, cbar_kws={'label': 'Sharpe Ratio'})
    ax1.scatter(best_overall['enter_threshold'] - pivot_sharpe.columns.min(),
               best_overall['exit_threshold'] - pivot_sharpe.index.min(),
               color='blue', s=100, marker='*', label='Best Overall')
    ax1.set_title('Sharpe Ratio Heatmap')
    ax1.set_xlabel('Enter Threshold')
    ax1.set_ylabel('Exit Threshold')
    ax1.legend()

    # 2. 年化收益率热力图
    sns.heatmap(pivot_return, annot=False, cmap='RdYlGn', ax=ax2,
               cbar_kws={'label': 'Annual Return'}, fmt='.1%')
    ax2.scatter(best_overall['enter_threshold'] - pivot_return.columns.min(),
               best_overall['exit_threshold'] - pivot_return.index.min(),
               color='blue', s=100, marker='*', label='Best Overall')
    ax2.set_title('Annual Return Heatmap')
    ax2.set_xlabel('Enter Threshold')
    ax2.set_ylabel('Exit Threshold')
    ax2.legend()

    # 3. 最大回撤热力图
    sns.heatmap(pivot_drawdown, annot=False, cmap='RdYlGn_r', ax=ax3,
               cbar_kws={'label': 'Max Drawdown'})
    ax3.scatter(best_overall['enter_threshold'] - pivot_drawdown.columns.min(),
               best_overall['exit_threshold'] - pivot_drawdown.index.min(),
               color='blue', s=100, marker='*', label='Best Overall')
    ax3.set_title('Max Drawdown Heatmap')
    ax3.set_xlabel('Enter Threshold')
    ax3.set_ylabel('Exit Threshold')
    ax3.legend()

    # 4. 综合评分热力图
    sns.heatmap(pivot_composite, annot=False, cmap='RdYlGn', ax=ax4,
               cbar_kws={'label': 'Composite Score'})
    ax4.scatter(best_overall['enter_threshold'] - pivot_composite.columns.min(),
               best_overall['exit_threshold'] - pivot_composite.index.min(),
               color='blue', s=100, marker='*', label='Best Overall')
    ax4.set_title('Composite Score Heatmap')
    ax4.set_xlabel('Enter Threshold')
    ax4.set_ylabel('Exit Threshold')
    ax4.legend()

    plt.tight_layout()

    # 保存图表
    chart_path = os.path.join(CHARTS_DIR, 'threshold_optimization_heatmaps.png')
    plt.savefig(chart_path, dpi=300, bbox_inches='tight')
    print(f"优化结果热力图已保存到: {chart_path}")

    plt.show()

def main():
    """主函数"""
    print("择时策略阈值优化器")
    print("="*80)

    # 1. 加载数据
    timing_indicator, sp500_prices = load_data()
    if timing_indicator is None or sp500_prices is None:
        return

    # 2. 网格搜索
    results_df = grid_search_thresholds(
        timing_indicator, sp500_prices,
        enter_range=(10, 10),  # 进入阈值范围（低于10退出）
        exit_range=(25, 25),  # 退出阈值范围（高于25重新进入）
        step=1
    )

    # 3. 分析结果
    results_df, best_combinations, best_overall = analyze_optimization_results(results_df)

    # 4. 保存结果
    output_file = os.path.join(DATA_DIR, 'threshold_optimization_results.csv')
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"\n优化结果已保存到: {output_file}")

    # 5. 绘制图表
    plot_optimization_results(results_df, best_overall)

    print("\n阈值优化完成！")

if __name__ == "__main__":
    main()
