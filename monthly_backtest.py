#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
月度调仓回测对比
"""

from portfolio_backtest_framework import PortfolioBacktest

def run_monthly_backtest():
    """运行月度调仓回测"""
    print("🚀 营收增长加速度因子月度调仓回测")
    print("="*50)
    
    # 创建回测实例
    backtest = PortfolioBacktest()
    
    # 加载数据
    backtest.load_data()
    
    # 计算因子
    backtest.calculate_factors()
    
    # 运行月度回测
    monthly_results = backtest.run_portfolio_backtest(
        start_date="2020-01-01",
        end_date="2024-12-31",
        rebalance_freq="M",  # 月度调仓
        top_pct=0.3,         # 前30%做多
        bottom_pct=0.3       # 后30%做空
    )
    
    # 分析结果
    print("\n" + "="*60)
    print("📊 月度调仓 vs 季度调仓对比")
    print("="*60)
    
    if not monthly_results.empty:
        # 月度调仓结果
        monthly_cumulative = (1 + monthly_results['portfolio_return'] / 100).cumprod().iloc[-1]
        monthly_total_return = (monthly_cumulative - 1) * 100
        monthly_win_rate = (monthly_results['portfolio_return'] > 0).mean() * 100
        monthly_avg_return = monthly_results['portfolio_return'].mean()
        
        print(f"📅 月度调仓结果:")
        print(f"  调仓次数: {len(monthly_results)}")
        print(f"  累计收益: {monthly_total_return:.2f}%")
        print(f"  平均收益: {monthly_avg_return:.2f}%")
        print(f"  胜率: {monthly_win_rate:.1f}%")
        
        # 保存月度结果
        monthly_results.to_csv('monthly_backtest_results.csv', index=False)
        print(f"\n✅ 月度回测结果已保存到 monthly_backtest_results.csv")
    
    return monthly_results

if __name__ == "__main__":
    run_monthly_backtest()
