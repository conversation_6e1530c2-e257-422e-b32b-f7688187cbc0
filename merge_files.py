#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import re

def main():
    # 获取所有的Markdown文件
    md_files = [f for f in os.listdir('.') if re.match(r'\d+\.md', f)]
    
    # 按年份排序
    md_files.sort(key=lambda x: int(x.split('.')[0]))
    
    # 创建合并的Markdown文件
    with open('巴菲特致股东信合集.md', 'w', encoding='utf-8') as combined_file:
        combined_file.write('# 巴菲特致股东信合集\n\n')
        
        # 添加每个Markdown文件到合并文件
        for md_file in md_files:
            year = md_file.split('.')[0]
            print(f'Adding letter from {year} to the combined markdown')
            
            combined_file.write(f'\n\n## {year}年致股东信\n\n')
            
            with open(md_file, 'r', encoding='utf-8') as f:
                content = f.read()
                combined_file.write(content)
    
    print('Combined markdown file created: 巴菲特致股东信合集.md')

if __name__ == '__main__':
    main()
