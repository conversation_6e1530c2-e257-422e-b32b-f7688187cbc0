#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plot HSI (Hang Seng Index) Relative Strength Results - Demo Version
恒生指数成分股相对强度分析和可视化 - 演示版本
"""

import pandas as pd
import numpy as np
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

def create_demo_data():
    """创建演示数据"""
    print("🎯 创建恒生指数成分股演示数据...")
    
    # 创建演示分析结果
    demo_results = [
        # 强势科技股
        {'symbol': '700', 'name': '腾讯控股', 'current_rs': 108.5, 'strength_score': 85, 
         'total_change_pct': 12.3, '1M_slope': 2.1, '3M_slope': 1.8, '6M_slope': 1.2, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '9988', 'name': '阿里巴巴-W', 'current_rs': 105.2, 'strength_score': 80, 
         'total_change_pct': 8.7, '1M_slope': 1.8, '3M_slope': 1.5, '6M_slope': 0.9, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1024', 'name': '快手-W', 'current_rs': 109.6, 'strength_score': 88, 
         'total_change_pct': 15.2, '1M_slope': 2.3, '3M_slope': 2.0, '6M_slope': 1.5, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '3690', 'name': '美团-W', 'current_rs': 103.7, 'strength_score': 75, 
         'total_change_pct': 6.4, '1M_slope': 1.2, '3M_slope': 1.0, '6M_slope': 0.8, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1810', 'name': '小米集团-W', 'current_rs': 107.9, 'strength_score': 78, 
         'total_change_pct': 11.1, '1M_slope': 1.6, '3M_slope': 1.3, '6M_slope': 0.7, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '9618', 'name': '京东集团-SW', 'current_rs': 104.3, 'strength_score': 72, 
         'total_change_pct': 7.8, '1M_slope': 1.1, '3M_slope': 0.9, '6M_slope': 0.5, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '981', 'name': '中芯国际', 'current_rs': 110.2, 'strength_score': 85, 
         'total_change_pct': 16.7, '1M_slope': 2.0, '3M_slope': 1.7, '6M_slope': 1.3, 'above_ma20': True, 'above_ma50': True},
        
        # 强势金融股
        {'symbol': '1299', 'name': '友邦保险', 'current_rs': 112.3, 'strength_score': 90, 
         'total_change_pct': 18.9, '1M_slope': 2.5, '3M_slope': 2.1, '6M_slope': 1.8, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '388', 'name': '香港交易所', 'current_rs': 106.8, 'strength_score': 82, 
         'total_change_pct': 9.5, '1M_slope': 1.9, '3M_slope': 1.6, '6M_slope': 1.1, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '2318', 'name': '中国平安', 'current_rs': 101.2, 'strength_score': 65, 
         'total_change_pct': 2.8, '1M_slope': 0.8, '3M_slope': 0.6, '6M_slope': 0.2, 'above_ma20': True, 'above_ma50': True},
        
        # 中等表现股票
        {'symbol': '939', 'name': '建设银行', 'current_rs': 98.5, 'strength_score': 55, 
         'total_change_pct': -1.2, '1M_slope': 0.5, '3M_slope': 0.3, '6M_slope': -0.2, 'above_ma20': False, 'above_ma50': True},
        
        {'symbol': '1398', 'name': '工商银行', 'current_rs': 96.2, 'strength_score': 45, 
         'total_change_pct': -3.8, '1M_slope': -0.2, '3M_slope': -0.1, '6M_slope': -0.5, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '883', 'name': '中国海洋石油', 'current_rs': 99.7, 'strength_score': 60, 
         'total_change_pct': 0.3, '1M_slope': 0.3, '3M_slope': 0.1, '6M_slope': -0.1, 'above_ma20': True, 'above_ma50': False},
        
        # 弱势股票
        {'symbol': '5', 'name': '汇丰控股', 'current_rs': 89.3, 'strength_score': 25, 
         'total_change_pct': -8.7, '1M_slope': -1.2, '3M_slope': -1.5, '6M_slope': -2.1, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '857', 'name': '中国石油股份', 'current_rs': 87.6, 'strength_score': 20, 
         'total_change_pct': -12.4, '1M_slope': -1.8, '3M_slope': -2.1, '6M_slope': -2.8, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '386', 'name': '中国石油化工股份', 'current_rs': 91.4, 'strength_score': 30, 
         'total_change_pct': -6.6, '1M_slope': -0.8, '3M_slope': -1.1, '6M_slope': -1.6, 'above_ma20': False, 'above_ma50': False},
        
        # 更多股票
        {'symbol': '1088', 'name': '中国神华', 'current_rs': 93.2, 'strength_score': 35, 
         'total_change_pct': -4.8, '1M_slope': -0.3, '3M_slope': -0.6, '6M_slope': -1.2, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '2628', 'name': '中国人寿', 'current_rs': 95.1, 'strength_score': 50, 
         'total_change_pct': -2.9, '1M_slope': 0.1, '3M_slope': -0.2, '6M_slope': -0.8, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '1093', 'name': '石药集团', 'current_rs': 102.4, 'strength_score': 68, 
         'total_change_pct': 4.4, '1M_slope': 1.0, '3M_slope': 0.8, '6M_slope': 0.4, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1177', 'name': '中国生物制药', 'current_rs': 100.8, 'strength_score': 62, 
         'total_change_pct': 1.8, '1M_slope': 0.6, '3M_slope': 0.4, '6M_slope': 0.1, 'above_ma20': True, 'above_ma50': False},
        
        {'symbol': '762', 'name': '中国联通', 'current_rs': 97.3, 'strength_score': 48, 
         'total_change_pct': -2.7, '1M_slope': 0.2, '3M_slope': -0.1, '6M_slope': -0.4, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '1211', 'name': '比亚迪股份', 'current_rs': 105.6, 'strength_score': 76, 
         'total_change_pct': 8.9, '1M_slope': 1.4, '3M_slope': 1.1, '6M_slope': 0.6, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '175', 'name': '吉利汽车', 'current_rs': 103.1, 'strength_score': 70, 
         'total_change_pct': 5.1, '1M_slope': 1.0, '3M_slope': 0.7, '6M_slope': 0.3, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1929', 'name': '周大福', 'current_rs': 99.8, 'strength_score': 58, 
         'total_change_pct': 0.2, '1M_slope': 0.4, '3M_slope': 0.2, '6M_slope': -0.1, 'above_ma20': True, 'above_ma50': False},
        
        {'symbol': '27', 'name': '银河娱乐', 'current_rs': 94.7, 'strength_score': 42, 
         'total_change_pct': -5.3, '1M_slope': -0.1, '3M_slope': -0.4, '6M_slope': -0.9, 'above_ma20': False, 'above_ma50': False},
    ]
    
    # 转换为DataFrame并排序
    analysis_results = pd.DataFrame(demo_results)
    analysis_results = analysis_results.sort_values('strength_score', ascending=False)
    
    return analysis_results

def create_demo_relative_strength_data(analysis_results):
    """创建演示相对强度时间序列数据"""
    print("📈 创建相对强度时间序列数据...")
    
    # 创建日期范围（过去一年）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    relative_strength_data = {}
    
    for _, stock in analysis_results.iterrows():
        symbol = stock['symbol']
        current_rs = stock['current_rs']
        
        # 生成模拟的相对强度数据
        np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
        
        # 基于当前相对强度和趋势生成历史数据
        n_points = len(date_range)
        
        # 生成趋势
        if stock['6M_slope'] > 0:
            # 上升趋势
            trend = np.linspace(current_rs - 15, current_rs, n_points)
        else:
            # 下降趋势
            trend = np.linspace(current_rs + 10, current_rs, n_points)
        
        # 添加随机波动
        noise = np.random.normal(0, 3, n_points)
        
        # 添加周期性波动
        cycle = 2 * np.sin(np.linspace(0, 4*np.pi, n_points))
        
        # 组合生成最终数据
        rs_values = trend + noise + cycle
        
        # 确保数据合理性
        rs_values = np.clip(rs_values, 70, 130)
        
        # 创建时间序列
        rs_series = pd.Series(rs_values, index=date_range)
        relative_strength_data[symbol] = rs_series
    
    return relative_strength_data

def get_rising_stocks(analysis_results, min_score=60):
    """获取持续上升的港股"""
    rising_stocks = analysis_results[
        (analysis_results['1M_slope'] > 0) &
        (analysis_results['3M_slope'] > 0) &
        (analysis_results['6M_slope'] > 0) &
        (analysis_results['strength_score'] >= min_score)
    ]
    
    return rising_stocks.sort_values('strength_score', ascending=False)

def plot_relative_strength_analysis():
    """创建恒生指数成分股相对强度综合可视化"""
    
    print("🎯 创建恒生指数成分股相对强度可视化（演示版）")
    print("=" * 60)
    
    # 创建演示数据
    analysis_results = create_demo_data()
    relative_strength_data = create_demo_relative_strength_data(analysis_results)
    
    print(f"✅ 演示数据创建完成: {len(analysis_results)} 只港股")
    
    # 创建综合可视化
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('恒生指数成分股相对强度分析 - 完整市场概览（演示版）', 
                fontsize=16, fontweight='bold')
    
    # 1. 前15强势股相对强度线图（左上）
    ax1 = axes[0, 0]
    top_15 = analysis_results.head(15)
    colors = plt.cm.tab20(np.linspace(0, 1, 15))
    
    for i, (_, stock) in enumerate(top_15.iterrows()):
        symbol = stock['symbol']
        if symbol in relative_strength_data:
            rs_data = relative_strength_data[symbol]
            ax1.plot(rs_data.index, rs_data.values, 
                    label=f"{symbol} ({stock['strength_score']:.0f})", 
                    color=colors[i], linewidth=2, alpha=0.8)
    
    ax1.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='恒指基准线')
    ax1.set_title('前15强势港股', fontsize=12, fontweight='bold')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('相对强度')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 2. 强度评分分布（中上）
    ax2 = axes[0, 1]
    scores = analysis_results['strength_score']
    bins = [0, 30, 50, 70, 85, 100]
    labels = ['弱势\n(0-30)', '偏弱\n(30-50)', '中等\n(50-70)', '强势\n(70-85)', '很强\n(85-100)']
    colors_hist = ['red', 'orange', 'yellow', 'lightgreen', 'green']
    
    counts, _, patches = ax2.hist(scores, bins=bins, alpha=0.7, edgecolor='black')

    # 手动设置柱状图颜色
    for i, patch in enumerate(patches):
        patch.set_facecolor(colors_hist[i])
    ax2.set_title(f'强度分布\n({len(analysis_results)} 只港股)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('强度评分')
    ax2.set_ylabel('股票数量')
    ax2.set_xticks([(bins[i] + bins[i+1])/2 for i in range(len(bins)-1)])
    ax2.set_xticklabels(labels, rotation=45, ha='right')
    
    for i, count in enumerate(counts):
        ax2.text((bins[i] + bins[i+1])/2, count + 0.2, f'{int(count)}', 
                ha='center', va='bottom', fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. 相对恒指表现（右上）
    ax3 = axes[0, 2]
    above_hsi = len(analysis_results[analysis_results['current_rs'] > 100])
    below_hsi = len(analysis_results) - above_hsi
    
    performance_data = [above_hsi, below_hsi]
    performance_labels = [f'跑赢恒指\n({above_hsi})', f'跑输恒指\n({below_hsi})']
    colors_pie = ['lightgreen', 'lightcoral']
    
    wedges, texts, autotexts = ax3.pie(performance_data, labels=performance_labels, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax3.set_title('相对恒指表现', fontsize=12, fontweight='bold')
    
    # 4. 上升股票柱状图（左下）
    ax4 = axes[1, 0]
    rising_stocks = get_rising_stocks(analysis_results, min_score=60)
    
    if not rising_stocks.empty:
        top_rising = rising_stocks.head(12)
        bars = ax4.barh(range(len(top_rising)), top_rising['current_rs'], 
                       color=plt.cm.RdYlGn(top_rising['strength_score']/100))
        
        ax4.set_title(f'前12上升港股\n(共{len(rising_stocks)}只)', fontsize=12, fontweight='bold')
        ax4.set_xlabel('当前相对强度')
        ax4.set_ylabel('股票排名')
        ax4.set_yticks(range(len(top_rising)))
        ax4.set_yticklabels([f"{i+1}. {symbol}" for i, symbol in enumerate(top_rising['symbol'])])
        
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax4.text(width + 2, bar.get_y() + bar.get_height()/2, 
                    f'{width:.0f}', ha='left', va='center', fontsize=8)
        
        ax4.axvline(x=100, color='red', linestyle='--', alpha=0.7, label='恒指基准线')
        ax4.legend()
        ax4.grid(True, alpha=0.3, axis='x')
    
    # 5. 相对强度vs变化散点图（中下）
    ax5 = axes[1, 1]
    scatter = ax5.scatter(analysis_results['current_rs'], analysis_results['total_change_pct'], 
                         c=analysis_results['strength_score'], cmap='RdYlGn', 
                         s=60, alpha=0.7, edgecolors='black')
    
    # 为前10添加标签
    for _, stock in analysis_results.head(10).iterrows():
        ax5.annotate(stock['symbol'], 
                   (stock['current_rs'], stock['total_change_pct']),
                   xytext=(3, 3), textcoords='offset points', 
                   fontsize=8, alpha=0.8)
    
    ax5.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax5.axvline(x=100, color='red', linestyle='--', alpha=0.5)
    ax5.set_title('当前相对强度 vs 总变化\n(所有港股)', fontsize=12, fontweight='bold')
    ax5.set_xlabel('当前相对强度')
    ax5.set_ylabel('总变化 (%)')
    
    cbar = plt.colorbar(scatter, ax=ax5)
    cbar.set_label('强度评分')
    ax5.grid(True, alpha=0.3)
    
    # 6. 市场统计（右下）
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    # 计算统计数据
    total_stocks = len(analysis_results)
    high_strength = len(analysis_results[analysis_results['strength_score'] >= 70])
    rising_count = len(rising_stocks)
    
    # 计算科技股表现
    tech_stocks = analysis_results[analysis_results['symbol'].isin(['700', '9988', '3690', '1810', '9618', '1024', '9888', '981'])]
    tech_avg_score = tech_stocks['strength_score'].mean() if not tech_stocks.empty else 0
    
    stats_text = f"""
📊 市场统计

总分析港股数: {total_stocks}
分析周期: 1年
基准: 恒生指数

🏆 表现分析:
• 跑赢恒指: {above_hsi} ({above_hsi/total_stocks*100:.1f}%)
• 跑输恒指: {below_hsi} ({below_hsi/total_stocks*100:.1f}%)

💪 强度分析:
• 高强度(≥70): {high_strength} ({high_strength/total_stocks*100:.1f}%)
• 中等强度(50-69): {len(analysis_results[(analysis_results['strength_score'] >= 50) & (analysis_results['strength_score'] < 70)])} 只
• 低强度(<50): {len(analysis_results[analysis_results['strength_score'] < 50])} 只

📈 上升股票:
• 持续上升: {rising_count} ({rising_count/total_stocks*100:.1f}%)

🔝 最强表现:
• {analysis_results.iloc[0]['symbol']}: 相对强度 {analysis_results.iloc[0]['current_rs']:.1f}

🏭 科技股平均评分: {tech_avg_score:.1f}
"""
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=10,
            verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    # 保存可视化
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hsi_relative_strength_complete_demo_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📁 综合可视化已保存至: {filename}")
    plt.close()
    
    # 创建顶级表现者详细图表
    create_top_performers_detail(analysis_results, relative_strength_data)

def create_top_performers_detail(analysis_results, relative_strength_data):
    """为顶级表现港股创建详细图表"""
    
    print("\n📊 为顶级表现港股创建详细图表...")
    
    # 获取前9名表现者用于3x3网格
    top_performers = analysis_results.head(9)
    
    fig, axes = plt.subplots(3, 3, figsize=(18, 12))
    fig.suptitle('前9强势港股 - 详细相对强度分析（演示版）', 
                fontsize=16, fontweight='bold')
    
    for i, (_, stock) in enumerate(top_performers.iterrows()):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        symbol = stock['symbol']
        if symbol in relative_strength_data:
            rs_data = relative_strength_data[symbol]
            
            # 绘制相对强度线
            ax.plot(rs_data.index, rs_data.values, 'b-', linewidth=2, label='相对强度')
            
            # 添加移动平均线
            if len(rs_data) >= 20:
                ma20 = rs_data.rolling(20).mean()
                ax.plot(ma20.index, ma20.values, 'orange', linewidth=1, alpha=0.8, label='MA20')
            
            if len(rs_data) >= 50:
                ma50 = rs_data.rolling(50).mean()
                ax.plot(ma50.index, ma50.values, 'red', linewidth=1, alpha=0.8, label='MA50')
            
            # 添加基准线
            ax.axhline(y=100, color='gray', linestyle='--', alpha=0.7, label='恒指基准线')
            
            # 标题包含关键指标
            title = f"{symbol} ({stock['name']})\n评分: {stock['strength_score']:.0f} | 相对强度: {stock['current_rs']:.1f} | 变化: {stock['total_change_pct']:.1f}%"
            ax.set_title(title, fontsize=10, fontweight='bold')
            ax.set_xlabel('日期')
            ax.set_ylabel('相对强度')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # 格式化x轴日期
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # 保存顶级表现者图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"hsi_top_performers_detail_demo_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📁 顶级表现者详细图表已保存至: {filename}")
    plt.close()

def main():
    """主函数"""
    print("🎯 恒生指数成分股相对强度可视化（演示版）")
    print("=" * 60)
    
    # 创建可视化
    plot_relative_strength_analysis()
    
    print("\n✅ 可视化完成!")
    print("📁 请查看生成的PNG文件获取详细图表。")
    print("🎨 这是一个演示版本，展示了类似SPY的完整可视化分析功能")

if __name__ == "__main__":
    main()
