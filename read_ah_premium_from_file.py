#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
从本地 JSON 文件读取 AH 股溢价指数数据

该脚本用于从本地 JSON 文件读取恒生 AH 股溢价指数的历史数据，
并将其转换为 CSV 格式保存，同时绘制历史走势图。
"""

import os
import pandas as pd
import numpy as np
import json
from datetime import datetime
import matplotlib.pyplot as plt
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class AHPremiumFileReader:
    """从本地文件读取 AH 股溢价指数数据"""
    
    def __init__(self, input_file='data/ah_kline.json', output_dir='./data'):
        """
        初始化 AH 股溢价指数数据读取器
        
        Args:
            input_file: 输入文件路径，默认为'data/ah_kline.json'
            output_dir: 输出目录，默认为'./data'
        """
        self.input_file = input_file
        self.output_dir = output_dir
        
        # 确保输出目录存在
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)
            
        # 初始化数据存储
        self.ah_premium_data = None
    
    def read_data(self):
        """从本地 JSON 文件读取数据"""
        logger.info(f"从本地文件 {self.input_file} 读取 AH 股溢价指数数据...")
        
        try:
            # 检查文件是否存在
            if not os.path.exists(self.input_file):
                logger.error(f"文件 {self.input_file} 不存在")
                return False
            
            # 读取 JSON 文件
            with open(self.input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 检查数据结构
            if 'data' not in data or 'item' not in data['data']:
                logger.error("JSON 文件格式不正确，缺少 'data' 或 'item' 字段")
                return False
            
            # 提取数据
            items = data['data']['item']
            
            # 创建 DataFrame
            ah_data = []
            for item in items:
                # 雪球 API 返回的数据格式通常是：[时间戳, 成交量, 开盘价, 最高价, 最低价, 收盘价, ...]
                timestamp = item[0]
                close_price = item[5]  # 收盘价
                
                date = datetime.fromtimestamp(timestamp / 1000).strftime('%Y-%m-%d')
                
                ah_data.append({
                    'date': date,
                    'value': close_price
                })
            
            self.ah_premium_data = pd.DataFrame(ah_data)
            self.ah_premium_data['date'] = pd.to_datetime(self.ah_premium_data['date'])
            self.ah_premium_data.sort_values('date', inplace=True)
            
            logger.info(f"成功读取 AH 股溢价指数数据，共{len(self.ah_premium_data)}条记录")
            
            # 保存数据
            self.save_data()
            
            return True
            
        except Exception as e:
            logger.error(f"读取数据失败: {str(e)}")
            return False
    
    def save_data(self):
        """保存读取的数据"""
        if self.ah_premium_data is None or self.ah_premium_data.empty:
            logger.error("没有数据可保存")
            return False
        
        logger.info("保存 AH 股溢价指数数据...")
        
        try:
            # 保存为 CSV 文件
            output_file = os.path.join(self.output_dir, 'ah_premium_index_history.csv')
            self.ah_premium_data.to_csv(output_file, index=False)
            
            logger.info(f"成功保存 AH 股溢价指数数据到 {output_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"保存数据失败: {str(e)}")
            return False
    
    def plot_data(self):
        """绘制 AH 股溢价指数走势图"""
        if self.ah_premium_data is None or self.ah_premium_data.empty:
            logger.error("没有数据可绘制")
            return False
        
        logger.info("绘制 AH 股溢价指数走势图...")
        
        try:
            plt.figure(figsize=(12, 6))
            plt.plot(self.ah_premium_data['date'], self.ah_premium_data['value'])
            plt.title('恒生 AH 股溢价指数走势图')
            plt.xlabel('日期')
            plt.ylabel('溢价指数')
            plt.grid(True)
            
            # 添加水平线，表示 100 的位置（无溢价）
            plt.axhline(y=100, color='r', linestyle='--', alpha=0.5)
            
            # 添加当前值的标注
            current_value = self.ah_premium_data['value'].iloc[-1]
            plt.annotate(f'当前值: {current_value:.2f}', 
                         xy=(self.ah_premium_data['date'].iloc[-1], current_value),
                         xytext=(self.ah_premium_data['date'].iloc[-1] - pd.Timedelta(days=30), current_value + 10),
                         arrowprops=dict(arrowstyle='->'))
            
            # 保存图表
            output_file = os.path.join(self.output_dir, 'ah_premium_index_history.png')
            plt.savefig(output_file)
            
            logger.info(f"成功保存 AH 股溢价指数走势图到 {output_file}")
            
            # 显示图表
            plt.close()
            
            return True
            
        except Exception as e:
            logger.error(f"绘制走势图失败: {str(e)}")
            return False
    
    def run(self):
        """运行完整的数据处理流程"""
        logger.info("开始处理 AH 股溢价指数数据...")
        
        # 读取数据
        if not self.read_data():
            logger.error("读取数据失败")
            return False
        
        # 绘制走势图
        if not self.plot_data():
            logger.warning("绘制走势图失败")
        
        logger.info("AH 股溢价指数数据处理完成")
        return True


def main():
    """主函数"""
    # 创建读取器实例
    reader = AHPremiumFileReader()
    
    # 运行数据处理流程
    reader.run()


if __name__ == "__main__":
    main()
