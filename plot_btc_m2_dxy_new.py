import pandas as pd
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime
import os
import numpy as np
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# 设置代理
def setup_proxy():
    """设置代理，使用config目录下的配置"""
    try:
        # 从config目录导入代理设置
        from config.btc_data_config import PROXY_SETTINGS

        # 设置环境变量
        for protocol, proxy in PROXY_SETTINGS.items():
            os.environ[f"{protocol.upper()}_PROXY"] = proxy

        print(f"Using proxy settings from config: {PROXY_SETTINGS}")
    except Exception as e:
        print(f"Error setting up proxy: {e}")
        # 使用默认代理
        os.environ['HTTP_PROXY'] = 'socks5h://127.0.0.1:13659'
        os.environ['HTTPS_PROXY'] = 'socks5h://127.0.0.1:13659'
        print("Using default proxy settings")

# 读取M2SL数据
def load_m2sl_data():
    try:
        # 尝试从本地文件读取
        m2sl_df = pd.read_csv('M2SL.csv')
        m2sl_df['observation_date'] = pd.to_datetime(m2sl_df['observation_date'])
        m2sl_df.set_index('observation_date', inplace=True)
    except FileNotFoundError:
        # 如果本地文件不存在，从FRED API获取
        print("M2SL.csv not found, downloading from FRED API...")
        import pandas_datareader.data as web
        m2sl_df = web.DataReader('M2SL', 'fred', '2010-01-01')
        # 保存到本地文件
        m2sl_df.to_csv('M2SL.csv')

    # 创建日期范围到最新的一天
    today = pd.Timestamp.today()
    full_date_range = pd.date_range(start=m2sl_df.index.min(), end=today, freq='D')

    # 重新索引并进行线性插值
    m2sl_df = m2sl_df.reindex(full_date_range)
    m2sl_df = m2sl_df.interpolate(method='linear')

    return m2sl_df

# 获取DXY数据
def get_dxy_data(start_date):
    print(f"Getting DXY data from {start_date}...")
    dxy = yf.download('DX-Y.NYB', start=start_date)
    print(f"Successfully retrieved {len(dxy)} records of DXY data")
    return dxy['Close']

# 获取比特币数据
def get_btc_data(start_date):
    print(f"Getting Bitcoin data from {start_date}...")
    btc = yf.download('BTC-USD', start=start_date)
    print(f"Successfully retrieved {len(btc)} records of Bitcoin data")
    return btc['Close']

# 绘制M2/DXY比率和比特币价格
def plot_btc_and_m2_dxy_ratio():
    # 设置代理
    setup_proxy()

    # 加载M2SL数据
    print("Loading M2SL data...")
    m2sl_data = load_m2sl_data()
    print(f"M2SL data range: {m2sl_data.index.min()} to {m2sl_data.index.max()}")

    # 获取DXY数据和比特币数据
    start_date = m2sl_data.index.min()
    dxy_data = get_dxy_data(start_date)
    btc_data = get_btc_data(start_date)

    # 创建日期范围内的数据框
    df = pd.DataFrame(index=pd.date_range(start=start_date, end=m2sl_data.index.max(), freq='D'))
    df['M2SL'] = m2sl_data['M2SL']
    df['DXY'] = dxy_data
    df['BTC'] = btc_data

    # 对齐数据并删除缺失值
    df = df.dropna()

    # 计算M2/DXY比率
    df['M2_DXY_Ratio'] = df['M2SL'] / df['DXY']

    # 删除包含NaN的行
    df = df.dropna()

    print(f"Final data range: {df.index.min()} to {df.index.max()}")
    print(f"Number of data points: {len(df)}")

    # 计算相关系数
    correlation = df['BTC'].corr(df['M2_DXY_Ratio'])
    print(f"Correlation between BTC and M2/DXY Ratio: {correlation:.4f}")

    # 计算滚动相关系数（12个月窗口）
    df['Rolling_Correlation'] = df['BTC'].rolling(window=365).corr(df['M2_DXY_Ratio'])

    # 创建图表
    fig, axes = plt.subplots(3, 1, figsize=(14, 18), sharex=True)

    # 1. 比特币价格
    ax1 = axes[0]
    ax1.plot(df.index, df['BTC'], color='orange', linewidth=2, label='Bitcoin Price (USD)')
    ax1.set_title('Bitcoin Price', fontsize=14)
    ax1.set_ylabel('Price (USD)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')

    # 使用对数刻度
    ax1.set_yscale('log')

    # 格式化y轴标签为千分位分隔
    ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'${x:,.0f}'))

    # 2. M2/DXY比率
    ax2 = axes[1]
    ax2.plot(df.index, df['M2_DXY_Ratio'], color='blue', linewidth=2, label='M2/DXY Ratio')
    ax2.set_title('M2/DXY Ratio', fontsize=14)
    ax2.set_ylabel('Ratio', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left')

    # 格式化y轴标签为千分位分隔
    ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{x:,.0f}'))

    # 3. 滚动相关系数
    ax3 = axes[2]
    ax3.plot(df.index, df['Rolling_Correlation'], color='green', linewidth=2, label='12-Month Rolling Correlation')
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax3.set_title('12-Month Rolling Correlation between Bitcoin and M2/DXY Ratio', fontsize=14)
    ax3.set_ylabel('Correlation', fontsize=12)
    ax3.set_ylim(-1, 1)
    ax3.grid(True, alpha=0.3)
    ax3.legend(loc='upper left')

    # 格式化x轴日期
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.YearLocator())

    plt.tight_layout()
    plt.savefig('btc_m2_dxy_ratio.png', dpi=300)
    plt.show()

    # 分析不同时期的相关性
    # 将数据分为几个时期
    periods = [
        ('2015-2017', '2015-01-01', '2017-12-31'),
        ('2018-2019', '2018-01-01', '2019-12-31'),
        ('2020-2021', '2020-01-01', '2021-12-31'),
        ('2022-Present', '2022-01-01', datetime.now().strftime('%Y-%m-%d'))
    ]

    print("\nCorrelation by Period:")
    for period_name, period_start, period_end in periods:
        period_data = df[(df.index >= period_start) & (df.index <= period_end)]
        if not period_data.empty:
            period_corr = period_data['BTC'].corr(period_data['M2_DXY_Ratio'])
            print(f"{period_name}: {period_corr:.4f}")

    # 分析平移后的相关性
    print("\nAnalyzing correlation with time shifts:")
    max_shift = 365  # 最大平移天数
    shift_step = 5   # 平移步长
    shifts = range(0, max_shift + 1, shift_step)
    correlations = []

    for shift in shifts:
        # 平移M2/DXY比率
        shifted_ratio = df['M2_DXY_Ratio'].shift(shift)
        # 计算相关系数
        corr = df['BTC'].corr(shifted_ratio)
        correlations.append((shift, corr))

    # 找出最大相关性的平移天数
    max_corr_shift = max(correlations, key=lambda x: abs(x[1]))
    print(f"Maximum correlation ({max_corr_shift[1]:.4f}) found with {max_corr_shift[0]} days shift")

    # 绘制平移相关性图表
    plt.figure(figsize=(12, 6))
    shifts_days = [s for s, _ in correlations]
    corr_values = [c for _, c in correlations]
    plt.plot(shifts_days, corr_values, marker='o', markersize=3)
    plt.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    plt.axvline(x=max_corr_shift[0], color='green', linestyle='--', alpha=0.7,
                label=f'Max Correlation at {max_corr_shift[0]} days')
    plt.xlabel('Shift (days)')
    plt.ylabel('Correlation')
    plt.title('Correlation between BTC and Shifted M2/DXY Ratio')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.tight_layout()
    plt.savefig('btc_m2_dxy_shift_correlation.png', dpi=300)
    plt.show()

    # 绘制最佳平移的M2/DXY比率和比特币价格
    best_shift = max_corr_shift[0]

    # 创建新的图表
    plt.figure(figsize=(15, 8))

    # 创建两个Y轴
    ax1 = plt.gca()
    ax2 = ax1.twinx()

    # 平移M2/DXY比率
    shifted_ratio = df['M2_DXY_Ratio'].shift(best_shift)

    # 绘制平移后的M2/DXY比率
    ax1.plot(df.index, shifted_ratio, color='blue', linewidth=2,
             label=f'M2/DXY Ratio (Shifted {best_shift} days)')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('M2/DXY Ratio', color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')

    # 绘制比特币价格
    ax2.plot(df.index, df['BTC'], color='orange', linewidth=2, label='BTC-USD')
    ax2.set_ylabel('BTC Price (USD)', color='orange')
    ax2.tick_params(axis='y', labelcolor='orange')
    ax2.set_yscale('log')

    # 添加标题
    plt.title(f'M2/DXY Ratio (Shifted {best_shift} days) vs BTC-USD')

    # 添加图例
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

    plt.grid(True)
    plt.tight_layout()

    # 保存图表
    plt.savefig('btc_m2_dxy_best_shift.png', dpi=300)
    plt.show()

if __name__ == "__main__":
    plot_btc_and_m2_dxy_ratio()
