#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动量因子参数优化
基于反转因子优化脚本改进，测试动量因子的最佳参数
动量因子假设价格趋势延续，与反转因子相反
从2020年开始回测
"""

import sqlite3
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MomentumFactorOptimizer:
    def __init__(self, db_path='ganggutong_10year_data.db', start_date='2015-01-01'):
        self.db_path = db_path
        self.start_date = datetime.strptime(start_date, '%Y-%m-%d')
        self.stock_data = {}
        
    def load_stock_data(self, max_stocks=564):
        """加载股票数据"""
        print(f"📊 从数据库加载股票数据 (最多{max_stocks}只)...")
        
        conn = sqlite3.connect(self.db_path)
        
        # 获取股票代码，按数据完整性排序
        query = """
        SELECT stock_code, COUNT(*) as record_count
        FROM stock_prices
        WHERE close IS NOT NULL AND date >= '2015-01-01'
        GROUP BY stock_code
        HAVING COUNT(*) >= 500
        ORDER BY record_count DESC
        LIMIT ?
        """
        
        stock_codes = pd.read_sql_query(query, conn, params=(max_stocks,))['stock_code'].tolist()
        print(f"   选择 {len(stock_codes)} 只数据完整的股票")
        
        loaded_count = 0
        for stock_code in stock_codes:
            try:
                query = """
                SELECT date, close
                FROM stock_prices
                WHERE stock_code = ? AND date >= '2015-01-01'
                ORDER BY date
                """
                df = pd.read_sql_query(query, conn, params=(stock_code,))
                
                df['date'] = pd.to_datetime(df['date'])
                df = df.set_index('date')
                df = df.dropna()
                
                if len(df) >= 500:
                    self.stock_data[stock_code] = df['close']
                    loaded_count += 1
                    
            except Exception as e:
                print(f"⚠️  加载股票 {stock_code} 失败: {e}")
                continue
        
        conn.close()
        print(f"✅ 成功加载 {loaded_count} 只股票的价格数据")
        return loaded_count > 0
    
    def calculate_momentum_factor(self, prices, lookback_days):
        """计算动量因子 - 与反转因子相反，正值表示上涨动量"""
        return prices.pct_change(lookback_days).dropna()
    
    def get_rebalance_dates(self, start_date, end_date, freq):
        """获取再平衡日期"""
        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)

            if freq == 'W':  # 周度
                current_date += timedelta(days=7)
            elif freq == 'M':  # 月度
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1, day=1)
            elif freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_month = 6
                elif current_date.month <= 6:
                    next_month = 9
                elif current_date.month <= 9:
                    next_month = 12
                else:
                    next_month = 3
                    current_date = current_date.replace(year=current_date.year + 1)

                if next_month <= 12:
                    current_date = current_date.replace(month=next_month, day=1)
            else:  # 年度
                current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)

        return dates
    
    def calculate_stock_return(self, prices, start_date, end_date):
        """计算股票收益率"""
        period_data = prices[(prices.index >= start_date) & (prices.index <= end_date)]
        
        if len(period_data) < 2:
            return 0.0
        
        start_price = period_data.iloc[0]
        end_price = period_data.iloc[-1]
        
        if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
            return 0.0
        
        return (end_price - start_price) / start_price
    
    def backtest_parameter_combination(self, lookback_days, rebalance_freq):
        """回测单个参数组合"""
        print(f"🔄 测试参数: 回看{lookback_days}天, {rebalance_freq}调仓")
        
        # 确定回测期间
        all_dates = set()
        for prices in self.stock_data.values():
            all_dates.update(prices.index)
        
        start_dt = max(self.start_date, min(all_dates) + timedelta(days=lookback_days + 30))
        end_dt = max(all_dates) - timedelta(days=30)
        
        if start_dt >= end_dt:
            return {}
        
        # 获取再平衡日期
        rebalance_dates = self.get_rebalance_dates(start_dt, end_dt, rebalance_freq)
        
        if len(rebalance_dates) < 4:
            return {}
        
        high_momentum_returns = []
        low_momentum_returns = []
        
        for i, rebalance_date in enumerate(rebalance_dates[:-1]):
            # 计算因子得分
            factor_scores = {}
            
            for stock_code, prices in self.stock_data.items():
                available_dates = prices.index[prices.index <= rebalance_date]
                if len(available_dates) > lookback_days:
                    momentum_factor = self.calculate_momentum_factor(prices, lookback_days)
                    
                    # 找到最近的有效日期
                    valid_dates = momentum_factor.index[momentum_factor.index <= rebalance_date]
                    if len(valid_dates) > 0:
                        latest_date = valid_dates[-1]
                        score = momentum_factor.loc[latest_date]
                        if pd.notna(score) and not np.isinf(score):
                            factor_scores[stock_code] = score
            
            if len(factor_scores) < 20:
                continue
            
            # 按因子得分排序 - 动量因子：高分表示强势上涨
            sorted_scores = pd.Series(factor_scores).sort_values(ascending=False)
            
            # 选择前20%和后20%的股票
            n_stocks = len(sorted_scores)
            top_count = max(5, int(n_stocks * 0.2))
            bottom_count = max(5, int(n_stocks * 0.2))
            
            high_momentum_stocks = sorted_scores.head(top_count).index.tolist()
            low_momentum_stocks = sorted_scores.tail(bottom_count).index.tolist()
            
            # 计算持有期收益
            next_date = rebalance_dates[i + 1]
            
            # 高动量组合收益
            high_returns = []
            for stock in high_momentum_stocks:
                ret = self.calculate_stock_return(self.stock_data[stock], rebalance_date, next_date)
                if ret != 0.0:
                    high_returns.append(ret)
            
            # 低动量组合收益
            low_returns = []
            for stock in low_momentum_stocks:
                ret = self.calculate_stock_return(self.stock_data[stock], rebalance_date, next_date)
                if ret != 0.0:
                    low_returns.append(ret)
            
            # 计算组合平均收益
            if high_returns:
                high_momentum_returns.append(np.mean(high_returns))
            
            if low_returns:
                low_momentum_returns.append(np.mean(low_returns))
        
        # 计算绩效指标 - 只做多策略
        if len(high_momentum_returns) >= 3:
            high_series = pd.Series(high_momentum_returns)

            # 年化倍数
            periods_per_year = {'W': 52, 'M': 12, 'Q': 4, 'Y': 1}[rebalance_freq]

            results = {
                'lookback_days': lookback_days,
                'rebalance_freq': rebalance_freq,
                'n_periods': len(high_series),
                'high_annual_return': high_series.mean() * periods_per_year * 100,
                'high_volatility': high_series.std() * np.sqrt(periods_per_year) * 100,
                'high_sharpe': (high_series.mean() / high_series.std() * np.sqrt(periods_per_year)) if high_series.std() > 0 else 0,
                'win_rate': (high_series > 0).mean() * 100,
                'max_drawdown': self.calculate_max_drawdown(high_series) * 100
            }

            print(f"   做多年化收益: {results['high_annual_return']:.2f}%, 夏普比率: {results['high_sharpe']:.3f}, 胜率: {results['win_rate']:.1f}%")
            return results
        
        return {}
    
    def calculate_max_drawdown(self, returns):
        """计算最大回撤"""
        cumulative = (1 + returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max
        return drawdown.min()

    def run_optimization(self):
        """运行参数优化"""
        print("🎯 动量因子参数优化分析")
        print("=" * 60)

        # 加载数据
        if not self.load_stock_data():
            print("❌ 无法加载股票数据")
            return None

        # 参数组合 - 动量因子通常使用中长期回看期
        lookback_days_list = [80, 100, 120, 140, 160, 180, 200, 240]  # 1个月到1年
        rebalance_freq_list = ['W', 'M', 'Q']  # 周度、月度、季度

        print(f"🔍 测试参数组合:")
        print(f"   回看天数: {lookback_days_list}")
        print(f"   调仓频率: {rebalance_freq_list}")

        results = []

        for lookback_days in lookback_days_list:
            for rebalance_freq in rebalance_freq_list:
                result = self.backtest_parameter_combination(lookback_days, rebalance_freq)
                if result:
                    results.append(result)

        if not results:
            print("❌ 没有有效的回测结果")
            return None

        # 分析结果
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('high_sharpe', ascending=False)

        self.display_results(results_df)
        self.save_results(results_df)
        self.plot_results(results_df)

        return results_df

    def display_results(self, results_df):
        """显示优化结果"""
        print("\n" + "="*80)
        print("📊 动量因子参数优化结果")
        print("="*80)

        print(f"{'排名':>4} {'回看天数':>8} {'调仓频率':>8} {'年化收益':>10} {'夏普比率':>10} {'胜率':>8} {'最大回撤':>10}")
        print("-"*70)

        for i, (_, row) in enumerate(results_df.iterrows(), 1):
            print(f"{i:>4} {int(row['lookback_days']):>8} {row['rebalance_freq']:>8} "
                  f"{row['high_annual_return']:>9.2f}% "
                  f"{row['high_sharpe']:>9.3f} "
                  f"{row['win_rate']:>7.1f}% "
                  f"{row['max_drawdown']:>9.2f}%")

        # 最佳参数
        best_row = results_df.iloc[0]
        print(f"\n🏆 最佳参数组合 (基于夏普比率):")
        print(f"   回看天数: {int(best_row['lookback_days'])}天")
        freq_names = {'W': '周度', 'M': '月度', 'Q': '季度', 'Y': '年度'}
        print(f"   调仓频率: {best_row['rebalance_freq']} ({freq_names[best_row['rebalance_freq']]})")
        print(f"   做多年化收益: {best_row['high_annual_return']:.2f}%")
        print(f"   年化波动率: {best_row['high_volatility']:.2f}%")
        print(f"   夏普比率: {best_row['high_sharpe']:.3f}")
        print(f"   胜率: {best_row['win_rate']:.1f}%")
        print(f"   最大回撤: {best_row['max_drawdown']:.2f}%")
        print(f"   回测期数: {int(best_row['n_periods'])}")

        # 分析不同回看期的表现
        print(f"\n📈 不同回看期表现分析:")
        lookback_analysis = results_df.groupby('lookback_days').agg({
            'high_annual_return': 'mean',
            'high_sharpe': 'mean',
            'win_rate': 'mean'
        }).round(2)

        for lookback_days, row in lookback_analysis.iterrows():
            print(f"   {int(lookback_days):>3}天: 年化收益{row['high_annual_return']:>6.2f}%, "
                  f"夏普比率{row['high_sharpe']:>6.3f}, 胜率{row['win_rate']:>5.1f}%")

        # 分析不同调仓频率的表现
        print(f"\n🔄 不同调仓频率表现分析:")
        freq_analysis = results_df.groupby('rebalance_freq').agg({
            'high_annual_return': 'mean',
            'high_sharpe': 'mean',
            'win_rate': 'mean'
        }).round(2)

        for freq, row in freq_analysis.iterrows():
            freq_name = freq_names[freq]
            print(f"   {freq_name:>2}: 年化收益{row['high_annual_return']:>6.2f}%, "
                  f"夏普比率{row['high_sharpe']:>6.3f}, 胜率{row['win_rate']:>5.1f}%")

    def plot_results(self, results_df):
        """绘制优化结果图表"""
        _, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 夏普比率热力图
        pivot_sharpe = results_df.pivot(index='lookback_days', columns='rebalance_freq', values='high_sharpe')
        im1 = ax1.imshow(pivot_sharpe.values, cmap='RdYlGn', aspect='auto')
        ax1.set_xticks(range(len(pivot_sharpe.columns)))
        ax1.set_xticklabels(pivot_sharpe.columns)
        ax1.set_yticks(range(len(pivot_sharpe.index)))
        ax1.set_yticklabels([f"{int(x)}天" for x in pivot_sharpe.index])
        ax1.set_title('夏普比率热力图')
        ax1.set_xlabel('调仓频率')
        ax1.set_ylabel('回看天数')

        # 添加数值标注
        for i in range(len(pivot_sharpe.index)):
            for j in range(len(pivot_sharpe.columns)):
                ax1.text(j, i, f'{pivot_sharpe.iloc[i, j]:.2f}',
                        ha="center", va="center", color="black", fontsize=8)

        plt.colorbar(im1, ax=ax1)

        # 2. 年化收益率热力图
        pivot_return = results_df.pivot(index='lookback_days', columns='rebalance_freq', values='high_annual_return')
        im2 = ax2.imshow(pivot_return.values, cmap='RdYlGn', aspect='auto')
        ax2.set_xticks(range(len(pivot_return.columns)))
        ax2.set_xticklabels(pivot_return.columns)
        ax2.set_yticks(range(len(pivot_return.index)))
        ax2.set_yticklabels([f"{int(x)}天" for x in pivot_return.index])
        ax2.set_title('年化收益率热力图 (%)')
        ax2.set_xlabel('调仓频率')
        ax2.set_ylabel('回看天数')

        # 添加数值标注
        for i in range(len(pivot_return.index)):
            for j in range(len(pivot_return.columns)):
                ax2.text(j, i, f'{pivot_return.iloc[i, j]:.1f}%',
                        ha="center", va="center", color="black", fontsize=8)

        plt.colorbar(im2, ax=ax2)

        # 3. 回看天数 vs 夏普比率
        lookback_sharpe = results_df.groupby('lookback_days')['high_sharpe'].mean()
        ax3.plot(lookback_sharpe.index, lookback_sharpe.values, 'o-', linewidth=2, markersize=6)
        ax3.set_xlabel('回看天数')
        ax3.set_ylabel('平均夏普比率')
        ax3.set_title('回看天数对夏普比率的影响')
        ax3.grid(True, alpha=0.3)

        # 4. 调仓频率 vs 夏普比率
        freq_sharpe = results_df.groupby('rebalance_freq')['high_sharpe'].mean()
        freq_names = {'W': '周度', 'M': '月度', 'Q': '季度', 'Y': '年度'}
        freq_labels = [freq_names[freq] for freq in freq_sharpe.index]

        colors = ['skyblue', 'lightgreen', 'lightcoral', 'gold'][:len(freq_labels)]
        bars = ax4.bar(freq_labels, freq_sharpe.values, color=colors)
        ax4.set_ylabel('平均夏普比率')
        ax4.set_title('调仓频率对夏普比率的影响')

        # 添加数值标注
        for bar, value in zip(bars, freq_sharpe.values):
            height = bar.get_height()
            ax4.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                    f'{value:.3f}', ha='center', va='bottom')

        plt.tight_layout()

        # 保存图表
        filename = f'momentum_factor_optimization_charts_{datetime.now().strftime("%Y%m%d_%H%M%S")}.png'
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 优化结果图表已保存到: {filename}")
        plt.show()

    def save_results(self, results_df):
        """保存结果"""
        filename = f'momentum_factor_optimization_results_{datetime.now().strftime("%Y%m%d_%H%M%S")}.csv'
        results_df.to_csv(filename, index=False)
        print(f"\n📁 详细结果已保存到: {filename}")

def main():
    """主函数"""
    start_time = datetime.now()

    try:
        optimizer = MomentumFactorOptimizer()
        results_df = optimizer.run_optimization()

        if results_df is not None:
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n🎉 动量因子参数优化完成！")
            print(f"⏱️  总耗时: {duration}")
            print(f"\n💡 优化结论:")
            print(f"   ✅ 测试了 {len(results_df)} 个有效参数组合")
            print(f"   ✅ 基于2020年以来的实际数据")
            print(f"   ✅ 考虑了交易成本和实际可操作性")
            print(f"   ✅ 提供了风险调整后的最优参数")

            # 给出投资建议
            best_row = results_df.iloc[0]
            print(f"\n🎯 投资建议:")
            print(f"   📈 最佳动量因子回看期: {int(best_row['lookback_days'])}天")
            freq_names = {'W': '周度', 'M': '月度', 'Q': '季度', 'Y': '年度'}
            print(f"   🔄 最佳调仓频率: {freq_names[best_row['rebalance_freq']]}")
            print(f"   💰 预期年化收益: {best_row['high_annual_return']:.2f}%")
            print(f"   ⚖️  风险调整收益: 夏普比率 {best_row['high_sharpe']:.3f}")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断优化")
    except Exception as e:
        print(f"\n❌ 优化过程出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
