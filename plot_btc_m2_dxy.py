# 导入必要的库
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
import pandas_datareader.data as web
from datetime import datetime
import matplotlib.dates as mdates
from matplotlib.ticker import FuncFormatter
import os
import sys
import requests
from urllib.request import ProxyHandler, build_opener, install_opener

# 设置代理
def setup_proxy():
    """设置代理，使用config目录下的配置"""
    try:
        # 导入config目录下的代理配置
        sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        from config.btc_data_config import PROXY_SETTINGS

        # 获取代理设置
        proxies = PROXY_SETTINGS

        print(f"Using proxy settings from config: {proxies}")

        # 设置环境变量
        for protocol, proxy in proxies.items():
            os.environ[f"{protocol.upper()}_PROXY"] = proxy

        # 设置urllib的代理
        proxy_handler = ProxyHandler(proxies)
        opener = build_opener(proxy_handler)
        install_opener(opener)

        # 设置requests的默认代理，yfinance内部使用requests
        requests.Session.request = (lambda original_func: lambda self, method, url, **kwargs: original_func(
            self, method, url, **{**kwargs, 'proxies': proxies}))(requests.Session.request)

        print("Proxy setup completed")
    except Exception as e:
        print(f"Error setting up proxy: {e}")
        print("Continuing without proxy...")

def get_bitcoin_data(start_date, end_date=None):
    """获取比特币历史价格数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')

    print(f"Loading Bitcoin data from {start_date} to {end_date}...")
    try:
        # 从Yahoo Finance获取数据
        btc_data = yf.download('BTC-USD', start=start_date, end=end_date)
        print(f"Successfully retrieved {len(btc_data)} records of Bitcoin data")

        # 处理多级索引列名
        if isinstance(btc_data.columns, pd.MultiIndex):
            btc_data.columns = [col[0] for col in btc_data.columns]

        return btc_data
    except Exception as e:
        print(f"Failed to get Bitcoin data: {e}")
        return pd.DataFrame()

def get_dxy_data(start_date, end_date=None):
    """获取美元指数(DXY)数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')

    try:
        print(f"Getting US Dollar Index data from {start_date} to {end_date}...")
        # 从Yahoo Finance获取数据
        dxy_data = yf.download('DX-Y.NYB', start=start_date, end=end_date)

        # 创建DXY列
        dxy_data['DXY'] = dxy_data['Close']

        print(f"Successfully retrieved {len(dxy_data)} records of US Dollar Index data")
        return dxy_data
    except Exception as e:
        print(f"Failed to get US Dollar Index data: {e}")
        return pd.DataFrame()

def get_m2_data(start_date, end_date=None):
    """获取M2货币供应量数据"""
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')

    try:
        print(f"Getting M2 Money Supply data from {start_date} to {end_date}...")
        # 使用pandas_datareader从FRED获取数据
        m2_data = web.DataReader('M2SL', 'fred', start_date, end_date)
        m2_data.rename(columns={'M2SL': 'M2'}, inplace=True)

        # 计算同比增长率
        m2_data['M2_YoY'] = m2_data['M2'].pct_change(12) * 100  # 12个月的同比增长率

        print(f"Successfully retrieved {len(m2_data)} records of M2 Money Supply data")
        return m2_data
    except Exception as e:
        print(f"Failed to get M2 Money Supply data: {e}")
        return pd.DataFrame()

def plot_btc_and_m2_dxy_ratio():
    """绘制比特币价格和M2/DXY比率"""
    # 设置代理
    setup_proxy()

    # 设置时间范围
    start_date = '2015-01-01'
    end_date = datetime.now().strftime('%Y-%m-%d')

    # 获取数据
    btc_data = get_bitcoin_data(start_date, end_date)
    dxy_data = get_dxy_data(start_date, end_date)
    m2_data = get_m2_data(start_date, end_date)

    # 确保所有数据都不为空
    if btc_data.empty or dxy_data.empty or m2_data.empty:
        print("Error: One or more datasets are empty")
        return

    # 打印数据的日期范围
    print(f"Bitcoin data range: {btc_data.index.min()} to {btc_data.index.max()}")
    print(f"DXY data range: {dxy_data.index.min()} to {dxy_data.index.max()}")
    print(f"M2 data range: {m2_data.index.min()} to {m2_data.index.max()}")

    # 将所有数据重采样为月度数据，以匹配M2数据的频率
    btc_monthly = btc_data['Close'].resample('ME').last()
    dxy_monthly = dxy_data['DXY'].resample('ME').last()

    # 确保所有索引都是日期类型
    btc_monthly.index = pd.to_datetime(btc_monthly.index)
    dxy_monthly.index = pd.to_datetime(dxy_monthly.index)
    m2_data.index = pd.to_datetime(m2_data.index)

    # 找出共同的日期范围
    common_start = max(btc_monthly.index.min(), dxy_monthly.index.min(), m2_data.index.min())
    common_end = min(btc_monthly.index.max(), dxy_monthly.index.max(), m2_data.index.max())

    print(f"Common date range: {common_start} to {common_end}")

    # 创建一个新的日期范围，确保所有数据都有相同的索引
    date_range = pd.date_range(start=common_start, end=common_end, freq='ME')

    # 创建一个新的DataFrame，包含所有数据
    df = pd.DataFrame(index=date_range)
    df['BTC'] = btc_monthly.reindex(date_range)
    df['DXY'] = dxy_monthly.reindex(date_range)
    df['M2'] = m2_data['M2'].reindex(date_range)

    # 计算M2/DXY比率
    df['M2_DXY_Ratio'] = df['M2'] / df['DXY']

    # 计算M2/DXY比率的变化率
    df['M2_DXY_Ratio_Change'] = df['M2_DXY_Ratio'].pct_change(periods=3) * 100  # 3个月的变化率

    # 去除缺失值
    df = df.dropna()

    # 打印数据样本
    print("\nData sample:")
    print(df.head())

    # 打印数据范围
    print(f"Data range: {df.index.min()} to {df.index.max()}")
    print(f"Number of data points: {len(df)}")

    # 打印相关系数
    correlation = df['BTC'].corr(df['M2_DXY_Ratio'])
    print(f"Correlation between BTC and M2/DXY Ratio: {correlation:.4f}")

    # 计算滚动相关系数（12个月窗口）
    df['Rolling_Correlation'] = df['BTC'].rolling(window=12).corr(df['M2_DXY_Ratio'])

    # 绘制图表
    fig, axes = plt.subplots(3, 1, figsize=(14, 18), sharex=True)

    # 1. 比特币价格
    ax1 = axes[0]
    ax1.plot(df.index, df['BTC'], color='orange', linewidth=2, label='Bitcoin Price (USD)')
    ax1.set_title('Bitcoin Price', fontsize=14)
    ax1.set_ylabel('Price (USD)', fontsize=12)
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')

    # 使用对数刻度
    ax1.set_yscale('log')

    # 格式化y轴标签为千分位分隔
    ax1.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'${x:,.0f}'))

    # 2. M2/DXY比率
    ax2 = axes[1]
    ax2.plot(df.index, df['M2_DXY_Ratio'], color='blue', linewidth=2, label='M2/DXY Ratio')
    ax2.set_title('M2/DXY Ratio', fontsize=14)
    ax2.set_ylabel('Ratio', fontsize=12)
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left')

    # 格式化y轴标签为千分位分隔
    ax2.yaxis.set_major_formatter(FuncFormatter(lambda x, _: f'{x:,.0f}'))

    # 3. 滚动相关系数
    ax3 = axes[2]
    ax3.plot(df.index, df['Rolling_Correlation'], color='green', linewidth=2, label='12-Month Rolling Correlation')
    ax3.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax3.set_title('12-Month Rolling Correlation between Bitcoin and M2/DXY Ratio', fontsize=14)
    ax3.set_ylabel('Correlation', fontsize=12)
    ax3.set_ylim(-1, 1)
    ax3.grid(True, alpha=0.3)
    ax3.legend(loc='upper left')

    # 格式化x轴日期
    for ax in axes:
        ax.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax.xaxis.set_major_locator(mdates.YearLocator())

    plt.tight_layout()
    plt.savefig('btc_m2_dxy_ratio.png', dpi=300)
    plt.show()

    # 打印一些统计信息
    print("\nStatistical Summary:")
    print(df[['BTC', 'M2_DXY_Ratio', 'M2_DXY_Ratio_Change', 'Rolling_Correlation']].describe())

    # 分析不同时期的相关性
    # 将数据分为几个时期
    periods = [
        ('2015-2017', '2015-01-01', '2017-12-31'),
        ('2018-2019', '2018-01-01', '2019-12-31'),
        ('2020-2021', '2020-01-01', '2021-12-31'),
        ('2022-Present', '2022-01-01', end_date)
    ]

    print("\nCorrelation by Period:")
    for period_name, period_start, period_end in periods:
        period_data = df[(df.index >= period_start) & (df.index <= period_end)]
        if not period_data.empty:
            period_corr = period_data['BTC'].corr(period_data['M2_DXY_Ratio'])
            print(f"{period_name}: {period_corr:.4f}")

if __name__ == "__main__":
    plot_btc_and_m2_dxy_ratio()
