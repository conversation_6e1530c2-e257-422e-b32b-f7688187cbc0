{"analysis_date": "2025-06-03 14:24:52", "data_source": "hsi_dividend_quick_20250603_141448.csv", "total_stocks": 80, "groups": {"Group_1": [{"symbol": "09618", "dividend_yield": 3.13, "name": "京东集团-SW"}, {"symbol": "00316", "dividend_yield": 2.92, "name": "东方海外国际"}, {"symbol": "00941", "dividend_yield": 2.08, "name": "中国移动"}, {"symbol": "00388", "dividend_yield": 1.65, "name": "香港交易所"}, {"symbol": "00700", "dividend_yield": 1.44, "name": "腾讯控股"}, {"symbol": "01211", "dividend_yield": 1.27, "name": "比亚迪股份"}, {"symbol": "09961", "dividend_yield": 1.21, "name": "携程集团-S"}, {"symbol": "01038", "dividend_yield": 1.02, "name": "长江基建集团"}, {"symbol": "00006", "dividend_yield": 0.89, "name": "电能实业"}, {"symbol": "02318", "dividend_yield": 0.8, "name": "中国平安"}, {"symbol": "01088", "dividend_yield": 0.76, "name": "中国神华"}, {"symbol": "09988", "dividend_yield": 0.72, "name": "阿里巴巴-W"}, {"symbol": "02313", "dividend_yield": 0.71, "name": "申洲国际"}, {"symbol": "06690", "dividend_yield": 0.71, "name": "海尔智家"}, {"symbol": "01044", "dividend_yield": 0.69, "name": "恒安国际"}, {"symbol": "02020", "dividend_yield": 0.63, "name": "安踏体育"}], "Group_2": [{"symbol": "03968", "dividend_yield": 0.61, "name": "招商银行"}, {"symbol": "01299", "dividend_yield": 0.59, "name": "友邦保险"}, {"symbol": "02359", "dividend_yield": 0.59, "name": "药明康德"}, {"symbol": "00881", "dividend_yield": 0.59, "name": "中升控股"}, {"symbol": "01997", "dividend_yield": 0.58, "name": "九龙仓置业"}, {"symbol": "09633", "dividend_yield": 0.54, "name": "农夫山泉"}, {"symbol": "01109", "dividend_yield": 0.51, "name": "华润置地"}, {"symbol": "02688", "dividend_yield": 0.49, "name": "新奥能源"}, {"symbol": "00012", "dividend_yield": 0.42, "name": "恒基地产"}, {"symbol": "00001", "dividend_yield": 0.42, "name": "长和"}, {"symbol": "00669", "dividend_yield": 0.34, "name": "创科实业"}, {"symbol": "01113", "dividend_yield": 0.34, "name": "长实集团"}, {"symbol": "00011", "dividend_yield": 0.31, "name": "恒生银行"}, {"symbol": "09999", "dividend_yield": 0.28, "name": "网易-S"}, {"symbol": "00175", "dividend_yield": 0.28, "name": "吉利汽车"}, {"symbol": "00688", "dividend_yield": 0.28, "name": "中国海外发展"}], "Group_3": [{"symbol": "00066", "dividend_yield": 0.27, "name": "港铁公司"}, {"symbol": "00005", "dividend_yield": 0.26, "name": "汇丰控股"}, {"symbol": "01928", "dividend_yield": 0.25, "name": "金沙中国有限公司"}, {"symbol": "01378", "dividend_yield": 0.23, "name": "中国宏桥"}, {"symbol": "00285", "dividend_yield": 0.22, "name": "比亚迪电子"}, {"symbol": "02331", "dividend_yield": 0.22, "name": "李宁"}, {"symbol": "01099", "dividend_yield": 0.21, "name": "国药控股"}, {"symbol": "00016", "dividend_yield": 0.21, "name": "新鸿基地产"}, {"symbol": "01209", "dividend_yield": 0.2, "name": "华润万象生活"}, {"symbol": "00322", "dividend_yield": 0.19, "name": "康师傅控股"}, {"symbol": "00003", "dividend_yield": 0.18, "name": "香港中华煤气"}, {"symbol": "09901", "dividend_yield": 0.16, "name": "新东方-S"}, {"symbol": "06862", "dividend_yield": 0.16, "name": "海底捞"}, {"symbol": "02388", "dividend_yield": 0.15, "name": "中银香港"}, {"symbol": "00267", "dividend_yield": 0.15, "name": "中信股份"}, {"symbol": "00836", "dividend_yield": 0.15, "name": "华润电力"}], "Group_4": [{"symbol": "01876", "dividend_yield": 0.15, "name": "百威亚太"}, {"symbol": "02899", "dividend_yield": 0.14, "name": "紫金矿业"}, {"symbol": "00939", "dividend_yield": 0.14, "name": "建设银行"}, {"symbol": "00883", "dividend_yield": 0.14, "name": "中国海洋石油"}, {"symbol": "00291", "dividend_yield": 0.14, "name": "华润啤酒"}, {"symbol": "02628", "dividend_yield": 0.14, "name": "中国人寿"}, {"symbol": "03692", "dividend_yield": 0.13, "name": "翰森制药"}, {"symbol": "00992", "dividend_yield": 0.13, "name": "联想集团"}, {"symbol": "02382", "dividend_yield": 0.13, "name": "舜宇光学科技"}, {"symbol": "00027", "dividend_yield": 0.12, "name": "银河娱乐"}, {"symbol": "02319", "dividend_yield": 0.12, "name": "蒙牛乳业"}, {"symbol": "03988", "dividend_yield": 0.12, "name": "中国银行"}, {"symbol": "00288", "dividend_yield": 0.12, "name": "万洲国际"}, {"symbol": "00857", "dividend_yield": 0.11, "name": "中国石油股份"}, {"symbol": "00101", "dividend_yield": 0.1, "name": "恒隆地产"}, {"symbol": "00868", "dividend_yield": 0.07, "name": "信义玻璃"}], "Group_5": [{"symbol": "00960", "dividend_yield": 0.07, "name": "龙湖集团"}, {"symbol": "00968", "dividend_yield": 0.07, "name": "信义光能"}, {"symbol": "00762", "dividend_yield": 0.05, "name": "中国联通"}, {"symbol": "01929", "dividend_yield": 0.05, "name": "周大福"}, {"symbol": "01398", "dividend_yield": 0.05, "name": "工商银行"}, {"symbol": "00386", "dividend_yield": 0.05, "name": "中国石油化工股份"}, {"symbol": "01093", "dividend_yield": 0.03, "name": "石药集团"}, {"symbol": "01177", "dividend_yield": 0.01, "name": "中国生物制药"}, {"symbol": "06618", "dividend_yield": 0.0, "name": "京东健康"}, {"symbol": "01810", "dividend_yield": 0.0, "name": "小米集团-W"}, {"symbol": "02015", "dividend_yield": 0.0, "name": "理想汽车-W"}, {"symbol": "00981", "dividend_yield": 0.0, "name": "中芯国际"}, {"symbol": "02269", "dividend_yield": 0.0, "name": "药明生物"}, {"symbol": "00241", "dividend_yield": 0.0, "name": "阿里健康"}, {"symbol": "01024", "dividend_yield": 0.0, "name": "快手-W"}, {"symbol": "03690", "dividend_yield": 0.0, "name": "美团-W"}]}, "performance": {"Group_1": {"avg_dividend_yield": 1.289375, "estimated_annual_return": 0.06289375, "volatility": 0.18, "sharpe_ratio": 0.3494097222222222, "estimated_max_drawdown": 0.27, "stock_count": 16}, "Group_2": {"avg_dividend_yield": 0.448125, "estimated_annual_return": 0.08448125000000001, "volatility": 0.25, "sharpe_ratio": 0.33792500000000003, "estimated_max_drawdown": 0.375, "stock_count": 16}, "Group_3": {"avg_dividend_yield": 0.200625, "estimated_annual_return": 0.08200625, "volatility": 0.25, "sharpe_ratio": 0.328025, "estimated_max_drawdown": 0.375, "stock_count": 16}, "Group_4": {"avg_dividend_yield": 0.125, "estimated_annual_return": 0.08125, "volatility": 0.25, "sharpe_ratio": 0.325, "estimated_max_drawdown": 0.375, "stock_count": 16}, "Group_5": {"avg_dividend_yield": 0.02375, "estimated_annual_return": 0.0802375, "volatility": 0.25, "sharpe_ratio": 0.32095, "estimated_max_drawdown": 0.375, "stock_count": 16}}, "factor_analysis": {"factor_effectiveness": {"high_dividend_group": {"group": "Group_1", "return": 0.06289375, "dividend_yield": 1.289375, "sharpe": 0.3494097222222222}, "low_dividend_group": {"group": "Group_5", "return": 0.0802375, "dividend_yield": 0.02375, "sharpe": 0.32095}, "return_spread": -0.017343750000000005, "yield_spread": 1.265625, "factor_works": false}, "monotonic": false}}