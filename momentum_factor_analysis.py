#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动量因子深度分析和改进建议
分析当前动量因子失效的原因并提出针对性改进方案
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os
import pickle
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MomentumFactorAnalysis:
    def __init__(self, data_dir='sp500_price_cache'):
        self.data_dir = data_dir
        self.price_data = {}
        
    def load_price_data(self):
        """加载价格数据"""
        print("📊 加载价格数据...")
        
        if os.path.exists(self.data_dir):
            pkl_files = [f for f in os.listdir(self.data_dir) if f.endswith('_price.pkl')]
            print(f"   发现 {len(pkl_files)} 个缓存文件")

            success_count = 0
            for file in pkl_files[:50]:  # 限制数量以加快分析
                symbol = file.replace('_price.pkl', '')
                file_path = os.path.join(self.data_dir, file)

                try:
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)

                    if isinstance(data, dict) and 'price_data' in data:
                        price_series = data['price_data']
                        if isinstance(price_series, pd.Series) and len(price_series) > 500:
                            if not isinstance(price_series.index, pd.DatetimeIndex):
                                price_series.index = pd.to_datetime(price_series.index)
                            if price_series.index.tz is not None:
                                price_series.index = price_series.index.tz_convert('UTC').tz_localize(None)
                            self.price_data[symbol] = price_series.dropna()
                            success_count += 1

                except Exception as e:
                    continue

        print(f"✅ 成功加载 {len(self.price_data)} 只股票的价格数据")

    def analyze_momentum_patterns(self):
        """分析动量模式"""
        print("🔍 分析动量模式...")
        
        # 计算不同周期的动量
        momentum_results = {}
        
        for symbol, prices in self.price_data.items():
            returns = prices.pct_change()
            
            # 计算不同周期的动量
            momentum_1m = prices.pct_change(21)   # 1个月
            momentum_3m = prices.pct_change(63)   # 3个月
            momentum_6m = prices.pct_change(126)  # 6个月
            momentum_12m = prices.pct_change(252) # 12个月
            
            # 计算未来收益率
            future_1m = prices.shift(-21).pct_change(21)
            future_3m = prices.shift(-63).pct_change(63)
            
            momentum_results[symbol] = pd.DataFrame({
                'momentum_1m': momentum_1m,
                'momentum_3m': momentum_3m,
                'momentum_6m': momentum_6m,
                'momentum_12m': momentum_12m,
                'future_1m': future_1m,
                'future_3m': future_3m
            })
        
        # 合并所有数据
        all_data = pd.concat(momentum_results.values(), ignore_index=True)
        all_data = all_data.dropna()
        
        print(f"   有效观测数: {len(all_data)}")
        
        # 计算相关性
        print("\n📊 动量因子与未来收益相关性分析:")
        print("-" * 50)
        
        correlations = {}
        for momentum_col in ['momentum_1m', 'momentum_3m', 'momentum_6m', 'momentum_12m']:
            for future_col in ['future_1m', 'future_3m']:
                corr = all_data[momentum_col].corr(all_data[future_col])
                correlations[f"{momentum_col}_vs_{future_col}"] = corr
                print(f"{momentum_col:>15} vs {future_col:>10}: {corr:>8.4f}")
        
        return all_data, correlations

    def analyze_market_regimes(self):
        """分析不同市场环境下的动量效应"""
        print("\n🌊 分析市场环境对动量效应的影响...")
        
        # 使用SPY作为市场代理（如果有的话）
        market_data = None
        if 'SPY' in self.price_data:
            market_data = self.price_data['SPY']
        elif len(self.price_data) > 0:
            # 使用所有股票的等权重平均作为市场代理
            price_matrix = pd.DataFrame(self.price_data)
            market_data = price_matrix.mean(axis=1)
        
        if market_data is None:
            print("   无法获取市场数据")
            return
        
        # 计算市场状态
        market_returns = market_data.pct_change()
        market_vol = market_returns.rolling(60).std() * np.sqrt(252)  # 年化波动率
        
        # 定义市场状态
        vol_median = market_vol.median()
        high_vol_periods = market_vol > vol_median
        
        # 计算牛熊市
        ma_200 = market_data.rolling(200).mean()
        bull_market = market_data > ma_200
        
        print(f"   高波动期间占比: {high_vol_periods.mean():.2%}")
        print(f"   牛市期间占比: {bull_market.mean():.2%}")
        
        return {
            'market_data': market_data,
            'high_vol_periods': high_vol_periods,
            'bull_market': bull_market,
            'market_vol': market_vol
        }

    def test_alternative_momentum_factors(self):
        """测试替代动量因子"""
        print("\n🧪 测试替代动量因子...")
        
        alternative_factors = {}
        
        for symbol, prices in list(self.price_data.items())[:10]:  # 限制数量
            returns = prices.pct_change()
            
            # 1. 跳跃动量（跳过最近1个月）
            skip_momentum = prices.pct_change(126).shift(21)  # 6个月动量，跳过最近1个月
            
            # 2. 风险调整动量
            vol_60d = returns.rolling(60).std()
            risk_adj_momentum = (prices.pct_change(126) / vol_60d).shift(21)
            
            # 3. 趋势强度动量
            def calculate_trend_momentum(price_series, window=126):
                trend_momentum = pd.Series(index=price_series.index, dtype=float)
                for i in range(window + 21, len(price_series)):
                    # 使用6个月前到1个月前的数据
                    y = np.log(price_series.iloc[i-window-21:i-21].values)
                    x = np.arange(len(y))
                    if len(y) > 10:
                        try:
                            slope, _ = np.polyfit(x, y, 1)
                            # 计算R²
                            y_pred = slope * x + np.mean(y)
                            ss_res = np.sum((y - y_pred) ** 2)
                            ss_tot = np.sum((y - np.mean(y)) ** 2)
                            r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
                            # 趋势动量 = 斜率 * R²
                            trend_momentum.iloc[i] = slope * max(0, r_squared)
                        except:
                            trend_momentum.iloc[i] = 0
                return trend_momentum
            
            trend_momentum = calculate_trend_momentum(prices)
            
            # 4. 相对强度动量（相对于市场）
            if 'SPY' in self.price_data:
                market_returns = self.price_data['SPY'].pct_change()
                stock_returns = returns
                # 对齐数据
                common_dates = stock_returns.index.intersection(market_returns.index)
                if len(common_dates) > 126:
                    stock_ret_aligned = stock_returns.loc[common_dates]
                    market_ret_aligned = market_returns.loc[common_dates]
                    relative_momentum = (stock_ret_aligned.rolling(126).sum() - 
                                       market_ret_aligned.rolling(126).sum()).shift(21)
                else:
                    relative_momentum = pd.Series(index=prices.index, dtype=float)
            else:
                relative_momentum = pd.Series(index=prices.index, dtype=float)
            
            # 计算未来收益
            future_returns = prices.shift(-21).pct_change(21)
            
            alternative_factors[symbol] = pd.DataFrame({
                'skip_momentum': skip_momentum,
                'risk_adj_momentum': risk_adj_momentum,
                'trend_momentum': trend_momentum,
                'relative_momentum': relative_momentum,
                'future_returns': future_returns
            })
        
        # 合并数据并计算相关性
        all_alt_data = pd.concat(alternative_factors.values(), ignore_index=True)
        all_alt_data = all_alt_data.dropna()
        
        print(f"   替代因子有效观测数: {len(all_alt_data)}")
        
        if len(all_alt_data) > 0:
            print("\n📊 替代动量因子与未来收益相关性:")
            print("-" * 50)
            
            for factor in ['skip_momentum', 'risk_adj_momentum', 'trend_momentum', 'relative_momentum']:
                if factor in all_alt_data.columns:
                    corr = all_alt_data[factor].corr(all_alt_data['future_returns'])
                    print(f"{factor:>20}: {corr:>8.4f}")
        
        return all_alt_data

    def generate_improvement_recommendations(self, correlations, market_analysis, alt_factors_data):
        """生成改进建议"""
        print("\n💡 动量因子改进建议:")
        print("=" * 60)
        
        recommendations = []
        
        # 1. 基于相关性分析的建议
        best_momentum = max(correlations.items(), key=lambda x: abs(x[1]))
        print(f"1. 最佳动量周期: {best_momentum[0]} (相关性: {best_momentum[1]:.4f})")
        
        if abs(best_momentum[1]) < 0.05:
            recommendations.append("传统动量因子在当前市场环境下效果较弱")
            recommendations.append("考虑使用替代因子或组合因子")
        
        # 2. 基于替代因子的建议
        if len(alt_factors_data) > 0:
            print("\n2. 替代因子表现:")
            alt_corrs = {}
            for factor in ['skip_momentum', 'risk_adj_momentum', 'trend_momentum', 'relative_momentum']:
                if factor in alt_factors_data.columns:
                    corr = alt_factors_data[factor].corr(alt_factors_data['future_returns'])
                    alt_corrs[factor] = corr
                    print(f"   {factor}: {corr:.4f}")
            
            if alt_corrs:
                best_alt = max(alt_corrs.items(), key=lambda x: abs(x[1]))
                print(f"   最佳替代因子: {best_alt[0]} (相关性: {best_alt[1]:.4f})")
                
                if abs(best_alt[1]) > abs(best_momentum[1]):
                    recommendations.append(f"推荐使用 {best_alt[0]} 替代传统动量因子")
        
        # 3. 市场环境建议
        if market_analysis:
            recommendations.append("考虑市场环境的影响:")
            recommendations.append("- 在高波动期间降低动量因子权重")
            recommendations.append("- 在熊市中考虑反转策略")
            recommendations.append("- 使用动态因子权重调整")
        
        # 4. 技术改进建议
        recommendations.extend([
            "技术改进建议:",
            "- 使用机器学习方法优化因子权重",
            "- 加入宏观经济变量作为调节因子",
            "- 实施动态再平衡频率",
            "- 考虑交易成本和流动性约束",
            "- 使用多因子模型而非单一动量因子"
        ])
        
        # 5. 具体实施建议
        recommendations.extend([
            "具体实施建议:",
            "- 动量周期: 3-6个月最优",
            "- 跳过最近1个月避免反转效应",
            "- 使用风险调整后的动量指标",
            "- 结合趋势强度和持续性指标",
            "- 实施分位数分组而非等权重分组",
            "- 加入止损和止盈机制"
        ])
        
        print("\n📋 详细改进建议:")
        print("-" * 40)
        for i, rec in enumerate(recommendations, 1):
            if rec.endswith(":"):
                print(f"\n{rec}")
            else:
                print(f"   {rec}")
        
        return recommendations

    def create_optimized_momentum_strategy(self):
        """创建优化的动量策略建议"""
        print("\n🎯 优化动量策略建议:")
        print("=" * 60)
        
        strategy_code = '''
# 优化动量策略伪代码
def optimized_momentum_strategy(prices, market_data):
    """
    优化的动量策略
    """
    # 1. 计算基础动量指标
    momentum_3m = prices.pct_change(63)  # 3个月动量
    momentum_6m = prices.pct_change(126) # 6个月动量
    
    # 2. 跳过最近期效应
    skip_period = 21  # 跳过最近1个月
    momentum_3m = momentum_3m.shift(skip_period)
    momentum_6m = momentum_6m.shift(skip_period)
    
    # 3. 风险调整
    volatility = prices.pct_change().rolling(60).std()
    risk_adj_momentum = momentum_6m / volatility
    
    # 4. 趋势强度
    trend_strength = calculate_trend_strength(prices, window=126)
    
    # 5. 市场环境调整
    market_regime = identify_market_regime(market_data)
    regime_weight = get_regime_weight(market_regime)
    
    # 6. 复合动量因子
    composite_momentum = (
        momentum_3m * 0.3 + 
        momentum_6m * 0.4 + 
        risk_adj_momentum * 0.3
    ) * trend_strength * regime_weight
    
    # 7. 分位数排序和组合构建
    factor_scores = composite_momentum.rank(pct=True)
    
    # 8. 动态权重分配
    weights = calculate_dynamic_weights(factor_scores, volatility)
    
    return weights

# 关键改进点:
# 1. 多周期动量组合
# 2. 跳过近期避免反转
# 3. 风险调整
# 4. 趋势强度验证
# 5. 市场环境适应
# 6. 动态权重分配
        '''
        
        print(strategy_code)
        
        return strategy_code


def main():
    """主函数"""
    print("🔍 动量因子深度分析")
    print("=" * 60)
    
    # 初始化分析器
    analyzer = MomentumFactorAnalysis()
    
    # 1. 加载数据
    analyzer.load_price_data()
    
    if len(analyzer.price_data) < 5:
        print("❌ 数据不足，无法进行分析")
        return
    
    # 2. 分析动量模式
    momentum_data, correlations = analyzer.analyze_momentum_patterns()
    
    # 3. 分析市场环境
    market_analysis = analyzer.analyze_market_regimes()
    
    # 4. 测试替代因子
    alt_factors_data = analyzer.test_alternative_momentum_factors()
    
    # 5. 生成改进建议
    recommendations = analyzer.generate_improvement_recommendations(
        correlations, market_analysis, alt_factors_data
    )
    
    # 6. 创建优化策略
    strategy_code = analyzer.create_optimized_momentum_strategy()
    
    # 7. 保存分析结果
    with open('momentum_factor_analysis_report.txt', 'w', encoding='utf-8') as f:
        f.write("动量因子深度分析报告\n")
        f.write("=" * 80 + "\n")
        f.write(f"分析时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        
        f.write("📊 相关性分析结果:\n")
        f.write("-" * 40 + "\n")
        for key, value in correlations.items():
            f.write(f"{key}: {value:.4f}\n")
        
        f.write(f"\n💡 改进建议:\n")
        f.write("-" * 40 + "\n")
        for rec in recommendations:
            f.write(f"{rec}\n")
        
        f.write(f"\n🎯 优化策略代码:\n")
        f.write("-" * 40 + "\n")
        f.write(strategy_code)
    
    print(f"\n📋 分析报告已保存到: momentum_factor_analysis_report.txt")
    print("\n🎯 动量因子分析完成!")


if __name__ == "__main__":
    main()
