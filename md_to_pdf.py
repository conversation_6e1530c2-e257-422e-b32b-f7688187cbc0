#!/usr/bin/env python
# -*- coding: utf-8 -*-

import markdown
import os
from weasyprint import HTML, CSS
from weasyprint.text.fonts import FontConfiguration

def main():
    # 读取Markdown文件
    with open('巴菲特致股东信合集.md', 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 转换为HTML
    html_content = markdown.markdown(md_content, extensions=['tables', 'fenced_code'])
    
    # 添加CSS样式
    html_document = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <title>巴菲特致股东信合集</title>
        <style>
            body {{
                font-family: "SimSun", "宋体", serif;
                margin: 2cm;
                font-size: 12pt;
                line-height: 1.5;
            }}
            h1, h2, h3 {{
                font-weight: bold;
                margin-top: 1em;
                margin-bottom: 0.5em;
            }}
            h1 {{
                font-size: 20pt;
                text-align: center;
                page-break-before: always;
            }}
            h2 {{
                font-size: 16pt;
                page-break-before: always;
            }}
            p {{
                margin-bottom: 0.5em;
                text-align: justify;
            }}
        </style>
    </head>
    <body>
        {html_content}
    </body>
    </html>
    """
    
    # 创建临时HTML文件
    with open('temp.html', 'w', encoding='utf-8') as f:
        f.write(html_document)
    
    # 配置字体
    font_config = FontConfiguration()
    
    # 转换为PDF
    HTML('temp.html').write_pdf(
        '巴菲特致股东信合集.pdf',
        font_config=font_config
    )
    
    # 删除临时HTML文件
    os.remove('temp.html')
    
    print('PDF file created: 巴菲特致股东信合集.pdf')

if __name__ == '__main__':
    main()
