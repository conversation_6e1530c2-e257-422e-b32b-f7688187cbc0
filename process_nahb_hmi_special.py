#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
NAHB Housing Market Index Data Processor (Special Format)

This script processes NAHB Housing Market Index (HMI) data downloaded from the NAHB website.
It is specifically designed to handle the special format of the NAHB HMI Excel file.

Data source: https://www.nahb.org/news-and-economics/housing-economics/indices/housing-market-index
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import matplotlib.dates as mdates

# Set up directories
DATA_DIR = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'data')
os.makedirs(DATA_DIR, exist_ok=True)

def process_hmi_data(file_path):
    """
    Process the downloaded NAHB HMI data file with special format.
    
    Args:
        file_path (str): Path to the downloaded data file
        
    Returns:
        pandas.DataFrame: Processed HMI data
    """
    print(f"Processing NAHB HMI data from: {file_path}")
    
    try:
        # Read the Excel file
        df = pd.read_excel(file_path)
        
        # Print the first few rows to inspect the data structure
        print("\nFirst few rows of the raw data:")
        print(df.head())
        
        # Print column names to help with debugging
        print("\nColumn names in the raw data:")
        print(df.columns.tolist())
        
        # Extract the year column (first column)
        year_col = df.columns[0]
        
        # Create an empty list to store the processed data
        data_rows = []
        
        # Define month columns (they are unnamed in the original file)
        month_cols = df.columns[1:13]  # Columns 1-12 represent Jan-Dec
        
        # Define month names
        month_names = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 
                       'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']
        
        # Process each row (year)
        for _, row in df.iterrows():
            # Skip header rows and rows without a valid year
            if not isinstance(row[year_col], (int, float)) or np.isnan(row[year_col]):
                continue
            
            year = int(row[year_col])
            
            # Process each month in the row
            for i, month_col in enumerate(month_cols):
                # Skip if the value is not a number
                if not isinstance(row[month_col], (int, float)) or np.isnan(row[month_col]):
                    continue
                
                # Create a date for this year and month
                month = i + 1  # 1-based month number
                date = pd.Timestamp(year=year, month=month, day=1)
                
                # Add the data point
                data_rows.append({
                    'Date': date,
                    'NAHB_HMI': row[month_col]
                })
        
        # Create a DataFrame from the processed data
        result = pd.DataFrame(data_rows)
        
        # Set date as index
        result.set_index('Date', inplace=True)
        
        # Sort by date
        result.sort_index(inplace=True)
        
        print("\nProcessed data:")
        print(result.head())
        
        return result
    
    except Exception as e:
        print(f"Error processing data: {str(e)}")
        return None

def calculate_indicators(df):
    """
    Calculate additional indicators for the HMI data.
    
    Args:
        df (pandas.DataFrame): DataFrame containing HMI data
        
    Returns:
        pandas.DataFrame: DataFrame with additional indicators
    """
    if df is None or df.empty:
        print("No data to calculate indicators for.")
        return df
    
    # Make a copy to avoid modifying the original
    result = df.copy()
    
    # Calculate moving averages
    result['MA_3M'] = result['NAHB_HMI'].rolling(window=3).mean()
    result['MA_12M'] = result['NAHB_HMI'].rolling(window=12).mean()
    
    # Calculate rate of change (month-over-month)
    result['MoM_Change'] = result['NAHB_HMI'].diff()
    
    # Calculate rate of change (year-over-year)
    result['YoY_Change'] = result['NAHB_HMI'].diff(12)
    
    # Calculate Z-score (standardized value) using 5-year rolling window
    window = 60  # 5 years (60 months)
    result['Z_Score'] = result['NAHB_HMI'].rolling(window=window, min_periods=12).apply(
        lambda x: (x.iloc[-1] - x.mean()) / x.std() if x.std() != 0 else 0
    )
    
    # Calculate percentile rank using 5-year rolling window
    result['Percentile_Rank'] = result['NAHB_HMI'].rolling(window=window, min_periods=12).apply(
        lambda x: pd.Series(x).rank(pct=True).iloc[-1] * 100
    )
    
    # Add market condition categories
    # HMI values above 50 indicate favorable conditions
    result['Market_Condition'] = pd.cut(
        result['NAHB_HMI'],
        bins=[-float('inf'), 25, 50, 75, float('inf')],
        labels=['Poor', 'Fair', 'Good', 'Excellent']
    )
    
    return result

def save_data(df, filename='nahb_hmi_processed.csv'):
    """
    Save data to CSV file.
    
    Args:
        df (pandas.DataFrame): DataFrame to save
        filename (str): Name of the file to save to
    """
    if df is None or df.empty:
        print("No data to save.")
        return
    
    file_path = os.path.join(DATA_DIR, filename)
    df.to_csv(file_path)
    print(f"Data saved to {file_path}")

def plot_nahb_hmi(df, filename='nahb_hmi.png'):
    """
    Create a plot of the NAHB/Wells Fargo Housing Market Index data.
    
    Args:
        df (pandas.DataFrame): DataFrame containing HMI data
        filename (str): Name of the file to save the plot to
    """
    if df is None or df.empty:
        print("No data to plot.")
        return
    
    # Create figure and primary axis
    fig, ax1 = plt.subplots(figsize=(12, 8))
    
    # Plot HMI
    ax1.plot(df.index, df['NAHB_HMI'], 'b-', label='NAHB/Wells Fargo Housing Market Index')
    
    # Plot moving averages if available
    if 'MA_3M' in df.columns:
        ax1.plot(df.index, df['MA_3M'], 'r--', label='3-Month MA')
    if 'MA_12M' in df.columns:
        ax1.plot(df.index, df['MA_12M'], 'g--', label='12-Month MA')
    
    # Add horizontal line at HMI = 50 (favorable/unfavorable threshold)
    ax1.axhline(y=50, color='k', linestyle='-', alpha=0.3, label='Favorable/Unfavorable Threshold')
    
    # Set labels and title
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Index Value', color='b')
    ax1.tick_params('y', colors='b')
    ax1.set_title('NAHB/Wells Fargo Housing Market Index')
    
    # Format x-axis to show years
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y'))
    ax1.xaxis.set_major_locator(mdates.YearLocator(5))  # Show every 5 years
    
    # Add grid
    ax1.grid(True, alpha=0.3)
    
    # Add legend
    ax1.legend(loc='upper left')
    
    # Create a secondary axis for the YoY change if available
    if 'YoY_Change' in df.columns:
        ax2 = ax1.twinx()
        ax2.plot(df.index, df['YoY_Change'], 'c-', alpha=0.5, label='YoY Change')
        ax2.set_ylabel('Year-over-Year Change', color='c')
        ax2.tick_params('y', colors='c')
        
        # Add horizontal line at YoY = 0
        ax2.axhline(y=0, color='k', linestyle='-', alpha=0.2)
        
        # Add legend for secondary axis
        lines1, labels1 = ax1.get_legend_handles_labels()
        lines2, labels2 = ax2.get_legend_handles_labels()
        ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left')
    
    # Adjust layout and save
    plt.tight_layout()
    plt.savefig(os.path.join(DATA_DIR, filename))
    print(f"Plot saved to {os.path.join(DATA_DIR, filename)}")
    
    # Show plot
    plt.show()

def main():
    """Main function to run the data processing."""
    import sys
    
    # Check if a file path was provided as a command-line argument
    if len(sys.argv) > 1:
        file_path = sys.argv[1]
        
        # Check if the file exists
        if not os.path.isfile(file_path):
            print(f"Error: File not found: {file_path}")
            return
        
        # Process the data
        hmi_data = process_hmi_data(file_path)
        
        if hmi_data is not None:
            # Calculate indicators
            hmi_data_with_indicators = calculate_indicators(hmi_data)
            
            # Save data
            save_data(hmi_data_with_indicators)
            
            # Plot data
            plot_nahb_hmi(hmi_data_with_indicators)
            
            # Print recent data
            print("\nMost recent data:")
            print(hmi_data_with_indicators.tail())
            
            # Print summary statistics
            print("\nSummary statistics:")
            print(hmi_data_with_indicators['NAHB_HMI'].describe())
            
            # Print current market condition
            latest = hmi_data_with_indicators.iloc[-1]
            market_condition = latest['Market_Condition']
            
            print(f"\nCurrent housing market condition: {market_condition}")
            print(f"Current HMI value: {latest['NAHB_HMI']}")
            print(f"Month-over-month change: {latest['MoM_Change']}")
            print(f"Year-over-year change: {latest['YoY_Change']}")
            print(f"Percentile rank (5-year window): {latest['Percentile_Rank']:.1f}%")
            print(f"Z-Score (5-year window): {latest['Z_Score']:.2f}")
    else:
        # No file path provided
        print("Please provide the path to the NAHB HMI data file:")
        print("python process_nahb_hmi_special.py data/nahb_hmi_data.xls")

if __name__ == "__main__":
    main()
