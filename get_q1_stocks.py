#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
获取营收增长加速度最高组（Q1组）股票的简单脚本
"""

from revenue_acce_quintile_backtest import get_latest_q1_stocks, QuintileBacktest
import pandas as pd
from datetime import datetime

def main():
    """主函数 - 获取最新Q1组股票"""
    print("🚀 获取营收增长加速度最高组（Q1组）股票")
    print("="*60)
    
    try:
        # 方法1：使用便捷函数获取最新Q1组股票
        print("\n📊 方法1：获取最新Q1组股票")
        q1_stocks = get_latest_q1_stocks()
        
        if not q1_stocks.empty:
            print(f"\n✅ 成功获取 {len(q1_stocks)} 只Q1组股票")
            
            # 显示前10只股票的详细信息
            print("\n🏆 前10只股票详情:")
            print(f"{'排名':<4} {'股票代码':<12} {'营收增长加速度':<15} {'营收增长率':<12} {'报告日期':<12}")
            print("-" * 70)
            
            for i, (_, row) in enumerate(q1_stocks.head(10).iterrows(), 1):
                revenue_yoy = row.get('revenue_yoy', 'N/A')
                revenue_yoy_str = f"{revenue_yoy:.2f}%" if isinstance(revenue_yoy, (int, float)) else str(revenue_yoy)
                print(f"{i:<4} {row['stock_code']:<12} {row['revenue_acceleration']:<15.2f} {revenue_yoy_str:<12} {row['report_date'].strftime('%Y-%m-%d'):<12}")
        
        # 方法2：指定特定日期获取Q1组股票
        print("\n📊 方法2：获取特定日期的Q1组股票")
        target_date = "2024-12-31"  # 可以修改为你想要的日期
        print(f"目标日期: {target_date}")
        
        q1_stocks_specific = get_latest_q1_stocks(target_date=target_date)
        
        if not q1_stocks_specific.empty:
            print(f"✅ 成功获取 {target_date} 的 {len(q1_stocks_specific)} 只Q1组股票")
        
        # 方法3：使用类实例进行更灵活的操作
        print("\n📊 方法3：使用类实例进行灵活操作")
        backtest = QuintileBacktest()
        
        # 可以调整回看天数
        q1_stocks_custom = backtest.get_latest_q1_stocks(
            target_date="2024-12-31",
            lookback_days=120  # 回看120天
        )
        
        if not q1_stocks_custom.empty:
            print(f"✅ 使用120天回看期获取到 {len(q1_stocks_custom)} 只Q1组股票")
            
            # 获取统计信息
            print(f"\n📈 Q1组股票统计信息:")
            print(f"  营收增长加速度范围: {q1_stocks_custom['revenue_acceleration'].min():.2f} ~ {q1_stocks_custom['revenue_acceleration'].max():.2f}")
            print(f"  营收增长加速度均值: {q1_stocks_custom['revenue_acceleration'].mean():.2f}")
            print(f"  营收增长加速度中位数: {q1_stocks_custom['revenue_acceleration'].median():.2f}")
            
            if 'revenue_yoy' in q1_stocks_custom.columns:
                revenue_yoy_valid = q1_stocks_custom['revenue_yoy'].dropna()
                if not revenue_yoy_valid.empty:
                    print(f"  营收增长率范围: {revenue_yoy_valid.min():.2f}% ~ {revenue_yoy_valid.max():.2f}%")
                    print(f"  营收增长率均值: {revenue_yoy_valid.mean():.2f}%")
        
        print("\n" + "="*60)
        print("🎉 Q1组股票获取完成！")
        print("📁 结果已保存到 latest_q1_stocks/ 目录下")
        
        return q1_stocks
        
    except Exception as e:
        print(f"❌ 获取Q1组股票时出错: {e}")
        return pd.DataFrame()


def get_q1_stocks_summary():
    """获取Q1组股票摘要信息"""
    print("📊 获取Q1组股票摘要...")
    
    try:
        q1_stocks = get_latest_q1_stocks()
        
        if q1_stocks.empty:
            print("❌ 未获取到Q1组股票")
            return None
        
        # 创建摘要
        summary = {
            '总股票数': len(q1_stocks),
            '最高加速度': q1_stocks['revenue_acceleration'].max(),
            '最低加速度': q1_stocks['revenue_acceleration'].min(),
            '平均加速度': q1_stocks['revenue_acceleration'].mean(),
            '中位数加速度': q1_stocks['revenue_acceleration'].median(),
        }
        
        # 如果有营收增长率数据
        if 'revenue_yoy' in q1_stocks.columns:
            revenue_yoy_valid = q1_stocks['revenue_yoy'].dropna()
            if not revenue_yoy_valid.empty:
                summary.update({
                    '最高增长率': revenue_yoy_valid.max(),
                    '最低增长率': revenue_yoy_valid.min(),
                    '平均增长率': revenue_yoy_valid.mean(),
                })
        
        print("📈 Q1组股票摘要:")
        for key, value in summary.items():
            if isinstance(value, float):
                print(f"  {key}: {value:.2f}")
            else:
                print(f"  {key}: {value}")
        
        return summary
        
    except Exception as e:
        print(f"❌ 获取摘要时出错: {e}")
        return None


if __name__ == "__main__":
    # 获取Q1组股票
    q1_stocks = main()
    
    # 获取摘要信息
    print("\n" + "="*60)
    summary = get_q1_stocks_summary()
