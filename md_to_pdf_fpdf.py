#!/usr/bin/env python
# -*- coding: utf-8 -*-

from fpdf import FPDF
import re

def main():
    # 创建PDF对象
    pdf = FPDF()
    pdf.add_page()
    
    # 设置中文字体
    pdf.add_font('SimSun', '', '/System/Library/Fonts/STHeiti Light.ttc', uni=True)
    pdf.set_font('SimSun', '', 12)
    
    # 读取Markdown文件
    with open('巴菲特致股东信合集.md', 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    # 处理每一行
    for line in lines:
        # 处理标题
        if line.startswith('# '):
            pdf.add_page()
            pdf.set_font('SimSun', '', 20)
            pdf.cell(0, 10, line[2:].strip(), 0, 1, 'C')
            pdf.set_font('SimSun', '', 12)
        elif line.startswith('## '):
            pdf.add_page()
            pdf.set_font('SimSun', '', 16)
            pdf.cell(0, 10, line[3:].strip(), 0, 1, 'L')
            pdf.set_font('SimSun', '', 12)
        elif line.startswith('### '):
            pdf.set_font('SimSun', '', 14)
            pdf.cell(0, 10, line[4:].strip(), 0, 1, 'L')
            pdf.set_font('SimSun', '', 12)
        # 处理普通文本
        elif line.strip():
            pdf.multi_cell(0, 8, line.strip())
            pdf.ln(4)
    
    # 保存PDF文件
    pdf.output('巴菲特致股东信合集.pdf', 'F')
    print('PDF file created: 巴菲特致股东信合集.pdf')

if __name__ == '__main__':
    main()
