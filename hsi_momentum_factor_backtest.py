#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股动量因子回测分析
基于历史价格数据计算动量因子进行因子有效性验证
"""

import os
import json
import pickle
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from collections import defaultdict
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIMomentumFactorBacktest:
    """恒生指数动量因子回测器"""

    def __init__(self, cache_dir: str = "hsi_cache_5y", rebalance_freq: str = 'Q',
                 enable_short: bool = False, n_groups: int = 5):
        """
        初始化回测器

        Args:
            cache_dir: 价格数据缓存目录
            rebalance_freq: 再平衡频率 ('M'=月度, 'Q'=季度, 'H'=半年, 'Y'=年度)
            enable_short: 是否允许做空
            n_groups: 分组数量
        """
        self.cache_dir = cache_dir
        self.stock_data = {}
        self.momentum_data = {}
        self.backtest_results = {}
        self.hsi_constituents = []

        # 回测参数
        self.rebalance_freq = rebalance_freq
        self.enable_short = enable_short
        self.n_groups = n_groups
        self.lookback_days = 252   # 1年回看期

        # 根据再平衡频率调整前瞻期
        if rebalance_freq == 'M':
            self.forward_days = 21     # 1个月前瞻期
        elif rebalance_freq == 'Q':
            self.forward_days = 63     # 3个月前瞻期
        elif rebalance_freq == 'H':
            self.forward_days = 126    # 6个月前瞻期
        else:  # 'Y'
            self.forward_days = 252    # 1年前瞻期
        
        freq_names = {'M': '月度', 'Q': '季度', 'H': '半年度', 'Y': '年度'}
        print("🚀 恒生指数动量因子回测器已初始化")
        print(f"📁 缓存目录: {cache_dir}")
        print(f"🔄 再平衡频率: {freq_names.get(rebalance_freq, rebalance_freq)}")
        print(f"📊 分组数量: {n_groups}")
        print(f"🔄 做空策略: {'启用' if enable_short else '禁用'}")
    
    def load_hsi_constituents(self) -> bool:
        """加载恒生指数成分股列表"""
        try:
            csv_file = 'data_files/hsi_constituents.csv'
            print(f"📁 从 {csv_file} 加载恒生指数成分股...")
            
            df = pd.read_csv(csv_file, dtype={'代码': str})
            
            # 格式化股票代码
            hsi_stocks = []
            for _, row in df.iterrows():
                code = str(row['代码']).strip()
                name = str(row['名称']).strip()
                if code and code != 'nan':
                    code_formatted = str(code).zfill(5)  # 补齐到5位数
                    hsi_stocks.append({
                        'code': code_formatted,
                        'name': name
                    })
            
            self.hsi_constituents = hsi_stocks
            print(f"✅ 已加载 {len(hsi_stocks)} 只恒生指数成分股")
            return True
            
        except Exception as e:
            print(f"❌ 加载成分股列表失败: {e}")
            return False

    def load_stock_data(self) -> bool:
        """从缓存加载所有股票的价格数据"""
        try:
            print("📊 从缓存加载股票价格数据...")

            if not self.hsi_constituents:
                print("❌ 请先加载恒生指数成分股列表")
                return False

            loaded_count = 0
            for stock_info in self.hsi_constituents:
                stock_code = stock_info['code']
                stock_name = stock_info['name']
                
                # 尝试加载缓存数据
                cache_file = os.path.join(self.cache_dir, f"{stock_code}_5y.pkl")
                
                if os.path.exists(cache_file):
                    try:
                        with open(cache_file, 'rb') as f:
                            price_data = pickle.load(f)
                        
                        # 处理不同的数据格式
                        if isinstance(price_data, pd.Series) and len(price_data) > 100:
                            # 确保索引是datetime类型
                            if not isinstance(price_data.index, pd.DatetimeIndex):
                                price_data.index = pd.to_datetime(price_data.index)
                            
                            self.stock_data[stock_code] = {
                                'name': stock_name,
                                'price': price_data.dropna()
                            }
                            loaded_count += 1
                            
                    except Exception as e:
                        print(f"⚠️  加载 {stock_code} 缓存失败: {e}")
                        continue
            
            print(f"✅ 成功加载 {loaded_count} 只股票的价格数据")
            
            if loaded_count == 0:
                print("❌ 未找到有效的价格数据")
                return False
            
            return True
            
        except Exception as e:
            print(f"❌ 加载股票数据失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def calculate_momentum_factors(self) -> bool:
        """计算动量因子"""
        try:
            print("🔄 计算动量因子...")
            
            if not self.stock_data:
                print("❌ 请先加载股票数据")
                return False
            
            momentum_data = {}
            
            for stock_code, stock_info in self.stock_data.items():
                try:
                    prices = stock_info['price']
                    
                    if len(prices) < 500:  # 至少需要500个交易日的数据（约2年）
                        continue
                    
                    # 计算收益率
                    returns = prices.pct_change()
                    
                    # 1. 基础动量因子（不同周期）
                    momentum_20d = prices.pct_change(20)   # 1个月动量
                    momentum_60d = prices.pct_change(60)   # 3个月动量
                    momentum_120d = prices.pct_change(120) # 6个月动量
                    momentum_252d = prices.pct_change(252) # 12个月动量
                    
                    # 2. 跳跃动量（跳过最近1个月，避免短期反转效应）
                    skip_period = 21  # 跳过最近21个交易日
                    skip_momentum_60d = prices.pct_change(60).shift(skip_period)   # 跳跃3个月动量
                    skip_momentum_120d = prices.pct_change(120).shift(skip_period) # 跳跃6个月动量
                    skip_momentum_252d = prices.pct_change(252).shift(skip_period) # 跳跃12个月动量
                    
                    # 3. 移动平均动量
                    ma_20 = prices.rolling(window=20).mean()
                    ma_60 = prices.rolling(window=60).mean()
                    ma_momentum_20d = (prices - ma_20) / ma_20
                    ma_momentum_60d = (prices - ma_60) / ma_60
                    
                    # 4. 风险调整动量
                    volatility_60d = returns.rolling(60).std()
                    risk_adj_momentum_60d = momentum_60d / volatility_60d
                    risk_adj_momentum_120d = momentum_120d / volatility_60d
                    
                    # 5. 复合动量因子（多周期加权平均）
                    # 权重: 短期20%, 中期40%, 长期40%
                    composite_momentum = (
                        0.2 * momentum_60d +
                        0.4 * momentum_120d +
                        0.4 * momentum_252d
                    )
                    
                    # 6. 改进复合动量（跳跃版本）
                    improved_momentum = (
                        0.3 * skip_momentum_60d +
                        0.4 * skip_momentum_120d +
                        0.3 * skip_momentum_252d
                    )
                    
                    # 创建动量数据DataFrame
                    momentum_df = pd.DataFrame({
                        'momentum_20d': momentum_20d,
                        'momentum_60d': momentum_60d,
                        'momentum_120d': momentum_120d,
                        'momentum_252d': momentum_252d,
                        'skip_momentum_60d': skip_momentum_60d,
                        'skip_momentum_120d': skip_momentum_120d,
                        'skip_momentum_252d': skip_momentum_252d,
                        'ma_momentum_20d': ma_momentum_20d,
                        'ma_momentum_60d': ma_momentum_60d,
                        'risk_adj_momentum_60d': risk_adj_momentum_60d,
                        'risk_adj_momentum_120d': risk_adj_momentum_120d,
                        'composite_momentum': composite_momentum,
                        'improved_momentum': improved_momentum
                    }, index=prices.index)
                    
                    momentum_data[stock_code] = momentum_df
                    
                except Exception as e:
                    print(f"⚠️  计算 {stock_code} 动量因子失败: {e}")
                    continue
            
            self.momentum_data = momentum_data
            print(f"✅ 成功计算 {len(momentum_data)} 只股票的动量因子")
            
            if len(momentum_data) == 0:
                print("❌ 未能计算任何动量因子")
                return False
            
            return True

        except Exception as e:
            print(f"❌ 计算动量因子失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def calculate_factor_scores(self, date: datetime, factor_name: str = 'improved_momentum') -> pd.Series:
        """计算指定日期的动量因子得分"""
        scores = {}

        for stock_code in self.momentum_data.keys():
            if stock_code in self.momentum_data:
                momentum_df = self.momentum_data[stock_code]

                # 找到指定日期之前的最新数据
                available_dates = momentum_df.index[momentum_df.index <= date]
                if len(available_dates) > 0:
                    latest_date = available_dates[-1]

                    # 获取动量因子得分
                    if factor_name in momentum_df.columns:
                        momentum_score = momentum_df.loc[latest_date, factor_name]

                        # 确保动量得分有效
                        if pd.notna(momentum_score) and not np.isinf(momentum_score):
                            scores[stock_code] = momentum_score

        return pd.Series(scores)

    def create_portfolios(self, factor_scores: pd.Series) -> Dict[str, Dict]:
        """基于动量因子得分创建投资组合"""
        if len(factor_scores) == 0:
            return {}

        # 按因子得分排序（降序，高动量在前）
        sorted_scores = factor_scores.sort_values(ascending=False)

        portfolios = {}

        # 分组
        n_stocks_per_group = len(sorted_scores) // self.n_groups

        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(sorted_scores)
            else:
                end_idx = (i + 1) * n_stocks_per_group

            group_stocks = sorted_scores.iloc[start_idx:end_idx].index.tolist()

            # 为每个组合设置权重和方向
            if i == 0:  # 最高动量组合 - 做多
                portfolios[f'Group_{i+1}'] = {
                    'stocks': group_stocks,
                    'weights': {stock: 1.0/len(group_stocks) for stock in group_stocks},
                    'direction': 'long'
                }
            elif i == self.n_groups - 1 and self.enable_short:  # 最低动量组合 - 做空
                portfolios[f'Group_{i+1}'] = {
                    'stocks': group_stocks,
                    'weights': {stock: -1.0/len(group_stocks) for stock in group_stocks},
                    'direction': 'short'
                }
            else:  # 中间组合 - 纯多头
                portfolios[f'Group_{i+1}'] = {
                    'stocks': group_stocks,
                    'weights': {stock: 1.0/len(group_stocks) for stock in group_stocks},
                    'direction': 'long'
                }

        # 添加特殊组合
        total_stocks = len(sorted_scores)

        # 高动量组合（前20%）- 做多
        top_20_count = max(1, int(total_stocks * 0.2))
        top_stocks = sorted_scores.head(top_20_count).index.tolist()
        portfolios['高动量组合'] = {
            'stocks': top_stocks,
            'weights': {stock: 1.0/len(top_stocks) for stock in top_stocks},
            'direction': 'long'
        }

        # 低动量组合（后20%）- 做空（如果启用）
        bottom_20_count = max(1, int(total_stocks * 0.2))
        bottom_stocks = sorted_scores.tail(bottom_20_count).index.tolist()
        if self.enable_short:
            portfolios['低动量组合'] = {
                'stocks': bottom_stocks,
                'weights': {stock: -1.0/len(bottom_stocks) for stock in bottom_stocks},
                'direction': 'short'
            }
        else:
            portfolios['低动量组合'] = {
                'stocks': bottom_stocks,
                'weights': {stock: 1.0/len(bottom_stocks) for stock in bottom_stocks},
                'direction': 'long'
            }

        # 多空组合（高动量做多 + 低动量做空）
        if self.enable_short:
            all_weights = {}
            # 高动量做多（权重0.5）
            for stock in top_stocks:
                all_weights[stock] = 0.5 / len(top_stocks)
            # 低动量做空（权重-0.5）
            for stock in bottom_stocks:
                all_weights[stock] = -0.5 / len(bottom_stocks)

            portfolios['多空组合'] = {
                'stocks': top_stocks + bottom_stocks,
                'weights': all_weights,
                'direction': 'long_short'
            }

        # 全市场组合（所有股票等权重做多）
        all_stocks = sorted_scores.index.tolist()
        portfolios['全市场组合'] = {
            'stocks': all_stocks,
            'weights': {stock: 1.0/len(all_stocks) for stock in all_stocks},
            'direction': 'long'
        }

        return portfolios

    def calculate_stock_return(self, stock_code: str, start_date: datetime, end_date: datetime) -> float:
        """计算股票在指定期间的收益率"""
        if stock_code not in self.stock_data:
            return 0.0

        price_series = self.stock_data[stock_code]['price']

        # 获取期间数据
        period_data = price_series[(price_series.index >= start_date) & (price_series.index <= end_date)]

        if len(period_data) < 2:
            return 0.0

        # 获取起始和结束价格
        start_price = period_data.iloc[0]
        end_price = period_data.iloc[-1]

        if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
            return 0.0

        # 计算收益率
        return (end_price - start_price) / start_price

    def calculate_portfolio_returns(self, portfolios: Dict[str, Dict],
                                  start_date: datetime, end_date: datetime) -> Dict[str, float]:
        """计算投资组合在指定期间的收益率"""
        portfolio_returns = {}

        for portfolio_name, portfolio_info in portfolios.items():
            stocks = portfolio_info['stocks']
            weights = portfolio_info['weights']
            direction = portfolio_info['direction']

            if not stocks:
                continue

            portfolio_return = 0.0
            valid_stocks = 0

            for stock_code in stocks:
                if stock_code in self.stock_data and stock_code in weights:
                    # 计算股票收益率
                    stock_return = self.calculate_stock_return(
                        stock_code, start_date, end_date
                    )

                    if stock_return != 0.0:  # 有效收益率
                        weight = weights[stock_code]

                        # 根据权重计算贡献（负权重表示做空）
                        portfolio_return += weight * stock_return
                        valid_stocks += 1

            # 保存组合收益率
            if valid_stocks > 0:
                portfolio_returns[portfolio_name] = portfolio_return
            else:
                portfolio_returns[portfolio_name] = 0.0

        return portfolio_returns

    def get_rebalance_dates(self, start_date: datetime, end_date: datetime) -> List[datetime]:
        """获取再平衡日期"""
        dates = []
        current_date = start_date

        while current_date <= end_date:
            dates.append(current_date)

            # 根据再平衡频率确定下一个日期
            if self.rebalance_freq == 'M':  # 月度
                if current_date.month == 12:
                    current_date = current_date.replace(year=current_date.year + 1, month=1, day=1)
                else:
                    current_date = current_date.replace(month=current_date.month + 1, day=1)
            elif self.rebalance_freq == 'Q':  # 季度
                if current_date.month <= 3:
                    next_month = 6
                elif current_date.month <= 6:
                    next_month = 9
                elif current_date.month <= 9:
                    next_month = 12
                else:
                    next_month = 3
                    current_date = current_date.replace(year=current_date.year + 1)

                if next_month <= 12:
                    current_date = current_date.replace(month=next_month, day=1)
            elif self.rebalance_freq == 'H':  # 半年度
                if current_date.month <= 6:
                    current_date = current_date.replace(month=12, day=1)
                else:
                    current_date = current_date.replace(year=current_date.year + 1, month=6, day=1)
            else:  # 年度
                current_date = current_date.replace(year=current_date.year + 1)

        return dates

    def run_backtest(self, factor_name: str = 'improved_momentum') -> bool:
        """运行动量因子回测"""
        try:
            print(f"🔄 开始动量因子回测（因子: {factor_name}）...")

            # 确定回测期间
            all_dates = set()
            for stock_info in self.stock_data.values():
                all_dates.update(stock_info['price'].index)

            if not all_dates:
                print("❌ 没有可用的日期数据")
                return False

            start_date = min(all_dates) + timedelta(days=self.lookback_days)
            end_date = max(all_dates) - timedelta(days=self.forward_days)

            print(f"📅 回测期间: {start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}")

            # 获取再平衡日期
            rebalance_dates = self.get_rebalance_dates(start_date, end_date)
            print(f"🔄 再平衡次数: {len(rebalance_dates)}")

            # 存储所有组合的收益率
            all_portfolio_returns = defaultdict(list)
            all_factor_scores = []

            for i, rebalance_date in enumerate(rebalance_dates):
                print(f"🔄 再平衡 {i+1}/{len(rebalance_dates)}: {rebalance_date.strftime('%Y-%m-%d')}")

                # 计算因子得分
                factor_scores = self.calculate_factor_scores(rebalance_date, factor_name)
                if factor_scores.empty:
                    print(f"   ⚠️  {rebalance_date.strftime('%Y-%m-%d')} 因子得分为空，跳过")
                    continue

                all_factor_scores.append(factor_scores)

                # 创建投资组合
                portfolios = self.create_portfolios(factor_scores)

                # 确定持有期间
                if i < len(rebalance_dates) - 1:
                    hold_end = rebalance_dates[i + 1]
                else:
                    hold_end = end_date

                # 计算期间收益
                period_returns = self.calculate_portfolio_returns(
                    portfolios, rebalance_date, hold_end
                )

                # 累积收益
                for portfolio_name, returns in period_returns.items():
                    all_portfolio_returns[portfolio_name].append(returns)

                # 显示组合信息（仅第一次）
                if i == 0:
                    print("📊 投资组合构成:")
                    for portfolio_name, portfolio_info in portfolios.items():
                        if portfolio_name.startswith('Group_'):
                            stocks = portfolio_info['stocks']
                            direction = portfolio_info['direction']
                            avg_momentum = factor_scores[stocks].mean() if stocks else 0
                            direction_text = "做多" if direction == "long" else "做空" if direction == "short" else "多空"
                            print(f"   {portfolio_name}: {len(stocks)}只股票, 平均动量: {avg_momentum:.4f}, 策略: {direction_text}")

            # 保存回测结果
            self.backtest_results = {
                'portfolio_returns': dict(all_portfolio_returns),
                'factor_scores': all_factor_scores,
                'rebalance_dates': rebalance_dates,
                'start_date': start_date,
                'end_date': end_date,
                'factor_name': factor_name
            }

            print("✅ 回测完成")
            return True

        except Exception as e:
            print(f"❌ 回测失败: {e}")
            import traceback
            traceback.print_exc()
            return False

    def analyze_factor_performance(self) -> Dict:
        """分析因子表现"""
        if not self.backtest_results:
            print("❌ 请先运行回测")
            return {}

        print("📊 分析因子表现...")

        portfolio_returns = self.backtest_results['portfolio_returns']
        analysis_results = {}

        # 计算各组合的统计指标
        for portfolio_name, returns in portfolio_returns.items():
            if not returns or len(returns) == 0:
                continue

            returns_array = np.array(returns)



            # 基本统计
            mean_return = np.mean(returns_array)
            std_return = np.std(returns_array) if len(returns_array) > 1 else 0
            sharpe_ratio = mean_return / std_return if std_return > 0 else 0

            # 累积收益
            cumulative_return = np.prod(1 + returns_array) - 1

            # 年化收益率
            if self.rebalance_freq == 'M':
                periods_per_year = 12
            elif self.rebalance_freq == 'Q':
                periods_per_year = 4
            elif self.rebalance_freq == 'H':
                periods_per_year = 2
            else:  # 'Y'
                periods_per_year = 1

            annual_return = (1 + mean_return) ** periods_per_year - 1

            # 最大回撤
            if len(returns_array) > 1:
                cumulative_returns = np.cumprod(1 + returns_array)
                running_max = np.maximum.accumulate(cumulative_returns)
                drawdowns = (cumulative_returns - running_max) / running_max
                max_drawdown = np.min(drawdowns)
            else:
                max_drawdown = 0

            # 胜率
            win_rate = np.sum(returns_array > 0) / len(returns_array)

            analysis_results[portfolio_name] = {
                'mean_return': mean_return,
                'std_return': std_return,
                'sharpe_ratio': sharpe_ratio,
                'cumulative_return': cumulative_return,
                'annual_return': annual_return,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'total_periods': len(returns)
            }

        return analysis_results

    def calculate_information_coefficient(self) -> Dict:
        """计算信息系数（IC）"""
        if not self.backtest_results:
            return {}

        print("📈 计算信息系数...")

        factor_scores = self.backtest_results['factor_scores']
        ic_values = []

        for i, scores in enumerate(factor_scores):
            if i >= len(factor_scores) - 1:
                break

            # 获取下期收益率
            next_period_returns = {}
            rebalance_date = self.backtest_results['rebalance_dates'][i]
            next_date = self.backtest_results['rebalance_dates'][i + 1] if i + 1 < len(self.backtest_results['rebalance_dates']) else self.backtest_results['end_date']

            for stock_code in scores.index:
                if stock_code in self.stock_data:
                    # 计算股票收益率
                    stock_return = self.calculate_stock_return(
                        stock_code, rebalance_date, next_date
                    )

                    if stock_return != 0.0:
                        next_period_returns[stock_code] = stock_return

            # 计算IC
            if len(next_period_returns) >= 5:  # 至少需要5只股票
                factor_values = []
                return_values = []

                for stock_code in scores.index:
                    if stock_code in next_period_returns:
                        factor_values.append(scores[stock_code])
                        return_values.append(next_period_returns[stock_code])

                if len(factor_values) >= 5:
                    ic = np.corrcoef(factor_values, return_values)[0, 1]
                    if not np.isnan(ic):
                        ic_values.append(ic)

        if ic_values:
            return {
                'ic_mean': np.mean(ic_values),
                'ic_std': np.std(ic_values),
                'ic_ir': np.mean(ic_values) / np.std(ic_values) if np.std(ic_values) > 0 else 0,
                'ic_values': ic_values,
                'ic_positive_rate': np.sum(np.array(ic_values) > 0) / len(ic_values)
            }
        else:
            return {}

    def generate_report(self, analysis_results: Dict, ic_results: Dict):
        """生成分析报告"""
        print("\n" + "="*80)
        print("恒生指数成分股动量因子回测分析报告")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: {self.cache_dir}")
        print(f"分析股票数: {len(self.stock_data)}")
        strategy_type = "多空策略" if self.enable_short else "纯多头策略"
        factor_name = self.backtest_results.get('factor_name', 'improved_momentum')
        print(f"投资策略: 基于{factor_name}动量因子的{strategy_type}")
        print(f"分组数量: {self.n_groups}")
        print(f"做空策略: {'启用' if self.enable_short else '禁用'}")
        print()

        # 回测基本信息
        if self.backtest_results:
            print("回测基本信息")
            print("-"*40)
            print(f"回测期间: {self.backtest_results['start_date'].strftime('%Y-%m-%d')} 至 {self.backtest_results['end_date'].strftime('%Y-%m-%d')}")
            print(f"再平衡频率: {self.rebalance_freq}")
            print(f"再平衡次数: {len(self.backtest_results['rebalance_dates'])}")
            print()

        # 组合表现分析
        if analysis_results:
            print("投资组合表现分析")
            print("-"*90)
            print(f"{'组合名称':<15} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'最大回撤':<8} {'胜率':<8}")
            print("-"*90)

            # 按预期顺序显示组合
            portfolio_order = [f'Group_{i+1}' for i in range(self.n_groups)]
            portfolio_order.extend(['高动量组合', '低动量组合', '全市场组合'])
            if self.enable_short:
                portfolio_order.append('多空组合')

            for portfolio_name in portfolio_order:
                if portfolio_name in analysis_results:
                    perf = analysis_results[portfolio_name]
                    print(f"{portfolio_name:<15} {perf['annual_return']*100:<10.2f}% "
                          f"{perf['std_return']*100:<8.2f}% {perf['sharpe_ratio']:<8.2f} "
                          f"{abs(perf['max_drawdown'])*100:<8.2f}% {perf['win_rate']*100:<8.1f}%")
            print()

        # 动量因子有效性分析
        print("动量因子有效性分析")
        print("-"*40)

        if '高动量组合' in analysis_results and '低动量组合' in analysis_results:
            high_momentum = analysis_results['高动量组合']
            low_momentum = analysis_results['低动量组合']

            print(f"高动量组合年化收益: {high_momentum['annual_return']*100:.2f}%")
            print(f"低动量组合年化收益: {low_momentum['annual_return']*100:.2f}%")

            if self.enable_short:
                # 做空策略下，低动量组合收益为负表示做空盈利
                long_short_return = high_momentum['annual_return'] - low_momentum['annual_return']
                print(f"多空收益差: {long_short_return*100:.2f}%")

                if '多空组合' in analysis_results:
                    multi_short = analysis_results['多空组合']
                    print(f"多空组合年化收益: {multi_short['annual_return']*100:.2f}%")
                    print(f"多空组合夏普比率: {multi_short['sharpe_ratio']:.3f}")

                if high_momentum['annual_return'] > 0 and low_momentum['annual_return'] < 0:
                    print("✅ 动量因子表现优异：高动量做多盈利，低动量做空盈利")
                elif high_momentum['annual_return'] > low_momentum['annual_return']:
                    print("✅ 动量因子表现正向：高动量股票收益更高")
                else:
                    print("❌ 动量因子表现负向：策略需要调整")
            else:
                # 纯多头策略
                print(f"收益差: {(high_momentum['annual_return'] - low_momentum['annual_return'])*100:.2f}%")
                if high_momentum['annual_return'] > low_momentum['annual_return']:
                    print("✅ 动量因子表现正向：高动量股票收益更高")
                else:
                    print("❌ 动量因子表现负向：低动量股票收益更高")
            print()

        # IC分析
        if ic_results:
            print("信息系数（IC）分析")
            print("-"*40)
            print(f"平均IC: {ic_results['ic_mean']:.4f}")
            print(f"IC标准差: {ic_results['ic_std']:.4f}")
            print(f"IC信息比率: {ic_results['ic_ir']:.4f}")
            print(f"IC正值比例: {ic_results['ic_positive_rate']*100:.1f}%")

            if ic_results['ic_mean'] > 0.02:
                print("✅ IC表现良好：因子预测能力较强")
            elif ic_results['ic_mean'] > 0:
                print("⚠️ IC表现一般：因子预测能力有限")
            else:
                print("❌ IC表现较差：因子预测能力不足")
            print()

    def run_validation(self, factor_name: str = 'improved_momentum') -> bool:
        """运行完整的因子验证流程"""
        try:
            # 1. 加载成分股列表
            if not self.load_hsi_constituents():
                return False

            # 2. 加载数据
            if not self.load_stock_data():
                return False

            # 3. 计算动量因子
            if not self.calculate_momentum_factors():
                return False

            # 4. 运行回测
            if not self.run_backtest(factor_name):
                return False

            # 5. 分析表现
            analysis_results = self.analyze_factor_performance()
            if not analysis_results:
                print("❌ 分析结果为空")
                return False

            # 6. 计算IC
            ic_results = self.calculate_information_coefficient()

            # 7. 生成报告
            self.generate_report(analysis_results, ic_results)

            return True

        except Exception as e:
            print(f"❌ 验证过程失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎯 恒生指数成分股动量因子回测分析")
    print("=" * 80)
    print("📊 基于缓存价格数据进行动量因子有效性验证")
    print("🔬 使用多种动量因子计算方法")
    print("📈 分成5组进行详细分层分析（纯多头策略）")
    print()

    # 创建回测器（禁用做空策略，分成5组）
    backtest = HSIMomentumFactorBacktest(
        cache_dir="hsi_cache_5y",
        rebalance_freq="M",  # 月度再平衡
        enable_short=False,  # 禁用做空策略
        n_groups=5          # 分成5组
    )

    start_time = datetime.now()

    try:
        # 运行完整的因子验证流程
        success = backtest.run_validation('momentum_60d')

        if success:
            end_time = datetime.now()
            duration = end_time - start_time

            print(f"\n🎉 动量因子回测分析完成！")
            print(f"⏱️  总耗时: {duration}")
            print(f"\n💡 分析要点:")
            print(f"   ✅ 使用了恒生指数成分股缓存价格数据")
            print(f"   ✅ 基于改进动量因子（跳跃动量）")
            print(f"   ✅ 避免了短期反转效应")
            print(f"   ✅ 分成5组进行详细分层分析")
            print(f"   ✅ 计算了信息系数(IC)和风险调整收益")
            print(f"   ✅ 提供了完整的因子有效性验证")
        else:
            print("\n💥 回测过程中遇到问题，请检查错误信息。")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断分析")
    except Exception as e:
        print(f"\n❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
