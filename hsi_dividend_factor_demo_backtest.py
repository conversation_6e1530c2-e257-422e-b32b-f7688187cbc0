#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子回测演示

演示正确的回测思路：
1. 每个再平衡日，用当时的股价和过去12个月分红计算股息率
2. 按股息率分组构建投资组合
3. 持有到下个再平衡日，计算收益率
4. 重复上述过程

使用模拟数据演示完整流程
"""

import csv
import json
import math
import random
from datetime import datetime, timedelta
from collections import defaultdict

class HSIDividendFactorDemoBacktest:
    """股息率因子回测演示器"""
    
    def __init__(self):
        self.start_date = datetime(2022, 1, 1)
        self.end_date = datetime(2024, 12, 31)
        self.n_groups = 5
        
        # 数据存储
        self.stocks = []  # 股票基本信息
        self.dividend_schedule = {}  # 分红时间表 {symbol: [(date, amount), ...]}
        self.price_data = {}  # 价格数据 {symbol: {date: price}}
        
        # 回测结果
        self.rebalance_dates = []
        self.portfolio_returns = defaultdict(list)
        
        print("🚀 股息率因子回测演示器已初始化")
        print("📅 演示期间: 2022-01-01 至 2024-12-31")
    
    def load_stock_data(self):
        """加载股票数据并生成模拟的历史分红和价格"""
        print("📁 加载股票数据...")
        
        # 从CSV文件加载股票基本信息
        with open("hsi_dividend_quick_20250603_141448.csv", 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                if row['状态'] == 'success':
                    symbol = row['股票代码']
                    name = row['股票名称']
                    latest_dividend = float(row['最新分红(港元)'])
                    
                    self.stocks.append({
                        'symbol': symbol,
                        'name': name,
                        'latest_dividend': latest_dividend
                    })
        
        print(f"✅ 加载了 {len(self.stocks)} 只股票")
        
        # 生成模拟的历史分红时间表
        self.generate_dividend_schedule()
        
        # 生成模拟的价格数据
        self.generate_price_data()
    
    def generate_dividend_schedule(self):
        """生成模拟的分红时间表"""
        print("💰 生成模拟分红时间表...")
        
        for stock in self.stocks:
            symbol = stock['symbol']
            latest_dividend = stock['latest_dividend']
            
            if latest_dividend > 0:
                # 生成历史分红记录（假设每年分红一次，在5月）
                dividend_history = []
                
                for year in range(2020, 2025):
                    # 模拟分红金额的历史变化
                    if year == 2024:
                        amount = latest_dividend
                    else:
                        # 历史分红有一定波动
                        growth_rate = random.uniform(-0.1, 0.2)  # -10%到+20%的年度变化
                        amount = latest_dividend * (1 + growth_rate * (2024 - year))
                        amount = max(0.01, amount)  # 最小0.01港元
                    
                    # 分红日期通常在5月中旬
                    dividend_date = datetime(year, 5, 15)
                    dividend_history.append((dividend_date, amount))
                
                self.dividend_schedule[symbol] = dividend_history
            else:
                self.dividend_schedule[symbol] = []
        
        total_dividends = sum(len(divs) for divs in self.dividend_schedule.values())
        print(f"✅ 生成了 {total_dividends} 条分红记录")
    
    def generate_price_data(self):
        """生成模拟的价格数据"""
        print("📈 生成模拟价格数据...")
        
        for stock in self.stocks:
            symbol = stock['symbol']
            
            # 基于股票代码生成稳定的随机种子
            random.seed(hash(symbol) % 1000000)
            
            # 生成基础价格（基于最新分红推算合理价格）
            latest_dividend = stock['latest_dividend']
            if latest_dividend > 0:
                # 假设合理的股息率在1-5%之间
                reasonable_yield = random.uniform(0.01, 0.05)
                base_price = latest_dividend / reasonable_yield
            else:
                base_price = random.uniform(50, 300)
            
            # 生成日度价格序列
            price_series = {}
            current_date = self.start_date
            current_price = base_price
            
            while current_date <= self.end_date:
                # 模拟价格波动
                daily_return = random.normalvariate(0.0003, 0.018)  # 年化收益约8%，波动率约28%
                current_price *= (1 + daily_return)
                current_price = max(current_price, base_price * 0.3)  # 防止价格过低
                
                price_series[current_date] = current_price
                current_date += timedelta(days=1)
            
            self.price_data[symbol] = price_series
        
        print(f"✅ 生成了 {len(self.price_data)} 只股票的价格数据")
    
    def calculate_dividend_yield(self, symbol: str, calc_date: datetime) -> float:
        """计算指定日期的股息率（过去12个月分红/当前股价）"""
        try:
            # 获取过去12个月的分红
            start_period = calc_date - timedelta(days=365)
            
            total_dividend = 0.0
            if symbol in self.dividend_schedule:
                for div_date, amount in self.dividend_schedule[symbol]:
                    if start_period <= div_date <= calc_date:
                        total_dividend += amount
            
            # 获取当前股价
            if symbol not in self.price_data:
                return 0.0
            
            # 找到最接近的价格
            current_price = None
            for date in sorted(self.price_data[symbol].keys()):
                if date <= calc_date:
                    current_price = self.price_data[symbol][date]
                else:
                    break
            
            if current_price is None or current_price <= 0:
                return 0.0
            
            # 计算股息率（百分比）
            dividend_yield = (total_dividend / current_price) * 100
            return dividend_yield
            
        except Exception as e:
            return 0.0
    
    def generate_rebalance_dates(self) -> list:
        """生成季度再平衡日期"""
        dates = []
        current_date = self.start_date
        
        while current_date <= self.end_date:
            dates.append(current_date)
            # 下一个季度
            if current_date.month <= 3:
                next_date = current_date.replace(month=4, day=1)
            elif current_date.month <= 6:
                next_date = current_date.replace(month=7, day=1)
            elif current_date.month <= 9:
                next_date = current_date.replace(month=10, day=1)
            else:
                next_date = current_date.replace(year=current_date.year+1, month=1, day=1)
            current_date = next_date
        
        return dates
    
    def create_portfolios_by_dividend_yield(self, calc_date: datetime) -> dict:
        """基于股息率创建投资组合"""
        # 计算所有股票的股息率
        stock_yields = []
        for stock in self.stocks:
            symbol = stock['symbol']
            dividend_yield = self.calculate_dividend_yield(symbol, calc_date)
            stock_yields.append((symbol, dividend_yield, stock['name']))
        
        # 按股息率排序（降序）
        stock_yields.sort(key=lambda x: x[1], reverse=True)
        
        # 分组
        n_stocks_per_group = len(stock_yields) // self.n_groups
        portfolios = {}
        
        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余股票
                end_idx = len(stock_yields)
            else:
                end_idx = (i + 1) * n_stocks_per_group
            
            group_stocks = stock_yields[start_idx:end_idx]
            portfolios[f'Group_{i+1}'] = group_stocks
        
        return portfolios
    
    def calculate_portfolio_return(self, portfolio: list, start_date: datetime, end_date: datetime) -> float:
        """计算投资组合在持有期间的收益率"""
        if not portfolio:
            return 0.0
        
        stock_returns = []
        
        for symbol, yield_rate, name in portfolio:
            if symbol in self.price_data:
                # 获取开始和结束价格
                start_price = self.price_data[symbol].get(start_date)
                end_price = self.price_data[symbol].get(end_date)
                
                if start_price and end_price and start_price > 0:
                    stock_return = (end_price - start_price) / start_price
                    stock_returns.append(stock_return)
        
        if stock_returns:
            # 等权重组合收益率
            return sum(stock_returns) / len(stock_returns)
        else:
            return 0.0
    
    def run_backtest(self):
        """运行完整的回测"""
        print("\n🚀 开始股息率因子回测...")
        
        # 生成再平衡日期
        self.rebalance_dates = self.generate_rebalance_dates()
        print(f"🔄 生成 {len(self.rebalance_dates)} 个再平衡日期")
        
        # 执行回测
        for i, rebalance_date in enumerate(self.rebalance_dates[:-1]):
            print(f"🔄 再平衡 {i+1}/{len(self.rebalance_dates)-1}: {rebalance_date.strftime('%Y-%m-%d')}")
            
            # 基于当时的股息率创建投资组合
            portfolios = self.create_portfolios_by_dividend_yield(rebalance_date)
            
            # 计算持有期间表现
            next_rebalance = self.rebalance_dates[i+1]
            
            for group_name, portfolio in portfolios.items():
                portfolio_return = self.calculate_portfolio_return(portfolio, rebalance_date, next_rebalance)
                self.portfolio_returns[group_name].append(portfolio_return)
                
                # 显示组合信息
                if i == 0:  # 只在第一次显示组合构成
                    avg_yield = sum(s[1] for s in portfolio) / len(portfolio) if portfolio else 0
                    print(f"   {group_name}: {len(portfolio)}只股票, 平均股息率: {avg_yield:.2f}%")
        
        print("✅ 回测完成!")
    
    def analyze_results(self):
        """分析回测结果"""
        print("\n📊 回测结果分析")
        print("=" * 80)
        
        # 计算各组合的绩效指标
        results = {}
        
        for group_name, returns in self.portfolio_returns.items():
            if returns:
                # 基本统计
                avg_return = sum(returns) / len(returns)
                
                # 累积收益
                cumulative_return = 1.0
                for r in returns:
                    cumulative_return *= (1 + r)
                cumulative_return -= 1
                
                # 年化收益（假设季度再平衡）
                periods_per_year = 4
                total_periods = len(returns)
                annual_return = (1 + cumulative_return) ** (periods_per_year / total_periods) - 1
                
                # 波动率
                if len(returns) > 1:
                    variance = sum((r - avg_return) ** 2 for r in returns) / (len(returns) - 1)
                    volatility = math.sqrt(variance) * math.sqrt(periods_per_year)
                else:
                    volatility = 0
                
                # 夏普比率（假设无风险利率为3%）
                risk_free_rate = 0.03
                sharpe_ratio = (annual_return - risk_free_rate) / volatility if volatility > 0 else 0
                
                # 胜率
                win_rate = sum(1 for r in returns if r > 0) / len(returns)
                
                results[group_name] = {
                    'periods': len(returns),
                    'avg_return': avg_return,
                    'cumulative_return': cumulative_return,
                    'annual_return': annual_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'win_rate': win_rate
                }
        
        # 显示结果表格
        print(f"{'组合':<10} {'期数':<6} {'平均收益':<10} {'累积收益':<10} {'年化收益':<10} {'波动率':<8} {'夏普比率':<8} {'胜率':<8}")
        print("-" * 80)
        
        for group_name in sorted(results.keys()):
            r = results[group_name]
            print(f"{group_name:<10} {r['periods']:<6} {r['avg_return']*100:<10.2f}% "
                  f"{r['cumulative_return']*100:<10.1f}% {r['annual_return']*100:<10.1f}% "
                  f"{r['volatility']*100:<8.1f}% {r['sharpe_ratio']:<8.2f} {r['win_rate']*100:<8.1f}%")
        
        # 因子有效性分析
        if 'Group_1' in results and 'Group_5' in results:
            high_group = results['Group_1']
            low_group = results['Group_5']
            
            return_spread = high_group['annual_return'] - low_group['annual_return']
            
            print(f"\n📈 因子有效性分析:")
            print(f"高股息率组合年化收益: {high_group['annual_return']*100:.2f}%")
            print(f"低股息率组合年化收益: {low_group['annual_return']*100:.2f}%")
            print(f"多空收益差: {return_spread*100:.2f}%")
            
            if return_spread > 0:
                print("✅ 股息率因子表现正向：高股息率股票表现更好")
            else:
                print("❌ 股息率因子表现负向：低股息率股票表现更好")
            
            # 信息比率
            spread_returns = [h - l for h, l in zip(self.portfolio_returns['Group_1'], self.portfolio_returns['Group_5'])]
            if len(spread_returns) > 1:
                spread_volatility = math.sqrt(sum((r - return_spread/4) ** 2 for r in spread_returns) / (len(spread_returns) - 1)) * 2
                information_ratio = return_spread / spread_volatility if spread_volatility > 0 else 0
                print(f"信息比率: {information_ratio:.2f}")
        
        return results
    
    def save_results(self, results: dict):
        """保存回测结果"""
        try:
            # 保存到JSON
            output_data = {
                'backtest_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'backtest_period': f"{self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}",
                'methodology': 'Proper dividend yield factor backtest with dynamic rebalancing',
                'rebalance_frequency': 'Quarterly',
                'number_of_groups': self.n_groups,
                'results': results
            }
            
            with open('hsi_dividend_factor_proper_backtest_results.json', 'w', encoding='utf-8') as f:
                json.dump(output_data, f, ensure_ascii=False, indent=2)
            
            print(f"\n💾 回测结果已保存到: hsi_dividend_factor_proper_backtest_results.json")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")


def main():
    """主函数"""
    print("🎯 恒生指数成分股股息率因子回测演示")
    print("📚 演示正确的因子回测方法论")
    print("=" * 60)
    
    # 创建回测器
    backtest = HSIDividendFactorDemoBacktest()
    
    try:
        # 1. 加载数据
        backtest.load_stock_data()
        
        # 2. 运行回测
        backtest.run_backtest()
        
        # 3. 分析结果
        results = backtest.analyze_results()
        
        # 4. 保存结果
        backtest.save_results(results)
        
        print(f"\n🎉 股息率因子回测演示完成！")
        print(f"📋 这个演示展示了正确的因子回测方法论：")
        print(f"   ✅ 动态计算股息率（过去12个月分红/当前股价）")
        print(f"   ✅ 定期再平衡（季度）")
        print(f"   ✅ 避免前瞻性偏差")
        print(f"   ✅ 计算风险调整收益指标")
        
    except Exception as e:
        print(f"❌ 回测过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
