#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股分红数据收集器

功能：
1. 从东方财富网和akshare获取港股分红数据
2. 将分红数据存储到SQLite数据库中
3. 不计算股息率，只存储原始分红数据
4. 支持批量处理和增量更新

作者：AI Assistant
创建时间：2025年
"""

import sqlite3
import pandas as pd
import urllib.request
import urllib.parse
import json
import re
import time
import logging
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import threading
import os
import warnings
warnings.filterwarnings('ignore')

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hk_dividend_collector.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HKDividendDataCollector:
    """港股分红数据收集器"""
    
    def __init__(self, db_name: str = "hk_dividend_data.db"):
        """
        初始化分红数据收集器
        
        Args:
            db_name: 数据库文件名
        """
        self.db_name = db_name
        self.db_lock = threading.Lock()
        
        # 东方财富网API配置
        self.eastmoney_base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
        self.headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
            'Referer': 'https://datacenter.eastmoney.com/',
            'Accept': 'application/json, text/plain, */*',
        }
        
        # 初始化数据库
        self.init_database()
        
        logger.info("港股分红数据收集器初始化完成")
    
    def init_database(self):
        """初始化数据库表结构"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()
            
            # 创建股票基本信息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS stock_info (
                    stock_code TEXT PRIMARY KEY,
                    stock_name TEXT NOT NULL,
                    industry TEXT,
                    market TEXT DEFAULT 'HK',
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            ''')
            
            # 创建分红数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS dividend_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT,
                    announcement_date TEXT,      -- 公告日期
                    ex_dividend_date TEXT,       -- 除权除息日
                    record_date TEXT,            -- 股权登记日
                    payment_date TEXT,           -- 派息日期
                    dividend_amount REAL,        -- 每股分红金额(港元)
                    dividend_type TEXT,          -- 分红类型(现金/股票)
                    plan_description TEXT,       -- 分红方案描述
                    year INTEGER,                -- 分红年份
                    currency TEXT DEFAULT 'HKD', -- 货币单位
                    data_source TEXT,            -- 数据来源(eastmoney/akshare)
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, ex_dividend_date, dividend_amount),
                    FOREIGN KEY (stock_code) REFERENCES stock_info (stock_code)
                )
            ''')
            
            # 创建数据收集日志表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS collection_log (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    data_source TEXT NOT NULL,
                    status TEXT NOT NULL,        -- 'success' or 'failed'
                    records_count INTEGER DEFAULT 0,
                    error_message TEXT,
                    collection_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (stock_code) REFERENCES stock_info (stock_code)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_dividend_stock_date ON dividend_data(stock_code, ex_dividend_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_dividend_date ON dividend_data(ex_dividend_date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_collection_log_stock ON collection_log(stock_code, data_source)')
            
            conn.commit()
            conn.close()
            logger.info(f"数据库 {self.db_name} 初始化完成")
            
        except Exception as e:
            logger.error(f"数据库初始化失败: {e}")
            raise
    
    def save_stock_info(self, stock_code: str, stock_name: str, industry: str = None):
        """保存股票基本信息"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT OR REPLACE INTO stock_info 
                    (stock_code, stock_name, industry, updated_at)
                    VALUES (?, ?, ?, ?)
                ''', (stock_code, stock_name, industry, datetime.now().isoformat()))
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            logger.error(f"保存股票信息失败 {stock_code}: {e}")
    
    def save_dividend_data(self, dividend_records: List[Dict]):
        """保存分红数据到数据库"""
        if not dividend_records:
            return
        
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                saved_count = 0
                for record in dividend_records:
                    try:
                        cursor.execute('''
                            INSERT OR REPLACE INTO dividend_data 
                            (stock_code, stock_name, announcement_date, ex_dividend_date, 
                             record_date, payment_date, dividend_amount, dividend_type,
                             plan_description, year, currency, data_source)
                            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                        ''', (
                            record.get('stock_code'),
                            record.get('stock_name'),
                            record.get('announcement_date'),
                            record.get('ex_dividend_date'),
                            record.get('record_date'),
                            record.get('payment_date'),
                            record.get('dividend_amount'),
                            record.get('dividend_type', '现金分红'),
                            record.get('plan_description'),
                            record.get('year'),
                            record.get('currency', 'HKD'),
                            record.get('data_source')
                        ))
                        saved_count += 1
                    except sqlite3.IntegrityError:
                        # 重复数据，跳过
                        continue
                    except Exception as e:
                        logger.warning(f"保存单条分红记录失败: {e}")
                        continue
                
                conn.commit()
                conn.close()
                logger.info(f"成功保存 {saved_count} 条分红记录")
                return saved_count
                
        except Exception as e:
            logger.error(f"保存分红数据失败: {e}")
            return 0
    
    def log_collection_status(self, stock_code: str, data_source: str, status: str, 
                            records_count: int = 0, error_message: str = None):
        """记录数据收集状态"""
        try:
            with self.db_lock:
                conn = sqlite3.connect(self.db_name)
                cursor = conn.cursor()
                
                cursor.execute('''
                    INSERT INTO collection_log
                    (stock_code, data_source, status, records_count, error_message)
                    VALUES (?, ?, ?, ?, ?)
                ''', (stock_code, data_source, status, records_count, error_message))
                
                conn.commit()
                conn.close()
                
        except Exception as e:
            logger.error(f"记录收集日志失败: {e}")

    def get_dividend_data_from_eastmoney(self, stock_code: str, stock_name: str) -> List[Dict]:
        """从东方财富网获取分红数据"""
        try:
            logger.info(f"从东方财富网获取 {stock_code} ({stock_name}) 分红数据")

            # 构建API参数（与hsi_dividend_quick_scraper.py保持一致）
            params = {
                'reportName': 'RPT_HKF10_MAIN_DIVBASIC',
                'columns': 'SECURITY_CODE,UPDATE_DATE,REPORT_TYPE,EX_DIVIDEND_DATE,DIVIDEND_DATE,TRANSFER_END_DATE,YEAR,PLAN_EXPLAIN,IS_BFP',
                'quoteColumns': '',
                'filter': f'(SECURITY_CODE="{stock_code}")',
                'pageNumber': 1,
                'pageSize': 50,
                'sortTypes': '-1,-1',
                'sortColumns': 'NOTICE_DATE,EX_DIVIDEND_DATE',
                'source': 'F10',
                'client': 'PC',
                'v': str(int(time.time() * 1000))
            }

            # 构建完整URL
            query_string = urllib.parse.urlencode(params)
            full_url = f"{self.eastmoney_base_url}?{query_string}"

            # 创建请求
            req = urllib.request.Request(full_url, headers=self.headers)

            # 发送请求
            with urllib.request.urlopen(req, timeout=30) as response:
                if response.status != 200:
                    logger.warning(f"东方财富网请求失败: {stock_code}, 状态码: {response.status}")
                    return []

                content = response.read().decode('utf-8')

                try:
                    data = json.loads(content)
                except json.JSONDecodeError as e:
                    logger.warning(f"东方财富网响应解析失败: {stock_code}, 错误: {e}")
                    return []

                # 检查响应结构（与原始实现保持一致）
                if 'result' in data and 'data' in data['result']:
                    raw_records = data['result']['data']
                    logger.info(f"东方财富网获取到 {stock_code} 的 {len(raw_records)} 条原始记录")

                    # 处理分红数据
                    dividend_records = []
                    for record in raw_records:
                        try:
                            plan_explain = record.get('PLAN_EXPLAIN', '')
                            amount = self._parse_dividend_amount(plan_explain)

                            # 构建分红记录
                            dividend_record = {
                                'stock_code': stock_code,
                                'stock_name': stock_name,
                                'announcement_date': None,  # 原始实现中没有使用NOTICE_DATE
                                'ex_dividend_date': self._format_date(record.get('EX_DIVIDEND_DATE')),
                                'record_date': None,  # 原始实现中没有使用RECORD_DATE
                                'payment_date': self._format_date(record.get('DIVIDEND_DATE')),
                                'dividend_amount': amount if amount and amount > 0 else None,
                                'dividend_type': '现金分红' if amount and amount > 0 else None,
                                'plan_description': plan_explain,
                                'year': record.get('YEAR'),
                                'data_source': 'eastmoney'
                            }

                            # 只保存有分红金额的记录
                            if amount and amount > 0:
                                dividend_records.append(dividend_record)

                        except Exception as e:
                            logger.warning(f"处理东方财富网分红记录失败 {stock_code}: {e}")
                            continue

                    logger.info(f"成功处理 {stock_code} 的 {len(dividend_records)} 条有效分红记录")
                    return dividend_records
                else:
                    logger.info(f"东方财富网无分红数据: {stock_code}")
                    return []

        except Exception as e:
            logger.error(f"从东方财富网获取 {stock_code} 分红数据失败: {e}")
            return []

    def _parse_dividend_amount(self, plan_description: str) -> Optional[float]:
        """从分红方案描述中解析分红金额（基于hsi_dividend_quick_scraper.py的实现）"""
        if not plan_description or plan_description == "未派发或宣派股息":
            return None

        plan_text = str(plan_description)

        # 港币金额模式
        hkd_patterns = [
            r'港币(\d+\.?\d*)元',
            r'相当于港币(\d+\.?\d+)元',
            r'港币(\d+\.?\d+)',
            r'每股派港币(\d+\.?\d*)元',
            r'派港币(\d+\.?\d*)元',
            r'派息港币(\d+\.?\d*)元',
        ]

        for pattern in hkd_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    return float(match.group(1))
                except:
                    continue

        # 其他格式
        other_patterns = [
            r'每股派(\d+\.?\d*)港币',
            r'派(\d+\.?\d*)港币',
            r'每股(\d+\.?\d*)港币',
            r'每股派(\d+\.?\d*)元',
            r'派(\d+\.?\d*)元',
            r'每股(\d+\.?\d*)仙',
            r'派(\d+\.?\d*)仙',
            r'(\d+\.?\d*)港币',
        ]

        for pattern in other_patterns:
            match = re.search(pattern, plan_text)
            if match:
                try:
                    amount = float(match.group(1))
                    if '仙' in pattern:
                        amount = amount / 100
                    return amount
                except:
                    continue

        # 括号中的港币等值
        bracket_pattern = r'\(相当于港币(\d+\.?\d+)元'
        match = re.search(bracket_pattern, plan_text)
        if match:
            try:
                return float(match.group(1))
            except:
                pass

        return None

    def _format_date(self, date_value) -> Optional[str]:
        """格式化日期为YYYY-MM-DD格式"""
        if not date_value or pd.isna(date_value):
            return None

        try:
            if isinstance(date_value, str):
                # 尝试解析不同的日期格式
                date_value = date_value.strip()
                if not date_value or date_value == 'None':
                    return None

                # 处理常见的日期格式
                for fmt in ['%Y-%m-%d', '%Y/%m/%d', '%Y%m%d', '%d/%m/%Y', '%d-%m-%Y']:
                    try:
                        parsed_date = datetime.strptime(date_value, fmt)
                        return parsed_date.strftime('%Y-%m-%d')
                    except ValueError:
                        continue

                # 尝试pandas的日期解析
                parsed_date = pd.to_datetime(date_value)
                return parsed_date.strftime('%Y-%m-%d')

            elif isinstance(date_value, (datetime, pd.Timestamp)):
                return date_value.strftime('%Y-%m-%d')

            else:
                # 尝试转换为pandas时间戳
                parsed_date = pd.to_datetime(date_value)
                return parsed_date.strftime('%Y-%m-%d')

        except Exception:
            return None

    def _safe_float(self, value) -> Optional[float]:
        """安全转换为浮点数"""
        if value is None or pd.isna(value):
            return None

        try:
            if isinstance(value, str):
                # 清理字符串
                value = value.strip().replace(',', '').replace(' ', '')
                if not value or value.lower() in ['none', 'nan', '--', '-']:
                    return None

            return float(value)
        except (ValueError, TypeError):
            return None

    def _parse_dividend_amount(self, plan_description: str) -> Optional[float]:
        """从分红方案描述中解析分红金额"""
        if not plan_description:
            return None

        try:
            import re

            # 常见的分红描述模式
            patterns = [
                r'每股派息?(\d+\.?\d*)港?元',
                r'每股分红(\d+\.?\d*)港?元',
                r'派息(\d+\.?\d*)港?元',
                r'现金分红(\d+\.?\d*)港?元',
                r'每股(\d+\.?\d*)港?元',
                r'(\d+\.?\d*)港?元/股',
                r'每10股派息(\d+\.?\d*)港?元',  # 需要除以10
            ]

            for pattern in patterns:
                match = re.search(pattern, plan_description)
                if match:
                    amount = float(match.group(1))

                    # 如果是每10股的描述，需要除以10
                    if '每10股' in pattern:
                        amount = amount / 10

                    return amount

            return None

        except Exception:
            return None

    def collect_single_stock_dividend(self, stock_code: str, stock_name: str) -> Dict:
        """收集单只股票的分红数据"""
        logger.info(f"开始收集 {stock_code} ({stock_name}) 的分红数据")

        result = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'total_records': 0,
            'eastmoney_records': 0,
            'success': False,
            'error_message': None
        }

        try:
            # 保存股票基本信息
            self.save_stock_info(stock_code, stock_name)

            all_dividend_records = []

            # 从东方财富网获取数据
            try:
                eastmoney_records = self.get_dividend_data_from_eastmoney(stock_code, stock_name)
                if eastmoney_records:
                    all_dividend_records.extend(eastmoney_records)
                    result['eastmoney_records'] = len(eastmoney_records)
                    self.log_collection_status(stock_code, 'eastmoney', 'success', len(eastmoney_records))
                else:
                    self.log_collection_status(stock_code, 'eastmoney', 'no_data', 0)

            except Exception as e:
                error_msg = f"东方财富网获取失败: {e}"
                logger.warning(f"{stock_code} {error_msg}")
                self.log_collection_status(stock_code, 'eastmoney', 'failed', 0, error_msg)

            # 保存分红数据
            if all_dividend_records:
                saved_count = self.save_dividend_data(all_dividend_records)
                result['total_records'] = saved_count
                result['success'] = True
                logger.info(f"✅ {stock_code} 成功收集 {saved_count} 条分红记录")
            else:
                logger.info(f"⚠️  {stock_code} 未找到分红数据")
                result['error_message'] = "未找到分红数据"

            return result

        except Exception as e:
            error_msg = f"收集过程异常: {e}"
            logger.error(f"❌ {stock_code} {error_msg}")
            result['error_message'] = error_msg
            return result

    def collect_batch_dividend_data(self, stock_list: List[Dict], delay: float = 1.0) -> Dict:
        """批量收集分红数据"""
        logger.info(f"开始批量收集 {len(stock_list)} 只股票的分红数据")

        results = {
            'total_stocks': len(stock_list),
            'success_count': 0,
            'failed_count': 0,
            'total_records': 0,
            'details': []
        }

        start_time = time.time()

        for i, stock in enumerate(stock_list, 1):
            stock_code = stock.get('code') or stock.get('stock_code')
            stock_name = stock.get('name') or stock.get('stock_name')

            if not stock_code or not stock_name:
                logger.warning(f"跳过无效股票信息: {stock}")
                continue

            # 确保股票代码格式正确
            stock_code = str(stock_code).zfill(5)

            print(f"\n[{i}/{len(stock_list)}] 处理 {stock_code} ({stock_name})")

            # 收集单只股票数据
            result = self.collect_single_stock_dividend(stock_code, stock_name)
            results['details'].append(result)

            if result['success']:
                results['success_count'] += 1
                results['total_records'] += result['total_records']
                print(f"✅ 成功: {result['total_records']} 条记录")
            else:
                results['failed_count'] += 1
                print(f"❌ 失败: {result.get('error_message', '未知错误')}")

            # 显示进度
            if i % 10 == 0:
                elapsed = time.time() - start_time
                avg_time = elapsed / i
                remaining = (len(stock_list) - i) * avg_time
                print(f"\n📊 进度: {i}/{len(stock_list)} ({i/len(stock_list)*100:.1f}%)")
                print(f"   成功: {results['success_count']}, 失败: {results['failed_count']}")
                print(f"   总记录: {results['total_records']}")
                print(f"   预计剩余时间: {remaining/60:.1f}分钟\n")

            # 添加延时避免API限制
            if delay > 0:
                time.sleep(delay)

        # 最终统计
        elapsed = time.time() - start_time
        logger.info(f"批量收集完成: 总耗时 {elapsed/60:.1f}分钟")
        logger.info(f"成功: {results['success_count']}, 失败: {results['failed_count']}")
        logger.info(f"总分红记录: {results['total_records']}")

        return results

    def load_hsi_stocks(self, csv_file: str = None) -> List[Dict]:
        """加载港股股票列表"""
        try:
            # 如果没有指定文件，尝试查找现有的港股列表文件
            if csv_file is None:
                possible_files = [
                    'hsi_stocks.csv',
                    'ganggutong_stocks.csv',
                    'hk_stocks.csv'
                ]

                for file in possible_files:
                    if os.path.exists(file):
                        csv_file = file
                        break

                if csv_file is None:
                    logger.error("未找到股票列表文件")
                    return []

            logger.info(f"从 {csv_file} 加载股票列表")
            df = pd.read_csv(csv_file, encoding='utf-8')

            stocks = []
            for _, row in df.iterrows():
                # 尝试不同的列名
                code_cols = ['代码', 'code', 'stock_code', '股票代码']
                name_cols = ['名称', 'name', 'stock_name', '股票名称']

                stock_code = None
                stock_name = None

                for col in code_cols:
                    if col in df.columns and pd.notna(row[col]):
                        stock_code = str(row[col]).zfill(5)
                        break

                for col in name_cols:
                    if col in df.columns and pd.notna(row[col]):
                        stock_name = str(row[col])
                        break

                if stock_code and stock_name:
                    stocks.append({
                        'code': stock_code,
                        'name': stock_name
                    })

            logger.info(f"成功加载 {len(stocks)} 只股票")
            return stocks

        except Exception as e:
            logger.error(f"加载股票列表失败: {e}")
            return []

    def get_collection_summary(self) -> Dict:
        """获取收集汇总信息"""
        try:
            conn = sqlite3.connect(self.db_name)
            cursor = conn.cursor()

            # 统计分红数据
            cursor.execute('SELECT COUNT(*) FROM dividend_data')
            total_records = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM dividend_data')
            stocks_with_dividend = cursor.fetchone()[0]

            cursor.execute('SELECT COUNT(DISTINCT stock_code) FROM stock_info')
            total_stocks = cursor.fetchone()[0]

            # 按数据源统计
            cursor.execute('SELECT data_source, COUNT(*) FROM dividend_data GROUP BY data_source')
            source_stats = dict(cursor.fetchall())

            # 最新收集时间
            cursor.execute('SELECT MAX(collection_time) FROM collection_log')
            last_collection = cursor.fetchone()[0]

            conn.close()

            return {
                'total_dividend_records': total_records,
                'stocks_with_dividend': stocks_with_dividend,
                'total_stocks': total_stocks,
                'coverage_rate': f"{stocks_with_dividend/total_stocks*100:.1f}%" if total_stocks > 0 else "0%",
                'source_stats': source_stats,
                'last_collection': last_collection
            }

        except Exception as e:
            logger.error(f"获取汇总信息失败: {e}")
            return {}

def main():
    """主函数"""
    print("🎯 港股分红数据收集器")
    print("=" * 60)

    # 初始化收集器
    collector = HKDividendDataCollector()

    # 显示菜单
    while True:
        print("\n📋 请选择操作:")
        print("1. 收集单只股票分红数据")
        print("2. 批量收集分红数据")
        print("3. 查看收集汇总")
        print("4. 测试API连接")
        print("5. 退出")

        choice = input("\n请输入选择 (1-5): ").strip()

        if choice == '1':
            # 单只股票
            stock_code = input("请输入股票代码 (如 00700): ").strip().zfill(5)
            stock_name = input("请输入股票名称 (如 腾讯控股): ").strip()

            if stock_code and stock_name:
                result = collector.collect_single_stock_dividend(stock_code, stock_name)
                print(f"\n收集结果:")
                print(f"  成功: {result['success']}")
                print(f"  总记录: {result['total_records']}")
                print(f"  东方财富: {result['eastmoney_records']}")
                if result.get('error_message'):
                    print(f"  错误: {result['error_message']}")

        elif choice == '2':
            # 批量收集
            csv_file = input("请输入股票列表CSV文件路径 (回车使用默认): ").strip()
            if not csv_file:
                csv_file = None

            stocks = collector.load_hsi_stocks(csv_file)
            if not stocks:
                print("❌ 无法加载股票列表")
                continue

            print(f"📊 找到 {len(stocks)} 只股票")
            confirm = input("是否开始批量收集? (y/N): ").strip().lower()

            if confirm == 'y':
                delay = float(input("请输入请求间隔秒数 (默认1.0): ").strip() or "1.0")
                results = collector.collect_batch_dividend_data(stocks, delay)

                print(f"\n📊 批量收集完成:")
                print(f"  总股票: {results['total_stocks']}")
                print(f"  成功: {results['success_count']}")
                print(f"  失败: {results['failed_count']}")
                print(f"  总记录: {results['total_records']}")

        elif choice == '3':
            # 查看汇总
            summary = collector.get_collection_summary()
            print(f"\n📊 收集汇总:")
            print(f"  总分红记录: {summary.get('total_dividend_records', 0)}")
            print(f"  有分红股票: {summary.get('stocks_with_dividend', 0)}")
            print(f"  总股票数: {summary.get('total_stocks', 0)}")
            print(f"  覆盖率: {summary.get('coverage_rate', '0%')}")
            print(f"  数据源统计: {summary.get('source_stats', {})}")
            print(f"  最后收集: {summary.get('last_collection', 'N/A')}")

        elif choice == '4':
            # 测试API
            print("\n🔍 测试API连接...")
            test_stocks = [
                {'code': '00700', 'name': '腾讯控股'},
                {'code': '00005', 'name': '汇丰控股'},
                {'code': '00939', 'name': '建设银行'}
            ]

            for stock in test_stocks:
                print(f"\n测试 {stock['code']} ({stock['name']}):")

                # 测试东方财富网
                try:
                    eastmoney_data = collector.get_dividend_data_from_eastmoney(stock['code'], stock['name'])
                    print(f"  东方财富网: ✅ {len(eastmoney_data)} 条记录")
                except Exception as e:
                    print(f"  东方财富网: ❌ {e}")

                time.sleep(1)  # 避免API限制

        elif choice == '5':
            print("👋 再见!")
            break

        else:
            print("❌ 无效选择，请重新输入")

if __name__ == "__main__":
    main()
