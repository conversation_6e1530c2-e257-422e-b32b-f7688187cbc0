#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股相对强度分析器 - 快速测试版本
"""

import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import warnings
from tqdm import tqdm

warnings.filterwarnings('ignore')

def test_hsi_analysis():
    """快速测试恒生指数成分股分析"""
    print("🎯 恒生指数成分股相对强度分析 - 快速测试")
    print("=" * 50)
    
    # 测试股票列表（前20只）
    test_stocks = [
        '9999',  # 网易-S
        '1093',  # 石药集团
        '1177',  # 中国生物制药
        '3692',  # 翰森制药
        '1211',  # 比亚迪股份
        '762',   # 中国联通
        '6618',  # 京东健康
        '1810',  # 小米集团-W
        '316',   # 东方海外国际
        '2015',  # 理想汽车-W
        '1929',  # 周大福
        '981',   # 中芯国际
        '941',   # 中国移动
        '669',   # 创科实业
        '101',   # 恒隆地产
        '2388',  # 中银香港
        '388',   # 香港交易所
        '27',    # 银河娱乐
        '1299',  # 友邦保险
        '700'    # 腾讯控股
    ]
    
    print(f"📊 测试股票数量: {len(test_stocks)}")
    
    # 1. 下载恒生指数数据
    print("\n📈 下载恒生指数数据...")
    try:
        # 尝试获取恒生指数数据
        end_date = datetime.now().strftime('%Y%m%d')
        start_date = (datetime.now() - timedelta(days=365)).strftime('%Y%m%d')
        
        # 使用A股上证指数作为替代（akshare更稳定）
        hsi_data = ak.index_zh_a_hist(symbol="000001", period="daily", start_date=start_date, end_date=end_date)
        
        if not hsi_data.empty:
            benchmark_data = pd.Series(hsi_data['收盘'].values, 
                                     index=pd.to_datetime(hsi_data['日期']))
            print(f"✅ 基准数据准备完成: {len(benchmark_data)} 个数据点")
        else:
            print("❌ 无法获取基准数据")
            return
            
    except Exception as e:
        print(f"❌ 下载基准数据失败: {e}")
        return
    
    # 2. 下载港股数据
    print(f"\n📊 下载 {len(test_stocks)} 只港股数据...")
    stock_data = {}
    successful_downloads = 0
    
    for symbol in tqdm(test_stocks, desc="下载港股"):
        try:
            # 使用akshare获取港股数据
            end_date = datetime.now().strftime('%Y%m%d')
            start_date = (datetime.now() - timedelta(days=400)).strftime('%Y%m%d')
            
            # 尝试不同的接口
            stock_hist = None
            
            # 方法1: 使用A股接口（某些港股在A股也有）
            try:
                if len(symbol) <= 4:
                    padded_symbol = symbol.zfill(6)
                    stock_hist = ak.stock_zh_a_hist(symbol=padded_symbol, period="daily", 
                                                  start_date=start_date, end_date=end_date)
            except:
                pass
            
            # 方法2: 使用港股接口
            if stock_hist is None or stock_hist.empty:
                try:
                    stock_hist = ak.stock_hk_daily(symbol=symbol, start_date=start_date, end_date=end_date)
                except:
                    pass
            
            if stock_hist is not None and not stock_hist.empty and len(stock_hist) > 50:
                # 寻找收盘价列
                close_col = None
                for col in ['收盘', 'close', '收盘价', 'Close']:
                    if col in stock_hist.columns:
                        close_col = col
                        break
                
                if close_col is None and len(stock_hist.columns) >= 5:
                    close_col = stock_hist.columns[4]  # 通常第5列是收盘价
                
                if close_col is not None:
                    dates = pd.to_datetime(stock_hist.iloc[:, 0])
                    prices = pd.to_numeric(stock_hist[close_col], errors='coerce')
                    
                    stock_close = pd.Series(prices.values, index=dates).dropna()
                    
                    if len(stock_close) > 50:
                        stock_data[symbol] = stock_close
                        successful_downloads += 1
                        
        except Exception as e:
            continue
    
    print(f"✅ 成功下载 {successful_downloads} 只港股数据")
    
    if successful_downloads == 0:
        print("❌ 没有成功下载任何股票数据")
        return
    
    # 3. 计算相对强度
    print(f"\n🔢 计算相对强度...")
    relative_strength_data = {}
    
    for symbol, stock_close in stock_data.items():
        try:
            # 处理时区问题
            stock_close_tz = stock_close.copy()
            benchmark_tz = benchmark_data.copy()
            
            if stock_close_tz.index.tz is not None:
                stock_close_tz.index = stock_close_tz.index.tz_localize(None)
            if benchmark_tz.index.tz is not None:
                benchmark_tz.index = benchmark_tz.index.tz_localize(None)
            
            # 对齐数据
            stock_df = pd.DataFrame({'stock': stock_close_tz})
            benchmark_df = pd.DataFrame({'benchmark': benchmark_tz})
            
            merged = stock_df.join(benchmark_df, how='inner').dropna()
            
            if len(merged) < 30:
                continue
            
            # 计算相对强度
            relative_strength = (merged['stock'] / merged['benchmark']) * 100
            
            # 标准化到起始点为100
            if len(relative_strength) > 0 and not pd.isna(relative_strength.iloc[0]) and relative_strength.iloc[0] != 0:
                relative_strength = relative_strength / relative_strength.iloc[0] * 100
                relative_strength_data[symbol] = relative_strength
                
        except Exception as e:
            continue
    
    print(f"✅ 计算了 {len(relative_strength_data)} 只港股的相对强度")
    
    # 4. 分析结果
    print(f"\n📈 分析趋势...")
    results = []
    
    for symbol, rs_data in relative_strength_data.items():
        try:
            if len(rs_data) < 30:
                continue
            
            current_rs = rs_data.iloc[-1]
            start_rs = rs_data.iloc[0]
            total_change = current_rs - start_rs
            total_change_pct = (current_rs / start_rs - 1) * 100
            
            # 计算不同周期的斜率
            periods = {
                '1M': min(22, len(rs_data)),
                '3M': min(66, len(rs_data)),
                '6M': min(132, len(rs_data))
            }
            
            slopes = {}
            for period_name, period_days in periods.items():
                if period_days >= 10:
                    period_data = rs_data.iloc[-period_days:]
                    x = np.arange(len(period_data))
                    y = period_data.values
                    slope = np.polyfit(x, y, 1)[0]
                    slopes[f'{period_name}_slope'] = slope
            
            # 移动平均线
            rs_ma20 = rs_data.rolling(20).mean()
            rs_ma50 = rs_data.rolling(50).mean()
            
            above_ma20 = current_rs > rs_ma20.iloc[-1] if not pd.isna(rs_ma20.iloc[-1]) else False
            above_ma50 = current_rs > rs_ma50.iloc[-1] if not pd.isna(rs_ma50.iloc[-1]) else False
            
            # 强度评分
            strength_score = 0
            
            if slopes.get('1M_slope', 0) > 0:
                strength_score += 15
            if slopes.get('3M_slope', 0) > 0:
                strength_score += 25
            if slopes.get('6M_slope', 0) > 0:
                strength_score += 25
            
            if above_ma20:
                strength_score += 15
            if above_ma50:
                strength_score += 10
            if current_rs > 100:
                strength_score += 10
            
            result = {
                'symbol': symbol,
                'current_rs': current_rs,
                'total_change': total_change,
                'total_change_pct': total_change_pct,
                'above_ma20': above_ma20,
                'above_ma50': above_ma50,
                'strength_score': strength_score,
                **slopes
            }
            
            results.append(result)
            
        except Exception as e:
            continue
    
    # 5. 显示结果
    if results:
        results_df = pd.DataFrame(results)
        results_df = results_df.sort_values('strength_score', ascending=False)
        
        print(f"\n🏆 分析结果 (共 {len(results_df)} 只港股):")
        print("\n前10强势港股:")
        for i, (_, stock) in enumerate(results_df.head(10).iterrows(), 1):
            print(f"{i:2d}. {stock['symbol']:<8} 评分:{stock['strength_score']:5.0f} 相对强度:{stock['current_rs']:6.1f}")
        
        # 持续上升的股票
        rising_stocks = results_df[
            (results_df.get('1M_slope', 0) > 0) &
            (results_df.get('3M_slope', 0) > 0) &
            (results_df.get('6M_slope', 0) > 0) &
            (results_df['strength_score'] >= 60)
        ]
        
        print(f"\n📈 持续上升港股 ({len(rising_stocks)} 只):")
        for _, stock in rising_stocks.iterrows():
            print(f"   {stock['symbol']:<8} 评分:{stock['strength_score']:5.0f} 相对强度:{stock['current_rs']:6.1f}")
        
        # 保存结果
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"hsi_test_results_{timestamp}.csv"
        results_df.to_csv(results_file, index=False)
        print(f"\n📁 结果已保存至: {results_file}")
        
        print("\n✅ 快速测试完成!")
    else:
        print("❌ 未生成分析结果")

if __name__ == "__main__":
    test_hsi_analysis()
