"""
Rebalancing Frequency Comparison for Equal Weight Asset Allocation

This script compares different rebalancing frequencies for an equal-weight
allocation strategy across nine major asset classes:
1. Monthly rebalancing
2. Quarterly rebalancing
3. Semi-annual rebalancing
4. Annual rebalancing
5. Buy and hold (no rebalancing)
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
import yfinance as yf
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

# Set matplotlib to support Chinese display
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS']
plt.rcParams['axes.unicode_minus'] = False

# Set proxy settings
os.environ['HTTP_PROXY'] = 'http://127.0.0.1:51081'
os.environ['HTTPS_PROXY'] = 'http://127.0.0.1:51081'

class RebalancingComparison:
    def __init__(self, start_date='2018-01-01', end_date=None):
        """
        Initialize the comparison with start and end dates.
        
        Parameters:
        -----------
        start_date : str
            Start date for the backtest in 'YYYY-MM-DD' format
        end_date : str
            End date for the backtest in 'YYYY-MM-DD' format (defaults to today)
        """
        self.start_date = start_date
        self.end_date = end_date if end_date else datetime.now().strftime('%Y-%m-%d')
        
        # Define asset tickers and their data sources
        self.assets = {
            'A股': 'FXI',                # iShares China Large-Cap ETF (proxy for A-shares)
            '中国债券': 'CBON',            # VanEck China Bond ETF
            '黄金': 'GLD',                # SPDR Gold Shares ETF
            '港股': 'EWH',                # iShares MSCI Hong Kong ETF
            '可转债': 'CWB',               # SPDR Bloomberg Convertible Securities ETF
            '原油': 'USO',                # United States Oil Fund
            '美股': 'SPY',                # SPDR S&P 500 ETF
            '美元债': 'TLT',               # iShares 20+ Year Treasury Bond ETF
            'REITs': 'VNQ'                # Vanguard Real Estate ETF
        }
        
        # Data storage
        self.asset_data = {}
        self.aligned_data = None
        self.strategy_returns = {}
        self.metrics = {}
        
    def fetch_data(self):
        """Fetch historical price data for all assets"""
        print("Fetching data for all assets...")
        
        for asset_name, ticker in self.assets.items():
            try:
                print(f"Downloading data for {asset_name}...")
                data = yf.download(ticker, start=self.start_date, end=self.end_date, progress=False)
                
                if not data.empty:
                    # Use closing price for all assets
                    data['Price'] = data['Close']
                    self.asset_data[asset_name] = data[['Price']].copy()
                else:
                    print(f"Warning: No data found for {asset_name}")
            except Exception as e:
                print(f"Error downloading {asset_name} data: {e}")
        
        # Align all data to common dates
        self.align_data()
        
    def align_data(self):
        """Align all asset data to common dates and handle missing values"""
        if not self.asset_data:
            print("No data to align. Please fetch data first.")
            return
        
        # Combine all price data
        all_data = pd.DataFrame()
        
        for asset_name, data in self.asset_data.items():
            all_data[asset_name] = data['Price']
        
        # Forward fill missing values (for non-trading days)
        all_data = all_data.fillna(method='ffill')
        
        # Backward fill any remaining NaNs at the beginning
        all_data = all_data.fillna(method='bfill')
        
        # Update asset data with aligned data
        for asset_name in self.asset_data:
            self.asset_data[asset_name] = pd.DataFrame(all_data[asset_name])
            self.asset_data[asset_name].columns = ['Price']
        
        # Store aligned data
        self.aligned_data = all_data
        
    def run_strategy(self, rebalance_freq='quarterly'):
        """
        Run the equal weight allocation strategy with specified rebalancing frequency
        
        Parameters:
        -----------
        rebalance_freq : str
            Rebalancing frequency ('monthly', 'quarterly', 'semi-annually', 'annually', 'none')
        """
        if self.aligned_data is None or self.aligned_data.empty:
            print("No aligned data available. Please fetch and align data first.")
            return
        
        # Initialize portfolio with equal weights
        num_assets = len(self.assets)
        equal_weight = 1.0 / num_assets
        
        # Create a DataFrame to store portfolio values
        portfolio = pd.DataFrame(index=self.aligned_data.index)
        portfolio['Portfolio_Value'] = 0
        
        # Initialize with equal allocation
        initial_investment = 10000  # $10,000 initial investment
        asset_values = {asset: initial_investment * equal_weight for asset in self.assets}
        
        # Calculate shares for each asset
        asset_shares = {}
        for asset in self.assets:
            initial_price = self.asset_data[asset]['Price'].iloc[0]
            asset_shares[asset] = asset_values[asset] / initial_price
        
        # Track portfolio value over time
        for date in portfolio.index:
            portfolio_value = 0
            for asset in self.assets:
                asset_price = self.asset_data[asset].loc[date, 'Price']
                asset_value = asset_shares[asset] * asset_price
                portfolio_value += asset_value
            
            portfolio.loc[date, 'Portfolio_Value'] = portfolio_value
            
            # Check if rebalancing is needed (skip if rebalance_freq is 'none')
            if rebalance_freq != 'none' and self.is_rebalance_date(date, rebalance_freq):
                # Rebalance to equal weights
                for asset in self.assets:
                    asset_price = self.asset_data[asset].loc[date, 'Price']
                    asset_shares[asset] = (portfolio_value * equal_weight) / asset_price
        
        # Calculate daily returns
        portfolio['Daily_Return'] = portfolio['Portfolio_Value'].pct_change()
        
        # Store results
        strategy_name = f"{rebalance_freq.capitalize()} Rebalancing" if rebalance_freq != 'none' else "Buy and Hold"
        self.strategy_returns[strategy_name] = portfolio['Daily_Return'].dropna()
        
        # Calculate performance metrics
        self.calculate_metrics(strategy_name)
        
        print(f"{strategy_name} strategy completed.")
        
    def is_rebalance_date(self, date, frequency):
        """
        Determine if the given date is a rebalancing date
        
        Parameters:
        -----------
        date : datetime
            The date to check
        frequency : str
            Rebalancing frequency ('monthly', 'quarterly', 'semi-annually', 'annually')
        
        Returns:
        --------
        bool : True if rebalancing should occur, False otherwise
        """
        date = pd.to_datetime(date)
        
        # Get the index we're working with
        index = self.aligned_data.index
        
        # First day of the series is always a rebalancing date
        if date == index[0]:
            return True
        
        # Get previous date
        date_loc = index.get_loc(date)
        if date_loc == 0:
            return False
        prev_date = index[date_loc - 1]
        
        # Check based on frequency
        if frequency == 'monthly':
            # First trading day of each month
            return date.month != prev_date.month
        
        elif frequency == 'quarterly':
            # First trading day of each quarter
            return (date.month in [1, 4, 7, 10]) and (date.month != prev_date.month)
        
        elif frequency == 'semi-annually':
            # First trading day of January and July
            return (date.month in [1, 7]) and (date.month != prev_date.month)
        
        elif frequency == 'annually':
            # First trading day of each year
            return date.year != prev_date.year
        
        return False
    
    def calculate_metrics(self, strategy_name):
        """
        Calculate performance metrics for a strategy
        
        Parameters:
        -----------
        strategy_name : str
            Name of the strategy
        """
        returns = self.strategy_returns[strategy_name]
        
        # Annualized return
        annual_return = (1 + returns.mean()) ** 252 - 1
        
        # Annualized volatility
        annual_volatility = returns.std() * np.sqrt(252)
        
        # Sharpe ratio (assuming risk-free rate of 0% for simplicity)
        sharpe_ratio = annual_return / annual_volatility
        
        # Maximum drawdown
        cumulative_returns = (1 + returns).cumprod()
        running_max = cumulative_returns.cummax()
        drawdown = (cumulative_returns / running_max) - 1
        max_drawdown = drawdown.min()
        
        # Store metrics
        self.metrics[strategy_name] = {
            'Annual Return': annual_return,
            'Annual Volatility': annual_volatility,
            'Sharpe Ratio': sharpe_ratio,
            'Maximum Drawdown': max_drawdown
        }
        
        print(f"\n{strategy_name} Performance Metrics:")
        print(f"Annual Return: {annual_return:.2%}")
        print(f"Annual Volatility: {annual_volatility:.2%}")
        print(f"Sharpe Ratio: {sharpe_ratio:.2f}")
        print(f"Maximum Drawdown: {max_drawdown:.2%}")
    
    def plot_comparison(self):
        """Plot the comparison of all strategies"""
        if not self.strategy_returns:
            print("No strategy results available. Please run strategies first.")
            return
        
        # Create figure with two subplots
        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), gridspec_kw={'height_ratios': [2, 1]})
        
        # Plot cumulative returns for all strategies
        for strategy_name, returns in self.strategy_returns.items():
            cumulative_returns = (1 + returns).cumprod()
            ax1.plot(cumulative_returns.index, cumulative_returns, linewidth=2, label=strategy_name)
        
        ax1.set_title('Comparison of Rebalancing Frequencies (2018-Present)', fontsize=16)
        ax1.set_ylabel('Cumulative Return (Initial = 1)', fontsize=12)
        ax1.grid(True)
        ax1.legend()
        
        # Format x-axis dates
        ax1.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax1.xaxis.set_major_locator(mdates.YearLocator())
        
        # Plot drawdowns for all strategies
        for strategy_name, returns in self.strategy_returns.items():
            cumulative_returns = (1 + returns).cumprod()
            running_max = cumulative_returns.cummax()
            drawdown = (cumulative_returns / running_max) - 1
            ax2.plot(drawdown.index, drawdown * 100, linewidth=2, label=strategy_name)
        
        ax2.set_title('Strategy Drawdowns', fontsize=16)
        ax2.set_ylabel('Drawdown (%)', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.grid(True)
        
        # Format x-axis dates
        ax2.xaxis.set_major_formatter(mdates.DateFormatter('%Y-%m'))
        ax2.xaxis.set_major_locator(mdates.YearLocator())
        
        # Create a table of performance metrics
        metrics_df = pd.DataFrame({
            strategy: {
                'Annual Return': f"{self.metrics[strategy]['Annual Return']:.2%}",
                'Annual Volatility': f"{self.metrics[strategy]['Annual Volatility']:.2%}",
                'Sharpe Ratio': f"{self.metrics[strategy]['Sharpe Ratio']:.2f}",
                'Maximum Drawdown': f"{self.metrics[strategy]['Maximum Drawdown']:.2%}"
            } for strategy in self.metrics
        })
        
        # Print metrics table
        print("\nPerformance Metrics Comparison:")
        print(metrics_df)
        
        plt.tight_layout()
        plt.savefig('rebalancing_frequency_comparison.png', dpi=300, bbox_inches='tight')
        plt.show()

def main():
    # Create comparison instance
    comparison = RebalancingComparison(start_date='2018-01-01')
    
    # Fetch data
    comparison.fetch_data()
    
    # Run strategies with different rebalancing frequencies
    comparison.run_strategy(rebalance_freq='monthly')
    comparison.run_strategy(rebalance_freq='quarterly')
    comparison.run_strategy(rebalance_freq='semi-annually')
    comparison.run_strategy(rebalance_freq='annually')
    comparison.run_strategy(rebalance_freq='none')  # Buy and hold
    
    # Plot comparison
    comparison.plot_comparison()

if __name__ == "__main__":
    main()
