#!/usr/bin/env python3
"""
恒生指数成分股智能股息率排名器
根据分红频率智能计算年度分红总和：
- 季度分红：取最近4次分红
- 半年分红：取最近2次分红  
- 年度分红：取最近1次分红
"""

import os
import json
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import time
from collections import Counter
import pickle
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

class HSISmartDividendYieldRanking:
    """
    恒生指数成分股智能股息率排名器
    根据分红频率智能计算年度分红总和
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841",
                 cache_dir: str = "cache",
                 output_dir: str = "hsi_smart_dividend_yield_rankings"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.output_dir = output_dir
        self.dividend_data = {}
        self.ranking_results = []
        self.failed_stocks = []
        self.price_cache = {}
        self.lock = threading.Lock()

        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(cache_dir, exist_ok=True)
        
    def load_dividend_data(self):
        """加载分红数据"""
        print("📁 加载分红数据...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        
        for file in dividend_files:
            try:
                file_path = os.path.join(self.dividend_data_dir, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                stock_code = data['stock_code']
                
                # 转换日期格式
                dividend_history = []
                for div in data['dividend_history']:
                    try:
                        # 处理日期格式
                        ex_date_str = div['ex_date']
                        if '/' in ex_date_str:
                            ex_date = datetime.strptime(ex_date_str, '%Y/%m/%d')
                        else:
                            ex_date = datetime.strptime(ex_date_str, '%Y-%m-%d')
                        
                        dividend_history.append({
                            'year': div['year'],
                            'ex_date': ex_date,
                            'amount': float(div['amount']),
                            'report_type': div.get('report_type', ''),
                            'plan': div.get('plan', '')
                        })
                    except Exception as e:
                        print(f"⚠️  处理 {stock_code} 分红记录失败: {e}")
                        continue
                
                # 按除权日期排序（最新的在前）
                dividend_history.sort(key=lambda x: x['ex_date'], reverse=True)
                
                self.dividend_data[stock_code] = {
                    'stock_name': data['stock_name'],
                    'dividend_history': dividend_history
                }
                
            except Exception as e:
                print(f"❌ 加载 {file} 失败: {e}")
                continue
        
        print(f"✅ 成功加载 {len(self.dividend_data)} 只股票的分红数据")
        return self.dividend_data
    
    def analyze_dividend_frequency(self, dividend_history):
        """分析分红频率"""
        if len(dividend_history) < 2:
            return "年度", 1
        
        # 计算相邻分红之间的间隔（月数）
        intervals = []
        for i in range(len(dividend_history) - 1):
            date1 = dividend_history[i]['ex_date']
            date2 = dividend_history[i + 1]['ex_date']
            months_diff = (date1.year - date2.year) * 12 + (date1.month - date2.month)
            intervals.append(abs(months_diff))
        
        # 分析分红类型
        report_types = [div.get('report_type', '') for div in dividend_history[:8]]  # 看最近8次
        
        # 判断分红频率
        if intervals:
            avg_interval = np.mean(intervals)
            
            # 检查是否有季度分红的迹象
            quarterly_indicators = sum(1 for interval in intervals if 2 <= interval <= 4)
            semi_annual_indicators = sum(1 for interval in intervals if 5 <= interval <= 7)
            annual_indicators = sum(1 for interval in intervals if 10 <= interval <= 14)
            
            # 检查报告类型
            has_quarterly = any('季' in rt or 'Q' in rt.upper() for rt in report_types)
            has_interim = sum(1 for rt in report_types if '中期' in rt or '半年' in rt)
            has_annual = sum(1 for rt in report_types if '年度' in rt or '年报' in rt)
            
            # 决策逻辑
            if quarterly_indicators >= 2 or has_quarterly:
                return "季度", 4
            elif (semi_annual_indicators >= 1 and has_interim >= 1) or avg_interval <= 7:
                return "半年", 2
            else:
                return "年度", 1
        
        return "年度", 1
    
    def calculate_smart_annual_dividend(self, stock_code: str):
        """根据分红频率智能计算年度分红总和"""
        if stock_code not in self.dividend_data:
            return 0.0, "无数据", 0
        
        dividend_history = self.dividend_data[stock_code]['dividend_history']
        
        if not dividend_history:
            return 0.0, "无分红", 0
        
        # 分析分红频率
        frequency_type, take_count = self.analyze_dividend_frequency(dividend_history)
        
        # 取最近N次分红
        recent_dividends = dividend_history[:take_count]
        total_dividend = sum(div['amount'] for div in recent_dividends)
        
        return total_dividend, frequency_type, len(recent_dividends)
    
    def get_stock_list(self):
        """获取所有股票列表"""
        print("📁 获取股票列表...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        stock_list = []
        
        for file in dividend_files:
            stock_code = file.split('_')[0]
            stock_name = file.split('_')[1].replace('.json', '')
            stock_list.append({
                'code': stock_code,
                'name': stock_name,
                'file': file
            })
        
        # 按股票代码排序
        stock_list.sort(key=lambda x: x['code'])
        
        print(f"✅ 找到 {len(stock_list)} 只股票")
        return stock_list
    
    def load_price_cache(self):
        """加载价格缓存"""
        cache_file = os.path.join(self.cache_dir, "hsi_prices_cache.pkl")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    # 检查缓存是否过期（1小时）
                    if datetime.now() - cache_data['timestamp'] < timedelta(hours=1):
                        self.price_cache = cache_data['prices']
                        print(f"💾 从缓存加载 {len(self.price_cache)} 只股票价格")
                        return True
            except:
                pass
        return False

    def save_price_cache(self):
        """保存价格缓存"""
        cache_file = os.path.join(self.cache_dir, "hsi_prices_cache.pkl")
        cache_data = {
            'timestamp': datetime.now(),
            'prices': self.price_cache
        }
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)

    def get_latest_stock_price(self, stock_code: str) -> float:
        """获取股票的最新价格（带缓存）"""
        # 检查缓存
        if stock_code in self.price_cache:
            return self.price_cache[stock_code]

        try:
            # 使用akshare获取最新价格数据
            df = ak.stock_hk_daily(symbol=stock_code)

            if not df.empty:
                # 获取最新的收盘价
                latest_price = float(df.iloc[-1]['close'])

                # 缓存价格
                with self.lock:
                    self.price_cache[stock_code] = latest_price

                return latest_price
            else:
                return None

        except Exception as e:
            print(f"   ❌ 获取 {stock_code} 价格失败: {e}")
            return None

    def batch_get_prices(self, stock_codes, max_workers=5):
        """批量获取股票价格"""
        print(f"🌐 批量获取 {len(stock_codes)} 只股票价格...")

        def get_single_price(stock_code):
            try:
                df = ak.stock_hk_daily(symbol=stock_code)
                if not df.empty:
                    price = float(df.iloc[-1]['close'])
                    with self.lock:
                        self.price_cache[stock_code] = price
                    return stock_code, price
                else:
                    return stock_code, None
            except Exception as e:
                return stock_code, None

        # 过滤已缓存的股票
        uncached_codes = [code for code in stock_codes if code not in self.price_cache]

        if uncached_codes:
            with ThreadPoolExecutor(max_workers=max_workers) as executor:
                future_to_code = {executor.submit(get_single_price, code): code for code in uncached_codes}

                for future in as_completed(future_to_code):
                    stock_code, price = future.result()
                    if price is not None:
                        print(f"   ✅ {stock_code}: {price:.2f} 港元")
                    else:
                        print(f"   ❌ {stock_code}: 获取失败")

                    # 添加小延迟避免API限制
                    time.sleep(0.1)

        print(f"✅ 价格获取完成，缓存中共有 {len(self.price_cache)} 只股票价格")
    
    def calculate_single_stock_ranking(self, stock_info):
        """计算单只股票的排名信息（优化版）"""
        stock_code = stock_info['code']
        stock_name = stock_info['name']

        try:
            # 获取最新价格（从缓存）
            latest_price = self.get_latest_stock_price(stock_code)

            if latest_price is None:
                with self.lock:
                    self.failed_stocks.append({
                        'code': stock_code,
                        'name': stock_name,
                        'reason': '无法获取最新价格'
                    })
                return False
            
            # 智能计算年度分红
            annual_dividend, frequency_type, dividend_count = self.calculate_smart_annual_dividend(stock_code)
            
            # 计算股息率
            if annual_dividend > 0 and latest_price > 0:
                dividend_yield = (annual_dividend / latest_price) * 100
            else:
                dividend_yield = 0.0
            
            # 获取分红历史统计
            dividend_history = []
            if stock_code in self.dividend_data:
                dividend_history = self.dividend_data[stock_code]['dividend_history']
            
            # 计算分红频率（过去3年）
            recent_dividends = [d for d in dividend_history 
                              if d['ex_date'] >= datetime.now() - timedelta(days=1095)]
            dividend_frequency_3y = len(recent_dividends)
            
            # 记录结果
            result = {
                'stock_code': stock_code,
                'stock_name': stock_name,
                'latest_price': latest_price,
                'dividend_yield': dividend_yield,
                'annual_dividend': annual_dividend,
                'frequency_type': frequency_type,
                'dividend_count_used': dividend_count,
                'dividend_frequency_3y': dividend_frequency_3y,
                'last_dividend_date': dividend_history[0]['ex_date'] if dividend_history else None,
                'last_dividend_amount': dividend_history[0]['amount'] if dividend_history else 0,
                'calculation_date': datetime.now()
            }
            
            with self.lock:
                self.ranking_results.append(result)

            return True

        except Exception as e:
            with self.lock:
                self.failed_stocks.append({
                    'code': stock_code,
                    'name': stock_name,
                    'reason': str(e)
                })
            return False
    
    def calculate_all_rankings(self, max_workers=8):
        """计算所有股票的排名（优化版）"""
        print("🚀 开始计算恒生指数成分股智能股息率排名")
        print("=" * 60)

        # 加载分红数据
        self.load_dividend_data()

        # 获取股票列表
        stock_list = self.get_stock_list()
        stock_codes = [stock['code'] for stock in stock_list]

        # 加载价格缓存
        cache_loaded = self.load_price_cache()

        # 批量获取价格（如果缓存未加载或不完整）
        if not cache_loaded or len(self.price_cache) < len(stock_codes):
            self.batch_get_prices(stock_codes, max_workers=max_workers)
            self.save_price_cache()

        print(f"\n🔄 开始批量计算股息率...")
        start_time = datetime.now()

        # 使用线程池并行处理
        successful_count = 0
        total_count = len(stock_list)

        with ThreadPoolExecutor(max_workers=max_workers) as executor:
            # 提交所有任务
            future_to_stock = {executor.submit(self.calculate_single_stock_ranking, stock): stock for stock in stock_list}

            # 处理完成的任务
            for i, future in enumerate(as_completed(future_to_stock), 1):
                stock_info = future_to_stock[future]
                try:
                    if future.result():
                        successful_count += 1
                        print(f"✅ [{i}/{total_count}] {stock_info['code']} ({stock_info['name']})")
                    else:
                        print(f"❌ [{i}/{total_count}] {stock_info['code']} ({stock_info['name']})")
                except Exception as e:
                    print(f"❌ [{i}/{total_count}] {stock_info['code']} 处理异常: {e}")

                # 每10只股票显示一次进度
                if i % 10 == 0:
                    elapsed = datetime.now() - start_time
                    avg_time = elapsed.total_seconds() / i
                    remaining = (total_count - i) * avg_time
                    print(f"📊 进度: {i}/{total_count} ({i/total_count*100:.1f}%), "
                          f"成功: {successful_count}, "
                          f"预计剩余: {remaining/60:.1f}分钟")

        end_time = datetime.now()
        total_time = end_time - start_time

        print(f"\n🎉 计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {successful_count}/{total_count} ({successful_count/total_count*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")

        return self.ranking_results
    
    def create_ranking_report(self):
        """创建排名报告"""
        if not self.ranking_results:
            print("❌ 没有排名数据可生成报告")
            return None
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 按股息率排序
        sorted_results = sorted(self.ranking_results, key=lambda x: x['dividend_yield'], reverse=True)
        
        # 创建DataFrame
        df = pd.DataFrame(sorted_results)
        df['ranking'] = range(1, len(df) + 1)
        
        # 重新排列列顺序
        columns_order = [
            'ranking', 'stock_code', 'stock_name', 'latest_price', 
            'dividend_yield', 'annual_dividend', 'frequency_type', 'dividend_count_used',
            'dividend_frequency_3y', 'last_dividend_date', 'last_dividend_amount', 'calculation_date'
        ]
        df = df[columns_order]
        
        # 保存详细排名文件
        ranking_file = os.path.join(self.output_dir, f"hsi_smart_dividend_yield_ranking_{timestamp}.csv")
        df.to_csv(ranking_file, index=False, encoding='utf-8-sig')
        
        # 创建简化版排名文件
        simple_df = df[['ranking', 'stock_code', 'stock_name', 'latest_price', 'dividend_yield', 'frequency_type']].copy()
        simple_df.columns = ['排名', '股票代码', '股票名称', '最新价格(港元)', '股息率(%)', '分红频率']
        
        simple_file = os.path.join(self.output_dir, f"hsi_smart_dividend_yield_ranking_simple_{timestamp}.csv")
        simple_df.to_csv(simple_file, index=False, encoding='utf-8-sig')
        
        print(f"📊 智能排名报告已生成:")
        print(f"   详细版: {ranking_file}")
        print(f"   简化版: {simple_file}")
        
        return ranking_file, simple_file, df
    
    def print_top_rankings(self, top_n: int = 20):
        """打印前N名股票排名"""
        if not self.ranking_results:
            print("❌ 没有排名数据")
            return
        
        # 按股息率排序
        sorted_results = sorted(self.ranking_results, key=lambda x: x['dividend_yield'], reverse=True)
        
        print(f"\n🏆 智能股息率最高的前{top_n}只恒生指数成分股:")
        print("=" * 100)
        print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<20} {'最新价格':<12} {'股息率':<10} {'年度分红':<10} {'分红频率':<8}")
        print("-" * 100)
        
        for i, result in enumerate(sorted_results[:top_n], 1):
            print(f"{i:<4} {result['stock_code']:<8} {result['stock_name']:<20} "
                  f"{result['latest_price']:<12.2f} {result['dividend_yield']:<10.4f}% "
                  f"{result['annual_dividend']:<10.2f} {result['frequency_type']:<8}")
        
        # 显示统计信息
        valid_yields = [r['dividend_yield'] for r in sorted_results if r['dividend_yield'] > 0]
        if valid_yields:
            print(f"\n📈 统计信息:")
            print(f"   有分红股票数: {len(valid_yields)}/{len(sorted_results)}")
            print(f"   股息率范围: {min(valid_yields):.4f}% - {max(valid_yields):.4f}%")
            print(f"   平均股息率: {np.mean(valid_yields):.4f}%")
            print(f"   中位数股息率: {np.median(valid_yields):.4f}%")
            
            # 分红频率统计
            frequency_stats = Counter([r['frequency_type'] for r in sorted_results if r['dividend_yield'] > 0])
            print(f"\n📊 分红频率分布:")
            for freq, count in frequency_stats.items():
                print(f"   {freq}分红: {count} 只股票")

def main():
    """主函数"""
    print("🚀 恒生指数成分股智能股息率排名器")
    print("=" * 50)
    
    # 创建排名器
    ranker = HSISmartDividendYieldRanking(
        dividend_data_dir="hsi_dividend_data_20250603_173841",
        output_dir="hsi_smart_dividend_yield_rankings"
    )
    
    try:
        # 计算所有股票排名
        results = ranker.calculate_all_rankings()
        
        if results:
            # 打印前20名
            ranker.print_top_rankings(20)
            
            # 生成排名报告
            report_files = ranker.create_ranking_report()
            
            if report_files:
                print(f"\n📁 报告文件已生成完成!")
            
            print(f"\n✅ 智能排名计算完成!")
        else:
            print("❌ 没有成功计算任何股票排名")
            
    except KeyboardInterrupt:
        print("\n⚠️  用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
