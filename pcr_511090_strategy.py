"""
使用PCR (Put-Call Ratio) 数据对上证50ETF (510050)进行择时回测

This script implements a timing strategy for the 510050 ETF (SSE 50 ETF)
based on the Put-Call Ratio (PCR) data from the SSE 50ETF options.

The strategy uses PCR as a market sentiment indicator:
- High PCR values (market fear/pessimism) -> Decrease position in stock ETF (510050)
- Low PCR values (market optimism) -> Increase position in stock ETF (510050)
"""

import os
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import datetime
import time
import pickle
import akshare as ak
from pcr import load_or_fetch_pcr_data

# Create a directory for cache files if it doesn't exist
CACHE_DIR = 'cache'
if not os.path.exists(CACHE_DIR):
    os.makedirs(CACHE_DIR)

# Create a directory for results
RESULTS_DIR = 'results'
if not os.path.exists(RESULTS_DIR):
    os.makedirs(RESULTS_DIR)

class PCR50ETFStrategy:
    """
    A strategy class that uses PCR data to time investments in the 510050 ETF (SSE 50 ETF).
    """

    def __init__(self, start_date=None, end_date=None):
        """
        Initialize the strategy with date range.

        Args:
            start_date (str): Start date in format 'YYYYMMDD'
            end_date (str): End date in format 'YYYYMMDD'
        """
        # Set default date range if not provided
        if end_date is None:
            self.end_date = datetime.datetime.now().strftime("%Y%m%d")
        else:
            self.end_date = end_date

        if start_date is None:
            # Default to 3 years ago
            self.start_date = (datetime.datetime.now() - datetime.timedelta(days=3*365)).strftime("%Y%m%d")
        else:
            self.start_date = start_date

        # ETF code
        self.etf_code = "510050"  # SSE 50 ETF

        # Data storage
        self.pcr_data = None
        self.etf_data = None
        self.merged_data = None
        self.backtest_results = None

        # Strategy parameters (can be adjusted)
        self.high_pcr_threshold = 1.5  # High PCR threshold for minimum position
        self.low_pcr_threshold = 0.8   # Low PCR threshold for maximum position

        # Position size limits
        self.max_position = 1.0  # Maximum position size (100%)
        self.min_position = 0.3  # Minimum position size (30%)

        print(f"PCR Timing Strategy initialized for {self.etf_code} from {self.start_date} to {self.end_date}")

    def fetch_etf_data(self):
        """
        Fetch historical data for the 510050 ETF with caching.

        Returns:
            DataFrame: Historical price data for the ETF
        """
        cache_file = os.path.join(CACHE_DIR, f"{self.etf_code}_price_data.pkl")

        # Check if data is already cached and not too old (less than 1 day old)
        if os.path.exists(cache_file):
            file_mod_time = os.path.getmtime(cache_file)
            if (time.time() - file_mod_time) < 86400:  # 86400 seconds = 1 day
                try:
                    print(f"Loading cached {self.etf_code} ETF price data")
                    with open(cache_file, 'rb') as f:
                        etf_data = pickle.load(f)
                    print(f"Successfully loaded {len(etf_data)} days of price data from cache")
                    self.etf_data = etf_data
                    return etf_data
                except Exception as e:
                    print(f"Error loading cached price data: {e}")

        # If not cached or cache is too old, fetch from API
        try:
            # Fetch ETF price data
            print(f"Fetching {self.etf_code} ETF price data from API")
            etf_data = ak.fund_etf_hist_sina(symbol=f"sh{self.etf_code}")

            # Convert date column to datetime
            etf_data['date'] = pd.to_datetime(etf_data['date'])

            # Sort by date
            etf_data = etf_data.sort_values('date')

            # Filter by date range
            start_date_dt = datetime.datetime.strptime(self.start_date, "%Y%m%d")
            end_date_dt = datetime.datetime.strptime(self.end_date, "%Y%m%d")
            etf_data = etf_data[(etf_data['date'] >= start_date_dt) & (etf_data['date'] <= end_date_dt)]

            # Cache the data
            with open(cache_file, 'wb') as f:
                pickle.dump(etf_data, f)

            print(f"Successfully fetched and cached {len(etf_data)} days of price data")
            self.etf_data = etf_data
            return etf_data
        except Exception as e:
            print(f"Error fetching {self.etf_code} ETF price data: {e}")
            return pd.DataFrame()

    def prepare_data(self):
        """
        Prepare all necessary data for the strategy.

        Returns:
            DataFrame: Merged data with PCR and ETF price
        """
        # Fetch PCR data
        print("Fetching PCR data...")
        self.pcr_data = load_or_fetch_pcr_data(self.start_date, self.end_date)

        # Fetch ETF data
        print(f"Fetching {self.etf_code} ETF data...")
        self.fetch_etf_data()

        if self.pcr_data is None or self.pcr_data.empty:
            raise ValueError("Failed to fetch PCR data")

        if self.etf_data is None or self.etf_data.empty:
            raise ValueError(f"Failed to fetch {self.etf_code} ETF data")

        # Prepare PCR data for merging
        pcr_df = self.pcr_data.copy()
        pcr_df['date'] = pcr_df['交易日']
        pcr_df = pcr_df[['date', '持仓PCR', '成交PCR']]

        # Merge ETF data with PCR data
        merged_data = pd.merge_asof(
            self.etf_data.sort_values('date'),
            pcr_df.sort_values('date'),
            on='date',
            direction='backward'
        )

        # Forward fill PCR values for days without options data
        merged_data['持仓PCR'] = merged_data['持仓PCR'].fillna(method='ffill')
        merged_data['成交PCR'] = merged_data['成交PCR'].fillna(method='ffill')

        # Calculate daily returns
        merged_data['daily_return'] = merged_data['close'].pct_change()

        # Drop rows with NaN values
        merged_data = merged_data.dropna()

        self.merged_data = merged_data
        print(f"Data prepared with {len(merged_data)} days of data")
        return merged_data

    def generate_signals(self):
        """
        Generate trading signals based on PCR values.

        Returns:
            DataFrame: Data with signals added
        """
        if self.merged_data is None:
            self.prepare_data()

        data = self.merged_data.copy()

        # Calculate position size based on PCR values
        # Linear scaling between min and max position based on PCR
        # For 510050, we use INVERSE relationship: high PCR -> low position, low PCR -> high position
        pcr_range = self.high_pcr_threshold - self.low_pcr_threshold
        position_range = self.max_position - self.min_position

        # Default position (for PCR between thresholds)
        # Inverse relationship: subtract from max_position
        data['signal'] = self.max_position - position_range * (data['持仓PCR'] - self.low_pcr_threshold) / pcr_range

        # Cap positions at min/max values (inverse logic)
        data.loc[data['持仓PCR'] >= self.high_pcr_threshold, 'signal'] = self.min_position  # High PCR -> minimum position
        data.loc[data['持仓PCR'] <= self.low_pcr_threshold, 'signal'] = self.max_position  # Low PCR -> maximum position

        # Shift signals by 1 day to avoid look-ahead bias
        data['signal'] = data['signal'].shift(1)

        # Fill NaN values in the first row with default position
        default_position = 0.5  # 50% position as default
        data['signal'] = data['signal'].fillna(default_position)

        # Calculate strategy returns
        data['strategy_return'] = data['signal'] * data['daily_return']

        # Calculate cumulative returns
        data['cum_return'] = (1 + data['daily_return']).cumprod() - 1
        data['cum_strategy_return'] = (1 + data['strategy_return']).cumprod() - 1

        self.backtest_results = data
        return data

    def calculate_metrics(self):
        """
        Calculate performance metrics for the strategy.

        Returns:
            dict: Performance metrics
        """
        if self.backtest_results is None:
            self.generate_signals()

        data = self.backtest_results

        # Calculate metrics
        buy_hold_return = data['cum_return'].iloc[-1]
        strategy_return = data['cum_strategy_return'].iloc[-1]

        # Calculate annualized returns
        days = (data['date'].iloc[-1] - data['date'].iloc[0]).days
        years = days / 365
        buy_hold_annual = (1 + buy_hold_return) ** (1 / years) - 1
        strategy_annual = (1 + strategy_return) ** (1 / years) - 1

        # Calculate max drawdowns
        buy_hold_drawdown = (data['cum_return'] / data['cum_return'].cummax() - 1).min()
        strategy_drawdown = (data['cum_strategy_return'] / data['cum_strategy_return'].cummax() - 1).min()

        # Calculate volatility
        buy_hold_vol = data['daily_return'].std() * np.sqrt(252)
        strategy_vol = data['strategy_return'].std() * np.sqrt(252)

        # Calculate Sharpe ratio (assuming 0% risk-free rate)
        buy_hold_sharpe = buy_hold_annual / buy_hold_vol if buy_hold_vol != 0 else 0
        strategy_sharpe = strategy_annual / strategy_vol if strategy_vol != 0 else 0

        # Calculate average position size
        avg_position = data['signal'].mean()

        metrics = {
            'Total Return (Buy & Hold)': buy_hold_return,
            'Total Return (Strategy)': strategy_return,
            'Annualized Return (Buy & Hold)': buy_hold_annual,
            'Annualized Return (Strategy)': strategy_annual,
            'Max Drawdown (Buy & Hold)': buy_hold_drawdown,
            'Max Drawdown (Strategy)': strategy_drawdown,
            'Volatility (Buy & Hold)': buy_hold_vol,
            'Volatility (Strategy)': strategy_vol,
            'Sharpe Ratio (Buy & Hold)': buy_hold_sharpe,
            'Sharpe Ratio (Strategy)': strategy_sharpe,
            'Average Position Size': avg_position
        }

        return metrics

    def plot_results(self):
        """
        Plot the backtest results.
        """
        if self.backtest_results is None:
            self.generate_signals()

        data = self.backtest_results
        metrics = self.calculate_metrics()

        # Create figure with 3 subplots
        fig, (ax1, ax2, ax3) = plt.subplots(3, 1, figsize=(12, 15), sharex=True, gridspec_kw={'height_ratios': [2, 1, 1]})

        # Plot cumulative returns
        ax1.plot(data['date'], data['cum_return'] * 100, label='Buy & Hold', color='blue', alpha=0.7)
        ax1.plot(data['date'], data['cum_strategy_return'] * 100, label='PCR Strategy', color='green', linewidth=2)
        ax1.set_ylabel('Cumulative Return (%)')
        ax1.set_title(f'PCR Timing Strategy vs Buy & Hold for {self.etf_code}')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # Add metrics as text
        metrics_text = (
            f"Strategy Return: {metrics['Total Return (Strategy)']:.2%}\n"
            f"Buy & Hold Return: {metrics['Total Return (Buy & Hold)']:.2%}\n"
            f"Strategy Sharpe: {metrics['Sharpe Ratio (Strategy)']:.2f}\n"
            f"Buy & Hold Sharpe: {metrics['Sharpe Ratio (Buy & Hold)']:.2f}\n"
            f"Strategy Max DD: {metrics['Max Drawdown (Strategy)']:.2%}\n"
            f"Buy & Hold Max DD: {metrics['Max Drawdown (Buy & Hold)']:.2%}\n"
            f"Avg Position Size: {metrics['Average Position Size']:.2f}"
        )
        ax1.text(0.02, 0.98, metrics_text, transform=ax1.transAxes,
                 verticalalignment='top', bbox=dict(boxstyle='round', facecolor='white', alpha=0.7))

        # Plot ETF price
        ax2.plot(data['date'], data['close'], color='black', label=f'{self.etf_code} Price')
        ax2.set_ylabel('ETF Price (CNY)')

        # Highlight position changes
        position_changes = data[data['signal'] != data['signal'].shift(1)]
        if not position_changes.empty:
            ax2.scatter(position_changes['date'], position_changes['close'],
                       color='orange', marker='o', s=50, label='Position Change')

        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # Plot PCR values and position size
        ax3.plot(data['date'], data['持仓PCR'], label='Open Interest PCR', color='purple')
        ax3.axhline(y=self.high_pcr_threshold, color='green', linestyle='--', alpha=0.7,
                   label=f'High PCR Threshold ({self.high_pcr_threshold})')
        ax3.axhline(y=self.low_pcr_threshold, color='red', linestyle='--', alpha=0.7,
                   label=f'Low PCR Threshold ({self.low_pcr_threshold})')

        # Add position size on secondary y-axis
        ax3b = ax3.twinx()
        ax3b.plot(data['date'], data['signal'] * 100, color='green', linestyle='-', alpha=0.5,
                 label='Position Size %')
        ax3b.set_ylabel('Position Size (%)', color='green')
        ax3b.tick_params(axis='y', labelcolor='green')
        ax3b.set_ylim(0, 110)

        # Set labels and legend
        ax3.set_ylabel('PCR Value', color='purple')
        ax3.tick_params(axis='y', labelcolor='purple')
        ax3.set_xlabel('Date')

        # Combine legends from both y-axes
        lines1, labels1 = ax3.get_legend_handles_labels()
        lines2, labels2 = ax3b.get_legend_handles_labels()
        ax3.legend(lines1 + lines2, labels1 + labels2, loc='upper left')

        ax3.grid(True, alpha=0.3)

        # Format x-axis dates
        fig.autofmt_xdate()

        # Save the plot
        plt.tight_layout()
        plt.savefig(os.path.join(RESULTS_DIR, f'pcr_{self.etf_code}_strategy.png'), dpi=300)
        plt.close()

        print(f"Results plot saved to {os.path.join(RESULTS_DIR, f'pcr_{self.etf_code}_strategy.png')}")

        # Return metrics for display
        return metrics

def main():
    # Create and run the strategy
    strategy = PCR50ETFStrategy()

    try:
        # Prepare data and generate signals
        strategy.prepare_data()
        strategy.generate_signals()

        # Calculate and display metrics
        metrics = strategy.calculate_metrics()

        print("\n===== PCR择时策略回测结果 =====")
        print(f"总收益率 (买入持有): {metrics['Total Return (Buy & Hold)']:.2%}")
        print(f"总收益率 (PCR策略): {metrics['Total Return (Strategy)']:.2%}")
        print(f"年化收益率 (买入持有): {metrics['Annualized Return (Buy & Hold)']:.2%}")
        print(f"年化收益率 (PCR策略): {metrics['Annualized Return (Strategy)']:.2%}")
        print(f"最大回撤 (买入持有): {metrics['Max Drawdown (Buy & Hold)']:.2%}")
        print(f"最大回撤 (PCR策略): {metrics['Max Drawdown (Strategy)']:.2%}")
        print(f"波动率 (买入持有): {metrics['Volatility (Buy & Hold)']:.2%}")
        print(f"波动率 (PCR策略): {metrics['Volatility (Strategy)']:.2%}")
        print(f"夏普比率 (买入持有): {metrics['Sharpe Ratio (Buy & Hold)']:.2f}")
        print(f"夏普比率 (PCR策略): {metrics['Sharpe Ratio (Strategy)']:.2f}")
        print(f"平均仓位: {metrics['Average Position Size']:.2f}")

        # Plot results
        strategy.plot_results()

        print("\n策略说明:")
        print(f"1. 当PCR值高于{strategy.high_pcr_threshold}时，持有{strategy.min_position*100:.0f}%仓位的{strategy.etf_code}") # 注意这里反转了逻辑
        print(f"2. 当PCR值低于{strategy.low_pcr_threshold}时，持有{strategy.max_position*100:.0f}%仓位的{strategy.etf_code}") # 注意这里反转了逻辑
        print(f"3. 当PCR值介于{strategy.low_pcr_threshold}和{strategy.high_pcr_threshold}之间时，线性调整仓位")
        print(f"4. 策略逻辑：PCR高表示市场恐慌/悲观，减少股票仓位；PCR低表示市场乐观，增加股票仓位")

    except Exception as e:
        print(f"Error running strategy: {e}")

if __name__ == "__main__":
    main()
