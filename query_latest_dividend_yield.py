#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
from datetime import datetime

def query_latest_dividend_yield():
    """查询最新的股息率数据"""
    try:
        # 连接数据库
        conn = sqlite3.connect('hsi_factor_data.db')
        
        # 查询6月6日的股息率数据
        query = '''
        SELECT stock_code, date, dividend_yield
        FROM daily_dividend_yield 
        WHERE date = '2025-06-06' AND dividend_yield IS NOT NULL AND dividend_yield > 0
        ORDER BY dividend_yield DESC
        '''
        
        df = pd.read_sql_query(query, conn)
        
        if len(df) > 0:
            print('🏆 恒生指数成分股最新智能股息率排名 (2025-06-06)')
            print('=' * 60)
            print(f'{"排名":<4} {"股票代码":<10} {"股息率":<10}')
            print('-' * 60)
            
            for i, row in df.iterrows():
                print(f'{i+1:<4} {row["stock_code"]:<10} {row["dividend_yield"]:<10.2f}%')
            
            print(f'\n共找到 {len(df)} 只有股息率数据的股票')
            print(f'数据日期: 2025-06-06')
            
            # 显示前15名用于文章
            print('\n📊 前15名高股息率股票:')
            top15 = df.head(15)
            for i, row in top15.iterrows():
                print(f'{i+1}. {row["stock_code"]} - {row["dividend_yield"]:.2f}%')
                
            return df
            
        else:
            print('❌ 未找到2025-06-06的股息率数据')
            
            # 查询最新可用日期
            query_latest = '''
            SELECT DISTINCT date 
            FROM daily_dividend_yield 
            WHERE dividend_yield IS NOT NULL AND dividend_yield > 0
            ORDER BY date DESC
            LIMIT 5
            '''
            
            conn_latest = sqlite3.connect('hsi_factor_data.db')
            df_dates = pd.read_sql_query(query_latest, conn_latest)
            conn_latest.close()
            
            print('\n最新可用的数据日期:')
            print(df_dates)
            
            if len(df_dates) > 0:
                latest_date = df_dates.iloc[0]['date']
                print(f'\n尝试查询最新日期: {latest_date}')
                
                query_latest_data = f'''
                SELECT stock_code, date, dividend_yield
                FROM daily_dividend_yield 
                WHERE date = '{latest_date}' AND dividend_yield IS NOT NULL AND dividend_yield > 0
                ORDER BY dividend_yield DESC
                '''
                
                df_latest = pd.read_sql_query(query_latest_data, conn)
                
                if len(df_latest) > 0:
                    print(f'\n🏆 恒生指数成分股最新智能股息率排名 ({latest_date})')
                    print('=' * 60)
                    print(f'{"排名":<4} {"股票代码":<10} {"股息率":<10}')
                    print('-' * 60)
                    
                    for i, row in df_latest.iterrows():
                        print(f'{i+1:<4} {row["stock_code"]:<10} {row["dividend_yield"]:<10.2f}%')
                    
                    print(f'\n📊 前15名高股息率股票:')
                    top15 = df_latest.head(15)
                    for i, row in top15.iterrows():
                        print(f'{i+1}. {row["stock_code"]} - {row["dividend_yield"]:.2f}%')
                    
                    return df_latest
        
        conn.close()
        return None
        
    except Exception as e:
        print(f'错误: {e}')
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    query_latest_dividend_yield()
