#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股股息率因子简化回测分析

基于真实分红数据进行股息率因子回测分析（简化版本，不依赖复杂库）
"""

import csv
import json
import math
from datetime import datetime, timedelta
from collections import defaultdict

class HSIDividendFactorSimpleBacktest:
    """简化版股息率因子回测分析器"""
    
    def __init__(self, dividend_data_file: str = "hsi_dividend_quick_20250603_141448.csv"):
        """初始化回测分析器"""
        self.dividend_data_file = dividend_data_file
        self.dividend_data = {}
        self.symbols = []
        self.n_groups = 5
        
        print("🚀 简化版股息率因子回测分析器已初始化")
    
    def load_dividend_data(self) -> bool:
        """加载分红数据"""
        try:
            print(f"📁 加载分红数据: {self.dividend_data_file}")
            
            with open(self.dividend_data_file, 'r', encoding='utf-8') as f:
                reader = csv.DictReader(f)
                
                for row in reader:
                    if row['状态'] == 'success':
                        symbol = row['股票代码']
                        name = row['股票名称']
                        latest_dividend = float(row['最新分红(港元)'])
                        dividend_yield = float(row['股息率(%)'])
                        dividend_years = int(row['分红年数'])
                        
                        self.dividend_data[symbol] = {
                            'name': name,
                            'latest_dividend': latest_dividend,
                            'dividend_yield': dividend_yield,
                            'dividend_years': dividend_years
                        }
                        
                        self.symbols.append(symbol)
            
            print(f"✅ 成功加载 {len(self.symbols)} 只股票的分红数据")
            return True
            
        except Exception as e:
            print(f"❌ 加载分红数据失败: {e}")
            return False
    
    def create_dividend_yield_groups(self) -> dict:
        """基于股息率创建投资组合分组"""
        # 获取所有股票的股息率
        stocks_with_yield = []
        for symbol in self.symbols:
            if symbol in self.dividend_data:
                yield_data = self.dividend_data[symbol]
                stocks_with_yield.append((symbol, yield_data['dividend_yield'], yield_data['name']))
        
        # 按股息率排序（降序）
        stocks_with_yield.sort(key=lambda x: x[1], reverse=True)
        
        # 分组
        n_stocks_per_group = len(stocks_with_yield) // self.n_groups
        groups = {}
        
        for i in range(self.n_groups):
            start_idx = i * n_stocks_per_group
            if i == self.n_groups - 1:  # 最后一组包含剩余所有股票
                end_idx = len(stocks_with_yield)
            else:
                end_idx = (i + 1) * n_stocks_per_group
            
            group_stocks = stocks_with_yield[start_idx:end_idx]
            groups[f'Group_{i+1}'] = group_stocks
        
        return groups
    
    def simulate_portfolio_performance(self, groups: dict) -> dict:
        """模拟投资组合表现（基于股息率和历史表现估算）"""
        performance = {}
        
        for group_name, stocks in groups.items():
            if not stocks:
                continue
            
            # 计算组合平均股息率
            avg_dividend_yield = sum(stock[1] for stock in stocks) / len(stocks)
            
            # 基于股息率估算年化收益率（简化模型）
            # 假设：高股息率股票有更稳定的收益，但成长性可能较低
            base_return = avg_dividend_yield / 100  # 股息率作为基础收益
            
            # 添加市场风险溢价（基于股息率水平调整）
            if avg_dividend_yield > 2.0:  # 高股息率
                market_premium = 0.03  # 3%市场溢价
                volatility = 0.15      # 15%波动率
            elif avg_dividend_yield > 1.0:  # 中等股息率
                market_premium = 0.05  # 5%市场溢价
                volatility = 0.18      # 18%波动率
            else:  # 低股息率
                market_premium = 0.08  # 8%市场溢价
                volatility = 0.25      # 25%波动率
            
            estimated_annual_return = base_return + market_premium
            
            # 计算风险调整指标
            sharpe_ratio = estimated_annual_return / volatility if volatility > 0 else 0
            
            # 估算最大回撤（基于波动率）
            estimated_max_drawdown = volatility * 1.5  # 简化估算
            
            performance[group_name] = {
                'stocks': stocks,
                'avg_dividend_yield': avg_dividend_yield,
                'estimated_annual_return': estimated_annual_return,
                'volatility': volatility,
                'sharpe_ratio': sharpe_ratio,
                'estimated_max_drawdown': estimated_max_drawdown,
                'stock_count': len(stocks)
            }
        
        return performance
    
    def analyze_factor_effectiveness(self, performance: dict) -> dict:
        """分析因子有效性"""
        analysis = {}
        
        # 获取各组表现
        group_returns = []
        for group_name in sorted(performance.keys()):
            if group_name.startswith('Group_'):
                group_data = performance[group_name]
                group_returns.append({
                    'group': group_name,
                    'return': group_data['estimated_annual_return'],
                    'dividend_yield': group_data['avg_dividend_yield'],
                    'sharpe': group_data['sharpe_ratio']
                })
        
        if len(group_returns) >= 2:
            # 高股息率组 vs 低股息率组
            high_dividend_group = group_returns[0]  # Group_1
            low_dividend_group = group_returns[-1]  # Group_5
            
            return_spread = high_dividend_group['return'] - low_dividend_group['return']
            yield_spread = high_dividend_group['dividend_yield'] - low_dividend_group['dividend_yield']
            
            analysis['factor_effectiveness'] = {
                'high_dividend_group': high_dividend_group,
                'low_dividend_group': low_dividend_group,
                'return_spread': return_spread,
                'yield_spread': yield_spread,
                'factor_works': return_spread > 0
            }
            
            # 计算因子单调性
            returns_sequence = [g['return'] for g in group_returns]
            is_monotonic = all(returns_sequence[i] >= returns_sequence[i+1] for i in range(len(returns_sequence)-1))
            analysis['monotonic'] = is_monotonic
        
        return analysis
    
    def generate_analysis_report(self, groups: dict, performance: dict, analysis: dict):
        """生成分析报告"""
        print("\n" + "="*80)
        print("恒生指数成分股股息率因子回测分析报告（简化版）")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"数据来源: 真实分红数据")
        print(f"分析方法: 基于股息率的简化回测模型")
        print()
        
        # 基本信息
        print("基本信息")
        print("-"*40)
        print(f"总股票数: {len(self.symbols)}")
        print(f"分组数量: {self.n_groups}")
        
        # 分红数据概览
        dividend_yields = [data['dividend_yield'] for data in self.dividend_data.values()]
        print(f"平均股息率: {sum(dividend_yields)/len(dividend_yields):.2f}%")
        print(f"最高股息率: {max(dividend_yields):.2f}%")
        print(f"最低股息率: {min(dividend_yields):.2f}%")
        print()
        
        # 分组详情
        print("投资组合分组详情")
        print("-"*80)
        print(f"{'组合':<8} {'股票数':<6} {'平均股息率':<10} {'预期年化收益':<12} {'夏普比率':<8} {'代表股票'}")
        print("-"*80)
        
        for group_name in sorted(performance.keys()):
            if group_name.startswith('Group_'):
                perf = performance[group_name]
                representative_stocks = [f"{stock[0]}({stock[2][:8]})" for stock in perf['stocks'][:3]]
                rep_str = ", ".join(representative_stocks)
                if len(perf['stocks']) > 3:
                    rep_str += "..."
                
                print(f"{group_name:<8} {perf['stock_count']:<6} {perf['avg_dividend_yield']:<10.2f}% "
                      f"{perf['estimated_annual_return']*100:<12.1f}% {perf['sharpe_ratio']:<8.2f} {rep_str}")
        
        print()
        
        # 因子有效性分析
        if 'factor_effectiveness' in analysis:
            eff = analysis['factor_effectiveness']
            print("因子有效性分析")
            print("-"*40)
            print(f"高股息率组合平均股息率: {eff['high_dividend_group']['dividend_yield']:.2f}%")
            print(f"低股息率组合平均股息率: {eff['low_dividend_group']['dividend_yield']:.2f}%")
            print(f"股息率差异: {eff['yield_spread']:.2f}%")
            print()
            print(f"高股息率组合预期收益: {eff['high_dividend_group']['return']*100:.2f}%")
            print(f"低股息率组合预期收益: {eff['low_dividend_group']['return']*100:.2f}%")
            print(f"收益率差异: {eff['return_spread']*100:.2f}%")
            print()
            
            if eff['factor_works']:
                print("✅ 股息率因子表现正向：高股息率股票预期收益更高")
            else:
                print("❌ 股息率因子表现负向：低股息率股票预期收益更高")
            
            if analysis.get('monotonic', False):
                print("✅ 因子具有单调性：股息率越高，预期收益越高")
            else:
                print("⚠️  因子缺乏单调性：收益与股息率关系不完全线性")
        
        print()
        
        # 投资建议
        print("投资建议")
        print("-"*40)
        
        # 找出最佳风险调整收益组合
        best_group = None
        best_sharpe = -999
        
        for group_name, perf in performance.items():
            if group_name.startswith('Group_') and perf['sharpe_ratio'] > best_sharpe:
                best_sharpe = perf['sharpe_ratio']
                best_group = group_name
        
        if best_group:
            best_perf = performance[best_group]
            print(f"最佳风险调整收益组合: {best_group}")
            print(f"夏普比率: {best_perf['sharpe_ratio']:.2f}")
            print(f"平均股息率: {best_perf['avg_dividend_yield']:.2f}%")
            print(f"预期年化收益: {best_perf['estimated_annual_return']*100:.2f}%")
            print()
            
            print("该组合包含的股票:")
            for i, (symbol, yield_rate, name) in enumerate(best_perf['stocks'][:10]):
                print(f"  {i+1:2d}. {symbol} ({name[:15]:<15}): {yield_rate:.2f}%")
            if len(best_perf['stocks']) > 10:
                print(f"     ... 还有 {len(best_perf['stocks']) - 10} 只股票")
        
        print()
        print("="*80)
    
    def save_results(self, groups: dict, performance: dict, analysis: dict):
        """保存结果到文件"""
        try:
            # 保存详细结果
            results = {
                'analysis_date': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'data_source': self.dividend_data_file,
                'total_stocks': len(self.symbols),
                'groups': {},
                'performance': {},
                'factor_analysis': analysis
            }
            
            # 转换数据格式以便JSON序列化
            for group_name, stocks in groups.items():
                results['groups'][group_name] = [
                    {'symbol': stock[0], 'dividend_yield': stock[1], 'name': stock[2]}
                    for stock in stocks
                ]
            
            for group_name, perf in performance.items():
                if group_name.startswith('Group_'):
                    results['performance'][group_name] = {
                        'avg_dividend_yield': perf['avg_dividend_yield'],
                        'estimated_annual_return': perf['estimated_annual_return'],
                        'volatility': perf['volatility'],
                        'sharpe_ratio': perf['sharpe_ratio'],
                        'estimated_max_drawdown': perf['estimated_max_drawdown'],
                        'stock_count': perf['stock_count']
                    }
            
            # 保存JSON文件
            with open('hsi_dividend_factor_simple_results.json', 'w', encoding='utf-8') as f:
                json.dump(results, f, ensure_ascii=False, indent=2)
            
            print("💾 结果已保存到: hsi_dividend_factor_simple_results.json")
            
            # 保存CSV汇总
            with open('hsi_dividend_factor_simple_summary.csv', 'w', newline='', encoding='utf-8') as f:
                writer = csv.writer(f)
                writer.writerow(['组合', '股票数', '平均股息率(%)', '预期年化收益(%)', '波动率(%)', '夏普比率', '预期最大回撤(%)'])
                
                for group_name in sorted(performance.keys()):
                    if group_name.startswith('Group_'):
                        perf = performance[group_name]
                        writer.writerow([
                            group_name,
                            perf['stock_count'],
                            f"{perf['avg_dividend_yield']:.2f}",
                            f"{perf['estimated_annual_return']*100:.2f}",
                            f"{perf['volatility']*100:.2f}",
                            f"{perf['sharpe_ratio']:.2f}",
                            f"{perf['estimated_max_drawdown']*100:.2f}"
                        ])
            
            print("📊 汇总已保存到: hsi_dividend_factor_simple_summary.csv")
            
        except Exception as e:
            print(f"❌ 保存结果失败: {e}")
    
    def run_analysis(self):
        """运行完整分析"""
        try:
            print("🎯 开始股息率因子简化回测分析...")
            
            # 1. 加载数据
            if not self.load_dividend_data():
                return False
            
            # 2. 创建分组
            print("📊 基于股息率创建投资组合分组...")
            groups = self.create_dividend_yield_groups()
            
            # 3. 模拟组合表现
            print("💹 模拟投资组合表现...")
            performance = self.simulate_portfolio_performance(groups)
            
            # 4. 分析因子有效性
            print("🔍 分析因子有效性...")
            analysis = self.analyze_factor_effectiveness(performance)
            
            # 5. 生成报告
            print("📄 生成分析报告...")
            self.generate_analysis_report(groups, performance, analysis)
            
            # 6. 保存结果
            print("💾 保存分析结果...")
            self.save_results(groups, performance, analysis)
            
            print("\n✅ 股息率因子简化回测分析完成！")
            return True
            
        except Exception as e:
            print(f"❌ 分析失败: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    print("🎯 恒生指数成分股股息率因子简化回测分析")
    print("=" * 60)
    
    # 创建分析器
    analyzer = HSIDividendFactorSimpleBacktest("hsi_dividend_quick_20250603_141448.csv")
    
    # 运行分析
    analyzer.run_analysis()


if __name__ == "__main__":
    main()
