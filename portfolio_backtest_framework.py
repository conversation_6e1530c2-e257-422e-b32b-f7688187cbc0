#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
真实的分组回测框架
基于定期调仓的组合回测，更接近实际投资情况
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
import os
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class PortfolioBacktest:
    """组合回测框架"""
    
    def __init__(self, 
                 financial_db_path: str = "ganggutong_financial_data.db",
                 price_db_path: str = "ganggutong_10year_data.db"):
        self.financial_db_path = financial_db_path
        self.price_db_path = price_db_path
        self.financial_data = None
        self.price_data = None
        
    def load_data(self):
        """加载数据并优化存储结构"""
        print("📊 加载数据...")

        # 加载财务数据
        conn = sqlite3.connect(self.financial_db_path)
        query = """
        SELECT stock_code, report_date, period_type, revenue
        FROM financial_data
        WHERE revenue IS NOT NULL
        ORDER BY stock_code, report_date
        """
        self.financial_data = pd.read_sql_query(query, conn)
        conn.close()

        self.financial_data['report_date'] = pd.to_datetime(self.financial_data['report_date'])
        self.financial_data['revenue'] = pd.to_numeric(self.financial_data['revenue'], errors='coerce')

        # 加载价格数据并转换为字典结构以提高查询效率
        conn = sqlite3.connect(self.price_db_path)
        query = """
        SELECT stock_code, date, close
        FROM stock_prices
        WHERE close IS NOT NULL
        ORDER BY stock_code, date
        """
        price_df = pd.read_sql_query(query, conn)
        conn.close()

        price_df['date'] = pd.to_datetime(price_df['date'])
        price_df['close'] = pd.to_numeric(price_df['close'], errors='coerce')

        # 转换为字典结构以提高查询效率
        print("🔄 优化价格数据结构...")
        self.price_data = {}
        for stock_code in price_df['stock_code'].unique():
            stock_prices = price_df[price_df['stock_code'] == stock_code].copy()
            stock_prices = stock_prices.set_index('date').sort_index()
            self.price_data[stock_code] = stock_prices['close']

        print(f"✅ 财务数据: {len(self.financial_data)} 条")
        print(f"✅ 价格数据: {len(self.price_data)} 只股票")
        print(f"✅ 价格时间范围: {price_df['date'].min()} 到 {price_df['date'].max()}")
    
    def calculate_factors(self):
        """计算因子"""
        print("🧮 计算营收增长加速度因子...")
        
        factor_data = []
        
        for stock_code in self.financial_data['stock_code'].unique():
            stock_data = self.financial_data[
                self.financial_data['stock_code'] == stock_code
            ].copy().sort_values('report_date')
            
            # 分别处理季度和年度数据
            for period_type in ['quarterly', 'annual']:
                period_data = stock_data[
                    stock_data['period_type'] == period_type
                ].copy()
                
                if len(period_data) < 6:  # 至少需要6个数据点
                    continue
                
                # 计算营收增长率
                if period_type == 'quarterly':
                    period_data['revenue_yoy'] = period_data['revenue'].pct_change(4) * 100
                else:
                    period_data['revenue_yoy'] = period_data['revenue'].pct_change(1) * 100
                
                # 计算营收增长加速度
                period_data['revenue_acceleration'] = period_data['revenue_yoy'].diff()
                
                factor_data.append(period_data)
        
        if factor_data:
            self.factor_data = pd.concat(factor_data, ignore_index=True)
            self.factor_data = self.factor_data.dropna(subset=['revenue_acceleration'])
            print(f"✅ 因子数据: {len(self.factor_data)} 条")
        else:
            raise ValueError("无法计算因子数据")
    
    def generate_rebalance_dates(self, start_date: str, end_date: str, frequency: str = 'Q'):
        """生成调仓日期"""
        dates = pd.date_range(start=start_date, end=end_date, freq=frequency)
        return [d.date() for d in dates]
    
    def get_factor_scores_at_date(self, target_date: pd.Timestamp, lookback_days: int = 90):
        """获取指定日期的因子得分"""
        # 获取目标日期前lookback_days天内的最新财务数据
        start_date = target_date - timedelta(days=lookback_days)
        
        recent_data = self.factor_data[
            (self.factor_data['report_date'] >= start_date) & 
            (self.factor_data['report_date'] <= target_date)
        ].copy()
        
        if recent_data.empty:
            return pd.DataFrame()
        
        # 对每只股票，取最新的财务数据
        latest_data = recent_data.groupby('stock_code').last().reset_index()
        
        # 标准化因子得分
        if len(latest_data) >= 10:
            latest_data['factor_score'] = (
                latest_data['revenue_acceleration'] - latest_data['revenue_acceleration'].mean()
            ) / latest_data['revenue_acceleration'].std()
            
            latest_data['factor_rank'] = latest_data['revenue_acceleration'].rank(pct=True)
        
        return latest_data
    
    def get_prices_at_date(self, target_date: pd.Timestamp, stock_list: List[str]):
        """获取指定日期的股票价格"""
        target_prices = {}

        for stock_code in stock_list:
            if stock_code in self.price_data:
                stock_prices = self.price_data[stock_code]
                # 获取目标日期当天或之前最近的价格
                valid_prices = stock_prices[stock_prices.index <= target_date]

                if not valid_prices.empty:
                    latest_price = valid_prices.iloc[-1]
                    target_prices[stock_code] = latest_price

        return target_prices
    
    def run_portfolio_backtest(self,
                             start_date: str = "2020-01-01",
                             end_date: str = "2024-12-31",
                             rebalance_freq: str = "Q",
                             n_groups: int = 5):
        """运行5分组组合回测"""
        print(f"🚀 开始5分组组合回测: {start_date} 到 {end_date}")
        print(f"📅 调仓频率: {rebalance_freq} (Q=季度, M=月度)")
        print(f"📊 分组数量: {n_groups}组 (G1=最低增长加速度, G{n_groups}=最高增长加速度)")
        
        # 生成调仓日期
        rebalance_dates = self.generate_rebalance_dates(start_date, end_date, rebalance_freq)
        print(f"📅 调仓日期: {len(rebalance_dates)} 次")
        
        portfolio_returns = []
        
        for i, rebal_date in enumerate(rebalance_dates[:-1]):
            next_rebal_date = rebalance_dates[i + 1]
            
            print(f"\n📅 调仓日期: {rebal_date}")
            
            # 获取因子得分
            target_date = pd.Timestamp(rebal_date)
            factor_scores = self.get_factor_scores_at_date(target_date)
            
            if len(factor_scores) < 20:
                print(f"⚠️ 可用股票数量不足: {len(factor_scores)}")
                continue
            
            # 构建5分组组合
            n_stocks = len(factor_scores)

            # 按营收增长加速度分为5组
            factor_scores['group'] = pd.qcut(
                factor_scores['revenue_acceleration'],
                n_groups,
                labels=[f'G{i}' for i in range(1, n_groups + 1)],
                duplicates='drop'
            )

            # 获取各组股票
            group_stocks = {}
            for group in factor_scores['group'].unique():
                if pd.notna(group):
                    group_stocks[group] = factor_scores[factor_scores['group'] == group]['stock_code'].tolist()

            print(f"📊 分组情况:")
            for group, stocks in group_stocks.items():
                print(f"  {group}: {len(stocks)} 只股票")
            
            # 获取期初价格
            start_prices = self.get_prices_at_date(target_date, long_stocks + short_stocks)
            
            # 获取期末价格
            end_date_ts = pd.Timestamp(next_rebal_date)
            end_prices = self.get_prices_at_date(end_date_ts, long_stocks + short_stocks)
            
            # 计算组合收益率
            long_returns = []
            short_returns = []
            
            for stock in long_stocks:
                if stock in start_prices and stock in end_prices:
                    ret = (end_prices[stock] / start_prices[stock] - 1) * 100
                    long_returns.append(ret)
            
            for stock in short_stocks:
                if stock in start_prices and stock in end_prices:
                    ret = (end_prices[stock] / start_prices[stock] - 1) * 100
                    short_returns.append(ret)
            
            if long_returns and short_returns:
                long_return = np.mean(long_returns)
                short_return = np.mean(short_returns)
                portfolio_return = long_return - short_return  # 多空组合收益
                
                portfolio_returns.append({
                    'rebalance_date': rebal_date,
                    'next_rebalance_date': next_rebal_date,
                    'long_return': long_return,
                    'short_return': short_return,
                    'portfolio_return': portfolio_return,
                    'n_long_stocks': len(long_returns),
                    'n_short_stocks': len(short_returns)
                })
                
                print(f"📊 多头收益: {long_return:.2f}%")
                print(f"📊 空头收益: {short_return:.2f}%")
                print(f"📊 组合收益: {portfolio_return:.2f}%")
        
        return pd.DataFrame(portfolio_returns)

    def create_visualizations(self, results_df: pd.DataFrame):
        """创建可视化图表"""
        if results_df.empty:
            return

        print("📊 创建可视化图表...")

        # 创建输出目录
        output_dir = "portfolio_backtest_results"
        os.makedirs(output_dir, exist_ok=True)

        # 计算累计收益
        results_df['cumulative_return'] = (1 + results_df['portfolio_return'] / 100).cumprod()
        results_df['cumulative_long'] = (1 + results_df['long_return'] / 100).cumprod()
        results_df['cumulative_short'] = (1 + results_df['short_return'] / 100).cumprod()

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))

        # 1. 累计收益曲线
        ax1 = axes[0, 0]
        ax1.plot(results_df['rebalance_date'], results_df['cumulative_return'],
                linewidth=2, label='多空组合', color='red')
        ax1.plot(results_df['rebalance_date'], results_df['cumulative_long'],
                linewidth=2, label='多头组合', color='green', alpha=0.7)
        ax1.plot(results_df['rebalance_date'], results_df['cumulative_short'],
                linewidth=2, label='空头组合', color='blue', alpha=0.7)
        ax1.axhline(y=1, color='black', linestyle='--', alpha=0.5)
        ax1.set_title('累计收益曲线')
        ax1.set_xlabel('日期')
        ax1.set_ylabel('累计收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 单期收益分布
        ax2 = axes[0, 1]
        ax2.hist(results_df['portfolio_return'], bins=20, alpha=0.7, edgecolor='black')
        ax2.axvline(x=results_df['portfolio_return'].mean(), color='red',
                   linestyle='--', label=f'平均收益: {results_df["portfolio_return"].mean():.2f}%')
        ax2.set_title('单期收益分布')
        ax2.set_xlabel('收益率 (%)')
        ax2.set_ylabel('频数')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 多空收益对比
        ax3 = axes[1, 0]
        x = np.arange(len(results_df))
        width = 0.35
        ax3.bar(x - width/2, results_df['long_return'], width,
               label='多头收益', alpha=0.7, color='green')
        ax3.bar(x + width/2, results_df['short_return'], width,
               label='空头收益', alpha=0.7, color='red')
        ax3.set_title('多空收益对比')
        ax3.set_xlabel('调仓期间')
        ax3.set_ylabel('收益率 (%)')
        ax3.legend()
        ax3.grid(True, alpha=0.3)

        # 4. 滚动夏普比率
        ax4 = axes[1, 1]
        if len(results_df) >= 4:
            rolling_sharpe = []
            for i in range(3, len(results_df)):
                window_returns = results_df['portfolio_return'].iloc[i-3:i+1]
                if window_returns.std() > 0:
                    sharpe = window_returns.mean() / window_returns.std()
                    rolling_sharpe.append(sharpe)
                else:
                    rolling_sharpe.append(0)

            if rolling_sharpe:
                ax4.plot(results_df['rebalance_date'].iloc[3:], rolling_sharpe,
                        linewidth=2, color='purple')
                ax4.axhline(y=0, color='black', linestyle='--', alpha=0.5)
                ax4.set_title('滚动夏普比率 (4期)')
                ax4.set_xlabel('日期')
                ax4.set_ylabel('夏普比率')
                ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(f'{output_dir}/portfolio_performance.png', dpi=300, bbox_inches='tight')
        plt.close()

        print(f"✅ 图表已保存到 {output_dir}/portfolio_performance.png")
    
    def analyze_results(self, results_df: pd.DataFrame):
        """分析回测结果"""
        print("\n" + "="*60)
        print("📊 营收增长加速度因子组合回测结果分析")
        print("="*60)

        if results_df.empty:
            print("❌ 无有效回测结果")
            return

        # 基本统计
        print(f"📅 回测期间: {results_df['rebalance_date'].min()} 到 {results_df['next_rebalance_date'].max()}")
        print(f"🔄 调仓次数: {len(results_df)}")
        print(f"📈 平均多头收益: {results_df['long_return'].mean():.2f}%")
        print(f"📉 平均空头收益: {results_df['short_return'].mean():.2f}%")
        print(f"💰 平均组合收益: {results_df['portfolio_return'].mean():.2f}%")

        # 胜率分析
        win_rate = (results_df['portfolio_return'] > 0).mean() * 100
        print(f"🎯 胜率: {win_rate:.1f}%")

        # 累计收益
        results_df['cumulative_return'] = (1 + results_df['portfolio_return'] / 100).cumprod()
        total_return = (results_df['cumulative_return'].iloc[-1] - 1) * 100
        print(f"📈 累计收益: {total_return:.2f}%")

        # 年化收益和波动率
        periods_per_year = 4 if len(results_df) > 0 else 1  # 假设季度调仓
        annualized_return = (results_df['cumulative_return'].iloc[-1] ** (periods_per_year / len(results_df)) - 1) * 100
        annualized_vol = results_df['portfolio_return'].std() * np.sqrt(periods_per_year)

        print(f"📊 年化收益: {annualized_return:.2f}%")
        print(f"📊 年化波动: {annualized_vol:.2f}%")

        if annualized_vol > 0:
            sharpe_ratio = annualized_return / annualized_vol
            print(f"📊 夏普比率: {sharpe_ratio:.2f}")

        # 最大回撤分析
        cumulative = results_df['cumulative_return']
        running_max = cumulative.expanding().max()
        drawdown = (cumulative - running_max) / running_max * 100
        max_drawdown = drawdown.min()
        print(f"📉 最大回撤: {max_drawdown:.2f}%")

        # 分年度分析
        results_df['year'] = pd.to_datetime(results_df['rebalance_date']).dt.year
        yearly_stats = results_df.groupby('year').agg({
            'portfolio_return': ['mean', 'std', 'count'],
            'long_return': 'mean',
            'short_return': 'mean'
        }).round(2)

        print(f"\n📅 分年度表现:")
        for year in yearly_stats.index:
            year_data = results_df[results_df['year'] == year]
            year_return = (1 + year_data['portfolio_return'] / 100).prod() - 1
            win_rate_year = (year_data['portfolio_return'] > 0).mean() * 100
            print(f"  {year}年: 收益{year_return*100:.1f}%, 胜率{win_rate_year:.0f}%, 调仓{len(year_data)}次")

        # 保存详细报告
        self.save_detailed_report(results_df)

        return results_df

    def save_detailed_report(self, results_df: pd.DataFrame):
        """保存详细报告"""
        output_dir = "portfolio_backtest_results"
        os.makedirs(output_dir, exist_ok=True)

        report = []
        report.append("=" * 80)
        report.append("营收增长加速度因子定期调仓组合回测报告")
        report.append("=" * 80)
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")

        # 策略说明
        report.append("📋 策略说明:")
        report.append("  因子: 营收增长加速度 (revenue_acceleration)")
        report.append("  调仓频率: 季度调仓")
        report.append("  组合构建: 前30%做多，后30%做空")
        report.append("  权重: 等权重")
        report.append("")

        # 回测结果
        if not results_df.empty:
            cumulative_return = (results_df['cumulative_return'].iloc[-1] - 1) * 100
            win_rate = (results_df['portfolio_return'] > 0).mean() * 100
            periods_per_year = 4
            annualized_return = (results_df['cumulative_return'].iloc[-1] ** (periods_per_year / len(results_df)) - 1) * 100
            annualized_vol = results_df['portfolio_return'].std() * np.sqrt(periods_per_year)
            sharpe_ratio = annualized_return / annualized_vol if annualized_vol > 0 else 0

            # 最大回撤
            cumulative = results_df['cumulative_return']
            running_max = cumulative.expanding().max()
            drawdown = (cumulative - running_max) / running_max * 100
            max_drawdown = drawdown.min()

            report.append("📊 核心指标:")
            report.append(f"  累计收益: {cumulative_return:.2f}%")
            report.append(f"  年化收益: {annualized_return:.2f}%")
            report.append(f"  年化波动: {annualized_vol:.2f}%")
            report.append(f"  夏普比率: {sharpe_ratio:.2f}")
            report.append(f"  最大回撤: {max_drawdown:.2f}%")
            report.append(f"  胜率: {win_rate:.1f}%")
            report.append(f"  调仓次数: {len(results_df)}")
            report.append("")

            # 分年度表现
            report.append("📅 分年度表现:")
            results_df['year'] = pd.to_datetime(results_df['rebalance_date']).dt.year
            for year in sorted(results_df['year'].unique()):
                year_data = results_df[results_df['year'] == year]
                year_return = (1 + year_data['portfolio_return'] / 100).prod() - 1
                year_win_rate = (year_data['portfolio_return'] > 0).mean() * 100
                report.append(f"  {year}年: 收益{year_return*100:.1f}%, 胜率{year_win_rate:.0f}%, 调仓{len(year_data)}次")
            report.append("")

        # 投资建议
        report.append("💡 投资建议:")
        if not results_df.empty:
            avg_return = results_df['portfolio_return'].mean()
            if avg_return > 2:
                report.append("  ✅ 营收增长加速度因子在定期调仓策略中表现良好")
                report.append("  ✅ 建议作为量化投资组合的核心因子之一")
            elif avg_return > 0:
                report.append("  ⚠️ 营收增长加速度因子表现一般，建议与其他因子结合")
            else:
                report.append("  ❌ 营收增长加速度因子在当前市场环境下表现不佳")

        report.append("  📊 建议结合风险管理和仓位控制")
        report.append("  📊 定期评估因子有效性，适时调整策略")
        report.append("")
        report.append("=" * 80)

        # 保存报告
        with open(f'{output_dir}/detailed_report.txt', 'w', encoding='utf-8') as f:
            f.write('\n'.join(report))

        print(f"✅ 详细报告已保存到 {output_dir}/detailed_report.txt")


def main():
    """主函数"""
    print("🚀 营收增长加速度因子组合回测")
    print("="*50)
    
    # 创建回测实例
    backtest = PortfolioBacktest()
    
    # 加载数据
    backtest.load_data()
    
    # 计算因子
    backtest.calculate_factors()
    
    # 运行回测
    results = backtest.run_portfolio_backtest(
        start_date="2020-01-01",
        end_date="2024-12-31",
        rebalance_freq="Q",  # 季度调仓
        top_pct=0.3,         # 前30%做多
        bottom_pct=0.3       # 后30%做空
    )
    
    # 分析结果
    backtest.analyze_results(results)

    # 创建可视化
    backtest.create_visualizations(results)

    # 保存结果
    if not results.empty:
        output_dir = "portfolio_backtest_results"
        results.to_csv(f'{output_dir}/backtest_data.csv', index=False)
        print(f"\n✅ 数据已保存到 {output_dir}/backtest_data.csv")


if __name__ == "__main__":
    main()
