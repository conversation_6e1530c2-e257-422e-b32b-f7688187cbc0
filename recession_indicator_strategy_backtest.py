#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于经济衰退代理指标的投资策略回测
Investment Strategy Backtest Based on Economic Recession Proxy Indicator
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class RecessionIndicatorStrategy:
    def __init__(self):
        self.employment_data = None
        self.market_data = None
        self.indicator_data = None
        self.backtest_results = None
        
    def create_employment_data(self, start_date='2010-01-01'):
        """
        创建模拟的就业数据（基于真实历史趋势）
        """
        print("正在创建就业数据...")
        
        start = pd.to_datetime(start_date)
        end = pd.Timestamp.now()
        date_range = pd.date_range(start=start, end=end, freq='MS')
        
        # 全职就业数据（基于PAYEMS历史趋势）
        base_full_time = 130000
        trend_full_time = np.linspace(0, 25000, len(date_range))
        
        # 添加经济周期和重大事件影响
        cycle = 3000 * np.sin(2 * np.pi * np.arange(len(date_range)) / 120)
        
        # COVID-19冲击（2020年3月-2021年12月）
        covid_impact_full = np.zeros(len(date_range))
        for i, date in enumerate(date_range):
            if pd.to_datetime('2020-03-01') <= date <= pd.to_datetime('2021-12-01'):
                months_since_covid = (date - pd.to_datetime('2020-03-01')).days / 30
                if months_since_covid <= 3:
                    covid_impact_full[i] = -15000 * (months_since_covid / 3)
                else:
                    recovery_months = months_since_covid - 3
                    covid_impact_full[i] = -15000 + 15000 * min(1, recovery_months / 18)
        
        seasonal_full = 1000 * np.sin(2 * np.pi * np.arange(len(date_range)) / 12)
        np.random.seed(42)
        noise_full = np.random.normal(0, 800, len(date_range))
        
        full_time_employment = base_full_time + trend_full_time + cycle + covid_impact_full + seasonal_full + noise_full
        
        # 兼职就业数据（经济原因）
        base_part_time = 6000
        trend_part_time = np.linspace(0, -1000, len(date_range))
        cycle_part = -1000 * np.sin(2 * np.pi * np.arange(len(date_range)) / 120)
        
        # COVID-19期间兼职就业增加
        covid_impact_part = np.zeros(len(date_range))
        for i, date in enumerate(date_range):
            if pd.to_datetime('2020-03-01') <= date <= pd.to_datetime('2021-12-01'):
                months_since_covid = (date - pd.to_datetime('2020-03-01')).days / 30
                if months_since_covid <= 3:
                    covid_impact_part[i] = 8000 * (months_since_covid / 3)
                else:
                    recovery_months = months_since_covid - 3
                    covid_impact_part[i] = 8000 - 8000 * min(1, recovery_months / 18)
        
        seasonal_part = 500 * np.sin(2 * np.pi * np.arange(len(date_range)) / 12 + np.pi)
        np.random.seed(43)
        noise_part = np.random.normal(0, 400, len(date_range))
        
        part_time_employment = base_part_time + trend_part_time + cycle_part + covid_impact_part + seasonal_part + noise_part
        part_time_employment = np.maximum(part_time_employment, 1000)
        
        self.employment_data = pd.DataFrame({
            'full_time_employment': full_time_employment,
            'part_time_employment': part_time_employment
        }, index=date_range)
        
        print(f"成功创建 {len(self.employment_data)} 个月的就业数据")
        return self.employment_data
    
    def fetch_market_data(self, symbols=['SPY', 'TLT', 'GLD'], start_date='2010-01-01'):
        """
        获取市场数据（直接使用模拟数据以避免网络问题）
        """
        print("正在创建市场数据...")
        self.create_simulated_market_data(start_date)
        return self.market_data

    def create_simulated_market_data(self, start_date='2010-01-01'):
        """
        创建模拟市场数据（基于真实历史表现）
        """
        print("正在创建模拟市场数据...")

        start = pd.to_datetime(start_date)
        end = pd.Timestamp.now()
        date_range = pd.date_range(start=start, end=end, freq='MS')

        # 模拟SPY数据（基于真实历史表现：年化约10%收益）
        np.random.seed(100)
        spy_monthly_return = 0.10 / 12  # 年化10%转月度
        spy_monthly_vol = 0.15 / np.sqrt(12)  # 年化15%波动率转月度

        spy_returns = np.random.normal(spy_monthly_return, spy_monthly_vol, len(date_range))
        # 添加一些极端事件（如2020年COVID-19）
        covid_start = pd.to_datetime('2020-03-01')
        covid_end = pd.to_datetime('2020-04-01')
        for i, date in enumerate(date_range):
            if covid_start <= date <= covid_end:
                spy_returns[i] = -0.30  # 30%的大跌
            elif pd.to_datetime('2020-05-01') <= date <= pd.to_datetime('2021-12-01'):
                spy_returns[i] = spy_returns[i] + 0.02  # 恢复期额外收益

        spy_prices = [100]  # 起始价格
        for ret in spy_returns[1:]:
            spy_prices.append(spy_prices[-1] * (1 + ret))

        # 模拟TLT数据（债券ETF，年化约5%收益，较低波动）
        np.random.seed(101)
        tlt_monthly_return = 0.05 / 12
        tlt_monthly_vol = 0.08 / np.sqrt(12)

        tlt_returns = np.random.normal(tlt_monthly_return, tlt_monthly_vol, len(date_range))
        # 在股市下跌时债券表现更好
        for i, date in enumerate(date_range):
            if covid_start <= date <= covid_end:
                tlt_returns[i] = 0.15  # 避险需求推高债券价格

        tlt_prices = [100]
        for ret in tlt_returns[1:]:
            tlt_prices.append(tlt_prices[-1] * (1 + ret))

        # 模拟GLD数据（黄金ETF，年化约7%收益）
        np.random.seed(102)
        gld_monthly_return = 0.07 / 12
        gld_monthly_vol = 0.18 / np.sqrt(12)

        gld_returns = np.random.normal(gld_monthly_return, gld_monthly_vol, len(date_range))

        gld_prices = [100]
        for ret in gld_returns[1:]:
            gld_prices.append(gld_prices[-1] * (1 + ret))

        self.market_data = pd.DataFrame({
            'SPY': spy_prices,
            'TLT': tlt_prices,
            'GLD': gld_prices
        }, index=date_range)

        print(f"成功创建模拟市场数据，期间: {self.market_data.index[0].strftime('%Y-%m')} 至 {self.market_data.index[-1].strftime('%Y-%m')}")
        print(f"SPY累计收益: {(spy_prices[-1]/spy_prices[0] - 1)*100:.1f}%")
        print(f"TLT累计收益: {(tlt_prices[-1]/tlt_prices[0] - 1)*100:.1f}%")
        print(f"GLD累计收益: {(gld_prices[-1]/gld_prices[0] - 1)*100:.1f}%")
        return self.market_data
    
    def calculate_recession_indicator(self):
        """
        计算经济衰退代理指标
        """
        if self.employment_data is None:
            raise ValueError("请先创建就业数据")
        
        print("正在计算经济衰退代理指标...")
        
        data = self.employment_data.copy()
        
        # 计算12个月前的数据
        data['full_time_12m_ago'] = data['full_time_employment'].shift(12)
        data['part_time_12m_ago'] = data['part_time_employment'].shift(12)
        
        # 计算就业变化率
        data['full_time_change_rate'] = data['full_time_employment'] / data['full_time_12m_ago']
        data['part_time_change_rate'] = data['part_time_employment'] / data['part_time_12m_ago']
        
        # 计算就业变化率差值
        data['employment_rate_diff'] = data['full_time_change_rate'] - data['part_time_change_rate']
        
        # 计算6个月移动平均（经济衰退代理指标）
        data['recession_indicator'] = data['employment_rate_diff'].rolling(window=6, center=False).mean()
        
        # 生成交易信号
        data['signal'] = 0  # 0: 现金, 1: 股票, 2: 债券
        data.loc[data['recession_indicator'] > 0, 'signal'] = 1  # 正值时买入股票
        data.loc[data['recession_indicator'] <= 0, 'signal'] = 2  # 负值时买入债券
        
        self.indicator_data = data.dropna()
        print(f"成功计算指标，有效数据点：{len(self.indicator_data)}")
        return self.indicator_data
    
    def backtest_strategy(self):
        """
        回测投资策略
        """
        if self.indicator_data is None or self.market_data is None:
            raise ValueError("请先计算指标和获取市场数据")
        
        print("正在进行策略回测...")
        
        # 合并数据
        combined_data = pd.merge(self.indicator_data[['recession_indicator', 'signal']], 
                               self.market_data, left_index=True, right_index=True, how='inner')
        
        # 计算收益率
        combined_data['spy_return'] = combined_data['SPY'].pct_change()
        combined_data['tlt_return'] = combined_data['TLT'].pct_change()
        combined_data['gld_return'] = combined_data['GLD'].pct_change()
        
        # 策略收益率
        combined_data['strategy_return'] = 0.0
        
        for i in range(1, len(combined_data)):
            signal = combined_data['signal'].iloc[i-1]  # 使用前一期信号
            
            if signal == 1:  # 股票
                combined_data['strategy_return'].iloc[i] = combined_data['spy_return'].iloc[i]
            elif signal == 2:  # 债券
                combined_data['strategy_return'].iloc[i] = combined_data['tlt_return'].iloc[i]
            else:  # 现金
                combined_data['strategy_return'].iloc[i] = 0.0
        
        # 计算累计收益
        combined_data['strategy_cumret'] = (1 + combined_data['strategy_return']).cumprod()
        combined_data['spy_cumret'] = (1 + combined_data['spy_return']).cumprod()
        combined_data['tlt_cumret'] = (1 + combined_data['tlt_return']).cumprod()
        combined_data['gld_cumret'] = (1 + combined_data['gld_return']).cumprod()
        
        # 计算60/40组合作为基准
        combined_data['benchmark_return'] = 0.6 * combined_data['spy_return'] + 0.4 * combined_data['tlt_return']
        combined_data['benchmark_cumret'] = (1 + combined_data['benchmark_return']).cumprod()
        
        self.backtest_results = combined_data
        print("策略回测完成")
        return combined_data
    
    def calculate_performance_metrics(self):
        """
        计算绩效指标
        """
        if self.backtest_results is None:
            raise ValueError("请先进行回测")
        
        data = self.backtest_results.dropna()
        
        # 年化收益率
        years = len(data) / 12
        strategy_annual_return = (data['strategy_cumret'].iloc[-1] ** (1/years)) - 1
        spy_annual_return = (data['spy_cumret'].iloc[-1] ** (1/years)) - 1
        benchmark_annual_return = (data['benchmark_cumret'].iloc[-1] ** (1/years)) - 1
        
        # 年化波动率
        strategy_annual_vol = data['strategy_return'].std() * np.sqrt(12)
        spy_annual_vol = data['spy_return'].std() * np.sqrt(12)
        benchmark_annual_vol = data['benchmark_return'].std() * np.sqrt(12)
        
        # 夏普比率（假设无风险利率为2%）
        risk_free_rate = 0.02
        strategy_sharpe = (strategy_annual_return - risk_free_rate) / strategy_annual_vol
        spy_sharpe = (spy_annual_return - risk_free_rate) / spy_annual_vol
        benchmark_sharpe = (benchmark_annual_return - risk_free_rate) / benchmark_annual_vol
        
        # 最大回撤
        def calculate_max_drawdown(cumret_series):
            peak = cumret_series.expanding().max()
            drawdown = (cumret_series - peak) / peak
            return drawdown.min()
        
        strategy_max_dd = calculate_max_drawdown(data['strategy_cumret'])
        spy_max_dd = calculate_max_drawdown(data['spy_cumret'])
        benchmark_max_dd = calculate_max_drawdown(data['benchmark_cumret'])
        
        metrics = {
            '策略': {
                '年化收益率': strategy_annual_return,
                '年化波动率': strategy_annual_vol,
                '夏普比率': strategy_sharpe,
                '最大回撤': strategy_max_dd,
                '累计收益': data['strategy_cumret'].iloc[-1] - 1
            },
            'SPY': {
                '年化收益率': spy_annual_return,
                '年化波动率': spy_annual_vol,
                '夏普比率': spy_sharpe,
                '最大回撤': spy_max_dd,
                '累计收益': data['spy_cumret'].iloc[-1] - 1
            },
            '60/40基准': {
                '年化收益率': benchmark_annual_return,
                '年化波动率': benchmark_annual_vol,
                '夏普比率': benchmark_sharpe,
                '最大回撤': benchmark_max_dd,
                '累计收益': data['benchmark_cumret'].iloc[-1] - 1
            }
        }
        
        return metrics

    def plot_backtest_results(self, save_path=None):
        """
        绘制回测结果
        """
        if self.backtest_results is None:
            raise ValueError("请先进行回测")

        data = self.backtest_results.dropna()

        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        # 第一个子图：经济衰退指标和信号
        ax1 = axes[0]
        ax1.plot(data.index, data['recession_indicator'], linewidth=2, color='darkgreen',
                label='经济衰退代理指标')
        ax1.axhline(y=0, color='black', linestyle='-', alpha=0.8, linewidth=1)

        # 标记信号区域
        stock_mask = data['signal'] == 1
        bond_mask = data['signal'] == 2

        if stock_mask.any():
            ax1.fill_between(data.index, data['recession_indicator'], 0,
                           where=stock_mask, alpha=0.3, color='green', label='股票信号')
        if bond_mask.any():
            ax1.fill_between(data.index, data['recession_indicator'], 0,
                           where=bond_mask, alpha=0.3, color='red', label='债券信号')

        ax1.set_title('经济衰退代理指标与交易信号', fontsize=14, fontweight='bold')
        ax1.set_ylabel('指标值')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 第二个子图：累计收益对比
        ax2 = axes[1]
        ax2.plot(data.index, (data['strategy_cumret'] - 1) * 100,
                linewidth=3, color='blue', label='衰退指标策略')
        ax2.plot(data.index, (data['spy_cumret'] - 1) * 100,
                linewidth=2, color='red', label='SPY买入持有')
        ax2.plot(data.index, (data['benchmark_cumret'] - 1) * 100,
                linewidth=2, color='orange', label='60/40基准组合')

        ax2.set_title('累计收益对比', fontsize=14, fontweight='bold')
        ax2.set_ylabel('累计收益 (%)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 第三个子图：资产配置
        ax3 = axes[2]

        # 创建资产配置数据
        allocation_data = pd.DataFrame(index=data.index)
        allocation_data['股票'] = (data['signal'] == 1).astype(int) * 100
        allocation_data['债券'] = (data['signal'] == 2).astype(int) * 100
        allocation_data['现金'] = (data['signal'] == 0).astype(int) * 100

        ax3.stackplot(allocation_data.index,
                     allocation_data['股票'],
                     allocation_data['债券'],
                     allocation_data['现金'],
                     labels=['股票(SPY)', '债券(TLT)', '现金'],
                     colors=['green', 'red', 'gray'],
                     alpha=0.7)

        ax3.set_title('策略资产配置', fontsize=14, fontweight='bold')
        ax3.set_ylabel('配置比例 (%)')
        ax3.set_xlabel('日期')
        ax3.legend(loc='upper right')
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()

        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"回测图表已保存到: {save_path}")

        plt.close()
        return fig

    def generate_backtest_report(self, save_path=None):
        """
        生成回测报告
        """
        if self.backtest_results is None:
            raise ValueError("请先进行回测")

        metrics = self.calculate_performance_metrics()
        data = self.backtest_results.dropna()

        # 计算交易统计
        signal_changes = (data['signal'] != data['signal'].shift(1)).sum()
        stock_periods = (data['signal'] == 1).sum()
        bond_periods = (data['signal'] == 2).sum()
        cash_periods = (data['signal'] == 0).sum()

        report = f"""
经济衰退代理指标投资策略回测报告
{'='*60}

策略说明：
- 当经济衰退代理指标 > 0 时，投资股票(SPY)
- 当经济衰退代理指标 ≤ 0 时，投资债券(TLT)
- 指标基于全职就业与兼职就业的相对变化计算

回测期间：
- 开始日期: {data.index[0].strftime('%Y年%m月')}
- 结束日期: {data.index[-1].strftime('%Y年%m月')}
- 回测月数: {len(data)} 个月

交易统计：
- 总交易次数: {signal_changes} 次
- 股票持有期间: {stock_periods} 个月 ({stock_periods/len(data)*100:.1f}%)
- 债券持有期间: {bond_periods} 个月 ({bond_periods/len(data)*100:.1f}%)
- 现金持有期间: {cash_periods} 个月 ({cash_periods/len(data)*100:.1f}%)

绩效对比：
"""

        # 添加绩效对比表格
        for strategy_name, strategy_metrics in metrics.items():
            report += f"\n{strategy_name}:\n"
            report += f"  年化收益率: {strategy_metrics['年化收益率']:.2%}\n"
            report += f"  年化波动率: {strategy_metrics['年化波动率']:.2%}\n"
            report += f"  夏普比率: {strategy_metrics['夏普比率']:.3f}\n"
            report += f"  最大回撤: {strategy_metrics['最大回撤']:.2%}\n"
            report += f"  累计收益: {strategy_metrics['累计收益']:.2%}\n"

        # 策略优势分析
        strategy_metrics = metrics['策略']
        spy_metrics = metrics['SPY']
        benchmark_metrics = metrics['60/40基准']

        report += f"""
策略优势分析：
- 相对SPY超额收益: {(strategy_metrics['年化收益率'] - spy_metrics['年化收益率']):.2%}
- 相对60/40基准超额收益: {(strategy_metrics['年化收益率'] - benchmark_metrics['年化收益率']):.2%}
- 波动率降低: {(spy_metrics['年化波动率'] - strategy_metrics['年化波动率']):.2%}
- 夏普比率提升: {(strategy_metrics['夏普比率'] - spy_metrics['夏普比率']):.3f}

风险提示：
- 该策略基于历史数据回测，不保证未来表现
- 实际交易中需考虑交易成本和滑点
- 建议结合其他指标进行综合判断
- 过度频繁的交易可能影响实际收益
"""

        if save_path:
            with open(save_path, 'w', encoding='utf-8') as f:
                f.write(report)
            print(f"回测报告已保存到: {save_path}")

        print(report)
        return report

def main():
    """
    主函数
    """
    print("经济衰退代理指标投资策略回测")
    print("="*60)

    # 创建策略回测器
    strategy = RecessionIndicatorStrategy()

    # 创建就业数据
    strategy.create_employment_data()

    # 获取市场数据
    strategy.fetch_market_data()

    # 计算指标
    strategy.calculate_recession_indicator()

    # 进行回测
    strategy.backtest_strategy()

    # 生成图表和报告
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    chart_path = f'recession_strategy_backtest_{timestamp}.png'
    report_path = f'recession_strategy_backtest_report_{timestamp}.txt'

    strategy.plot_backtest_results(save_path=chart_path)
    strategy.generate_backtest_report(save_path=report_path)

    print(f"\n回测完成！")
    print(f"图表文件: {chart_path}")
    print(f"报告文件: {report_path}")

if __name__ == "__main__":
    main()
