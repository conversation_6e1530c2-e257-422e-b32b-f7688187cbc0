"""
获取沪深300指数成分股列表

This script retrieves the list of constituent stocks for the CSI 300 Index (沪深300)
using the AKShare library and saves it to a CSV file.
"""

import akshare as ak
import pandas as pd
import os
from datetime import datetime

def get_csi300_constituents():
    """
    获取沪深300指数成分股列表
    
    Returns:
        pandas.DataFrame: 沪深300成分股数据，包含股票代码、名称和权重等信息
    """
    print("正在获取沪深300指数成分股列表...")
    
    try:
        # 使用AKShare的index_stock_cons_weight_csindex函数获取沪深300成分股
        # symbol="000300"是沪深300指数的代码
        csi300_stocks = ak.index_stock_cons_weight_csindex(symbol="000300")
        
        print(f"成功获取到{len(csi300_stocks)}只沪深300成分股")
        return csi300_stocks
    except Exception as e:
        print(f"获取沪深300成分股失败: {str(e)}")
        return None

def save_to_csv(df, output_dir="data_files"):
    """
    将数据保存为CSV文件

    Args:
        df: 要保存的DataFrame
        output_dir: 输出目录

    Returns:
        str: 保存的文件路径
    """
    # 创建输出目录（如果不存在）
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # 生成文件名，包含当前日期
    current_date = datetime.now().strftime("%Y%m%d")
    filename = f"csi300_constituents.csv"  # 使用固定文件名便于分析器读取
    file_path = os.path.join(output_dir, filename)

    # 保存为CSV
    df.to_csv(file_path, index=False, encoding="utf-8-sig")
    print(f"沪深300成分股列表已保存至: {file_path}")

    return file_path

def main():
    # 获取沪深300成分股
    csi300_stocks = get_csi300_constituents()
    
    if csi300_stocks is not None and not csi300_stocks.empty:
        # 显示前10只成分股
        print("\n沪深300指数前10只成分股:")
        print(csi300_stocks.head(10))
        
        # 保存到CSV文件
        save_to_csv(csi300_stocks)
    else:
        print("未能获取沪深300成分股数据")

if __name__ == "__main__":
    main()
