#!/usr/bin/env python
# -*- coding: utf-8 -*-

import os
import markdown
from ebooklib import epub

def main():
    # 读取Markdown文件
    with open('巴菲特致股东信合集.md', 'r', encoding='utf-8') as f:
        md_content = f.read()
    
    # 创建EPUB书籍
    book = epub.EpubBook()
    
    # 设置元数据
    book.set_identifier('buffett-letters')
    book.set_title('巴菲特致股东信合集')
    book.set_language('zh-CN')
    book.add_author('沃伦·巴菲特')
    
    # 创建章节
    chapters = []
    
    # 添加封面
    cover = epub.EpubHtml(title='封面', file_name='cover.xhtml')
    cover.content = '<html><body><h1>巴菲特致股东信合集</h1></body></html>'
    book.add_item(cover)
    chapters.append(cover)
    
    # 分割Markdown内容为章节
    sections = md_content.split('## ')
    
    # 处理第一部分（标题）
    if sections[0].startswith('# '):
        intro = epub.EpubHtml(title='简介', file_name='intro.xhtml')
        intro.content = f'<html><body>{markdown.markdown(sections[0])}</body></html>'
        book.add_item(intro)
        chapters.append(intro)
    
    # 处理每个章节
    for i, section in enumerate(sections[1:], 1):
        # 提取年份
        year = section.split('年')[0] if '年' in section else f'章节{i}'
        
        # 创建章节
        chapter = epub.EpubHtml(title=f'{year}年致股东信', file_name=f'chapter_{i}.xhtml')
        chapter.content = f'<html><body><h2>{year}年致股东信</h2>{markdown.markdown(section)}</body></html>'
        
        # 添加章节
        book.add_item(chapter)
        chapters.append(chapter)
    
    # 定义目录
    book.toc = [(epub.Section('巴菲特致股东信'), chapters)]
    
    # 添加默认NCX和Nav文件
    book.add_item(epub.EpubNcx())
    book.add_item(epub.EpubNav())
    
    # 定义CSS样式
    style = '''
    body {
        font-family: "SimSun", "宋体", serif;
        margin: 5%;
        text-align: justify;
    }
    h1 {
        text-align: center;
        font-size: 2em;
        font-weight: bold;
        margin-bottom: 1em;
    }
    h2 {
        text-align: left;
        font-size: 1.5em;
        font-weight: bold;
        margin-top: 1em;
    }
    p {
        margin-top: 0.5em;
        margin-bottom: 0.5em;
    }
    '''
    
    css = epub.EpubItem(
        uid="style_default",
        file_name="style/default.css",
        media_type="text/css",
        content=style
    )
    book.add_item(css)
    
    # 添加章节到spine
    book.spine = ['cover'] + chapters
    
    # 写入EPUB文件
    epub.write_epub('巴菲特致股东信合集.epub', book, {})
    print('EPUB file created: 巴菲特致股东信合集.epub')

if __name__ == '__main__':
    main()
