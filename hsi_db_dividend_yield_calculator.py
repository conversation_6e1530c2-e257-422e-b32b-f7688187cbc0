#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
HSI成分股每日股息率计算器 - 基于SQLite数据库版本

功能：
1. 从hk_dividend_data.db读取分红数据
2. 从ganggutong_10year_data.db读取价格数据
3. 从data_files/hsi_constituents.csv读取股票列表
4. 计算每日股息率并存储到新的因子数据库

作者：AI Assistant
创建时间：2025年
"""

import sqlite3
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import logging
from typing import Dict, List, Tuple, Optional
import os

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('hsi_db_dividend_yield_calculator.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class HSIDBDividendYieldCalculator:
    """基于数据库的HSI股息率计算器"""
    
    def __init__(self, 
                 dividend_db: str = "hk_dividend_data.db",
                 price_db: str = "ganggutong_10year_data.db", 
                 factor_db: str = "hsi_factor_data.db",
                 stock_list_csv: str = "data_files/hsi_constituents.csv"):
        self.dividend_db = dividend_db
        self.price_db = price_db
        self.factor_db = factor_db
        self.stock_list_csv = stock_list_csv
        
        # 检查数据库文件是否存在
        self._check_databases()
        
        # 初始化因子数据库
        self.init_factor_database()
    
    def _check_databases(self):
        """检查数据库文件是否存在"""
        for db_name in [self.dividend_db, self.price_db]:
            if not os.path.exists(db_name):
                raise FileNotFoundError(f"数据库文件 {db_name} 不存在")
        
        if not os.path.exists(self.stock_list_csv):
            raise FileNotFoundError(f"股票列表文件 {self.stock_list_csv} 不存在")
    
    def init_factor_database(self):
        """初始化因子数据库"""
        try:
            conn = sqlite3.connect(self.factor_db)
            cursor = conn.cursor()
            
            # 创建每日股息率表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS daily_dividend_yield (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    stock_code TEXT NOT NULL,
                    stock_name TEXT,
                    date TEXT NOT NULL,
                    close_price REAL,
                    annual_dividend REAL,
                    dividend_yield REAL,
                    dividend_count INTEGER DEFAULT 0,
                    frequency_type TEXT,
                    analysis_detail TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(stock_code, date)
                )
            ''')
            
            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_dividend_yield_code_date ON daily_dividend_yield(stock_code, date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_dividend_yield_date ON daily_dividend_yield(date)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_dividend_yield_yield ON daily_dividend_yield(dividend_yield)')
            
            conn.commit()
            conn.close()
            logger.info(f"因子数据库 {self.factor_db} 初始化完成")
            
        except Exception as e:
            logger.error(f"初始化因子数据库失败: {e}")
            raise
    
    def load_hsi_stocks(self) -> List[Tuple[str, str]]:
        """加载HSI成分股列表"""
        try:
            df = pd.read_csv(self.stock_list_csv, encoding='utf-8')
            stocks = []
            
            for _, row in df.iterrows():
                if pd.notna(row['代码']) and pd.notna(row['名称']):
                    stock_code = str(row['代码']).zfill(5)  # 确保5位数
                    stock_name = str(row['名称'])
                    stocks.append((stock_code, stock_name))
            
            logger.info(f"成功加载 {len(stocks)} 只HSI成分股")
            return stocks
            
        except Exception as e:
            logger.error(f"加载HSI成分股列表失败: {e}")
            raise
    
    def get_dividend_data(self, stock_code: str) -> pd.DataFrame:
        """获取单只股票的分红数据"""
        try:
            conn = sqlite3.connect(self.dividend_db)
            
            query = '''
                SELECT stock_code, stock_name, ex_dividend_date, dividend_amount, year
                FROM dividend_data 
                WHERE stock_code = ?
                ORDER BY ex_dividend_date
            '''
            
            df = pd.read_sql_query(query, conn, params=(stock_code,))
            conn.close()
            
            if not df.empty:
                # 转换日期格式 - 处理 YYYY/MM/DD 格式
                df['ex_dividend_date'] = pd.to_datetime(df['ex_dividend_date'], format='%Y/%m/%d', errors='coerce')
                df = df.dropna(subset=['ex_dividend_date'])
                df = df.sort_values('ex_dividend_date')
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 分红数据失败: {e}")
            return pd.DataFrame()
    
    def get_price_data(self, stock_code: str) -> pd.DataFrame:
        """获取单只股票的价格数据"""
        try:
            conn = sqlite3.connect(self.price_db)
            
            query = '''
                SELECT stock_code, date, close
                FROM stock_prices 
                WHERE stock_code = ?
                ORDER BY date
            '''
            
            df = pd.read_sql_query(query, conn, params=(stock_code,))
            conn.close()
            
            if not df.empty:
                df['date'] = pd.to_datetime(df['date'])
                df = df.sort_values('date')
            
            return df
            
        except Exception as e:
            logger.error(f"获取股票 {stock_code} 价格数据失败: {e}")
            return pd.DataFrame()
    
    def analyze_dividend_frequency(self, dividend_df: pd.DataFrame) -> tuple:
        """
        分析分红频率（参考HSISmartDividendYieldCalculator的逻辑）
        返回: (频率类型, 应取的分红次数, 分析详情)
        """
        if dividend_df.empty or len(dividend_df) < 2:
            return "年度", 1, "单次分红"

        # 计算相邻分红之间的间隔（月数）
        intervals = []
        for i in range(min(len(dividend_df) - 1, 8)):
            date1 = dividend_df.iloc[i]['ex_dividend_date']
            date2 = dividend_df.iloc[i + 1]['ex_dividend_date']
            months_diff = abs((date1.year - date2.year) * 12 + (date1.month - date2.month))
            intervals.append(months_diff)

        if not intervals:
            return "年度", 1, "间隔不足"

        avg_interval = sum(intervals) / len(intervals)

        # 根据平均间隔判断分红频率
        if avg_interval <= 4:  # 3-4个月
            return "季度", 4, f"平均间隔{avg_interval:.1f}个月"
        elif avg_interval <= 8:  # 6-8个月
            return "半年", 2, f"平均间隔{avg_interval:.1f}个月"
        else:  # 12个月左右
            return "年度", 1, f"平均间隔{avg_interval:.1f}个月"

    def calculate_smart_annual_dividend(self, dividend_df: pd.DataFrame, target_date: pd.Timestamp) -> tuple:
        """
        根据分红频率智能计算年度分红总和（参考HSISmartDividendYieldCalculator）
        返回: (年度分红总额, 频率类型, 分红次数, 分析详情)
        """
        if dividend_df.empty:
            return 0.0, "无分红", 0, "无分红数据"

        # 只考虑目标日期之前的分红记录
        historical_dividends = dividend_df[dividend_df['ex_dividend_date'] <= target_date].copy()

        if historical_dividends.empty:
            return 0.0, "无历史分红", 0, "无历史分红数据"

        # 按除权日期排序（最新的在前）
        historical_dividends = historical_dividends.sort_values('ex_dividend_date', ascending=False)

        # 分析分红频率
        frequency_type, take_count, analysis_detail = self.analyze_dividend_frequency(historical_dividends)

        # 取最近N次分红
        recent_dividends = historical_dividends.head(take_count)
        total_dividend = recent_dividends['dividend_amount'].sum()

        return total_dividend, frequency_type, len(recent_dividends), analysis_detail
    
    def calculate_stock_dividend_yield(self, stock_code: str, stock_name: str) -> pd.DataFrame:
        """计算单只股票的每日智能股息率"""
        logger.info(f"计算 {stock_code} ({stock_name}) 的每日智能股息率")

        # 获取分红数据
        dividend_df = self.get_dividend_data(stock_code)
        if dividend_df.empty:
            logger.warning(f"股票 {stock_code} 无分红数据")
            return pd.DataFrame()

        # 获取价格数据
        price_df = self.get_price_data(stock_code)
        if price_df.empty:
            logger.warning(f"股票 {stock_code} 无价格数据")
            return pd.DataFrame()

        # 创建结果DataFrame
        results = []

        for _, row in price_df.iterrows():
            date = row['date']
            close_price = row['close']

            if pd.isna(close_price) or close_price <= 0:
                continue

            # 使用智能分红计算方法
            annual_dividend, frequency_type, dividend_count, analysis_detail = self.calculate_smart_annual_dividend(dividend_df, date)

            # 计算股息率 (年化百分比)
            if annual_dividend > 0:
                dividend_yield = (annual_dividend / close_price) * 100
            else:
                dividend_yield = 0.0

            results.append({
                'stock_code': stock_code,
                'stock_name': stock_name,
                'date': date.strftime('%Y-%m-%d'),
                'close_price': close_price,
                'annual_dividend': annual_dividend,
                'dividend_yield': dividend_yield,
                'dividend_count': dividend_count,
                'frequency_type': frequency_type,
                'analysis_detail': analysis_detail
            })

        return pd.DataFrame(results)
    
    def save_to_database(self, df: pd.DataFrame):
        """保存结果到数据库"""
        if df.empty:
            return
        
        try:
            conn = sqlite3.connect(self.factor_db)
            
            for _, row in df.iterrows():
                conn.execute('''
                    INSERT OR REPLACE INTO daily_dividend_yield
                    (stock_code, stock_name, date, close_price, annual_dividend, dividend_yield,
                     dividend_count, frequency_type, analysis_detail)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (
                    row['stock_code'],
                    row['stock_name'],
                    row['date'],
                    row['close_price'],
                    row['annual_dividend'],
                    row['dividend_yield'],
                    row['dividend_count'],
                    row.get('frequency_type', ''),
                    row.get('analysis_detail', '')
                ))
            
            conn.commit()
            conn.close()
            logger.info(f"成功保存 {len(df)} 条记录到数据库")
            
        except Exception as e:
            logger.error(f"保存数据到数据库失败: {e}")
            raise

    def calculate_all_stocks(self):
        """计算所有HSI成分股的每日股息率"""
        logger.info("开始计算所有HSI成分股的每日股息率")

        # 加载股票列表
        stocks = self.load_hsi_stocks()

        total_stocks = len(stocks)
        success_count = 0

        for i, (stock_code, stock_name) in enumerate(stocks, 1):
            logger.info(f"处理第 {i}/{total_stocks} 只股票: {stock_code} ({stock_name})")

            try:
                # 计算股息率
                result_df = self.calculate_stock_dividend_yield(stock_code, stock_name)

                if not result_df.empty:
                    # 保存到数据库
                    self.save_to_database(result_df)
                    success_count += 1
                    logger.info(f"✅ {stock_code} 计算完成，共 {len(result_df)} 条记录")
                else:
                    logger.warning(f"⚠️ {stock_code} 无有效数据")

            except Exception as e:
                logger.error(f"❌ 处理股票 {stock_code} 失败: {e}")
                continue

        logger.info(f"🎉 计算完成！成功处理 {success_count}/{total_stocks} 只股票")
        return success_count

    def get_statistics(self) -> pd.DataFrame:
        """获取股息率统计信息"""
        try:
            conn = sqlite3.connect(self.factor_db)

            query = '''
                SELECT
                    stock_code,
                    stock_name,
                    COUNT(*) as total_days,
                    COUNT(CASE WHEN dividend_yield > 0 THEN 1 END) as dividend_days,
                    AVG(CASE WHEN dividend_yield > 0 THEN dividend_yield END) as avg_dividend_yield,
                    MAX(dividend_yield) as max_dividend_yield,
                    MIN(CASE WHEN dividend_yield > 0 THEN dividend_yield END) as min_dividend_yield,
                    MAX(date) as latest_date,
                    (SELECT dividend_yield FROM daily_dividend_yield d2
                     WHERE d2.stock_code = d1.stock_code
                     ORDER BY d2.date DESC LIMIT 1) as latest_dividend_yield
                FROM daily_dividend_yield d1
                GROUP BY stock_code, stock_name
                ORDER BY avg_dividend_yield DESC
            '''

            df = pd.read_sql_query(query, conn)
            conn.close()

            return df

        except Exception as e:
            logger.error(f"获取统计信息失败: {e}")
            return pd.DataFrame()

    def export_to_csv(self, output_file: str = None) -> str:
        """导出所有数据到CSV文件"""
        if output_file is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            output_file = f"hsi_daily_dividend_yield_{timestamp}.csv"

        try:
            conn = sqlite3.connect(self.factor_db)

            query = '''
                SELECT stock_code, stock_name, date, close_price,
                       annual_dividend, dividend_yield, dividend_count,
                       frequency_type, analysis_detail
                FROM daily_dividend_yield
                ORDER BY stock_code, date
            '''

            df = pd.read_sql_query(query, conn)
            conn.close()

            df.to_csv(output_file, index=False, encoding='utf-8-sig')
            logger.info(f"✅ 数据已导出到 {output_file}，共 {len(df)} 条记录")

            return output_file

        except Exception as e:
            logger.error(f"导出数据失败: {e}")
            return None

def main():
    """主函数"""
    print("🚀 HSI成分股每日股息率计算器 (基于数据库版本)")
    print("=" * 60)

    try:
        # 创建计算器实例
        calculator = HSIDBDividendYieldCalculator()

        # 计算所有股票的股息率
        success_count = calculator.calculate_all_stocks()

        if success_count > 0:
            print(f"\n📊 计算完成！成功处理 {success_count} 只股票")

            # 获取统计信息
            stats_df = calculator.get_statistics()
            if not stats_df.empty:
                print("\n📈 股息率统计信息 (前10名):")
                print(stats_df.head(10)[['stock_code', 'stock_name', 'avg_dividend_yield',
                                       'latest_dividend_yield', 'dividend_days']].to_string(index=False))

            # 导出数据
            csv_file = calculator.export_to_csv()
            if csv_file:
                print(f"\n💾 数据已导出到: {csv_file}")

            print(f"\n📁 因子数据库: {calculator.factor_db}")
            print("✅ 所有任务完成！")
        else:
            print("❌ 没有成功处理任何股票，请检查数据源")

    except Exception as e:
        logger.error(f"程序执行失败: {e}")
        print(f"❌ 程序执行失败: {e}")

if __name__ == "__main__":
    main()
