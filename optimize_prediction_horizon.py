import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from strategies.btc_timing_strategy import (
    get_bitcoin_data, get_dxy_data, get_m2_data,
    calculate_technical_indicators, add_market_cycle_features,
    create_target_variable, prepare_features, train_model,
    generate_signals, backtest_strategy, calculate_performance_metrics
)
from datetime import datetime

def test_prediction_horizon(horizon):
    """测试特定预测天数的策略性能"""
    print(f"\n==== 测试预测天数: {horizon}天 ====")

    # 设置回测参数
    start_date = '2010-01-01'
    end_date = datetime.now().strftime('%Y-%m-%d')
    initial_capital = 10000

    # 1. 获取数据
    print("1. 获取数据...")
    btc_data = get_bitcoin_data(start_date, end_date)

    # 获取DXY数据
    dxy_data = get_dxy_data()
    if not dxy_data.empty:
        # 确保两个数据框的索引都是日期类型
        btc_data.index = pd.to_datetime(btc_data.index)
        dxy_data.index = pd.to_datetime(dxy_data.index)

        # 使用reindex方法将DXY数据对齐到比特币数据的日期
        aligned_dxy = dxy_data.reindex(btc_data.index)
        aligned_dxy = aligned_dxy.fillna(method='ffill')
        btc_data['DXY'] = aligned_dxy['DXY']

    # 获取M2数据
    m2_data = get_m2_data()
    if not m2_data.empty:
        # 确保两个数据框的索引都是日期类型
        btc_data.index = pd.to_datetime(btc_data.index)
        m2_data.index = pd.to_datetime(m2_data.index)

        # 使用reindex方法将M2数据对齐到比特币数据的日期
        aligned_m2 = m2_data.reindex(btc_data.index)
        aligned_m2 = aligned_m2.fillna(method='ffill')
        btc_data['M2'] = aligned_m2['M2']
        btc_data['M2_YoY'] = aligned_m2['M2_YoY']

        # 创建M2/DXY比率特征
        if 'DXY' in btc_data.columns and 'M2' in btc_data.columns:
            btc_data['M2_DXY_Ratio'] = btc_data['M2'] / btc_data['DXY']
            btc_data['M2_DXY_Ratio_Change'] = btc_data['M2_DXY_Ratio'].pct_change(periods=30) * 100
            btc_data['M2_DXY_Ratio_Shifted_78d'] = btc_data['M2_DXY_Ratio'].shift(-78)
            btc_data['M2_DXY_Ratio_Shifted_78d_Change'] = btc_data['M2_DXY_Ratio_Shifted_78d'].pct_change(periods=30) * 100

    # 2. 计算技术指标
    print("2. 计算技术指标...")
    btc_data = calculate_technical_indicators(btc_data)
    btc_data = add_market_cycle_features(btc_data)

    # 3. 创建目标变量（使用指定的预测天数）
    print(f"3. 创建目标变量 (预测天数: {horizon}天)...")
    btc_data = create_target_variable(btc_data, horizon=horizon)

    # 4. 特征选择
    print("4. 特征选择...")
    selected_features = [
        # 技术指标
        'MA_Cross_20_50', 'MA_Cross_50_200', 'RSI_14',
        'MACD', 'MACD_Hist', 'BB_Width', 'BB_Position',
        'Volume_Ratio', 'Momentum_10', 'Volatility_20',
        'Return_5d', 'Return_10d',

        # 市场周期特征
        'Price_From_ATH',
        'Day_of_Week_Sin', 'Day_of_Week_Cos',
        'Month_Sin', 'Month_Cos'
    ]

    # 添加M2、DXY和M2/DXY比率特征
    for feature in ['DXY', 'M2_YoY', 'M2_DXY_Ratio', 'M2_DXY_Ratio_Change',
                   'M2_DXY_Ratio_Shifted_78d', 'M2_DXY_Ratio_Shifted_78d_Change']:
        if feature in btc_data.columns:
            selected_features.append(feature)

    # 5. 准备建模数据
    print("5. 准备建模数据...")
    X, y, clean_data = prepare_features(btc_data, selected_features)

    # 6. 训练模型
    print("6. 训练模型...")
    model, scaler, feature_importance = train_model(X, y, train_size=0.8, val_size=0.1, test_size=0.1)

    # 7. 生成交易信号（仅用于测试集）
    print("7. 生成交易信号...")
    # 将数据分为训练集、验证集和测试集
    train_size = 0.8
    val_size = 0.1
    test_size = 0.1

    # 计算划分点
    train_end_idx = int(len(clean_data) * train_size)
    val_end_idx = train_end_idx + int(len(clean_data) * val_size)

    # 只使用测试集数据进行回测
    test_data = clean_data.iloc[val_end_idx:]

    # 只为测试集生成交易信号
    signals = generate_signals(test_data, model, scaler, selected_features)

    # 8. 回测策略
    print("8. 回测策略...")
    backtest_results = backtest_strategy(test_data, signals, initial_capital=initial_capital, trading_fee=0.001)

    # 9. 计算性能指标
    performance = calculate_performance_metrics(backtest_results)

    # 输出性能指标
    print("\n==== 策略性能指标 ====")
    print(f"总收益率: 策略={performance['Total Return']['Strategy']:.2%}, 市场={performance['Total Return']['Market']:.2%}")
    print(f"年化收益率: 策略={performance['Annual Return']['Strategy']:.2%}, 市场={performance['Annual Return']['Market']:.2%}")
    print(f"最大回撤: 策略={performance['Max Drawdown']['Strategy']:.2%}, 市场={performance['Max Drawdown']['Market']:.2%}")
    print(f"夏普比率: 策略={performance['Sharpe Ratio']['Strategy']:.2f}, 市场={performance['Sharpe Ratio']['Market']:.2f}")
    print(f"交易次数: {performance['Trading Stats']['Number of Trades']}")
    print(f"胜率: {performance['Trading Stats']['Win Rate']:.2%}")

    return {
        'horizon': horizon,
        'model': model,
        'scaler': scaler,
        'feature_importance': feature_importance,
        'test_accuracy': performance['Trading Stats']['Win Rate'],
        'total_return': performance['Total Return']['Strategy'],
        'annual_return': performance['Annual Return']['Strategy'],
        'max_drawdown': performance['Max Drawdown']['Strategy'],
        'sharpe_ratio': performance['Sharpe Ratio']['Strategy'],
        'num_trades': performance['Trading Stats']['Number of Trades']
    }

def optimize_prediction_horizon():
    """测试不同的预测天数，找出最优的预测天数"""
    # 要测试的预测天数列表
    horizons = [10, 20, 30]  # 只测试三个关键的预测天数

    # 存储每个预测天数的结果
    results = []

    # 测试每个预测天数
    for horizon in horizons:
        result = test_prediction_horizon(horizon)
        results.append(result)

    # 创建结果数据框
    results_df = pd.DataFrame(results)

    # 按夏普比率排序
    results_df = results_df.sort_values('sharpe_ratio', ascending=False)

    print("\n==== 预测天数优化结果 ====")
    print(results_df[['horizon', 'total_return', 'annual_return', 'max_drawdown', 'sharpe_ratio', 'num_trades', 'test_accuracy']])

    # 绘制结果
    plt.figure(figsize=(15, 10))

    # 1. 总收益率
    plt.subplot(2, 2, 1)
    plt.bar(results_df['horizon'].astype(str), results_df['total_return'] * 100)
    plt.title('总收益率 vs 预测天数')
    plt.xlabel('预测天数')
    plt.ylabel('总收益率 (%)')
    plt.grid(True, alpha=0.3)

    # 2. 夏普比率
    plt.subplot(2, 2, 2)
    plt.bar(results_df['horizon'].astype(str), results_df['sharpe_ratio'])
    plt.title('夏普比率 vs 预测天数')
    plt.xlabel('预测天数')
    plt.ylabel('夏普比率')
    plt.grid(True, alpha=0.3)

    # 3. 最大回撤
    plt.subplot(2, 2, 3)
    plt.bar(results_df['horizon'].astype(str), results_df['max_drawdown'] * 100)
    plt.title('最大回撤 vs 预测天数')
    plt.xlabel('预测天数')
    plt.ylabel('最大回撤 (%)')
    plt.grid(True, alpha=0.3)

    # 4. 交易次数
    plt.subplot(2, 2, 4)
    plt.bar(results_df['horizon'].astype(str), results_df['num_trades'])
    plt.title('交易次数 vs 预测天数')
    plt.xlabel('预测天数')
    plt.ylabel('交易次数')
    plt.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('prediction_horizon_optimization.png', dpi=300)
    plt.show()

    # 返回最优的预测天数
    best_horizon = results_df.iloc[0]['horizon']
    print(f"\n最优预测天数: {best_horizon}天")
    print(f"夏普比率: {results_df.iloc[0]['sharpe_ratio']:.2f}")
    print(f"总收益率: {results_df.iloc[0]['total_return']:.2%}")

    return best_horizon

if __name__ == "__main__":
    best_horizon = optimize_prediction_horizon()
