import csv
import os

# Input and output file paths
input_file = 'data/csi300_constituents_formatted_20250503.csv'
output_file = 'data/csi300_constituents_formatted_20250503_padded.csv'

# Create the output directory if it doesn't exist
os.makedirs(os.path.dirname(output_file), exist_ok=True)

# Read the input CSV file and write to the output CSV file
with open(input_file, 'r', encoding='utf-8') as infile, open(output_file, 'w', encoding='utf-8', newline='') as outfile:
    reader = csv.reader(infile)
    writer = csv.writer(outfile)
    
    # Process each row
    for row in reader:
        if len(row) > 0 and row[0] != '成分券代码':  # Skip header row
            # Pad the stock code with leading zeros if it has fewer than 6 digits
            row[0] = row[0].zfill(6)
        
        # Write the row to the output file
        writer.writerow(row)

print(f"Stock codes have been padded with leading zeros and saved to {output_file}")
