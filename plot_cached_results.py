#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Plot S&P 500 Relative Strength Results from Cached Data
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime
import warnings
from sp500_cached_analyzer import CachedSP500Analyzer

warnings.filterwarnings('ignore')

def plot_relative_strength_analysis():
    """Create comprehensive relative strength visualization"""
    
    print("🎯 Creating S&P 500 Relative Strength Visualization from Cache")
    print("=" * 60)
    
    # Create analyzer and run analysis (will use cache)
    analyzer = CachedSP500Analyzer(period="1y", benchmark="SPY", cache_dir="sp500_cache")
    
    print("🔄 Running analysis with cached data...")
    start_time = datetime.now()
    
    if not analyzer.run_analysis(force_refresh=False):
        print("❌ Analysis failed")
        return
    
    end_time = datetime.now()
    duration = (end_time - start_time).total_seconds()
    print(f"✅ Analysis completed in {duration:.1f} seconds (using cache)")
    
    # Create comprehensive visualization
    fig, axes = plt.subplots(2, 3, figsize=(20, 12))
    fig.suptitle('S&P 500 Relative Strength Analysis - Complete Market Overview', 
                fontsize=16, fontweight='bold')
    
    # 1. Top 15 Relative Strength Lines (Top Left)
    ax1 = axes[0, 0]
    top_15 = analyzer.analysis_results.head(15)
    colors = plt.cm.tab20(np.linspace(0, 1, 15))
    
    for i, (_, stock) in enumerate(top_15.iterrows()):
        symbol = stock['symbol']
        if symbol in analyzer.relative_strength_data:
            rs_data = analyzer.relative_strength_data[symbol]
            ax1.plot(rs_data.index, rs_data.values, 
                    label=f"{symbol} ({stock['strength_score']:.0f})", 
                    color=colors[i], linewidth=2, alpha=0.8)
    
    ax1.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='SPY Baseline')
    ax1.set_title('Top 15 Strongest Stocks', fontsize=12, fontweight='bold')
    ax1.set_xlabel('Date')
    ax1.set_ylabel('Relative Strength')
    ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
    ax1.grid(True, alpha=0.3)
    ax1.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
    ax1.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
    plt.setp(ax1.xaxis.get_majorticklabels(), rotation=45)
    
    # 2. Strength Score Distribution (Top Center)
    ax2 = axes[0, 1]
    scores = analyzer.analysis_results['strength_score']
    bins = [0, 30, 50, 70, 85, 100]
    labels = ['Weak\n(0-30)', 'Below Avg\n(30-50)', 'Average\n(50-70)', 'Strong\n(70-85)', 'Very Strong\n(85-100)']
    colors_hist = ['red', 'orange', 'yellow', 'lightgreen', 'green']
    
    counts, _, patches = ax2.hist(scores, bins=bins, alpha=0.7, edgecolor='black')

    # Color the bars manually
    for i, patch in enumerate(patches):
        patch.set_facecolor(colors_hist[i])
    ax2.set_title(f'Strength Distribution\n({len(analyzer.analysis_results)} Stocks)', fontsize=12, fontweight='bold')
    ax2.set_xlabel('Strength Score')
    ax2.set_ylabel('Number of Stocks')
    ax2.set_xticks([(bins[i] + bins[i+1])/2 for i in range(len(bins)-1)])
    ax2.set_xticklabels(labels, rotation=45, ha='right')
    
    for i, count in enumerate(counts):
        ax2.text((bins[i] + bins[i+1])/2, count + 2, f'{int(count)}', 
                ha='center', va='bottom', fontweight='bold')
    ax2.grid(True, alpha=0.3, axis='y')
    
    # 3. Performance vs SPY (Top Right)
    ax3 = axes[0, 2]
    above_spy = len(analyzer.analysis_results[analyzer.analysis_results['current_rs'] > 100])
    below_spy = len(analyzer.analysis_results) - above_spy
    
    performance_data = [above_spy, below_spy]
    performance_labels = [f'Outperforming\nSPY\n({above_spy})', f'Underperforming\nSPY\n({below_spy})']
    colors_pie = ['lightgreen', 'lightcoral']
    
    wedges, texts, autotexts = ax3.pie(performance_data, labels=performance_labels, autopct='%1.1f%%',
                                      colors=colors_pie, startangle=90)
    ax3.set_title('Performance vs SPY', fontsize=12, fontweight='bold')
    
    # 4. Rising Stocks Bar Chart (Bottom Left)
    ax4 = axes[1, 0]
    rising_stocks = analyzer.get_rising_stocks(min_score=60)
    
    if not rising_stocks.empty:
        top_rising = rising_stocks.head(12)
        bars = ax4.barh(range(len(top_rising)), top_rising['current_rs'], 
                       color=plt.cm.RdYlGn(top_rising['strength_score']/100))
        
        ax4.set_title(f'Top 12 Rising Stocks\n(Out of {len(rising_stocks)} total)', fontsize=12, fontweight='bold')
        ax4.set_xlabel('Current Relative Strength')
        ax4.set_ylabel('Stock Ranking')
        ax4.set_yticks(range(len(top_rising)))
        ax4.set_yticklabels([f"{i+1}. {symbol}" for i, symbol in enumerate(top_rising['symbol'])])
        
        for i, bar in enumerate(bars):
            width = bar.get_width()
            ax4.text(width + 2, bar.get_y() + bar.get_height()/2, 
                    f'{width:.0f}', ha='left', va='center', fontsize=8)
        
        ax4.axvline(x=100, color='red', linestyle='--', alpha=0.7, label='SPY Baseline')
        ax4.legend()
        ax4.grid(True, alpha=0.3, axis='x')
    
    # 5. Relative Strength vs Change Scatter (Bottom Center)
    ax5 = axes[1, 1]
    top_50 = analyzer.analysis_results.head(50)
    scatter = ax5.scatter(top_50['current_rs'], top_50['total_change_pct'], 
                         c=top_50['strength_score'], cmap='RdYlGn', 
                         s=60, alpha=0.7, edgecolors='black')
    
    # Add labels for top 10
    for _, stock in top_50.head(10).iterrows():
        ax5.annotate(stock['symbol'], 
                   (stock['current_rs'], stock['total_change_pct']),
                   xytext=(3, 3), textcoords='offset points', 
                   fontsize=8, alpha=0.8)
    
    ax5.axhline(y=0, color='red', linestyle='--', alpha=0.5)
    ax5.axvline(x=100, color='red', linestyle='--', alpha=0.5)
    ax5.set_title('Current RS vs Total Change\n(Top 50 Stocks)', fontsize=12, fontweight='bold')
    ax5.set_xlabel('Current Relative Strength')
    ax5.set_ylabel('Total Change (%)')
    
    cbar = plt.colorbar(scatter, ax=ax5)
    cbar.set_label('Strength Score')
    ax5.grid(True, alpha=0.3)
    
    # 6. Market Statistics (Bottom Right)
    ax6 = axes[1, 2]
    ax6.axis('off')
    
    # Calculate statistics
    total_stocks = len(analyzer.analysis_results)
    high_strength = len(analyzer.analysis_results[analyzer.analysis_results['strength_score'] >= 70])
    rising_count = len(rising_stocks)
    
    stats_text = f"""
📊 MARKET STATISTICS

Total Stocks Analyzed: {total_stocks}
Analysis Period: 1 Year
Benchmark: SPY

🏆 PERFORMANCE BREAKDOWN:
• Outperforming SPY: {above_spy} ({above_spy/total_stocks*100:.1f}%)
• Underperforming SPY: {below_spy} ({below_spy/total_stocks*100:.1f}%)

💪 STRENGTH BREAKDOWN:
• High Strength (≥70): {high_strength} ({high_strength/total_stocks*100:.1f}%)
• Medium Strength (50-69): {len(analyzer.analysis_results[(analyzer.analysis_results['strength_score'] >= 50) & (analyzer.analysis_results['strength_score'] < 70)])} stocks
• Low Strength (<50): {len(analyzer.analysis_results[analyzer.analysis_results['strength_score'] < 50])} stocks

📈 RISING STOCKS:
• Consistently Rising: {rising_count} ({rising_count/total_stocks*100:.1f}%)

🔝 TOP PERFORMER:
• {analyzer.analysis_results.iloc[0]['symbol']}: RS {analyzer.analysis_results.iloc[0]['current_rs']:.1f}
"""
    
    ax6.text(0.05, 0.95, stats_text, transform=ax6.transAxes, fontsize=10,
            verticalalignment='top', fontfamily='monospace',
            bbox=dict(boxstyle='round', facecolor='lightgray', alpha=0.8))
    
    plt.tight_layout()
    
    # Save the visualization
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"sp500_relative_strength_complete_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📁 Comprehensive visualization saved to: {filename}")
    
    plt.show()
    
    # Create individual charts for top performers
    create_top_performers_detail(analyzer)

def create_top_performers_detail(analyzer):
    """Create detailed charts for top performing stocks"""
    
    print("\n📊 Creating detailed charts for top performers...")
    
    # Get top 9 performers for a 3x3 grid
    top_performers = analyzer.analysis_results.head(9)
    
    fig, axes = plt.subplots(3, 3, figsize=(18, 12))
    fig.suptitle('Top 9 Strongest Stocks - Detailed Relative Strength Analysis', 
                fontsize=16, fontweight='bold')
    
    for i, (_, stock) in enumerate(top_performers.iterrows()):
        row = i // 3
        col = i % 3
        ax = axes[row, col]
        
        symbol = stock['symbol']
        if symbol in analyzer.relative_strength_data:
            rs_data = analyzer.relative_strength_data[symbol]
            
            # Plot relative strength line
            ax.plot(rs_data.index, rs_data.values, 'b-', linewidth=2, label='Relative Strength')
            
            # Add moving averages
            if len(rs_data) >= 20:
                ma20 = rs_data.rolling(20).mean()
                ax.plot(ma20.index, ma20.values, 'orange', linewidth=1, alpha=0.8, label='MA20')
            
            if len(rs_data) >= 50:
                ma50 = rs_data.rolling(50).mean()
                ax.plot(ma50.index, ma50.values, 'red', linewidth=1, alpha=0.8, label='MA50')
            
            # Add baseline
            ax.axhline(y=100, color='gray', linestyle='--', alpha=0.7, label='SPY Baseline')
            
            # Title with key metrics
            title = f"{symbol}\nScore: {stock['strength_score']:.0f} | RS: {stock['current_rs']:.1f} | Change: {stock['total_change_pct']:.1f}%"
            ax.set_title(title, fontsize=11, fontweight='bold')
            ax.set_xlabel('Date')
            ax.set_ylabel('Relative Strength')
            ax.legend(fontsize=8)
            ax.grid(True, alpha=0.3)
            
            # Format x-axis dates
            ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
            ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
            plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
    
    plt.tight_layout()
    
    # Save the top performers chart
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"sp500_top_performers_detail_{timestamp}.png"
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"📁 Top performers detail chart saved to: {filename}")
    
    plt.show()

def generate_summary_report(analyzer):
    """Generate a comprehensive summary report"""
    
    print("\n📝 Generating comprehensive summary report...")
    
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    report_file = f"sp500_summary_report_{timestamp}.txt"
    
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write("S&P 500 RELATIVE STRENGTH ANALYSIS - COMPREHENSIVE REPORT\n")
        f.write("=" * 70 + "\n")
        f.write(f"Generated: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
        f.write(f"Analysis Period: 1 Year\n")
        f.write(f"Benchmark: SPY\n")
        f.write(f"Total Stocks Analyzed: {len(analyzer.analysis_results)}\n\n")
        
        # Top 30 strongest stocks
        f.write("TOP 30 STRONGEST STOCKS:\n")
        f.write("-" * 50 + "\n")
        f.write(f"{'Rank':<4} {'Symbol':<8} {'Score':<6} {'Current RS':<12} {'Change %':<10}\n")
        f.write("-" * 50 + "\n")
        
        for i, (_, stock) in enumerate(analyzer.analysis_results.head(30).iterrows(), 1):
            f.write(f"{i:<4} {stock['symbol']:<8} {stock['strength_score']:<6.0f} "
                   f"{stock['current_rs']:<12.1f} {stock['total_change_pct']:<10.1f}\n")
        
        # Rising stocks
        rising_stocks = analyzer.get_rising_stocks(min_score=60)
        f.write(f"\nCONSISTENTLY RISING STOCKS ({len(rising_stocks)}):\n")
        f.write("-" * 50 + "\n")
        
        for i, (_, stock) in enumerate(rising_stocks.iterrows(), 1):
            f.write(f"{i:<4} {stock['symbol']:<8} {stock['strength_score']:<6.0f} "
                   f"{stock['current_rs']:<12.1f}\n")
        
        # Statistics
        total_stocks = len(analyzer.analysis_results)
        above_spy = len(analyzer.analysis_results[analyzer.analysis_results['current_rs'] > 100])
        high_strength = len(analyzer.analysis_results[analyzer.analysis_results['strength_score'] >= 70])
        
        f.write(f"\nMARKET STATISTICS:\n")
        f.write("-" * 30 + "\n")
        f.write(f"Outperforming SPY: {above_spy} ({above_spy/total_stocks*100:.1f}%)\n")
        f.write(f"High Strength (≥70): {high_strength} ({high_strength/total_stocks*100:.1f}%)\n")
        f.write(f"Rising Stocks: {len(rising_stocks)} ({len(rising_stocks)/total_stocks*100:.1f}%)\n")
    
    print(f"📁 Summary report saved to: {report_file}")

def main():
    """Main function"""
    print("🎯 S&P 500 Relative Strength Visualization from Cache")
    print("=" * 60)
    
    # Create visualization
    plot_relative_strength_analysis()
    
    print("\n✅ Visualization completed!")
    print("📁 Check the generated PNG files for detailed charts.")

if __name__ == "__main__":
    main()
