import requests
import pandas as pd
import json
from datetime import datetime
import os

def scrape_hsi_constituents_api():
    """
    Scrape Hang Seng Index constituents using the API endpoint

    Returns:
        pandas.DataFrame: DataFrame with HSI constituent stocks
    """
    print("Scraping Hang Seng Index constituents using API...")

    # API endpoint for HSI constituents
    url = "https://www.hsi.com.hk/data/eng/rt/index-series/hsi/constituents.do"

    try:
        # Send HTTP request to the API
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "Referer": "https://www.hsi.com.hk/eng/indexes/all-indexes/hsi",
            "Accept": "application/json, text/javascript, */*; q=0.01",
            "X-Requested-With": "XMLHttpRequest"
        }

        response = requests.get(url, headers=headers)
        response.raise_for_status()  # Raise an exception for HTTP errors

        # Get the response text
        response_text = response.text

        if not response_text:
            print("Error: Empty response received")
            return None

        print("Parsing JSON response...")

        # Parse the JSON response
        data = json.loads(response_text)

        # Save raw JSON to file (for debugging and future reference)
        output_dir = "data"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        json_file = os.path.join(output_dir, f"hsi_constituents_raw_{timestamp}.json")

        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2)

        print(f"Raw JSON data saved to {json_file}")

        # Extract constituents data
        constituents = []

        if 'indexSeriesList' in data:
            # Look for Hang Seng Index in the index series list
            for series in data['indexSeriesList']:
                if series.get('seriesName') == "Hang Seng Index and Sub-indexes" or series.get('seriesCode') == "hsi":
                    # Found the HSI series, now look for the main index
                    for index in series.get('indexList', []):
                        if index.get('indexName') == "Hang Seng Index":
                            # Found the main HSI index
                            # Check if constituents are directly in this index
                            if 'constituentContent' in index and isinstance(index['constituentContent'], list):
                                constituents = index['constituentContent']
                                print(f"Found {len(constituents)} constituents in the main HSI index")

                            # If not found, check sub-indexes
                            if not constituents:
                                for sub_index in index.get('subIndexList', []):
                                    if 'constituentContent' in sub_index and isinstance(sub_index['constituentContent'], list):
                                        constituents.extend(sub_index['constituentContent'])
                                        print(f"Found {len(sub_index['constituentContent'])} constituents in sub-index: {sub_index.get('indexName')}")

        if not constituents:
            print("Could not find constituents data in the JSON response.")
            return None

        # Convert to DataFrame
        df = pd.DataFrame(constituents)

        # Clean up column names
        df.columns = [col.strip() for col in df.columns]

        return df

    except Exception as e:
        print(f"Error accessing HSI constituents API: {str(e)}")
        return None

def scrape_hsi_constituents():
    """
    Scrape Hang Seng Index constituents from the official API
    https://www.hsi.com.hk/data/eng/rt/index-series/hsi/constituents.do

    Returns:
        pandas.DataFrame: DataFrame with HSI constituent stocks
    """
    print("Scraping Hang Seng Index constituents...")

    # Use the API method to get constituents
    df = scrape_hsi_constituents_api()

    if df is not None:
        return df
    else:
        print("Failed to scrape HSI constituents.")
        return None

def main():
    # Scrape HSI constituents
    constituents_df = scrape_hsi_constituents()

    if constituents_df is not None:
        # Save to CSV
        output_dir = "data"
        if not os.path.exists(output_dir):
            os.makedirs(output_dir)

        timestamp = datetime.now().strftime("%Y%m%d")
        output_file = os.path.join(output_dir, f"hsi_constituents_{timestamp}.csv")
        constituents_df.to_csv(output_file, index=False)

        # Also save to a standard filename for easy access
        standard_file = os.path.join(output_dir, "hsi_constituents.csv")
        constituents_df.to_csv(standard_file, index=False)

        print(f"Successfully scraped {len(constituents_df)} HSI constituent stocks")
        print(f"Data saved to {output_file} and {standard_file}")

        # Display the first few rows
        print("\nHSI Constituents Preview:")
        print(constituents_df.head())

        # Display summary
        print(f"\nTotal number of constituents: {len(constituents_df)}")

if __name__ == "__main__":
    print("Starting HSI constituents scraper...")
    main()
    print("Script execution completed.")
