#!/usr/bin/env python3
"""
恒生指数成分股快速股息率排名器
优化版本：缓存机制 + 简化处理流程
根据分红频率智能计算年度分红总和：
- 季度分红：取最近4次分红
- 半年分红：取最近2次分红  
- 年度分红：取最近1次分红
"""

import os
import json
import pandas as pd
import numpy as np
import akshare as ak
from datetime import datetime, timedelta
import time
from collections import Counter
import pickle

class HSIFastDividendYieldRanking:
    """
    恒生指数成分股快速股息率排名器
    """
    
    def __init__(self, dividend_data_dir: str = "hsi_dividend_data_20250603_173841", 
                 cache_dir: str = "cache", 
                 output_dir: str = "hsi_fast_dividend_yield_rankings"):
        self.dividend_data_dir = dividend_data_dir
        self.cache_dir = cache_dir
        self.output_dir = output_dir
        self.dividend_data = {}
        self.ranking_results = []
        self.failed_stocks = []
        self.price_cache = {}
        
        # 创建输出目录
        os.makedirs(output_dir, exist_ok=True)
        os.makedirs(cache_dir, exist_ok=True)
        
    def load_dividend_data(self):
        """加载分红数据"""
        print("📁 加载分红数据...")
        
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        
        for file in dividend_files:
            try:
                file_path = os.path.join(self.dividend_data_dir, file)
                with open(file_path, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                stock_code = data['stock_code']
                
                # 转换日期格式
                dividend_history = []
                for div in data['dividend_history']:
                    try:
                        # 处理日期格式
                        ex_date_str = div['ex_date']
                        if '/' in ex_date_str:
                            ex_date = datetime.strptime(ex_date_str, '%Y/%m/%d')
                        else:
                            ex_date = datetime.strptime(ex_date_str, '%Y-%m-%d')
                        
                        dividend_history.append({
                            'year': div['year'],
                            'ex_date': ex_date,
                            'amount': float(div['amount']),
                            'report_type': div.get('report_type', ''),
                            'plan': div.get('plan', '')
                        })
                    except Exception as e:
                        continue
                
                # 按除权日期排序（最新的在前）
                dividend_history.sort(key=lambda x: x['ex_date'], reverse=True)
                
                self.dividend_data[stock_code] = {
                    'stock_name': data['stock_name'],
                    'dividend_history': dividend_history
                }
                
            except Exception as e:
                continue
        
        print(f"✅ 成功加载 {len(self.dividend_data)} 只股票的分红数据")
        return self.dividend_data
    
    def analyze_dividend_frequency(self, dividend_history):
        """改进的分红频率分析"""
        if len(dividend_history) < 2:
            return "年度", 1, "单次分红"

        # 分析分红类型
        report_types = [div.get('report_type', '') for div in dividend_history[:8]]

        # 统计各种分红类型
        annual_count = sum(1 for rt in report_types if '年度' in rt)
        interim_count = sum(1 for rt in report_types if '中期' in rt)
        quarterly_count = sum(1 for rt in report_types if '季' in rt or 'Q' in rt.upper())
        special_count = sum(1 for rt in report_types if '特别' in rt)

        # 计算相邻分红之间的间隔（月数）
        intervals = []
        for i in range(min(len(dividend_history) - 1, 8)):  # 看最近8次间隔
            date1 = dividend_history[i]['ex_date']
            date2 = dividend_history[i + 1]['ex_date']
            months_diff = (date1.year - date2.year) * 12 + (date1.month - date2.month)
            intervals.append(abs(months_diff))

        # 判断分红频率
        if intervals:
            avg_interval = np.mean(intervals)

            # 统计不同间隔的出现次数
            quarterly_intervals = sum(1 for interval in intervals if 2 <= interval <= 4)
            semi_annual_intervals = sum(1 for interval in intervals if 5 <= interval <= 8)
            annual_intervals = sum(1 for interval in intervals if 10 <= interval <= 14)

            # 改进的决策逻辑
            if quarterly_count >= 3:
                return "季度", 4, f"季度分红({quarterly_count}次)"
            elif interim_count >= 1 and annual_count >= 1:
                # 有中期分配和年度分配，按半年分红处理，取最近2次
                return "半年", 2, f"年度+中期({annual_count}+{interim_count})"
            elif quarterly_intervals >= 3:
                # 间隔主要是3-4个月，可能是季度分红
                return "季度", 4, f"间隔季度({quarterly_intervals}次)"
            elif semi_annual_intervals >= 2:
                return "半年", 2, f"半年分红({semi_annual_intervals}次)"
            elif avg_interval >= 10:
                # 平均间隔超过10个月，年度分红
                return "年度", 1, f"年度分红(间隔{avg_interval:.1f}月)"
            else:
                # 默认判断为半年分红
                return "半年", 2, f"混合模式(间隔{avg_interval:.1f}月)"

        return "年度", 1, "默认年度"
    
    def calculate_rolling_12m_dividend(self, dividend_history):
        """计算滚动12个月分红总和"""
        cutoff_date = datetime.now() - timedelta(days=365)
        rolling_dividends = [
            div for div in dividend_history
            if div['ex_date'] >= cutoff_date
        ]

        total_dividend = sum(div['amount'] for div in rolling_dividends)
        return total_dividend, len(rolling_dividends)

    def calculate_smart_annual_dividend(self, stock_code: str):
        """改进的智能年度分红计算"""
        if stock_code not in self.dividend_data:
            return 0.0, "无数据", 0, ""

        dividend_history = self.dividend_data[stock_code]['dividend_history']

        if not dividend_history:
            return 0.0, "无分红", 0, ""

        # 分析分红频率
        frequency_type, take_count, analysis_detail = self.analyze_dividend_frequency(dividend_history)

        # 取最近N次分红
        recent_dividends = dividend_history[:take_count]
        total_dividend = sum(div['amount'] for div in recent_dividends)
        return total_dividend, frequency_type, len(recent_dividends), analysis_detail
    
    def load_price_cache(self):
        """加载价格缓存"""
        cache_file = os.path.join(self.cache_dir, "hsi_fast_prices_cache.pkl")
        if os.path.exists(cache_file):
            try:
                with open(cache_file, 'rb') as f:
                    cache_data = pickle.load(f)
                    # 检查缓存是否过期（延长到4小时）
                    if datetime.now() - cache_data['timestamp'] < timedelta(hours=4):
                        self.price_cache = cache_data['prices']
                        print(f"💾 从缓存加载 {len(self.price_cache)} 只股票价格")
                        print(f"   缓存时间: {cache_data['timestamp'].strftime('%Y-%m-%d %H:%M:%S')}")
                        return True
                    else:
                        print(f"⏰ 价格缓存已过期，需要重新获取")
            except Exception as e:
                print(f"⚠️ 加载缓存失败: {e}")
        return False
    
    def save_price_cache(self):
        """保存价格缓存"""
        cache_file = os.path.join(self.cache_dir, "hsi_fast_prices_cache.pkl")
        cache_data = {
            'timestamp': datetime.now(),
            'prices': self.price_cache
        }
        with open(cache_file, 'wb') as f:
            pickle.dump(cache_data, f)
    
    def get_stock_list(self):
        """获取所有股票列表"""
        dividend_files = [f for f in os.listdir(self.dividend_data_dir) if f.endswith('.json')]
        stock_list = []
        
        for file in dividend_files:
            stock_code = file.split('_')[0]
            stock_name = file.split('_')[1].replace('.json', '')
            stock_list.append({
                'code': stock_code,
                'name': stock_name,
                'file': file
            })
        
        stock_list.sort(key=lambda x: x['code'])
        return stock_list
    
    def batch_get_all_prices(self, stock_codes):
        """批量获取所有股票价格（串行处理，避免API冲突）"""
        print(f"🌐 批量获取 {len(stock_codes)} 只股票价格...")
        
        # 过滤已缓存的股票
        uncached_codes = [code for code in stock_codes if code not in self.price_cache]
        
        if not uncached_codes:
            print("✅ 所有价格已缓存")
            return
        
        print(f"📊 需要获取 {len(uncached_codes)} 只股票的新价格")
        
        for i, stock_code in enumerate(uncached_codes, 1):
            try:
                print(f"   [{i}/{len(uncached_codes)}] 获取 {stock_code}...", end="")
                
                df = ak.stock_hk_daily(symbol=stock_code)
                if not df.empty:
                    price = float(df.iloc[-1]['close'])
                    self.price_cache[stock_code] = price
                    print(f" {price:.2f} 港元 ✅")
                else:
                    print(" 无数据 ❌")
                
                # 添加延迟避免API限制
                time.sleep(0.3)
                
            except Exception as e:
                print(f" 失败: {e} ❌")
                continue
        
        print(f"✅ 价格获取完成，缓存中共有 {len(self.price_cache)} 只股票价格")
        self.save_price_cache()
    
    def calculate_all_rankings(self):
        """计算所有股票的排名"""
        print("🚀 开始计算恒生指数成分股快速股息率排名")
        print("=" * 60)
        
        # 加载分红数据
        self.load_dividend_data()
        
        # 获取股票列表
        stock_list = self.get_stock_list()
        stock_codes = [stock['code'] for stock in stock_list]
        
        # 加载价格缓存
        cache_loaded = self.load_price_cache()
        
        # 批量获取价格
        if not cache_loaded or len(self.price_cache) < len(stock_codes) * 0.8:
            self.batch_get_all_prices(stock_codes)
        
        print(f"\n🔄 开始计算股息率...")
        start_time = datetime.now()
        
        successful_count = 0
        total_count = len(stock_list)
        
        for i, stock_info in enumerate(stock_list, 1):
            stock_code = stock_info['code']
            stock_name = stock_info['name']
            
            try:
                # 获取价格
                latest_price = self.price_cache.get(stock_code)
                
                if latest_price is None:
                    self.failed_stocks.append({
                        'code': stock_code,
                        'name': stock_name,
                        'reason': '无法获取最新价格'
                    })
                    print(f"❌ [{i}/{total_count}] {stock_code} ({stock_name}) - 无价格")
                    continue
                
                # 智能计算年度分红
                annual_dividend, frequency_type, dividend_count, analysis_detail = self.calculate_smart_annual_dividend(stock_code)
                
                # 计算股息率
                if annual_dividend > 0 and latest_price > 0:
                    dividend_yield = (annual_dividend / latest_price) * 100
                else:
                    dividend_yield = 0.0
                
                # 获取分红历史统计
                dividend_history = self.dividend_data.get(stock_code, {}).get('dividend_history', [])
                recent_dividends = [d for d in dividend_history 
                                  if d['ex_date'] >= datetime.now() - timedelta(days=1095)]
                
                # 记录结果
                result = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'latest_price': latest_price,
                    'dividend_yield': dividend_yield,
                    'annual_dividend': annual_dividend,
                    'frequency_type': frequency_type,
                    'dividend_count_used': dividend_count,
                    'analysis_detail': analysis_detail,
                    'dividend_frequency_3y': len(recent_dividends),
                    'last_dividend_date': dividend_history[0]['ex_date'] if dividend_history else None,
                    'last_dividend_amount': dividend_history[0]['amount'] if dividend_history else 0,
                    'calculation_date': datetime.now()
                }
                
                self.ranking_results.append(result)
                successful_count += 1
                
                print(f"✅ [{i}/{total_count}] {stock_code} ({stock_name}) - {dividend_yield:.2f}% ({frequency_type}: {analysis_detail})")
                
            except Exception as e:
                self.failed_stocks.append({
                    'code': stock_code,
                    'name': stock_name,
                    'reason': str(e)
                })
                print(f"❌ [{i}/{total_count}] {stock_code} ({stock_name}) - 错误: {e}")
            
            # 每10只股票显示一次进度
            if i % 10 == 0:
                elapsed = datetime.now() - start_time
                avg_time = elapsed.total_seconds() / i
                remaining = (total_count - i) * avg_time
                print(f"📊 进度: {i}/{total_count} ({i/total_count*100:.1f}%), "
                      f"成功: {successful_count}, "
                      f"预计剩余: {remaining/60:.1f}分钟")
        
        end_time = datetime.now()
        total_time = end_time - start_time
        
        print(f"\n🎉 计算完成!")
        print(f"   总耗时: {total_time}")
        print(f"   成功: {successful_count}/{total_count} ({successful_count/total_count*100:.1f}%)")
        print(f"   失败: {len(self.failed_stocks)}")
        
        return self.ranking_results

    def create_ranking_report(self):
        """创建排名报告"""
        if not self.ranking_results:
            print("❌ 没有排名数据可生成报告")
            return None

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

        # 按股息率排序
        sorted_results = sorted(self.ranking_results, key=lambda x: x['dividend_yield'], reverse=True)

        # 创建DataFrame
        df = pd.DataFrame(sorted_results)
        df['ranking'] = range(1, len(df) + 1)

        # 重新排列列顺序
        columns_order = [
            'ranking', 'stock_code', 'stock_name', 'latest_price',
            'dividend_yield', 'annual_dividend', 'frequency_type', 'analysis_detail', 'dividend_count_used',
            'dividend_frequency_3y', 'last_dividend_date', 'last_dividend_amount', 'calculation_date'
        ]
        df = df[columns_order]

        # 保存详细排名文件
        ranking_file = os.path.join(self.output_dir, f"hsi_fast_dividend_yield_ranking_{timestamp}.csv")
        df.to_csv(ranking_file, index=False, encoding='utf-8-sig')

        # 创建简化版排名文件
        simple_df = df[['ranking', 'stock_code', 'stock_name', 'latest_price', 'dividend_yield', 'frequency_type']].copy()
        simple_df.columns = ['排名', '股票代码', '股票名称', '最新价格(港元)', '股息率(%)', '分红频率']

        simple_file = os.path.join(self.output_dir, f"hsi_fast_dividend_yield_ranking_simple_{timestamp}.csv")
        simple_df.to_csv(simple_file, index=False, encoding='utf-8-sig')

        print(f"📊 快速排名报告已生成:")
        print(f"   详细版: {ranking_file}")
        print(f"   简化版: {simple_file}")

        return ranking_file, simple_file, df

    def print_top_rankings(self, top_n: int = 20):
        """打印前N名股票排名"""
        if not self.ranking_results:
            print("❌ 没有排名数据")
            return

        # 按股息率排序
        sorted_results = sorted(self.ranking_results, key=lambda x: x['dividend_yield'], reverse=True)

        print(f"\n🏆 快速股息率最高的前{top_n}只恒生指数成分股:")
        print("=" * 100)
        print(f"{'排名':<4} {'股票代码':<8} {'股票名称':<20} {'最新价格':<12} {'股息率':<10} {'年度分红':<10} {'分红频率':<8}")
        print("-" * 100)

        for i, result in enumerate(sorted_results[:top_n], 1):
            print(f"{i:<4} {result['stock_code']:<8} {result['stock_name']:<20} "
                  f"{result['latest_price']:<12.2f} {result['dividend_yield']:<10.4f}% "
                  f"{result['annual_dividend']:<10.2f} {result['frequency_type']:<8}")

        # 显示统计信息
        valid_yields = [r['dividend_yield'] for r in sorted_results if r['dividend_yield'] > 0]
        if valid_yields:
            print(f"\n📈 统计信息:")
            print(f"   有分红股票数: {len(valid_yields)}/{len(sorted_results)}")
            print(f"   股息率范围: {min(valid_yields):.4f}% - {max(valid_yields):.4f}%")
            print(f"   平均股息率: {np.mean(valid_yields):.4f}%")
            print(f"   中位数股息率: {np.median(valid_yields):.4f}%")

            # 分红频率统计
            frequency_stats = Counter([r['frequency_type'] for r in sorted_results if r['dividend_yield'] > 0])
            print(f"\n📊 分红频率分布:")
            for freq, count in frequency_stats.items():
                print(f"   {freq}分红: {count} 只股票")

def main():
    """主函数"""
    print("🚀 恒生指数成分股快速股息率排名器")
    print("=" * 50)

    # 创建排名器
    ranker = HSIFastDividendYieldRanking(
        dividend_data_dir="hsi_dividend_data_20250603_173841",
        output_dir="hsi_fast_dividend_yield_rankings"
    )

    try:
        # 计算所有股票排名
        results = ranker.calculate_all_rankings()

        if results:
            # 打印前20名
            ranker.print_top_rankings(20)

            # 生成排名报告
            report_files = ranker.create_ranking_report()

            if report_files:
                print(f"\n📁 报告文件已生成完成!")

            print(f"\n✅ 快速排名计算完成!")
        else:
            print("❌ 没有成功计算任何股票排名")

    except KeyboardInterrupt:
        print("\n⚠️  用户中断计算")
    except Exception as e:
        print(f"\n❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()


def calculate_ccb_dividend_yield():
    """专门计算建设银行(00939)的股息率"""
    print("🏦 计算建设银行(00939)股息率")
    print("=" * 50)

    # 创建排名器实例
    ranker = HSIFastDividendYieldRanking(
        dividend_data_dir="hsi_dividend_data_20250603_173841"
    )

    # 加载分红数据
    ranker.load_dividend_data()

    stock_code = "00939"
    stock_name = "建设银行"

    if stock_code not in ranker.dividend_data:
        print(f"❌ 未找到{stock_name}的分红数据")
        return

    try:
        # 获取最新价格
        print(f"📊 获取{stock_name}最新价格...")
        df = ak.stock_hk_daily(symbol=stock_code)
        if df.empty:
            print(f"❌ 无法获取{stock_name}的价格数据")
            return

        latest_price = float(df.iloc[-1]['close'])
        print(f"✅ 最新价格: {latest_price:.2f} 港元")

        # 智能计算年度分红
        annual_dividend, frequency_type, dividend_count, analysis_detail = ranker.calculate_smart_annual_dividend(stock_code)

        # 计算股息率
        if annual_dividend > 0 and latest_price > 0:
            dividend_yield = (annual_dividend / latest_price) * 100
        else:
            dividend_yield = 0.0

        # 获取分红历史统计
        dividend_history = ranker.dividend_data[stock_code]['dividend_history']
        recent_dividends = [d for d in dividend_history
                          if d['ex_date'] >= datetime.now() - timedelta(days=1095)]

        print(f"\n📈 {stock_name}股息率计算结果:")
        print("-" * 40)
        print(f"股票代码: {stock_code}")
        print(f"股票名称: {stock_name}")
        print(f"最新价格: {latest_price:.2f} 港元")
        print(f"年度分红: {annual_dividend:.4f} 港元")
        print(f"股息率: {dividend_yield:.4f}%")
        print(f"分红频率: {frequency_type}")
        print(f"分析详情: {analysis_detail}")
        print(f"计算使用的分红次数: {dividend_count}")
        print(f"近3年分红次数: {len(recent_dividends)}")

        if dividend_history:
            print(f"最近分红日期: {dividend_history[0]['ex_date'].strftime('%Y-%m-%d')}")
            print(f"最近分红金额: {dividend_history[0]['amount']:.4f} 港元")

        print(f"\n📊 最近几次分红详情:")
        print("-" * 60)
        print(f"{'日期':<12} {'金额(港元)':<12} {'类型':<15}")
        print("-" * 60)
        for i, div in enumerate(dividend_history[:5]):
            print(f"{div['ex_date'].strftime('%Y-%m-%d'):<12} {div['amount']:<12.4f} {div['report_type']:<15}")

        return {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'latest_price': latest_price,
            'dividend_yield': dividend_yield,
            'annual_dividend': annual_dividend,
            'frequency_type': frequency_type,
            'dividend_count_used': dividend_count
        }

    except Exception as e:
        print(f"❌ 计算过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
        return None

if __name__ == "__main__":
    # 检查命令行参数
    import sys
    if len(sys.argv) > 1:
        if sys.argv[1] == "--ccb":
            calculate_ccb_dividend_yield()
        elif sys.argv[1] == "--help":
            print("🚀 恒生指数成分股快速股息率排名器")
            print("=" * 50)
            print("用法:")
            print("  python hsi_fast_dividend_yield_ranking.py        # 完整排名")
            print("  python hsi_fast_dividend_yield_ranking.py --ccb  # 只计算建设银行")
            print("  python hsi_fast_dividend_yield_ranking.py --help # 显示帮助")
        else:
            main()
    else:
        main()
