"""
沪深300指数月末交易策略回测

策略描述：
- 买入时机：每月倒数第2个交易日买入沪深300指数
- 卖出时机：下个月持有5个交易日后卖出
- 其余时间持有现金

作者：AI Assistant
创建时间：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
import akshare as ak
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class CSI300MonthlyStrategy:
    def __init__(self, start_date="2018-01-01", end_date=None):
        """
        初始化沪深300指数月末交易策略

        Args:
            start_date: 回测开始日期
            end_date: 回测结束日期，默认为当前日期
        """
        self.start_date = start_date
        self.end_date = end_date if end_date else datetime.now().strftime("%Y-%m-%d")
        self.data = None
        self.signals = None
        self.results = None
        
    def get_csi300_data(self):
        """获取沪深300指数数据"""
        print("正在获取沪深300指数数据...")

        try:
            # 方法1：尝试使用akshare获取沪深300指数数据
            print("尝试使用akshare获取数据...")
            csi300_data = ak.stock_zh_index_daily(symbol="sh000300")

            if not csi300_data.empty:
                # 重命名列
                csi300_data = csi300_data.rename(columns={
                    'date': 'date',
                    'open': 'open',
                    'close': 'close',
                    'high': 'high',
                    'low': 'low',
                    'volume': 'volume'
                })

                # 设置日期索引
                csi300_data['date'] = pd.to_datetime(csi300_data['date'])
                csi300_data.set_index('date', inplace=True)
                csi300_data = csi300_data.sort_index()

                # 筛选日期范围
                start_dt = pd.to_datetime(self.start_date)
                end_dt = pd.to_datetime(self.end_date)
                csi300_data = csi300_data[(csi300_data.index >= start_dt) & (csi300_data.index <= end_dt)]

                if len(csi300_data) >= 100:
                    print(f"akshare获取成功，数据点数：{len(csi300_data)}")
                    self.data = csi300_data[['open', 'high', 'low', 'close', 'volume']].copy()
                    return True
                else:
                    print("akshare数据量不足")

        except Exception as e:
            print(f"akshare获取失败：{e}")

        try:
            # 方法2：尝试使用yfinance获取沪深300数据
            print("尝试使用yfinance获取数据...")
            # 尝试不同的沪深300指数代码
            tickers_to_try = ["000300.SS", "^HSCCI", "ASHR"]

            for ticker in tickers_to_try:
                try:
                    print(f"  尝试代码: {ticker}")
                    csi300_ticker = yf.Ticker(ticker)
                    data = csi300_ticker.history(start=self.start_date, end=self.end_date)

                    if not data.empty and len(data) > 100:
                        print(f"yfinance获取成功，数据点数：{len(data)}")
                        # 重命名列以保持一致性
                        data = data.rename(columns={
                            'Open': 'open',
                            'Close': 'close',
                            'High': 'high',
                            'Low': 'low',
                            'Volume': 'volume'
                        })
                        self.data = data[['open', 'high', 'low', 'close', 'volume']].copy()
                        return True
                except Exception as e:
                    print(f"  {ticker} 失败: {e}")
                    continue

        except Exception as e:
            print(f"yfinance整体获取失败：{e}")

        # 方法3：生成模拟数据用于演示
        print("尝试生成模拟数据用于演示...")
        return self.generate_demo_data()

    def generate_demo_data(self):
        """生成模拟的沪深300指数数据用于演示"""
        print("正在生成模拟沪深300指数数据...")

        # 创建日期范围（只包含工作日）
        start_dt = pd.to_datetime(self.start_date)
        end_dt = pd.to_datetime(self.end_date)

        # 生成工作日日期
        date_range = pd.bdate_range(start=start_dt, end=end_dt)

        # 设置随机种子以确保可重复性
        np.random.seed(42)

        # 模拟沪深300指数的特征
        n_days = len(date_range)
        base_price = 3500  # 基础价格

        # 生成价格走势（带有趋势和波动）
        returns = np.random.normal(0.0005, 0.025, n_days)  # 日收益率

        # 添加一些趋势和周期性
        trend = np.linspace(-0.1, 0.2, n_days)  # 整体上升趋势
        cycle = 0.1 * np.sin(2 * np.pi * np.arange(n_days) / 252)  # 年度周期

        returns = returns + trend/n_days + cycle/n_days

        # 计算价格
        prices = [base_price]
        for i in range(1, n_days):
            new_price = prices[-1] * (1 + returns[i])
            prices.append(new_price)

        prices = np.array(prices)

        # 生成OHLC数据
        data = pd.DataFrame(index=date_range)
        data['close'] = prices

        # 生成开盘价（基于前一日收盘价加上小幅波动）
        data['open'] = data['close'].shift(1) * (1 + np.random.normal(0, 0.005, n_days))
        data['open'].iloc[0] = base_price

        # 生成最高价和最低价
        daily_volatility = np.random.uniform(0.01, 0.04, n_days)
        data['high'] = np.maximum(data['open'], data['close']) * (1 + daily_volatility/2)
        data['low'] = np.minimum(data['open'], data['close']) * (1 - daily_volatility/2)

        # 生成成交量
        base_volume = 1000000
        data['volume'] = np.random.lognormal(np.log(base_volume), 0.5, n_days)

        # 确保数据类型正确
        for col in ['open', 'high', 'low', 'close', 'volume']:
            data[col] = pd.to_numeric(data[col], errors='coerce')

        self.data = data
        print(f"✅ 模拟数据生成成功，数据点数：{len(data)}")
        print(f"价格范围：{data['close'].min():.2f} - {data['close'].max():.2f}")
        print("⚠️  注意：这是模拟数据，仅用于演示策略逻辑")

        return True

    def identify_trading_dates(self):
        """识别每月倒数第2个交易日和持有期"""
        if self.data is None:
            print("请先获取数据")
            return None
            
        print("正在识别交易日期...")
        
        # 按月分组，找到每月倒数第2个交易日
        monthly_groups = self.data.groupby(self.data.index.to_period('M'))
        
        buy_dates = []
        sell_dates = []
        
        for period, group in monthly_groups:
            # 获取该月的所有交易日，按日期排序
            month_dates = group.index.sort_values()
            
            if len(month_dates) >= 2:
                # 倒数第2个交易日作为买入日
                buy_date = month_dates[-2]
                buy_dates.append(buy_date)
                
                # 找到下个月的第5个交易日作为卖出日
                next_month_start = buy_date + pd.DateOffset(months=1)
                next_month_start = next_month_start.replace(day=1)
                
                # 找到下个月开始后的交易日
                future_dates = self.data.index[self.data.index >= next_month_start]
                if len(future_dates) >= 5:
                    sell_date = future_dates[4]  # 第5个交易日（索引从0开始）
                    sell_dates.append((buy_date, sell_date))
        
        print(f"识别到 {len(buy_dates)} 个买入日期")
        print(f"识别到 {len(sell_dates)} 个完整交易对")
        
        return buy_dates, sell_dates

    def generate_signals(self):
        """生成交易信号"""
        if self.data is None:
            print("请先获取数据")
            return None
            
        print("正在生成交易信号...")
        
        # 获取交易日期
        buy_dates, sell_dates = self.identify_trading_dates()
        
        # 初始化信号
        self.data['position'] = 0  # 0表示持有现金，1表示持有恒生科技指数
        self.data['buy_signal'] = False
        self.data['sell_signal'] = False
        
        # 生成买入和卖出信号
        for buy_date, sell_date in sell_dates:
            # 在买入日到卖出日期间持有
            mask = (self.data.index >= buy_date) & (self.data.index <= sell_date)
            self.data.loc[mask, 'position'] = 1
            
            # 标记买入和卖出信号
            if buy_date in self.data.index:
                self.data.loc[buy_date, 'buy_signal'] = True
            if sell_date in self.data.index:
                self.data.loc[sell_date, 'sell_signal'] = True
        
        # 计算每日收益率
        self.data['daily_return'] = self.data['close'].pct_change()
        
        # 计算策略收益（只有持仓时才有收益）
        self.data['strategy_return'] = self.data['daily_return'] * self.data['position'].shift(1)

        # 计算基准收益（买入并持有）
        self.data['benchmark_return'] = self.data['daily_return']
        
        print("✅ 交易信号生成完成")
        return True

    def backtest(self):
        """执行回测"""
        if self.data is None or 'strategy_return' not in self.data.columns:
            print("请先生成交易信号")
            return None
            
        print("正在执行回测...")
        
        # 计算累积收益
        self.data['strategy_cumulative'] = (1 + self.data['strategy_return'].fillna(0)).cumprod()
        self.data['benchmark_cumulative'] = (1 + self.data['benchmark_return'].fillna(0)).cumprod()
        
        # 计算回撤
        def calculate_drawdown(cumulative_returns):
            running_max = cumulative_returns.cummax()
            drawdown = (cumulative_returns - running_max) / running_max
            return drawdown
        
        self.data['strategy_drawdown'] = calculate_drawdown(self.data['strategy_cumulative'])
        self.data['benchmark_drawdown'] = calculate_drawdown(self.data['benchmark_cumulative'])
        
        # 计算关键指标
        strategy_returns = self.data['strategy_return'].dropna()
        benchmark_returns = self.data['benchmark_return'].dropna()
        
        results = {
            'strategy_total_return': self.data['strategy_cumulative'].iloc[-1] - 1,
            'benchmark_total_return': self.data['benchmark_cumulative'].iloc[-1] - 1,
            'strategy_annual_return': (self.data['strategy_cumulative'].iloc[-1] ** (252 / len(strategy_returns))) - 1,
            'benchmark_annual_return': (self.data['benchmark_cumulative'].iloc[-1] ** (252 / len(benchmark_returns))) - 1,
            'strategy_volatility': strategy_returns.std() * np.sqrt(252),
            'benchmark_volatility': benchmark_returns.std() * np.sqrt(252),
            'strategy_max_drawdown': self.data['strategy_drawdown'].min(),
            'benchmark_max_drawdown': self.data['benchmark_drawdown'].min(),
            'strategy_sharpe': (strategy_returns.mean() * 252) / (strategy_returns.std() * np.sqrt(252)),
            'benchmark_sharpe': (benchmark_returns.mean() * 252) / (benchmark_returns.std() * np.sqrt(252)),
            'win_rate': (strategy_returns > 0).mean(),
            'total_trades': self.data['buy_signal'].sum(),
            'holding_days': (self.data['position'] == 1).sum(),
            'total_days': len(self.data)
        }
        
        self.results = results
        print("✅ 回测完成")
        return results

    def print_results(self):
        """打印回测结果"""
        if self.results is None:
            print("请先执行回测")
            return

        print("\n" + "="*60)
        print("沪深300指数月末交易策略回测结果")
        print("="*60)
        print(f"回测期间: {self.start_date} 至 {self.end_date}")
        print(f"总交易日数: {self.results['total_days']}")
        print(f"持仓天数: {self.results['holding_days']} ({self.results['holding_days']/self.results['total_days']:.1%})")
        print(f"总交易次数: {self.results['total_trades']}")
        print()
        
        print("收益指标:")
        print(f"策略总收益: {self.results['strategy_total_return']:.2%}")
        print(f"基准总收益: {self.results['benchmark_total_return']:.2%}")
        print(f"策略年化收益: {self.results['strategy_annual_return']:.2%}")
        print(f"基准年化收益: {self.results['benchmark_annual_return']:.2%}")
        print()
        
        print("风险指标:")
        print(f"策略波动率: {self.results['strategy_volatility']:.2%}")
        print(f"基准波动率: {self.results['benchmark_volatility']:.2%}")
        print(f"策略最大回撤: {self.results['strategy_max_drawdown']:.2%}")
        print(f"基准最大回撤: {self.results['benchmark_max_drawdown']:.2%}")
        print()
        
        print("风险调整收益:")
        print(f"策略夏普比率: {self.results['strategy_sharpe']:.3f}")
        print(f"基准夏普比率: {self.results['benchmark_sharpe']:.3f}")
        print(f"胜率: {self.results['win_rate']:.2%}")
        print("="*60)

    def plot_results(self, save_path=None):
        """绘制回测结果图表"""
        if self.data is None or self.results is None:
            print("请先执行回测")
            return

        # 创建图表
        fig, axes = plt.subplots(3, 1, figsize=(15, 12))

        # 1. 累积收益对比
        ax1 = axes[0]
        ax1.plot(self.data.index, self.data['strategy_cumulative'],
                label='月末交易策略', linewidth=2, color='red')
        ax1.plot(self.data.index, self.data['benchmark_cumulative'],
                label='买入持有基准', linewidth=2, color='blue')

        # 标记买入卖出点
        buy_points = self.data[self.data['buy_signal']]
        sell_points = self.data[self.data['sell_signal']]

        if not buy_points.empty:
            ax1.scatter(buy_points.index, buy_points['strategy_cumulative'],
                       color='green', marker='^', s=50, label='买入', zorder=5)
        if not sell_points.empty:
            ax1.scatter(sell_points.index, sell_points['strategy_cumulative'],
                       color='red', marker='v', s=50, label='卖出', zorder=5)

        ax1.set_title('沪深300指数月末交易策略 vs 买入持有策略', fontsize=14, fontweight='bold')
        ax1.set_ylabel('累积收益')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 回撤对比
        ax2 = axes[1]
        ax2.fill_between(self.data.index, self.data['strategy_drawdown'], 0,
                        alpha=0.3, color='red', label='策略回撤')
        ax2.fill_between(self.data.index, self.data['benchmark_drawdown'], 0,
                        alpha=0.3, color='blue', label='基准回撤')
        ax2.set_title('回撤对比', fontsize=12, fontweight='bold')
        ax2.set_ylabel('回撤')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 持仓状态和价格
        ax3 = axes[2]

        # 绘制价格
        ax3_price = ax3.twinx()
        ax3_price.plot(self.data.index, self.data['close'],
                      color='black', alpha=0.7, linewidth=1, label='沪深300指数')
        ax3_price.set_ylabel('指数价格', color='black')
        ax3_price.legend(loc='upper left')

        # 绘制持仓状态
        ax3.fill_between(self.data.index, self.data['position'], 0,
                        alpha=0.3, color='orange', label='持仓状态')
        ax3.set_title('持仓状态与指数价格', fontsize=12, fontweight='bold')
        ax3.set_ylabel('持仓状态 (1=持有, 0=现金)')
        ax3.set_xlabel('日期')
        ax3.legend(loc='upper right')
        ax3.grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        if save_path:
            plt.savefig(save_path, dpi=300, bbox_inches='tight')
            print(f"图表已保存至: {save_path}")

        plt.show()

    def save_results(self, filename=None):
        """保存回测结果到CSV文件"""
        if self.data is None:
            print("请先执行回测")
            return

        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"hstech_monthly_strategy_results_{timestamp}.csv"

        # 保存详细数据
        output_data = self.data[['close', 'position', 'buy_signal', 'sell_signal',
                                'daily_return', 'strategy_return', 'benchmark_return',
                                'strategy_cumulative', 'benchmark_cumulative',
                                'strategy_drawdown', 'benchmark_drawdown']].copy()

        output_data.to_csv(filename)
        print(f"详细结果已保存至: {filename}")

        # 保存汇总结果
        summary_filename = filename.replace('.csv', '_summary.txt')
        with open(summary_filename, 'w', encoding='utf-8') as f:
            f.write("沪深300指数月末交易策略回测结果汇总\n")
            f.write("="*60 + "\n")
            f.write(f"回测期间: {self.start_date} 至 {self.end_date}\n")
            f.write(f"总交易日数: {self.results['total_days']}\n")
            f.write(f"持仓天数: {self.results['holding_days']} ({self.results['holding_days']/self.results['total_days']:.1%})\n")
            f.write(f"总交易次数: {self.results['total_trades']}\n\n")

            f.write("收益指标:\n")
            f.write(f"策略总收益: {self.results['strategy_total_return']:.2%}\n")
            f.write(f"基准总收益: {self.results['benchmark_total_return']:.2%}\n")
            f.write(f"策略年化收益: {self.results['strategy_annual_return']:.2%}\n")
            f.write(f"基准年化收益: {self.results['benchmark_annual_return']:.2%}\n\n")

            f.write("风险指标:\n")
            f.write(f"策略波动率: {self.results['strategy_volatility']:.2%}\n")
            f.write(f"基准波动率: {self.results['benchmark_volatility']:.2%}\n")
            f.write(f"策略最大回撤: {self.results['strategy_max_drawdown']:.2%}\n")
            f.write(f"基准最大回撤: {self.results['benchmark_max_drawdown']:.2%}\n\n")

            f.write("风险调整收益:\n")
            f.write(f"策略夏普比率: {self.results['strategy_sharpe']:.3f}\n")
            f.write(f"基准夏普比率: {self.results['benchmark_sharpe']:.3f}\n")
            f.write(f"胜率: {self.results['win_rate']:.2%}\n")

        print(f"汇总结果已保存至: {summary_filename}")

def main():
    """主函数"""
    print("🚀 沪深300指数月末交易策略回测")
    print("策略：月末倒数第2个交易日买入，下月持有5个交易日后卖出")
    print("="*60)

    # 创建策略实例
    strategy = CSI300MonthlyStrategy(start_date="2018-01-01")

    # 获取数据
    if not strategy.get_csi300_data():
        print("❌ 数据获取失败，程序退出")
        return

    print(f"✅ 数据获取成功，时间范围：{strategy.data.index[0].date()} 到 {strategy.data.index[-1].date()}")
    print(f"总数据点数：{len(strategy.data)}")

    # 生成交易信号
    if not strategy.generate_signals():
        print("❌ 信号生成失败，程序退出")
        return

    # 执行回测
    results = strategy.backtest()
    if results is None:
        print("❌ 回测执行失败，程序退出")
        return

    # 打印结果
    strategy.print_results()

    # 绘制图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    plot_filename = f"csi300_monthly_strategy_{timestamp}.png"
    strategy.plot_results(save_path=plot_filename)

    # 保存结果
    strategy.save_results()

    print(f"\n🎉 回测完成！")
    print(f"📊 图表文件：{plot_filename}")
    print(f"📋 详细结果已保存到CSV文件")

if __name__ == "__main__":
    main()
