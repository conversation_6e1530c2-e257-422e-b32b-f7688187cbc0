"""
获取沪深300成分股的财务数据（使用东方财富API）

这个脚本使用东方财富数据中心的API获取沪深300成分股的财务数据。
"""

import os
import pandas as pd
import requests
import json
import time
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_eastmoney_financial_data.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_eastmoney_financial_data"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def convert_to_eastmoney_code(stock_code):
    """
    将普通股票代码转换为东方财富API所需的格式

    Args:
        stock_code: 股票代码，如"600519"

    Returns:
        str: 东方财富格式的股票代码，如"600519.SH"
    """
    # 确保股票代码为字符串类型
    stock_code = str(stock_code)

    # 确保股票代码为6位，不足6位的用0补齐
    stock_code = stock_code.zfill(6)

    # 根据股票代码的前缀判断交易所
    if stock_code.startswith(('600', '601', '603', '605', '688', '689')):
        return f"{stock_code}.SH"  # 上海证券交易所
    elif stock_code.startswith(('000', '001', '002', '003', '300', '301')):
        return f"{stock_code}.SZ"  # 深圳证券交易所
    else:
        logger.warning(f"无法确定股票 {stock_code} 的交易所，默认使用.SH后缀")
        return f"{stock_code}.SH"

def fetch_eastmoney_finance_data(stock_code, stock_name, years=5):
    """
    从东方财富获取单个股票的财务数据

    Args:
        stock_code: 股票代码，如"600519"
        stock_name: 股票名称
        years: 获取多少年的数据，默认5年（约20个季度）

    Returns:
        pandas.DataFrame: 包含财务数据的DataFrame，如果获取失败则返回None
    """
    try:
        # 转换为东方财富格式的股票代码
        eastmoney_code = convert_to_eastmoney_code(stock_code)

        logger.info(f"获取 {stock_code} ({stock_name}) 的财务数据...")

        # 计算需要获取的页数（每页最多100条记录）
        # 一年4个季度，5年约20个季度，考虑到可能有年报、半年报等，设置为30条足够
        page_size = 100

        # 构建API URL - 利润表数据
        url = f"https://datacenter.eastmoney.com/securities/api/data/v1/get?reportName=RPT_F10_FINANCE_GRATIO&columns=SECUCODE%2CSECURITY_CODE%2CSECURITY_NAME_ABBR%2CORG_CODE%2CORG_TYPE%2CREPORT_DATE%2CSECURITY_TYPE_CODE%2CNOTICE_DATE%2CUPDATE_DATE%2CCURRENCY%2CTOTAL_OPERATE_INCOME%2COPERATE_COST%2COPERATE_TAX_ADD%2CTOTAL_EXPENSE%2CSALE_EXPENSE%2CMANAGE_EXPENSE%2CRESEARCH_EXPENSE%2CFINANCE_EXPENSE%2COTHER_INCOME%2CINVEST_INCOME%2CFAIRVALUE_CHANGE_INCOME%2CCREDIT_IMPAIRMENT_INCOME%2CASSET_IMPAIRMENT_INCOME%2CASSET_DISPOSAL_INCOME%2COPERATE_PROFIT%2CNONBUSINESS_INCOME%2CNONBUSINESS_EXPENSE%2CTOTAL_PROFIT%2CINCOME_TAX%2CNETPROFIT%2CTOTAL_OPERATE_COST&quoteColumns=&filter=(SECUCODE%3D%22{eastmoney_code}%22)&sortTypes=-1%2C1&sortColumns=REPORT_DATE%2CINTERFACE_TYPE&pageNumber=1&pageSize={page_size}&source=HSF10&client=PC&v=05451102233000454"

        # 发送请求
        response = requests.get(url)

        # 检查请求是否成功
        if response.status_code != 200:
            logger.error(f"{stock_code} ({stock_name}) 请求失败，状态码: {response.status_code}")
            return None

        # 解析返回的JSON数据
        data = response.json()

        # 提取数据部分
        if 'result' in data and 'data' in data['result']:
            financial_data = data['result']['data']
            logger.info(f"{stock_code} ({stock_name}) 成功获取到 {len(financial_data)} 条财务数据记录")
        else:
            logger.error(f"{stock_code} ({stock_name}) 未找到财务数据")
            return None

        # 将数据转换为DataFrame
        df = pd.DataFrame(financial_data)

        # 转换日期列
        date_columns = ['REPORT_DATE', 'NOTICE_DATE', 'UPDATE_DATE']
        for col in date_columns:
            if col in df.columns:
                df[col] = pd.to_datetime(df[col], errors='coerce')

        # 转换数值列为float
        numeric_columns = [
            'TOTAL_OPERATE_INCOME', 'OPERATE_COST', 'OPERATE_TAX_ADD',
            'TOTAL_EXPENSE', 'SALE_EXPENSE', 'MANAGE_EXPENSE',
            'RESEARCH_EXPENSE', 'FINANCE_EXPENSE', 'OTHER_INCOME',
            'INVEST_INCOME', 'FAIRVALUE_CHANGE_INCOME', 'CREDIT_IMPAIRMENT_INCOME',
            'ASSET_IMPAIRMENT_INCOME', 'ASSET_DISPOSAL_INCOME', 'OPERATE_PROFIT',
            'NONBUSINESS_INCOME', 'NONBUSINESS_EXPENSE', 'TOTAL_PROFIT',
            'INCOME_TAX', 'NETPROFIT', 'TOTAL_OPERATE_COST'
        ]

        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        # 添加自定义列
        df['STOCK_CODE'] = stock_code
        df['STOCK_NAME'] = stock_name

        # 按报告期排序
        if 'REPORT_DATE' in df.columns:
            df = df.sort_values('REPORT_DATE', ascending=False)

        # 过滤掉百分比行（有些数据会包含原始值和百分比两行）
        if 'TOTAL_OPERATE_INCOME' in df.columns:
            df = df[df['TOTAL_OPERATE_INCOME'] != 100.0]

        return df

    except Exception as e:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据时出错: {str(e)}")
        return None

def process_stock(stock_info):
    """
    处理单个股票的财务数据并保存

    Args:
        stock_info: 包含股票代码和名称的元组 (code, name)

    Returns:
        tuple: (股票代码, 成功/失败)
    """
    stock_code, stock_name = stock_info

    # 检查是否已经处理过
    output_file = os.path.join(OUTPUT_DIR, f"{stock_code}_financial_data.csv")
    if os.path.exists(output_file):
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已存在，跳过处理")
        return stock_code, True

    # 获取财务数据
    df = fetch_eastmoney_finance_data(stock_code, stock_name)

    if df is not None and not df.empty:
        # 保存到CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已保存到 {output_file}")
        return stock_code, True
    else:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
        return stock_code, False

def main():
    # 读取沪深300成分股列表
    try:
        # 查找最新的格式化文件
        data_dir = "data"
        files = [f for f in os.listdir(data_dir) if f.startswith("csi300_constituents_formatted_") and f.endswith(".csv")]
        if not files:
            logger.error("未找到沪深300成分股数据文件")
            return

        # 按文件名排序，获取最新的文件
        files.sort(reverse=True)
        input_file = os.path.join(data_dir, files[0])

        # 读取CSV文件
        df = pd.read_csv(input_file, encoding="utf-8-sig")
        logger.info(f"从 {input_file} 读取了 {len(df)} 只沪深300成分股")

        # 准备股票列表
        # 确保股票代码为6位，不足6位的用0补齐
        stock_list = list(zip(df["成分券代码"].astype(str).str.zfill(6), df["成分券名称"]))

        # 使用线程池并行处理
        success_count = 0
        fail_count = 0

        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = {executor.submit(process_stock, stock_info): stock_info for stock_info in stock_list}

            # 使用tqdm显示进度
            for future in tqdm(as_completed(futures), total=len(futures), desc="处理进度"):
                stock_info = futures[future]
                try:
                    stock_code, success = future.result()
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    logger.error(f"处理 {stock_info[0]} ({stock_info[1]}) 时发生异常: {str(e)}")
                    fail_count += 1

                # 添加延迟以避免请求过于频繁
                time.sleep(1)

        logger.info(f"处理完成: 成功 {success_count} 只, 失败 {fail_count} 只")

        # 创建汇总文件
        create_summary_file()

    except Exception as e:
        logger.error(f"处理沪深300成分股财务数据时出错: {str(e)}")

def create_summary_file():
    """创建汇总文件，包含所有公司的关键财务指标"""
    try:
        # 获取所有已处理的文件
        all_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith("_financial_data.csv")]

        if not all_files:
            logger.warning("没有找到已处理的财务数据文件，无法创建汇总")
            return

        # 读取并合并所有数据
        all_data = []
        all_historical_data = []

        for file in all_files:
            try:
                file_path = os.path.join(OUTPUT_DIR, file)
                df = pd.read_csv(file_path, encoding='utf-8-sig')

                # 过滤掉百分比行
                if 'TOTAL_OPERATE_INCOME' in df.columns:
                    df = df[df['TOTAL_OPERATE_INCOME'] != 100.0]

                # 只保留最新一期的数据用于汇总
                if not df.empty:
                    df = df.sort_values('REPORT_DATE', ascending=False)
                    latest_data = df.iloc[0].to_dict()
                    all_data.append(latest_data)

                # 保留所有历史数据用于创建历史汇总
                for _, row in df.iterrows():
                    # 只保留关键字段
                    historical_data = {
                        'STOCK_CODE': row.get('STOCK_CODE', ''),
                        'STOCK_NAME': row.get('STOCK_NAME', ''),
                        'REPORT_DATE': row.get('REPORT_DATE', ''),
                        'TOTAL_OPERATE_INCOME': row.get('TOTAL_OPERATE_INCOME', None),  # 营业总收入
                        'OPERATE_PROFIT': row.get('OPERATE_PROFIT', None),  # 营业利润
                        'TOTAL_PROFIT': row.get('TOTAL_PROFIT', None),  # 利润总额
                        'NETPROFIT': row.get('NETPROFIT', None)  # 净利润
                    }
                    all_historical_data.append(historical_data)
            except Exception as e:
                logger.error(f"读取文件 {file} 时出错: {str(e)}")

        if all_data:
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(all_data)

            # 保存汇总文件
            summary_file = os.path.join(OUTPUT_DIR, "csi300_financial_summary.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            logger.info(f"汇总文件已保存到 {summary_file}")
        else:
            logger.warning("没有有效的财务数据，无法创建汇总")

        if all_historical_data:
            # 创建历史数据汇总DataFrame
            historical_df = pd.DataFrame(all_historical_data)

            # 转换日期列
            if 'REPORT_DATE' in historical_df.columns:
                historical_df['REPORT_DATE'] = pd.to_datetime(historical_df['REPORT_DATE'], errors='coerce')

            # 按股票代码和报告期排序
            historical_df = historical_df.sort_values(['STOCK_CODE', 'REPORT_DATE'], ascending=[True, False])

            # 保存历史汇总文件
            historical_file = os.path.join(OUTPUT_DIR, "csi300_financial_history.csv")
            historical_df.to_csv(historical_file, index=False, encoding='utf-8-sig')
            logger.info(f"历史财务数据汇总文件已保存到 {historical_file}")
        else:
            logger.warning("没有有效的历史财务数据，无法创建历史汇总")

    except Exception as e:
        logger.error(f"创建汇总文件时出错: {str(e)}")

if __name__ == "__main__":
    main()
