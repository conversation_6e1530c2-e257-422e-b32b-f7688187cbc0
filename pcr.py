import akshare as ak
import pandas as pd
import matplotlib.pyplot as plt
import datetime
import time
import os
import numpy as np
from matplotlib.ticker import FormatStrFormatter
import concurrent.futures
import pickle
import threading

# Disable proxy settings for akshare
os.environ['http_proxy'] = ''
os.environ['https_proxy'] = ''

# Create a directory for cache files if it doesn't exist
CACHE_DIR = 'cache'
if not os.path.exists(CACHE_DIR):
    os.makedirs(CACHE_DIR)

# Lock for thread-safe operations
lock = threading.Lock()

def get_trading_dates(start_date, end_date, interval_days=7):
    """
    Generate a list of trading dates between start_date and end_date at regular intervals.

    Args:
        start_date (str): Start date in format 'YYYYMMDD'
        end_date (str): End date in format 'YYYYMMDD'
        interval_days (int): Number of days between samples

    Returns:
        list: List of dates in format 'YYYYMMDD'
    """
    start = datetime.datetime.strptime(start_date, "%Y%m%d")
    end = datetime.datetime.strptime(end_date, "%Y%m%d")

    # Generate sample dates
    sample_dates = []
    current = start

    while current <= end:
        # Skip weekends
        if current.weekday() < 5:  # 0-4 are Monday to Friday
            sample_dates.append(current.strftime("%Y%m%d"))

        # Move to next sample date
        current += datetime.timedelta(days=interval_days)

    # Add the most recent date if it's a weekday
    if end.weekday() < 5 and end.strftime("%Y%m%d") not in sample_dates:
        sample_dates.append(end.strftime("%Y%m%d"))

    return sample_dates

def fetch_single_date_data(date):
    """
    Fetch option data for a single date with caching.

    Args:
        date (str): Date in format 'YYYYMMDD'

    Returns:
        tuple: (DataFrame or None, date, success flag)
    """
    cache_file = os.path.join(CACHE_DIR, f"option_data_{date}.pkl")

    # Check if data is already cached
    if os.path.exists(cache_file):
        try:
            with open(cache_file, 'rb') as f:
                df_filtered = pickle.load(f)
            return df_filtered, date, True
        except Exception as e:
            print(f"Error loading cached data for {date}: {e}")

    # If not cached or error loading cache, fetch from API
    try:
        df = ak.option_daily_stats_sse(date=date)

        if not df.empty:
            # Keep only the rows for 510050 (SSE 50ETF)
            df_filtered = df[df['合约标的代码'] == '510050']
            if not df_filtered.empty:
                # Cache the data
                with lock:
                    with open(cache_file, 'wb') as f:
                        pickle.dump(df_filtered, f)
                return df_filtered, date, True
            else:
                return None, date, False
        else:
            return None, date, False
    except Exception as e:
        print(f"Error fetching data for date {date}: {e}")
        return None, date, False

def fetch_option_data(dates, max_workers=10):
    """
    Fetch real option data for a list of dates using akshare with parallel processing and caching.

    Args:
        dates (list): List of dates in format 'YYYYMMDD'
        max_workers (int): Maximum number of parallel workers

    Returns:
        DataFrame: Combined data from all dates
    """
    all_data = []
    successful_dates = []
    total_dates = len(dates)
    processed_count = 0

    # Use ThreadPoolExecutor for parallel fetching
    with concurrent.futures.ThreadPoolExecutor(max_workers=max_workers) as executor:
        # Submit all fetch tasks
        future_to_date = {executor.submit(fetch_single_date_data, date): date for date in dates}

        # Process results as they complete
        for future in concurrent.futures.as_completed(future_to_date):
            date = future_to_date[future]
            processed_count += 1

            try:
                df_filtered, date, success = future.result()
                if success and df_filtered is not None:
                    all_data.append(df_filtered)
                    successful_dates.append(date)
                    print(f"Successfully fetched data for {date} ({processed_count}/{total_dates})")
                else:
                    print(f"No data found for {date} ({processed_count}/{total_dates})")
            except Exception as e:
                print(f"Error processing {date}: {e} ({processed_count}/{total_dates})")

    if not all_data:
        raise ValueError("No data was fetched for any of the provided dates")

    # Combine all data into a single DataFrame
    combined_df = pd.concat(all_data, ignore_index=True)
    print(f"Successfully fetched data for {len(successful_dates)} dates out of {total_dates}")

    return combined_df

def calculate_pcr(df):
    """
    Calculate PCR metrics from the option data.

    Args:
        df (DataFrame): Option data

    Returns:
        DataFrame: Data with PCR metrics added
    """
    # Ensure the date column is in datetime format
    df['交易日'] = pd.to_datetime(df['交易日'])

    # Sort by date
    df = df.sort_values('交易日')

    # Calculate open interest PCR
    df['持仓PCR'] = df['未平仓认沽合约数'] / df['未平仓认购合约数']

    # Rename the existing PCR column for clarity
    df = df.rename(columns={'认沽/认购': '成交PCR'})

    return df

def fetch_50etf_price():
    """
    Fetch SSE 50ETF price data with caching.

    Returns:
        DataFrame: Price data for SSE 50ETF
    """
    cache_file = os.path.join(CACHE_DIR, "50etf_price_data.pkl")

    # Check if data is already cached and not too old (less than 1 day old)
    if os.path.exists(cache_file):
        file_mod_time = os.path.getmtime(cache_file)
        if (time.time() - file_mod_time) < 86400:  # 86400 seconds = 1 day
            try:
                print("Loading cached SSE 50ETF price data")
                with open(cache_file, 'rb') as f:
                    etf_data = pickle.load(f)
                print(f"Successfully loaded {len(etf_data)} days of price data from cache")
                return etf_data
            except Exception as e:
                print(f"Error loading cached price data: {e}")

    # If not cached or cache is too old, fetch from API
    try:
        # Fetch SSE 50ETF price data
        print("Fetching SSE 50ETF price data from API")
        etf_data = ak.fund_etf_hist_sina(symbol="sh510050")

        # Convert date column to datetime
        etf_data['date'] = pd.to_datetime(etf_data['date'])

        # Sort by date
        etf_data = etf_data.sort_values('date')

        # Keep only the last three years of data
        three_years_ago = datetime.datetime.now() - datetime.timedelta(days=3*365)
        etf_data = etf_data[etf_data['date'] >= three_years_ago]

        # Cache the data
        with open(cache_file, 'wb') as f:
            pickle.dump(etf_data, f)

        print(f"Successfully fetched and cached {len(etf_data)} days of price data")
        return etf_data
    except Exception as e:
        print(f"Error fetching SSE 50ETF price data: {e}")
        return pd.DataFrame()

def plot_pcr(df):
    """
    Create separate time series plots for PCR metrics.

    Args:
        df (DataFrame): Data with PCR metrics
    """
    # Create a figure with two subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

    # Get the latest PCR values
    latest_pcr = df.iloc[-1]
    latest_date = latest_pcr['交易日']
    latest_vol_pcr = latest_pcr['成交PCR']
    latest_oi_pcr = latest_pcr['持仓PCR']

    # Plot trading volume PCR
    ax1.plot(df['交易日'], df['成交PCR'], color='blue', marker=None, linestyle='-')
    ax1.axhline(y=1, color='gray', linestyle='--', alpha=0.7)
    ax1.set_ylabel('PCR Value', fontsize=12)
    ax1.set_title('SSE 50ETF Options Trading Volume PCR (Past Three Years)', fontsize=14)
    ax1.grid(True, alpha=0.3)

    # Highlight the latest trading volume PCR point
    ax1.scatter(latest_date, latest_vol_pcr, color='darkblue', s=100, zorder=5)

    # Add annotation for the latest trading volume PCR
    ax1.annotate(f'Latest: {latest_vol_pcr:.2f}',
                xy=(latest_date, latest_vol_pcr),
                xytext=(15, 15),
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='darkblue'),
                bbox=dict(boxstyle='round,pad=0.5', fc='lightskyblue', alpha=0.7),
                fontsize=10,
                fontweight='bold')

    # Plot open interest PCR
    ax2.plot(df['交易日'], df['持仓PCR'], color='red', marker=None, linestyle='-')
    ax2.axhline(y=1, color='gray', linestyle='--', alpha=0.7)
    ax2.set_ylabel('PCR Value', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.set_title('SSE 50ETF Options Open Interest PCR (Past Three Years)', fontsize=14)
    ax2.grid(True, alpha=0.3)

    # Highlight the latest open interest PCR point
    ax2.scatter(latest_date, latest_oi_pcr, color='darkred', s=100, zorder=5)

    # Add annotation for the latest open interest PCR
    ax2.annotate(f'Latest: {latest_oi_pcr:.4f}',
                xy=(latest_date, latest_oi_pcr),
                xytext=(15, 15),
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='darkred'),
                bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                fontsize=10,
                fontweight='bold')

    # Format x-axis dates
    fig.autofmt_xdate()

    # Save the plot
    plt.tight_layout()
    plt.savefig('sse_50etf_pcr_separate.png', dpi=300)
    plt.close()

def plot_pcr_with_price(pcr_df, price_df):
    """
    Create a plot that overlays the Open Interest PCR with the SSE 50ETF price.

    Args:
        pcr_df (DataFrame): PCR data
        price_df (DataFrame): Price data for SSE 50ETF
    """
    if price_df.empty:
        print("No price data available for overlay plot")
        return

    # Create figure with two y-axes
    fig, ax1 = plt.subplots(figsize=(12, 6))

    # Plot PCR on the left y-axis
    color = 'tab:red'
    ax1.set_xlabel('Date', fontsize=12)
    ax1.set_ylabel('Open Interest PCR', color=color, fontsize=12)
    ax1.plot(pcr_df['交易日'], pcr_df['持仓PCR'], color=color, marker=None, linestyle='-', label='Open Interest PCR')
    ax1.tick_params(axis='y', labelcolor=color)
    ax1.axhline(y=1, color='gray', linestyle='--', alpha=0.7)
    ax1.grid(True, alpha=0.3)

    # Create a second y-axis for the price
    ax2 = ax1.twinx()
    color = 'tab:blue'
    ax2.set_ylabel('SSE 50ETF Price (CNY)', color=color, fontsize=12)

    # Resample price data to daily frequency and forward fill missing values
    price_df = price_df.set_index('date').sort_index()

    # Plot price data
    ax2.plot(price_df.index, price_df['close'], color=color, linestyle='-', label='SSE 50ETF Price')
    ax2.tick_params(axis='y', labelcolor=color)

    # Format y-axis to show 2 decimal places
    ax2.yaxis.set_major_formatter(FormatStrFormatter('%.2f'))

    # Get the latest PCR value
    latest_pcr = pcr_df.iloc[-1]
    latest_date = latest_pcr['交易日']
    latest_oi_pcr = latest_pcr['持仓PCR']

    # Highlight the latest PCR point
    ax1.scatter(latest_date, latest_oi_pcr, color='darkred', s=100, zorder=5)

    # Add annotation for the latest PCR value
    ax1.annotate(f'Latest PCR: {latest_oi_pcr:.4f}',
                xy=(latest_date, latest_oi_pcr),
                xytext=(15, 15),
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='darkred'),
                bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                fontsize=10,
                fontweight='bold')

    # Add title and legend
    plt.title('SSE 50ETF Open Interest PCR vs Price (Past Three Years)', fontsize=14)

    # Add legends for both axes
    lines1, labels1 = ax1.get_legend_handles_labels()
    lines2, labels2 = ax2.get_legend_handles_labels()
    ax1.legend(lines1 + lines2, labels1 + labels2, loc='upper left', fontsize=10)

    # Format x-axis dates
    fig.autofmt_xdate()

    # Save the plot
    plt.tight_layout()
    plt.savefig('sse_50etf_pcr_price_overlay.png', dpi=300)
    plt.close()

def plot_volumes_and_oi(df):
    """
    Create plots for option volumes and open interest.

    Args:
        df (DataFrame): Data with volume and OI metrics
    """
    # Create a figure with two subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

    # Plot trading volumes
    ax1.plot(df['交易日'], df['认购成交量'], label='Call Volume', color='green', marker=None, linestyle='-')
    ax1.plot(df['交易日'], df['认沽成交量'], label='Put Volume', color='red', marker=None, linestyle='-')
    ax1.set_ylabel('Trading Volume', fontsize=12)
    ax1.set_title('SSE 50ETF Options Trading Volume (Past Three Years) - Real Data', fontsize=14)
    ax1.legend(fontsize=12)
    ax1.grid(True, alpha=0.3)

    # Plot open interest
    ax2.plot(df['交易日'], df['未平仓认购合约数'], label='Call Open Interest', color='green', marker=None, linestyle='-')
    ax2.plot(df['交易日'], df['未平仓认沽合约数'], label='Put Open Interest', color='red', marker=None, linestyle='-')
    ax2.set_ylabel('Open Interest', fontsize=12)
    ax2.set_xlabel('Date', fontsize=12)
    ax2.set_title('SSE 50ETF Options Open Interest (Past Three Years) - Real Data', fontsize=14)
    ax2.legend(fontsize=12)
    ax2.grid(True, alpha=0.3)

    # Format x-axis dates
    fig.autofmt_xdate()

    # Save the plot
    plt.tight_layout()
    plt.savefig('sse_50etf_volumes_oi_real.png', dpi=300)
    plt.close()

def load_or_fetch_pcr_data(start_date, end_date, interval_days=1):
    """
    Load PCR data from cache or fetch it if not available.

    Args:
        start_date (str): Start date in format 'YYYYMMDD'
        end_date (str): End date in format 'YYYYMMDD'
        interval_days (int): Interval between dates to sample

    Returns:
        DataFrame: PCR data
    """
    cache_file = os.path.join(CACHE_DIR, f"pcr_data_{start_date}_{end_date}_{interval_days}.pkl")

    # Check if data is already cached and not too old (less than 1 day old)
    if os.path.exists(cache_file):
        file_mod_time = os.path.getmtime(cache_file)
        if (time.time() - file_mod_time) < 86400:  # 86400 seconds = 1 day
            try:
                print(f"Loading cached PCR data for {start_date} to {end_date}")
                with open(cache_file, 'rb') as f:
                    pcr_data = pickle.load(f)
                print(f"Successfully loaded PCR data with {len(pcr_data)} records from cache")
                return pcr_data
            except Exception as e:
                print(f"Error loading cached PCR data: {e}")

    # If not cached or cache is too old, fetch the data
    print(f"Calculating PCR from {start_date} to {end_date} using real data")

    # Get sample dates
    sample_dates = get_trading_dates(start_date, end_date, interval_days=interval_days)
    print(f"Sampling {len(sample_dates)} dates over the past three years")

    # Fetch real option data
    option_data = fetch_option_data(sample_dates)

    # Calculate PCR metrics
    pcr_data = calculate_pcr(option_data)

    # Cache the data
    with open(cache_file, 'wb') as f:
        pickle.dump(pcr_data, f)

    return pcr_data

def fetch_bond_index_data():
    """
    Fetch China Bond Composite Index data with caching.

    Returns:
        DataFrame: Bond index data
    """
    cache_file = os.path.join(CACHE_DIR, "bond_composite_index_data.pkl")

    # Check if data is already cached and not too old (less than 1 day old)
    if os.path.exists(cache_file):
        file_mod_time = os.path.getmtime(cache_file)
        if (time.time() - file_mod_time) < 86400:  # 86400 seconds = 1 day
            try:
                print("Loading cached China Bond Composite Index data")
                with open(cache_file, 'rb') as f:
                    bond_data = pickle.load(f)
                print(f"Successfully loaded {len(bond_data)} days of bond index data from cache")
                return bond_data
            except Exception as e:
                print(f"Error loading cached bond index data: {e}")

    # If not cached or cache is too old, fetch from API
    try:
        print("Fetching China Bond Composite Index data from API")
        bond_data = ak.bond_composite_index_cbond(indicator="全价", period="总值")

        if bond_data.empty:
            print("No bond index data available")
            return pd.DataFrame()

        # Convert date column to datetime
        bond_data['date'] = pd.to_datetime(bond_data['date'])

        # Sort by date
        bond_data = bond_data.sort_values('date')

        # Keep only the last three years of data
        three_years_ago = datetime.datetime.now() - datetime.timedelta(days=3*365)
        bond_data = bond_data[bond_data['date'] >= three_years_ago]

        # Rename columns for consistency
        bond_data = bond_data.rename(columns={'value': 'bond_index'})

        # Cache the data
        with open(cache_file, 'wb') as f:
            pickle.dump(bond_data, f)

        print(f"Successfully fetched and cached {len(bond_data)} days of bond index data")
        return bond_data
    except Exception as e:
        print(f"Error fetching China Bond Composite Index data: {e}")
        return pd.DataFrame()

def plot_pcr_with_bond_index(pcr_df, bond_df):
    """
    Create a comprehensive comparison plot between PCR and China Bond Composite Index.

    Args:
        pcr_df (DataFrame): PCR data
        bond_df (DataFrame): Bond index data
    """
    if bond_df.empty:
        print("No bond index data available for comparison plot")
        return

    # Create figure with 2x2 subplots
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(16, 12))

    # Plot 1: PCR and Bond Index overlay (dual y-axis)
    ax1_twin = ax1.twinx()

    # Plot PCR on the left y-axis
    color1 = '#A23B72'
    ax1.set_ylabel('Open Interest PCR', color=color1, fontsize=12)
    line1 = ax1.plot(pcr_df['交易日'], pcr_df['持仓PCR'], color=color1, linewidth=2, label='50ETF PCR')
    ax1.tick_params(axis='y', labelcolor=color1)
    ax1.axhline(y=1, color='gray', linestyle='--', alpha=0.7)
    ax1.grid(True, alpha=0.3)

    # Plot Bond Index on the right y-axis
    color2 = '#2E86AB'
    ax1_twin.set_ylabel('China Bond Composite Index', color=color2, fontsize=12)
    line2 = ax1_twin.plot(bond_df['date'], bond_df['bond_index'], color=color2, linewidth=2, label='Bond Index')
    ax1_twin.tick_params(axis='y', labelcolor=color2)

    ax1.set_title('50ETF PCR vs China Bond Composite Index (Past Three Years)', fontsize=14, fontweight='bold')

    # Combine legends
    lines = line1 + line2
    labels = [l.get_label() for l in lines]
    ax1.legend(lines, labels, loc='upper left')

    # Plot 2: PCR time series only
    ax2.plot(pcr_df['交易日'], pcr_df['持仓PCR'], color='#A23B72', linewidth=2)
    ax2.axhline(y=1, color='gray', linestyle='--', alpha=0.7)
    ax2.set_ylabel('Open Interest PCR', fontsize=12)
    ax2.set_title('50ETF Options Open Interest PCR', fontsize=14)
    ax2.grid(True, alpha=0.3)

    # Highlight latest PCR point
    latest_pcr = pcr_df.iloc[-1]
    latest_date = latest_pcr['交易日']
    latest_oi_pcr = latest_pcr['持仓PCR']
    ax2.scatter(latest_date, latest_oi_pcr, color='darkred', s=100, zorder=5)
    ax2.annotate(f'Latest: {latest_oi_pcr:.4f}',
                xy=(latest_date, latest_oi_pcr),
                xytext=(15, 15),
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='darkred'),
                bbox=dict(boxstyle='round,pad=0.5', fc='yellow', alpha=0.7),
                fontsize=10,
                fontweight='bold')

    # Plot 3: Bond Index time series only
    ax3.plot(bond_df['date'], bond_df['bond_index'], color='#2E86AB', linewidth=2)
    ax3.set_ylabel('Bond Index Value', fontsize=12)
    ax3.set_title('China Bond Composite Index (Full Price)', fontsize=14)
    ax3.grid(True, alpha=0.3)

    # Highlight latest bond index point
    latest_bond = bond_df.iloc[-1]
    latest_bond_date = latest_bond['date']
    latest_bond_value = latest_bond['bond_index']
    ax3.scatter(latest_bond_date, latest_bond_value, color='darkblue', s=100, zorder=5)
    ax3.annotate(f'Latest: {latest_bond_value:.2f}',
                xy=(latest_bond_date, latest_bond_value),
                xytext=(15, 15),
                textcoords='offset points',
                arrowprops=dict(arrowstyle='->', color='darkblue'),
                bbox=dict(boxstyle='round,pad=0.5', fc='lightblue', alpha=0.7),
                fontsize=10,
                fontweight='bold')

    # Plot 4: Correlation analysis (if we have overlapping data)
    # Merge data on date for correlation analysis
    pcr_daily = pcr_df.copy()
    pcr_daily['date'] = pcr_daily['交易日'].dt.date
    bond_daily = bond_df.copy()
    bond_daily['date'] = bond_daily['date'].dt.date

    merged_data = pd.merge(pcr_daily[['date', '持仓PCR']],
                          bond_daily[['date', 'bond_index']],
                          on='date', how='inner')

    if len(merged_data) > 10:  # Only plot if we have enough data points
        ax4.scatter(merged_data['bond_index'], merged_data['持仓PCR'],
                   alpha=0.6, color='#F18F01', s=30)

        # Add trend line
        z = np.polyfit(merged_data['bond_index'], merged_data['持仓PCR'], 1)
        p = np.poly1d(z)
        ax4.plot(merged_data['bond_index'], p(merged_data['bond_index']),
                "r--", alpha=0.8, linewidth=2)

        # Calculate correlation
        correlation = merged_data['bond_index'].corr(merged_data['持仓PCR'])
        ax4.set_xlabel('China Bond Composite Index', fontsize=12)
        ax4.set_ylabel('50ETF PCR', fontsize=12)
        ax4.set_title(f'Correlation Analysis (r = {correlation:.3f})', fontsize=14)
        ax4.grid(True, alpha=0.3)

        # Add correlation text
        ax4.text(0.05, 0.95, f'Correlation: {correlation:.3f}\nData points: {len(merged_data)}',
                transform=ax4.transAxes, fontsize=10,
                bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.8),
                verticalalignment='top')
    else:
        ax4.text(0.5, 0.5, 'Insufficient overlapping data\nfor correlation analysis',
                transform=ax4.transAxes, fontsize=12, ha='center', va='center',
                bbox=dict(boxstyle='round,pad=0.5', fc='lightgray', alpha=0.8))
        ax4.set_title('Correlation Analysis', fontsize=14)

    # Format x-axis dates for time series plots
    for ax in [ax1, ax2, ax3]:
        ax.tick_params(axis='x', rotation=45)

    # Save the plot
    plt.tight_layout()
    plt.savefig('pcr_bond_index_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

def fetch_multiple_treasury_etf_data():
    """
    Fetch multiple Chinese Treasury ETF data with caching.

    Returns:
        dict: Dictionary containing different Treasury ETF data
    """
    cache_file = os.path.join(CACHE_DIR, "multiple_treasury_etf_data.pkl")

    # Check if data is already cached and not too old (less than 1 day old)
    if os.path.exists(cache_file):
        file_mod_time = os.path.getmtime(cache_file)
        if (time.time() - file_mod_time) < 86400:  # 86400 seconds = 1 day
            try:
                print("Loading cached multiple Treasury ETF data")
                with open(cache_file, 'rb') as f:
                    etf_data_dict = pickle.load(f)
                print(f"Successfully loaded multiple Treasury ETF data from cache")
                return etf_data_dict
            except Exception as e:
                print(f"Error loading cached multiple Treasury ETF data: {e}")

    # Define Treasury ETFs to fetch
    treasury_etfs = {
        '511010': '国泰上证5年期国债ETF',
        '511310': '国泰中证10年期国债ETF',
        '511260': '华安中证全债ETF'  # 作为长期债券代表
    }

    etf_data_dict = {}

    # If not cached or cache is too old, fetch from API
    for etf_code, etf_name in treasury_etfs.items():
        try:
            print(f"Fetching {etf_name} ({etf_code}) data from API")
            etf_data = ak.fund_etf_hist_sina(symbol=f"sh{etf_code}")

            if etf_data.empty:
                print(f"No data available for {etf_name}")
                continue

            # Convert date column to datetime
            etf_data['date'] = pd.to_datetime(etf_data['date'])

            # Sort by date
            etf_data = etf_data.sort_values('date')

            # Keep only the last three years of data
            three_years_ago = datetime.datetime.now() - datetime.timedelta(days=3*365)
            etf_data = etf_data[etf_data['date'] >= three_years_ago]

            # Rename columns for consistency
            etf_data = etf_data.rename(columns={'close': 'etf_price'})
            etf_data['etf_name'] = etf_name
            etf_data['etf_code'] = etf_code

            etf_data_dict[etf_code] = etf_data
            print(f"Successfully fetched {len(etf_data)} days of {etf_name} data")

        except Exception as e:
            print(f"Error fetching {etf_name} data: {e}")
            continue

    # Cache the data
    if etf_data_dict:
        with open(cache_file, 'wb') as f:
            pickle.dump(etf_data_dict, f)
        print("Successfully cached multiple Treasury ETF data")

    return etf_data_dict

def plot_bond_index_vs_multiple_etfs_comparison(bond_df, etf_data_dict):
    """
    Create a comprehensive comparison plot between China Bond Composite Index and multiple Treasury ETFs.

    Args:
        bond_df (DataFrame): Bond index data
        etf_data_dict (dict): Dictionary containing multiple Treasury ETF data
    """
    if bond_df.empty or not etf_data_dict:
        print("Insufficient data for bond index vs multiple ETFs comparison")
        return

    # Define colors for different ETFs
    colors = ['#F18F01', '#A23B72', '#2E8B57']  # Orange, Purple, Sea Green

    # Create figure with 2x3 subplots
    fig, ((ax1, ax2, ax3), (ax4, ax5, ax6)) = plt.subplots(2, 3, figsize=(20, 12))

    # Plot 1: Normalized comparison (all starting from 100)
    bond_normalized = (bond_df['bond_index'] / bond_df['bond_index'].iloc[0]) * 100
    ax1.plot(bond_df['date'], bond_normalized, color='#2E86AB', linewidth=3, label='中债综合全价指数', alpha=0.8)

    for i, (etf_code, etf_data) in enumerate(etf_data_dict.items()):
        if not etf_data.empty:
            etf_normalized = (etf_data['etf_price'] / etf_data['etf_price'].iloc[0]) * 100
            etf_name = etf_data['etf_name'].iloc[0] if 'etf_name' in etf_data.columns else f'ETF {etf_code}'
            ax1.plot(etf_data['date'], etf_normalized, color=colors[i % len(colors)],
                    linewidth=2, label=f'{etf_name} ({etf_code})', alpha=0.7)

    ax1.set_ylabel('Normalized Price (Base=100)', fontsize=12)
    ax1.set_title('Bond Index vs Multiple Treasury ETFs - Normalized Comparison', fontsize=14, fontweight='bold')
    ax1.legend(fontsize=10)
    ax1.grid(True, alpha=0.3)

    # Plot 2: Absolute values comparison (dual y-axis for bond index)
    ax2_twin = ax2.twinx()

    line1 = ax2.plot(bond_df['date'], bond_df['bond_index'], color='#2E86AB', linewidth=3, label='中债综合全价指数')
    ax2.set_ylabel('Bond Index Value', color='#2E86AB', fontsize=12)
    ax2.tick_params(axis='y', labelcolor='#2E86AB')

    lines = line1.copy()
    for i, (etf_code, etf_data) in enumerate(etf_data_dict.items()):
        if not etf_data.empty:
            etf_name = etf_data['etf_name'].iloc[0] if 'etf_name' in etf_data.columns else f'ETF {etf_code}'
            line = ax2_twin.plot(etf_data['date'], etf_data['etf_price'], color=colors[i % len(colors)],
                               linewidth=2, label=f'{etf_name}', alpha=0.7)
            lines.extend(line)

    ax2_twin.set_ylabel('ETF Price (CNY)', fontsize=12)
    ax2.set_title('Bond Index vs Treasury ETFs - Absolute Values', fontsize=14, fontweight='bold')
    ax2.grid(True, alpha=0.3)

    # Combine legends
    labels = [l.get_label() for l in lines]
    ax2.legend(lines, labels, loc='upper left', fontsize=10)

    # Plot 3: Daily returns comparison
    bond_returns = bond_df['bond_index'].pct_change().dropna() * 100
    bond_returns_df = pd.DataFrame({'date': bond_df['date'].iloc[1:], 'bond_returns': bond_returns})

    ax3.plot(bond_returns_df['date'], bond_returns_df['bond_returns'],
             color='#2E86AB', alpha=0.8, linewidth=2, label='中债综合全价指数日收益率')

    for i, (etf_code, etf_data) in enumerate(etf_data_dict.items()):
        if not etf_data.empty:
            etf_returns = etf_data['etf_price'].pct_change().dropna() * 100
            etf_returns_df = pd.DataFrame({'date': etf_data['date'].iloc[1:], 'etf_returns': etf_returns})
            etf_name = etf_data['etf_name'].iloc[0] if 'etf_name' in etf_data.columns else f'ETF {etf_code}'
            ax3.plot(etf_returns_df['date'], etf_returns_df['etf_returns'],
                    color=colors[i % len(colors)], alpha=0.7, linewidth=1.5, label=f'{etf_name}日收益率')

    ax3.set_ylabel('Daily Returns (%)', fontsize=12)
    ax3.set_title('Daily Returns Comparison', fontsize=14)
    ax3.legend(fontsize=10)
    ax3.grid(True, alpha=0.3)
    ax3.axhline(y=0, color='gray', linestyle='-', alpha=0.5)

    # Plot 4-6: Individual correlation analysis for each ETF
    correlation_results = {}

    for i, (etf_code, etf_data) in enumerate(etf_data_dict.items()):
        if i >= 3:  # Only plot first 3 ETFs
            break

        ax = [ax4, ax5, ax6][i]

        if not etf_data.empty:
            # Merge data on date for correlation analysis
            bond_daily = bond_df.copy()
            bond_daily['date'] = bond_daily['date'].dt.date
            etf_daily = etf_data.copy()
            etf_daily['date'] = etf_daily['date'].dt.date

            merged_data = pd.merge(bond_daily[['date', 'bond_index']],
                                  etf_daily[['date', 'etf_price']],
                                  on='date', how='inner')

            if len(merged_data) > 10:  # Only plot if we have enough data points
                ax.scatter(merged_data['bond_index'], merged_data['etf_price'],
                          alpha=0.6, color=colors[i % len(colors)], s=30)

                # Add trend line
                z = np.polyfit(merged_data['bond_index'], merged_data['etf_price'], 1)
                p = np.poly1d(z)
                ax.plot(merged_data['bond_index'], p(merged_data['bond_index']),
                       "r--", alpha=0.8, linewidth=2)

                # Calculate correlation
                correlation = merged_data['bond_index'].corr(merged_data['etf_price'])
                correlation_results[etf_code] = correlation

                etf_name = etf_data['etf_name'].iloc[0] if 'etf_name' in etf_data.columns else f'ETF {etf_code}'
                ax.set_xlabel('China Bond Composite Index', fontsize=12)
                ax.set_ylabel(f'{etf_name} Price (CNY)', fontsize=12)
                ax.set_title(f'{etf_name}\nCorrelation (r = {correlation:.3f})', fontsize=12)
                ax.grid(True, alpha=0.3)

                # Add correlation text
                ax.text(0.05, 0.95, f'Correlation: {correlation:.3f}\nData points: {len(merged_data)}',
                       transform=ax.transAxes, fontsize=10,
                       bbox=dict(boxstyle='round,pad=0.5', fc='white', alpha=0.8),
                       verticalalignment='top')

                # Calculate and display additional statistics
                bond_volatility = merged_data['bond_index'].std()
                etf_volatility = merged_data['etf_price'].std()

                print(f"\n📊 债券指数与{etf_name}对比统计:")
                print(f"   相关系数: {correlation:.4f}")
                print(f"   中债综合全价指数波动率: {bond_volatility:.4f}")
                print(f"   {etf_name}价格波动率: {etf_volatility:.4f}")

            else:
                ax.text(0.5, 0.5, 'Insufficient overlapping data\nfor correlation analysis',
                       transform=ax.transAxes, fontsize=12, ha='center', va='center',
                       bbox=dict(boxstyle='round,pad=0.5', fc='lightgray', alpha=0.8))
                etf_name = etf_data['etf_name'].iloc[0] if 'etf_name' in etf_data.columns else f'ETF {etf_code}'
                ax.set_title(f'{etf_name} - Correlation Analysis', fontsize=12)

    # Format x-axis dates for time series plots
    for ax in [ax1, ax2, ax3]:
        ax.tick_params(axis='x', rotation=45)

    # Save the plot
    plt.tight_layout()
    plt.savefig('bond_index_vs_multiple_treasury_etfs_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()

    return correlation_results

def main():
    # Define date range for the past three years
    end_date = datetime.datetime.now().strftime("%Y%m%d")
    start_date = (datetime.datetime.now() - datetime.timedelta(days=3*365)).strftime("%Y%m%d")

    try:
        print("🚀 开始收集50ETF PCR、中债综合全价指数和国债ETF数据进行对比分析")
        print("="*80)

        # Load or fetch PCR data (daily data)
        print("📊 正在获取50ETF期权PCR数据...")
        pcr_data = load_or_fetch_pcr_data(start_date, end_date, interval_days=1)

        # Display summary statistics
        print("\n📈 PCR数据统计摘要:")
        print(pcr_data[['成交PCR', '持仓PCR']].describe())

        # Display the latest PCR value
        latest_pcr = pcr_data.iloc[-1]
        latest_date = latest_pcr['交易日'].strftime("%Y-%m-%d")
        latest_oi_pcr = latest_pcr['持仓PCR']
        latest_vol_pcr = latest_pcr['成交PCR']

        print(f"\n📅 最新PCR数据 ({latest_date}):")
        print(f"   持仓PCR: {latest_oi_pcr:.4f}")
        print(f"   成交PCR: {latest_vol_pcr:.4f}")

        # Fetch China Bond Composite Index data
        print("\n🏦 正在获取中债综合全价指数数据...")
        bond_data = fetch_bond_index_data()

        if not bond_data.empty:
            latest_bond = bond_data.iloc[-1]
            latest_bond_date = latest_bond['date'].strftime("%Y-%m-%d")
            latest_bond_value = latest_bond['bond_index']

            print(f"\n📅 最新债券指数数据 ({latest_bond_date}):")
            print(f"   中债综合全价指数: {latest_bond_value:.4f}")

        # Fetch multiple Treasury ETF data
        print("\n🏛️ 正在获取多个国债ETF数据...")
        etf_data_dict = fetch_multiple_treasury_etf_data()

        if etf_data_dict:
            print(f"\n📅 最新国债ETF数据:")
            for etf_code, etf_data in etf_data_dict.items():
                if not etf_data.empty:
                    latest_etf = etf_data.iloc[-1]
                    latest_etf_date = latest_etf['date'].strftime("%Y-%m-%d")
                    latest_etf_price = latest_etf['etf_price']
                    etf_name = latest_etf['etf_name'] if 'etf_name' in etf_data.columns else f'ETF {etf_code}'
                    print(f"   {etf_name} ({etf_code}): {latest_etf_price:.4f} ({latest_etf_date})")

        # Create original PCR plots
        plot_pcr(pcr_data)
        print("\n✅ PCR单独图表已保存: 'sse_50etf_pcr_separate.png'")

        # Fetch SSE 50ETF price data for original overlay
        price_data = fetch_50etf_price()
        if not price_data.empty:
            plot_pcr_with_price(pcr_data, price_data)
            print("✅ PCR与50ETF价格对比图已保存: 'sse_50etf_pcr_price_overlay.png'")

        # Create PCR vs bond index comparison plot
        if not bond_data.empty:
            plot_pcr_with_bond_index(pcr_data, bond_data)
            print("✅ PCR与中债综合全价指数对比图已保存: 'pcr_bond_index_comparison.png'")

        # Create bond index vs multiple ETFs comparison plot
        if not bond_data.empty and etf_data_dict:
            correlation_results = plot_bond_index_vs_multiple_etfs_comparison(bond_data, etf_data_dict)
            print("✅ 中债综合全价指数与多个国债ETF对比图已保存: 'bond_index_vs_multiple_treasury_etfs_comparison.png'")

            print("\n📊 分析结果:")
            print("   1. PCR单独分析图表")
            print("   2. PCR与50ETF价格对比图表")
            print("   3. PCR与中债综合全价指数综合对比图表")
            print("   4. 中债综合全价指数与多个国债ETF对比图表")

            if correlation_results:
                print("\n📈 各ETF与中债综合全价指数相关性排名:")
                sorted_correlations = sorted(correlation_results.items(), key=lambda x: x[1], reverse=True)
                for etf_code, correlation in sorted_correlations:
                    etf_name = None
                    for code, data in etf_data_dict.items():
                        if code == etf_code and 'etf_name' in data.columns:
                            etf_name = data['etf_name'].iloc[0]
                            break
                    etf_display = etf_name if etf_name else f'ETF {etf_code}'
                    print(f"   {etf_display}: {correlation:.4f}")

            print("\n💡 建议:")
            print("   - PCR值接近1表示看涨看跌期权持仓相对平衡")
            print("   - PCR值大于1可能表示市场偏向防御性情绪")
            print("   - 债券指数与股票期权PCR的负相关可能反映资产配置轮动")
            print("   - 10年期国债ETF通常与债券指数相关性更高")
            print("   - 不同期限的国债ETF对利率变化敏感度不同")
        else:
            print("❌ 无法获取完整的债券数据，仅生成部分图表")

    except Exception as e:
        print(f"\n❌ 错误: {e}")
        print("请检查网络连接后重试")

if __name__ == "__main__":
    main()