"""
生成沪深300指数历史成分股模拟数据

由于无法直接获取沪深300历史成分股数据，这个脚本使用当前的成分股数据，
并模拟生成一些历史成分股数据，以便进行后续分析。
"""

import akshare as ak
import pandas as pd
import numpy as np
import os
import random
from datetime import datetime, timedelta
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_historical_constituents_simulation.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_historical_constituents"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def get_current_csi300_constituents():
    """
    获取当前沪深300指数成分股列表
    
    Returns:
        pandas.DataFrame: 沪深300成分股数据，包含股票代码、名称和权重等信息
    """
    logger.info("正在获取当前沪深300指数成分股列表...")
    
    try:
        # 使用AKShare的index_stock_cons_weight_csindex函数获取沪深300成分股
        # symbol="000300"是沪深300指数的代码
        csi300_stocks = ak.index_stock_cons_weight_csindex(symbol="000300")
        
        logger.info(f"成功获取到{len(csi300_stocks)}只沪深300成分股")
        return csi300_stocks
    except Exception as e:
        logger.error(f"获取沪深300成分股失败: {str(e)}")
        return None

def get_all_a_stocks():
    """
    获取所有A股股票列表
    
    Returns:
        pandas.DataFrame: 所有A股股票数据
    """
    logger.info("正在获取所有A股股票列表...")
    
    try:
        # 使用AKShare的stock_zh_a_spot_em函数获取所有A股股票
        all_stocks = ak.stock_zh_a_spot_em()
        
        logger.info(f"成功获取到{len(all_stocks)}只A股股票")
        return all_stocks
    except Exception as e:
        logger.error(f"获取所有A股股票失败: {str(e)}")
        return None

def generate_historical_constituents(current_constituents, all_stocks, num_periods=10, change_rate=0.05):
    """
    生成模拟的历史成分股数据
    
    Args:
        current_constituents: 当前沪深300成分股数据
        all_stocks: 所有A股股票数据
        num_periods: 生成的历史期数
        change_rate: 每期变动的成分股比例
        
    Returns:
        dict: 包含不同日期的成分股数据的字典
    """
    logger.info(f"正在生成{num_periods}期模拟的历史成分股数据...")
    
    # 确保当前成分股数据有效
    if current_constituents is None or current_constituents.empty:
        logger.error("当前成分股数据无效，无法生成历史数据")
        return {}
    
    # 确保所有A股数据有效
    if all_stocks is None or all_stocks.empty:
        logger.error("所有A股数据无效，无法生成历史数据")
        return {}
    
    # 存储不同日期的成分股数据
    historical_constituents = {}
    
    # 当前日期
    current_date = datetime.now()
    
    # 当前成分股
    current_stocks = current_constituents.copy()
    
    # 确保当前成分股数据包含必要的列
    if '成分券代码' not in current_stocks.columns:
        logger.error("当前成分股数据缺少'成分券代码'列")
        return {}
    
    # 添加当前成分股数据
    historical_constituents[current_date.strftime("%Y-%m-%d")] = current_stocks
    
    # 获取所有A股代码
    all_stock_codes = set(all_stocks['代码'].astype(str).tolist())
    
    # 获取当前成分股代码
    current_stock_codes = set(current_stocks['成分券代码'].astype(str).tolist())
    
    # 非成分股代码
    non_constituent_codes = all_stock_codes - current_stock_codes
    
    # 生成历史数据
    for i in range(1, num_periods + 1):
        # 上一期日期（每半年调整一次）
        previous_date = current_date - timedelta(days=i * 180)
        date_str = previous_date.strftime("%Y-%m-%d")
        
        # 复制上一期的成分股
        previous_stocks = current_stocks.copy()
        
        # 计算需要变动的成分股数量
        num_changes = int(len(previous_stocks) * change_rate)
        
        # 随机选择要移除的成分股
        stocks_to_remove = previous_stocks.sample(n=num_changes)
        
        # 从非成分股中随机选择要添加的股票
        stocks_to_add = []
        non_constituent_list = list(non_constituent_codes)
        
        for _ in range(num_changes):
            if non_constituent_list:
                # 随机选择一个非成分股
                random_code = random.choice(non_constituent_list)
                non_constituent_list.remove(random_code)
                
                # 查找该股票的信息
                stock_info = all_stocks[all_stocks['代码'].astype(str) == random_code]
                
                if not stock_info.empty:
                    # 创建新的成分股记录
                    new_stock = {
                        '成分券代码': random_code,
                        '成分券名称': stock_info.iloc[0]['名称'],
                        '权重': round(random.uniform(0.1, 1.0), 4)
                    }
                    stocks_to_add.append(new_stock)
        
        # 移除选中的成分股
        previous_stocks = previous_stocks.drop(stocks_to_remove.index)
        
        # 添加新的成分股
        if stocks_to_add:
            new_stocks_df = pd.DataFrame(stocks_to_add)
            previous_stocks = pd.concat([previous_stocks, new_stocks_df], ignore_index=True)
        
        # 重新计算权重，确保总和为100%
        total_weight = previous_stocks['权重'].sum()
        previous_stocks['权重'] = previous_stocks['权重'] / total_weight * 100
        
        # 添加到历史数据
        historical_constituents[date_str] = previous_stocks
        
        # 更新当前成分股和非成分股
        current_stocks = previous_stocks
        current_stock_codes = set(current_stocks['成分券代码'].astype(str).tolist())
        non_constituent_codes = all_stock_codes - current_stock_codes
    
    logger.info(f"成功生成{len(historical_constituents)}期模拟的历史成分股数据")
    return historical_constituents

def save_historical_constituents(historical_constituents):
    """
    保存历史成分股数据到CSV文件
    
    Args:
        historical_constituents: 包含不同日期的成分股数据的字典
    """
    logger.info("保存历史成分股数据...")
    
    # 创建一个汇总DataFrame
    all_data = []
    
    # 遍历所有日期的数据
    for date, constituents in historical_constituents.items():
        # 添加日期列
        if isinstance(constituents, pd.DataFrame):
            constituents_copy = constituents.copy()
            constituents_copy['adjustment_date'] = date
            all_data.append(constituents_copy)
    
    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)
        
        # 保存汇总文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(OUTPUT_DIR, f"csi300_historical_constituents_simulated_{timestamp}.csv")
        combined_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        logger.info(f"汇总文件已保存到: {summary_file}")
        
        # 按日期保存单独的文件
        for date, constituents in historical_constituents.items():
            if isinstance(constituents, pd.DataFrame):
                date_str = date.replace("-", "")
                file_path = os.path.join(OUTPUT_DIR, f"csi300_constituents_simulated_{date_str}.csv")
                constituents.to_csv(file_path, index=False, encoding='utf-8-sig')
                logger.info(f"{date} 的成分股数据已保存到: {file_path}")
    else:
        logger.warning("没有数据可保存")

def main():
    # 获取当前沪深300成分股
    current_constituents = get_current_csi300_constituents()
    
    # 获取所有A股股票
    all_stocks = get_all_a_stocks()
    
    # 生成模拟的历史成分股数据
    historical_constituents = generate_historical_constituents(
        current_constituents, 
        all_stocks, 
        num_periods=10,  # 生成10期历史数据
        change_rate=0.05  # 每期变动5%的成分股
    )
    
    # 保存历史成分股数据
    save_historical_constituents(historical_constituents)
    
    logger.info("处理完成")

if __name__ == "__main__":
    main()
