"""
获取沪深300成分股的季度财务数据（使用Tushare API）

This script retrieves quarterly financial data (revenue and profit) for CSI 300 constituent stocks
over the past three years using Tushare API.
"""

import tushare as ts
import pandas as pd
import os
import time
from datetime import datetime, timedelta
from concurrent.futures import ThreadPoolExecutor, as_completed
from tqdm import tqdm
import logging

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_financial_data_tushare.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_financial_data"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

# 使用Tushare的免费接口，不需要token
# 如果需要使用pro接口，请在 https://tushare.pro/ 注册并获取token
# TUSHARE_TOKEN = "your_tushare_token"  # 请替换为您的token
# ts.set_token(TUSHARE_TOKEN)
# pro = ts.pro_api()

def get_stock_financial_data(stock_code, stock_name):
    """
    获取单个股票的季度财务数据

    Args:
        stock_code: 股票代码
        stock_name: 股票名称

    Returns:
        pandas.DataFrame: 包含季度财务数据的DataFrame，如果获取失败则返回None
    """
    try:
        logger.info(f"获取 {stock_code} ({stock_name}) 的财务数据...")

        # 计算三年前的日期
        three_years_ago = (datetime.now() - timedelta(days=365*3))

        # 使用Tushare的基础接口获取财务数据
        # 获取利润表数据 - 使用基础接口
        # 将股票代码转换为Tushare基础接口格式（去掉.SH或.SZ后缀）
        if '.' in stock_code:
            basic_code = stock_code.split('.')[0]
        else:
            basic_code = stock_code

        # 添加市场前缀
        if basic_code.startswith('6') or basic_code.startswith('688'):
            market_code = f"sh{basic_code}"
        else:
            market_code = f"sz{basic_code}"

        logger.info(f"获取 {market_code} ({stock_name}) 的财务数据...")

        try:
            # 获取财务报表数据
            financial_data = ts.get_report_data(year=three_years_ago.year, quarter=4)

            # 如果数据为空，记录错误并返回None
            if financial_data is None or financial_data.empty:
                logger.error(f"{stock_code} ({stock_name}) 获取财务报表数据失败")
                return None

            # 筛选当前股票的数据
            financial_data = financial_data[financial_data['code'] == basic_code]

            if financial_data.empty:
                logger.error(f"{stock_code} ({stock_name}) 在财务报表中未找到数据")
                return None

            # 获取利润表数据
            profit_data = ts.get_profit_data(year=three_years_ago.year, quarter=4)
            if profit_data is not None and not profit_data.empty:
                profit_data = profit_data[profit_data['code'] == basic_code]

            # 获取运营数据
            operation_data = ts.get_operation_data(year=three_years_ago.year, quarter=4)
            if operation_data is not None and not operation_data.empty:
                operation_data = operation_data[operation_data['code'] == basic_code]

            # 获取成长数据
            growth_data = ts.get_growth_data(year=three_years_ago.year, quarter=4)
            if growth_data is not None and not growth_data.empty:
                growth_data = growth_data[growth_data['code'] == basic_code]

            # 尝试获取最近12个季度的数据
            all_quarterly_data = []

            for year in range(three_years_ago.year, datetime.now().year + 1):
                for quarter in range(1, 5):
                    # 跳过未来的季度
                    if year == datetime.now().year and quarter > (datetime.now().month // 3 + 1):
                        continue

                    try:
                        # 获取该季度的财务报表
                        quarter_data = ts.get_report_data(year=year, quarter=quarter)
                        if quarter_data is not None and not quarter_data.empty:
                            # 筛选当前股票
                            quarter_data = quarter_data[quarter_data['code'] == basic_code]
                            if not quarter_data.empty:
                                # 添加年份和季度信息
                                quarter_data['year'] = year
                                quarter_data['quarter'] = quarter
                                quarter_data['报告期'] = f"{year}Q{quarter}"
                                all_quarterly_data.append(quarter_data)
                    except Exception as e:
                        logger.warning(f"获取 {stock_code} ({stock_name}) {year}年第{quarter}季度数据失败: {str(e)}")

            # 合并所有季度数据
            if all_quarterly_data:
                income_df = pd.concat(all_quarterly_data, ignore_index=True)
            else:
                logger.error(f"{stock_code} ({stock_name}) 未获取到任何季度财务数据")
                return None

            # 使用profit_data, operation_data和growth_data作为补充数据
            balance_df = None
            cashflow_df = None
        except Exception as e:
            logger.error(f"{stock_code} ({stock_name}) 获取财务数据时出错: {str(e)}")
            return None

        # 处理利润表数据，提取营收和净利润
        result_df = pd.DataFrame()
        result_df['报告期'] = income_df['报告期']
        result_df['股票代码'] = stock_code
        result_df['股票名称'] = stock_name

        # 使用Tushare基础接口返回的列名
        if 'eps' in income_df.columns:
            result_df['每股收益'] = income_df['eps']
        if 'bvps' in income_df.columns:
            result_df['每股净资产'] = income_df['bvps']
        if 'roe' in income_df.columns:
            result_df['净资产收益率'] = income_df['roe']
        if 'net_profits' in income_df.columns:
            result_df['净利润'] = income_df['net_profits']
        if 'profits_yoy' in income_df.columns:
            result_df['净利润同比增长率'] = income_df['profits_yoy']
        if 'distrib' in income_df.columns:
            result_df['分配方案'] = income_df['distrib']

        # 添加其他可能的财务指标
        if profit_data is not None and not profit_data.empty:
            if 'net_profit_ratio' in profit_data.columns:
                result_df['销售净利率'] = profit_data['net_profit_ratio'].values[0] if len(profit_data) > 0 else None
            if 'gross_profit_rate' in profit_data.columns:
                result_df['毛利率'] = profit_data['gross_profit_rate'].values[0] if len(profit_data) > 0 else None
            if 'business_income' in profit_data.columns:
                result_df['营业收入'] = profit_data['business_income'].values[0] if len(profit_data) > 0 else None

        if operation_data is not None and not operation_data.empty:
            if 'arturn_days' in operation_data.columns:
                result_df['应收账款周转天数'] = operation_data['arturn_days'].values[0] if len(operation_data) > 0 else None
            if 'inventory_turn' in operation_data.columns:
                result_df['存货周转率'] = operation_data['inventory_turn'].values[0] if len(operation_data) > 0 else None

        if growth_data is not None and not growth_data.empty:
            if 'nprg' in growth_data.columns:
                result_df['净利润增长率'] = growth_data['nprg'].values[0] if len(growth_data) > 0 else None
            if 'mbrg' in growth_data.columns:
                result_df['主营业务收入增长率'] = growth_data['mbrg'].values[0] if len(growth_data) > 0 else None

        # 添加资产负债表数据
        if balance_df is not None and not balance_df.empty:
            # 确保日期匹配
            balance_df = balance_df.set_index('end_date')
            result_df = result_df.set_index('报告期')

            # 添加关键指标
            result_df['总资产'] = balance_df['total_assets']
            result_df['总负债'] = balance_df['total_liab']
            result_df['股东权益'] = balance_df['total_hldr_eqy_exc_min_int']

            # 重置索引
            result_df = result_df.reset_index().rename(columns={'index': '报告期'})

        # 添加现金流量表数据
        if cashflow_df is not None and not cashflow_df.empty:
            # 确保日期匹配
            cashflow_df = cashflow_df.set_index('end_date')
            result_df = result_df.set_index('报告期')

            # 添加关键指标
            result_df['经营活动产生的现金流量净额'] = cashflow_df['n_cashflow_act']
            result_df['投资活动产生的现金流量净额'] = cashflow_df['n_cashflow_inv_act']
            result_df['筹资活动产生的现金流量净额'] = cashflow_df['n_cash_flows_fnc_act']

            # 重置索引
            result_df = result_df.reset_index().rename(columns={'index': '报告期'})

        # 计算同比增长率（如果有足够的历史数据）
        if len(result_df) > 4:
            # 按报告期排序
            result_df = result_df.sort_values('报告期')

            # 计算同比增长率
            result_df['营业收入同比增长率'] = result_df['营业收入'].pct_change(4) * 100
            result_df['净利润同比增长率'] = result_df['净利润'].pct_change(4) * 100

            # 重新按报告期降序排序
            result_df = result_df.sort_values('报告期', ascending=False)

        return result_df

    except Exception as e:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据时出错: {str(e)}")
        return None

def process_stock(stock_info):
    """
    处理单个股票的财务数据并保存

    Args:
        stock_info: 包含股票代码和名称的元组 (code, name)

    Returns:
        tuple: (股票代码, 成功/失败)
    """
    stock_code, stock_name = stock_info

    # 检查是否已经处理过
    output_file = os.path.join(OUTPUT_DIR, f"{stock_code}_financial_data.csv")
    if os.path.exists(output_file):
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已存在，跳过处理")
        return stock_code, True

    # 获取财务数据
    df = get_stock_financial_data(stock_code, stock_name)

    if df is not None and not df.empty:
        # 保存到CSV
        df.to_csv(output_file, index=False, encoding='utf-8-sig')
        logger.info(f"{stock_code} ({stock_name}) 的财务数据已保存到 {output_file}")
        return stock_code, True
    else:
        logger.error(f"{stock_code} ({stock_name}) 获取财务数据失败")
        return stock_code, False

def main():
    # 读取沪深300成分股列表
    try:
        # 查找最新的格式化文件
        data_dir = "data"
        files = [f for f in os.listdir(data_dir) if f.startswith("csi300_constituents_formatted_") and f.endswith(".csv")]
        if not files:
            logger.error("未找到沪深300成分股数据文件")
            return

        # 按文件名排序，获取最新的文件
        files.sort(reverse=True)
        input_file = os.path.join(data_dir, files[0])

        # 读取CSV文件
        df = pd.read_csv(input_file, encoding="utf-8-sig")
        logger.info(f"从 {input_file} 读取了 {len(df)} 只沪深300成分股")

        # 准备股票列表，转换股票代码格式为Tushare格式
        stock_list = []
        for _, row in df.iterrows():
            code = str(row["成分券代码"])
            name = row["成分券名称"]

            # 转换为Tushare格式：上交所股票代码后缀为.SH，深交所股票代码后缀为.SZ
            if code.startswith('6') or code.startswith('688'):
                ts_code = f"{code}.SH"
            else:
                ts_code = f"{code}.SZ"

            stock_list.append((ts_code, name))

        # 使用线程池并行处理
        success_count = 0
        fail_count = 0

        with ThreadPoolExecutor(max_workers=5) as executor:
            futures = {executor.submit(process_stock, stock_info): stock_info for stock_info in stock_list}

            # 使用tqdm显示进度
            for future in tqdm(as_completed(futures), total=len(futures), desc="处理进度"):
                stock_info = futures[future]
                try:
                    code, success = future.result()
                    if success:
                        success_count += 1
                    else:
                        fail_count += 1
                except Exception as e:
                    logger.error(f"处理 {stock_info[0]} ({stock_info[1]}) 时发生异常: {str(e)}")
                    fail_count += 1

                # 添加延迟以避免请求过于频繁
                time.sleep(1)

        logger.info(f"处理完成: 成功 {success_count} 只, 失败 {fail_count} 只")

        # 创建汇总文件
        create_summary_file()

    except Exception as e:
        logger.error(f"处理沪深300成分股财务数据时出错: {str(e)}")

def create_summary_file():
    """创建汇总文件，包含所有公司的关键财务指标"""
    try:
        # 获取所有已处理的文件
        all_files = [f for f in os.listdir(OUTPUT_DIR) if f.endswith("_financial_data.csv")]

        if not all_files:
            logger.warning("没有找到已处理的财务数据文件，无法创建汇总")
            return

        # 读取并合并所有数据
        all_data = []
        for file in all_files:
            try:
                file_path = os.path.join(OUTPUT_DIR, file)
                df = pd.read_csv(file_path, encoding='utf-8-sig')

                # 只保留最新一期的数据
                if not df.empty:
                    latest_data = df.iloc[0].to_dict()
                    all_data.append(latest_data)
            except Exception as e:
                logger.error(f"读取文件 {file} 时出错: {str(e)}")

        if all_data:
            # 创建汇总DataFrame
            summary_df = pd.DataFrame(all_data)

            # 保存汇总文件
            summary_file = os.path.join(OUTPUT_DIR, "csi300_financial_summary.csv")
            summary_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
            logger.info(f"汇总文件已保存到 {summary_file}")

            # 创建按行业分类的汇总
            if '行业' in summary_df.columns:
                industry_summary = summary_df.groupby('行业').agg({
                    '营业收入': 'sum',
                    '净利润': 'sum',
                    '股票代码': 'count'
                }).reset_index()
                industry_summary.rename(columns={'股票代码': '公司数量'}, inplace=True)

                # 保存行业汇总文件
                industry_file = os.path.join(OUTPUT_DIR, "csi300_industry_summary.csv")
                industry_summary.to_csv(industry_file, index=False, encoding='utf-8-sig')
                logger.info(f"行业汇总文件已保存到 {industry_file}")
        else:
            logger.warning("没有有效的财务数据，无法创建汇总")

    except Exception as e:
        logger.error(f"创建汇总文件时出错: {str(e)}")

if __name__ == "__main__":
    main()
