"""
获取沪深300指数历史成分股列表

This script retrieves the historical constituent stocks for the CSI 300 Index (沪深300)
using multiple data sources and saves the data to CSV files.
"""

import akshare as ak
import pandas as pd
import numpy as np
import os
import requests
from datetime import datetime, timedelta
import time
import logging
import json
from bs4 import BeautifulSoup
import re

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("csi300_historical_constituents.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger()

# 创建输出目录
OUTPUT_DIR = "data/csi300_historical_constituents"
if not os.path.exists(OUTPUT_DIR):
    os.makedirs(OUTPUT_DIR)

def get_current_csi300_constituents():
    """
    获取当前沪深300指数成分股列表

    Returns:
        pandas.DataFrame: 沪深300成分股数据，包含股票代码、名称和权重等信息
    """
    logger.info("正在获取当前沪深300指数成分股列表...")

    try:
        # 使用AKShare的index_stock_cons_weight_csindex函数获取沪深300成分股
        # symbol="000300"是沪深300指数的代码
        csi300_stocks = ak.index_stock_cons_weight_csindex(symbol="000300")

        logger.info(f"成功获取到{len(csi300_stocks)}只沪深300成分股")
        return csi300_stocks
    except Exception as e:
        logger.error(f"获取沪深300成分股失败: {str(e)}")
        return None

def get_csi300_historical_constituents_from_csindex():
    """
    从中证指数官网获取沪深300历史成分股数据

    Returns:
        dict: 包含不同日期的成分股数据的字典
    """
    logger.info("尝试从中证指数官网获取沪深300历史成分股数据...")

    try:
        # 中证指数官网API
        url = "https://www.csindex.com.cn/csindex-home/search/index-constituents"

        # 获取最近5年的调整日期
        adjustment_dates = []
        current_year = datetime.now().year
        for year in range(current_year - 5, current_year + 1):
            for month in [6, 12]:  # 沪深300通常在6月和12月调整
                adjustment_date = f"{year}-{month:02d}-15"  # 使用每月15日作为近似调整日
                adjustment_dates.append(adjustment_date)

        # 按日期从新到旧排序
        adjustment_dates.sort(reverse=True)

        # 存储不同日期的成分股数据
        historical_constituents = {}

        # 遍历调整日期获取成分股数据
        for date in adjustment_dates:
            logger.info(f"获取 {date} 的沪深300成分股数据...")

            # 构建请求参数
            params = {
                "indexCode": "000300",  # 沪深300指数代码
                "dateStr": date,
                "pageNum": 1,
                "pageSize": 500  # 足够大以获取所有成分股
            }

            # 发送请求
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            }
            response = requests.post(url, json=params, headers=headers)

            # 检查请求是否成功
            if response.status_code == 200:
                data = response.json()
                if data.get("data") and data["data"].get("constituents"):
                    constituents = data["data"]["constituents"]
                    df = pd.DataFrame(constituents)
                    historical_constituents[date] = df
                    logger.info(f"成功获取到 {date} 的 {len(df)} 只沪深300成分股")
                else:
                    logger.warning(f"未找到 {date} 的沪深300成分股数据")
            else:
                logger.error(f"请求失败，状态码: {response.status_code}")

            # 添加延迟以避免请求过于频繁
            time.sleep(2)

        return historical_constituents

    except Exception as e:
        logger.error(f"从中证指数官网获取沪深300历史成分股数据失败: {str(e)}")
        return {}

def get_csi300_historical_constituents_from_akshare():
    """
    使用AKShare获取沪深300历史成分股数据

    Returns:
        dict: 包含不同日期的成分股数据的字典
    """
    logger.info("尝试使用AKShare获取沪深300历史成分股数据...")

    try:
        # 获取沪深300指数的历史成分股
        historical_constituents = {}

        # 使用index_stock_cons_csindex函数获取当前成分股
        try:
            # 获取当前成分股
            csi300_current = ak.index_stock_cons_csindex(symbol="000300")

            if csi300_current is not None and not csi300_current.empty:
                current_date = datetime.now().strftime("%Y-%m-%d")
                historical_constituents[current_date] = csi300_current
                logger.info(f"成功获取到当前沪深300成分股，共 {len(csi300_current)} 只")
            else:
                logger.warning("未能获取当前沪深300成分股")
        except Exception as e:
            logger.warning(f"获取当前沪深300成分股失败: {str(e)}")

        # 使用index_stock_hist_csindex函数获取历史成分股变动（如果存在）
        try:
            # 尝试获取历史成分股变动
            logger.info("尝试获取沪深300历史成分股变动...")

            # 获取最近5年的调整日期
            adjustment_dates = []
            current_year = datetime.now().year
            for year in range(current_year - 5, current_year + 1):
                for month in [6, 12]:  # 沪深300通常在6月和12月调整
                    adjustment_date = f"{year}-{month:02d}-15"  # 使用每月15日作为近似调整日
                    adjustment_dates.append(adjustment_date)

            # 按日期从新到旧排序
            adjustment_dates.sort(reverse=True)

            # 尝试使用stock_zh_index_hist_csindex函数获取历史数据
            for date in adjustment_dates:
                try:
                    logger.info(f"尝试获取 {date} 的沪深300成分股...")
                    # 注意：这个函数可能不存在或参数可能不正确
                    hist_data = ak.stock_zh_index_hist_csindex(symbol="000300", start_date=date, end_date=date)

                    if hist_data is not None and not hist_data.empty:
                        historical_constituents[date] = hist_data
                        logger.info(f"成功获取到 {date} 的沪深300成分股，共 {len(hist_data)} 只")
                    else:
                        logger.warning(f"未能获取 {date} 的沪深300成分股")
                except Exception as e:
                    logger.warning(f"获取 {date} 的沪深300成分股失败: {str(e)}")

                # 添加延迟以避免请求过于频繁
                time.sleep(1)
        except Exception as e:
            logger.warning(f"获取沪深300历史成分股变动失败: {str(e)}")

        # 尝试使用index_stock_cons函数获取历史成分股
        try:
            logger.info("尝试使用index_stock_cons函数获取沪深300历史成分股...")

            # 获取沪深300成分股
            csi300_cons = ak.index_stock_cons(symbol="000300")

            if csi300_cons is not None and not csi300_cons.empty:
                # 如果返回的数据包含日期信息，按日期分组
                if "date" in csi300_cons.columns:
                    for date, group in csi300_cons.groupby("date"):
                        historical_constituents[date] = group
                    logger.info(f"成功使用index_stock_cons获取到 {len(historical_constituents)} 个日期的沪深300成分股")
                else:
                    # 如果没有日期信息，使用当前日期
                    current_date = datetime.now().strftime("%Y-%m-%d")
                    if current_date not in historical_constituents:
                        historical_constituents[current_date] = csi300_cons
                        logger.info(f"成功获取到当前沪深300成分股，共 {len(csi300_cons)} 只")
            else:
                logger.warning("index_stock_cons未返回沪深300成分股数据")
        except Exception as e:
            logger.warning(f"使用index_stock_cons获取沪深300成分股失败: {str(e)}")

        logger.info(f"成功使用AKShare获取到 {len(historical_constituents)} 个日期的沪深300历史成分股")
        return historical_constituents

    except Exception as e:
        logger.error(f"使用AKShare获取沪深300历史成分股数据失败: {str(e)}")
        return {}

def get_csi300_historical_constituents_from_wind_choice():
    """
    模拟从Wind或Choice获取沪深300历史成分股数据

    注意：这个函数需要Wind或Choice的API访问权限，这里只是一个示例框架

    Returns:
        dict: 包含不同日期的成分股数据的字典
    """
    logger.info("尝试从Wind/Choice获取沪深300历史成分股数据...")

    # 这里需要实际的Wind或Choice API调用
    # 由于API访问需要授权，这里只提供框架

    # 示例代码（需要替换为实际API调用）:
    # import WindPy as w
    # w.start()
    # dates = ["2020-06-15", "2020-12-15", "2021-06-15", "2021-12-15"]
    # historical_constituents = {}
    # for date in dates:
    #     data = w.wset("indexconstituent","date="+date+";windcode=000300.SH")
    #     if data.ErrorCode == 0:
    #         df = pd.DataFrame(data.Data, index=data.Fields, columns=data.Codes).T
    #         historical_constituents[date] = df

    logger.warning("Wind/Choice API访问需要授权，此功能未实现")
    return {}

def get_csi300_historical_constituents_from_eastmoney():
    """
    从东方财富网获取沪深300历史成分股数据

    Returns:
        dict: 包含不同日期的成分股数据的字典
    """
    logger.info("尝试从东方财富网获取沪深300历史成分股数据...")

    try:
        # 东方财富网沪深300指数页面
        url = "http://quote.eastmoney.com/center/gridlist.html#hs300_constituents"

        # 发送请求
        headers = {
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
        }
        response = requests.get(url, headers=headers)

        # 检查请求是否成功
        if response.status_code == 200:
            # 使用BeautifulSoup解析HTML
            soup = BeautifulSoup(response.text, 'html.parser')

            # 尝试找到包含历史成分股的数据
            # 注意：这需要根据实际网页结构进行调整
            # 这里只是一个示例框架

            logger.warning("东方财富网页面解析需要根据实际网页结构进行调整，此功能未完全实现")
            return {}
        else:
            logger.error(f"请求失败，状态码: {response.status_code}")
            return {}

    except Exception as e:
        logger.error(f"从东方财富网获取沪深300历史成分股数据失败: {str(e)}")
        return {}

def combine_historical_constituents(sources):
    """
    合并来自不同来源的历史成分股数据

    Args:
        sources: 包含来自不同来源的历史成分股数据的字典

    Returns:
        dict: 合并后的历史成分股数据
    """
    logger.info("合并来自不同来源的历史成分股数据...")

    combined_data = {}

    # 遍历所有来源
    for source_name, source_data in sources.items():
        if source_data:
            logger.info(f"处理来源: {source_name}，包含 {len(source_data)} 个日期的数据")

            # 遍历该来源的所有日期
            for date, constituents in source_data.items():
                if date not in combined_data:
                    combined_data[date] = constituents
                    logger.info(f"添加 {date} 的数据，包含 {len(constituents)} 只股票")
                else:
                    # 如果该日期已存在，合并数据
                    # 这里需要根据实际数据结构进行调整
                    logger.info(f"合并 {date} 的数据")
                    # 示例合并逻辑（需要根据实际数据结构调整）
                    # combined_data[date] = pd.concat([combined_data[date], constituents]).drop_duplicates()

    logger.info(f"合并完成，共有 {len(combined_data)} 个日期的数据")
    return combined_data

def save_historical_constituents(historical_constituents):
    """
    保存历史成分股数据到CSV文件

    Args:
        historical_constituents: 包含不同日期的成分股数据的字典
    """
    logger.info("保存历史成分股数据...")

    # 创建一个汇总DataFrame
    all_data = []

    # 遍历所有日期的数据
    for date, constituents in historical_constituents.items():
        # 添加日期列
        if isinstance(constituents, pd.DataFrame):
            constituents_copy = constituents.copy()
            constituents_copy['adjustment_date'] = date
            all_data.append(constituents_copy)

    # 合并所有数据
    if all_data:
        combined_df = pd.concat(all_data, ignore_index=True)

        # 保存汇总文件
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        summary_file = os.path.join(OUTPUT_DIR, f"csi300_historical_constituents_{timestamp}.csv")
        combined_df.to_csv(summary_file, index=False, encoding='utf-8-sig')
        logger.info(f"汇总文件已保存到: {summary_file}")

        # 按日期保存单独的文件
        for date, constituents in historical_constituents.items():
            if isinstance(constituents, pd.DataFrame):
                date_str = date.replace("-", "")
                file_path = os.path.join(OUTPUT_DIR, f"csi300_constituents_{date_str}.csv")
                constituents.to_csv(file_path, index=False, encoding='utf-8-sig')
                logger.info(f"{date} 的成分股数据已保存到: {file_path}")
    else:
        logger.warning("没有数据可保存")

def main():
    # 获取当前沪深300成分股
    current_constituents = get_current_csi300_constituents()

    # 创建一个字典存储当前成分股
    current_date = datetime.now().strftime("%Y-%m-%d")
    historical_constituents = {}
    if current_constituents is not None and not current_constituents.empty:
        historical_constituents[current_date] = current_constituents

    # 从不同来源获取历史成分股数据
    sources = {
        "csindex": get_csi300_historical_constituents_from_csindex(),
        "akshare": get_csi300_historical_constituents_from_akshare(),
        # "wind_choice": get_csi300_historical_constituents_from_wind_choice(),
        # "eastmoney": get_csi300_historical_constituents_from_eastmoney()
    }

    # 合并来自不同来源的数据
    combined_data = combine_historical_constituents(sources)

    # 将当前成分股数据添加到合并数据中
    for date, constituents in historical_constituents.items():
        if date not in combined_data:
            combined_data[date] = constituents

    # 保存历史成分股数据
    save_historical_constituents(combined_data)

    logger.info("处理完成")

if __name__ == "__main__":
    main()
