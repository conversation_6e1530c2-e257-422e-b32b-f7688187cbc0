"""
恒生科技指数 vs 恒生高股息指数动量轮动策略参数优化

优化参数：
1. 动量回看天数 (momentum_window): 5, 10, 15, 20, 30, 40, 60天
2. 调仓周期 (rebalance_freq): 1, 3, 5, 10, 15, 20天

优化目标：
- 夏普比率最大化
- 总收益率
- 最大回撤控制

作者：AI Assistant
创建时间：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import akshare as ak
from datetime import datetime, timedelta
import warnings
import os
import pickle
import hashlib
from itertools import product
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class MomentumStrategyOptimizer:
    def __init__(self, hstech_data, hshdyi_data):
        """初始化优化器"""
        self.hstech_data = hstech_data
        self.hshdyi_data = hshdyi_data
        
        # 对齐数据
        self.align_data()
        
        # 参数范围
        self.momentum_windows = [5, 10, 15, 20, 30, 40, 60]  # 动量回看天数
        self.rebalance_freqs = [1, 3, 5, 10, 15, 20]  # 调仓周期
        
        # 存储优化结果
        self.optimization_results = []
        
        print(f"数据对齐完成，共{len(self.aligned_data)}个交易日")
        print(f"参数优化范围：")
        print(f"  动量回看天数: {self.momentum_windows}")
        print(f"  调仓周期: {self.rebalance_freqs}")
        print(f"  总共需要测试: {len(self.momentum_windows) * len(self.rebalance_freqs)} 种参数组合")
    
    def align_data(self):
        """对齐两个指数的数据"""
        # 找到共同的日期范围
        common_dates = self.hstech_data.index.intersection(self.hshdyi_data.index)
        
        # 创建对齐的数据
        self.aligned_data = pd.DataFrame(index=common_dates)
        self.aligned_data['hstech_close'] = self.hstech_data.loc[common_dates, 'close']
        self.aligned_data['hshdyi_close'] = self.hshdyi_data.loc[common_dates, 'close']
        
        # 去除空值
        self.aligned_data = self.aligned_data.dropna()
        
        # 计算收益率
        self.aligned_data['hstech_return'] = self.aligned_data['hstech_close'].pct_change()
        self.aligned_data['hshdyi_return'] = self.aligned_data['hshdyi_close'].pct_change()
    
    def momentum_strategy_with_params(self, momentum_window, rebalance_freq):
        """使用指定参数运行动量策略"""
        # 计算滚动收益率
        hstech_momentum = self.aligned_data['hstech_close'].pct_change(momentum_window)
        hshdyi_momentum = self.aligned_data['hshdyi_close'].pct_change(momentum_window)
        
        # 生成原始信号
        raw_signals = pd.Series(index=self.aligned_data.index, dtype=str)
        raw_signals[hstech_momentum > hshdyi_momentum] = 'HSTECH'
        raw_signals[hshdyi_momentum > hstech_momentum] = 'HSHDYI'
        raw_signals = raw_signals.fillna(method='ffill')
        
        # 应用调仓频率限制
        signals = self.apply_rebalance_frequency(raw_signals, rebalance_freq)
        
        return self.backtest_strategy(signals, momentum_window, rebalance_freq)
    
    def apply_rebalance_frequency(self, raw_signals, rebalance_freq):
        """应用调仓频率限制"""
        if rebalance_freq == 1:
            return raw_signals  # 每日调仓
        
        # 创建调仓信号
        rebalanced_signals = pd.Series(index=raw_signals.index, dtype=str)
        
        # 初始信号
        if len(raw_signals) > 0:
            rebalanced_signals.iloc[0] = raw_signals.iloc[0]
        
        # 按调仓频率更新信号
        for i in range(rebalance_freq, len(raw_signals), rebalance_freq):
            if i < len(raw_signals):
                rebalanced_signals.iloc[i:i+rebalance_freq] = raw_signals.iloc[i]
        
        # 前向填充
        rebalanced_signals = rebalanced_signals.fillna(method='ffill')
        
        return rebalanced_signals
    
    def backtest_strategy(self, signals, momentum_window, rebalance_freq):
        """回测策略"""
        portfolio_value = [1.0]  # 初始资金为1
        transaction_costs = []
        position_changes = 0
        
        prev_position = None
        
        for i in range(1, len(self.aligned_data)):
            current_signal = signals.iloc[i]
            prev_value = portfolio_value[-1]
            
            # 计算换仓成本
            transaction_cost = 0
            if prev_position is not None and prev_position != current_signal:
                transaction_cost = 0.001  # 0.1%的交易成本
                position_changes += 1
            
            if current_signal == 'HSTECH':
                daily_return = self.aligned_data['hstech_return'].iloc[i]
                new_value = prev_value * (1 + daily_return) * (1 - transaction_cost)
            elif current_signal == 'HSHDYI':
                daily_return = self.aligned_data['hshdyi_return'].iloc[i]
                new_value = prev_value * (1 + daily_return) * (1 - transaction_cost)
            else:
                new_value = prev_value * (1 - transaction_cost)
            
            portfolio_value.append(new_value)
            transaction_costs.append(transaction_cost)
            prev_position = current_signal
        
        # 计算策略统计
        final_value = portfolio_value[-1]
        total_return = (final_value - 1) * 100
        
        # 计算日收益率
        portfolio_returns = pd.Series(portfolio_value).pct_change().dropna()
        
        # 年化收益率
        trading_days = len(portfolio_returns)
        annual_return = ((final_value ** (252 / trading_days)) - 1) * 100
        
        # 波动率
        volatility = portfolio_returns.std() * np.sqrt(252) * 100
        
        # 夏普比率
        sharpe_ratio = annual_return / volatility if volatility > 0 else 0
        
        # 最大回撤
        portfolio_series = pd.Series(portfolio_value)
        peak = portfolio_series.expanding().max()
        drawdown = (portfolio_series - peak) / peak
        max_drawdown = drawdown.min() * 100
        
        # 胜率
        win_rate = (portfolio_returns > 0).mean() * 100
        
        # 换仓次数
        annual_turnover = position_changes * 252 / trading_days
        
        return {
            'momentum_window': momentum_window,
            'rebalance_freq': rebalance_freq,
            'total_return': total_return,
            'annual_return': annual_return,
            'volatility': volatility,
            'sharpe_ratio': sharpe_ratio,
            'max_drawdown': max_drawdown,
            'win_rate': win_rate,
            'position_changes': position_changes,
            'annual_turnover': annual_turnover,
            'final_value': final_value
        }
    
    def run_optimization(self):
        """运行参数优化"""
        print("\n🔍 开始参数优化...")
        print("="*80)
        
        total_combinations = len(self.momentum_windows) * len(self.rebalance_freqs)
        current_combination = 0
        
        for momentum_window in self.momentum_windows:
            for rebalance_freq in self.rebalance_freqs:
                current_combination += 1
                
                print(f"正在测试参数组合 {current_combination}/{total_combinations}: "
                      f"回看{momentum_window}天, 调仓{rebalance_freq}天", end=" ... ")
                
                try:
                    result = self.momentum_strategy_with_params(momentum_window, rebalance_freq)
                    self.optimization_results.append(result)
                    print(f"✅ 夏普比率: {result['sharpe_ratio']:.2f}")
                    
                except Exception as e:
                    print(f"❌ 失败: {e}")
        
        print(f"\n✅ 参数优化完成！共测试了 {len(self.optimization_results)} 种参数组合")
    
    def analyze_results(self):
        """分析优化结果"""
        if not self.optimization_results:
            print("❌ 没有优化结果可供分析")
            return
        
        # 转换为DataFrame
        results_df = pd.DataFrame(self.optimization_results)
        
        print("\n" + "="*100)
        print("📊 参数优化结果分析")
        print("="*100)
        
        # 按不同指标排序找出最佳参数
        best_sharpe = results_df.loc[results_df['sharpe_ratio'].idxmax()]
        best_return = results_df.loc[results_df['total_return'].idxmax()]
        best_drawdown = results_df.loc[results_df['max_drawdown'].idxmax()]  # 最小回撤（最大值因为是负数）
        
        print("\n🏆 最佳参数组合:")
        print("-" * 60)
        print(f"📈 最佳夏普比率: 回看{best_sharpe['momentum_window']}天, 调仓{best_sharpe['rebalance_freq']}天")
        print(f"   夏普比率: {best_sharpe['sharpe_ratio']:.3f}, 总收益: {best_sharpe['total_return']:.2f}%, 最大回撤: {best_sharpe['max_drawdown']:.2f}%")
        
        print(f"\n💰 最高总收益: 回看{best_return['momentum_window']}天, 调仓{best_return['rebalance_freq']}天")
        print(f"   总收益: {best_return['total_return']:.2f}%, 夏普比率: {best_return['sharpe_ratio']:.3f}, 最大回撤: {best_return['max_drawdown']:.2f}%")
        
        print(f"\n🛡️ 最小回撤: 回看{best_drawdown['momentum_window']}天, 调仓{best_drawdown['rebalance_freq']}天")
        print(f"   最大回撤: {best_drawdown['max_drawdown']:.2f}%, 夏普比率: {best_drawdown['sharpe_ratio']:.3f}, 总收益: {best_drawdown['total_return']:.2f}%")
        
        # 统计分析
        print(f"\n📊 统计摘要:")
        print(f"   平均夏普比率: {results_df['sharpe_ratio'].mean():.3f}")
        print(f"   最高夏普比率: {results_df['sharpe_ratio'].max():.3f}")
        print(f"   平均总收益率: {results_df['total_return'].mean():.2f}%")
        print(f"   最高总收益率: {results_df['total_return'].max():.2f}%")
        print(f"   平均最大回撤: {results_df['max_drawdown'].mean():.2f}%")
        print(f"   最小最大回撤: {results_df['max_drawdown'].max():.2f}%")
        
        return results_df, best_sharpe, best_return, best_drawdown

    def plot_optimization_results(self, results_df):
        """绘制优化结果热力图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 12))
        fig.suptitle('动量轮动策略参数优化结果', fontsize=16, fontweight='bold')

        # 创建透视表用于热力图
        sharpe_pivot = results_df.pivot(index='momentum_window', columns='rebalance_freq', values='sharpe_ratio')
        return_pivot = results_df.pivot(index='momentum_window', columns='rebalance_freq', values='total_return')
        drawdown_pivot = results_df.pivot(index='momentum_window', columns='rebalance_freq', values='max_drawdown')
        volatility_pivot = results_df.pivot(index='momentum_window', columns='rebalance_freq', values='volatility')

        # 1. 夏普比率热力图
        sns.heatmap(sharpe_pivot, annot=True, fmt='.2f', cmap='RdYlGn',
                   ax=axes[0,0], cbar_kws={'label': '夏普比率'})
        axes[0,0].set_title('夏普比率热力图')
        axes[0,0].set_xlabel('调仓周期 (天)')
        axes[0,0].set_ylabel('动量回看天数')

        # 2. 总收益率热力图
        sns.heatmap(return_pivot, annot=True, fmt='.1f', cmap='RdYlGn',
                   ax=axes[0,1], cbar_kws={'label': '总收益率 (%)'})
        axes[0,1].set_title('总收益率热力图')
        axes[0,1].set_xlabel('调仓周期 (天)')
        axes[0,1].set_ylabel('动量回看天数')

        # 3. 最大回撤热力图（注意：回撤越小越好，所以颜色反转）
        sns.heatmap(drawdown_pivot, annot=True, fmt='.1f', cmap='RdYlGn_r',
                   ax=axes[1,0], cbar_kws={'label': '最大回撤 (%)'})
        axes[1,0].set_title('最大回撤热力图')
        axes[1,0].set_xlabel('调仓周期 (天)')
        axes[1,0].set_ylabel('动量回看天数')

        # 4. 波动率热力图
        sns.heatmap(volatility_pivot, annot=True, fmt='.1f', cmap='RdYlGn_r',
                   ax=axes[1,1], cbar_kws={'label': '年化波动率 (%)'})
        axes[1,1].set_title('年化波动率热力图')
        axes[1,1].set_xlabel('调仓周期 (天)')
        axes[1,1].set_ylabel('动量回看天数')

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"momentum_optimization_heatmap_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"\n📊 参数优化热力图已保存至: {filename}")

        plt.show()

    def plot_parameter_analysis(self, results_df):
        """绘制参数分析图"""
        fig, axes = plt.subplots(2, 2, figsize=(16, 10))
        fig.suptitle('参数对策略表现的影响分析', fontsize=16, fontweight='bold')

        # 1. 动量回看天数对夏普比率的影响
        momentum_sharpe = results_df.groupby('momentum_window')['sharpe_ratio'].agg(['mean', 'std', 'max'])
        axes[0,0].errorbar(momentum_sharpe.index, momentum_sharpe['mean'],
                          yerr=momentum_sharpe['std'], marker='o', capsize=5)
        axes[0,0].plot(momentum_sharpe.index, momentum_sharpe['max'], 'r--', alpha=0.7, label='最大值')
        axes[0,0].set_title('动量回看天数 vs 夏普比率')
        axes[0,0].set_xlabel('动量回看天数')
        axes[0,0].set_ylabel('夏普比率')
        axes[0,0].legend()
        axes[0,0].grid(True, alpha=0.3)

        # 2. 调仓周期对夏普比率的影响
        rebalance_sharpe = results_df.groupby('rebalance_freq')['sharpe_ratio'].agg(['mean', 'std', 'max'])
        axes[0,1].errorbar(rebalance_sharpe.index, rebalance_sharpe['mean'],
                          yerr=rebalance_sharpe['std'], marker='s', capsize=5)
        axes[0,1].plot(rebalance_sharpe.index, rebalance_sharpe['max'], 'r--', alpha=0.7, label='最大值')
        axes[0,1].set_title('调仓周期 vs 夏普比率')
        axes[0,1].set_xlabel('调仓周期 (天)')
        axes[0,1].set_ylabel('夏普比率')
        axes[0,1].legend()
        axes[0,1].grid(True, alpha=0.3)

        # 3. 动量回看天数对总收益率的影响
        momentum_return = results_df.groupby('momentum_window')['total_return'].agg(['mean', 'std', 'max'])
        axes[1,0].errorbar(momentum_return.index, momentum_return['mean'],
                          yerr=momentum_return['std'], marker='o', capsize=5)
        axes[1,0].plot(momentum_return.index, momentum_return['max'], 'r--', alpha=0.7, label='最大值')
        axes[1,0].set_title('动量回看天数 vs 总收益率')
        axes[1,0].set_xlabel('动量回看天数')
        axes[1,0].set_ylabel('总收益率 (%)')
        axes[1,0].legend()
        axes[1,0].grid(True, alpha=0.3)

        # 4. 调仓周期对总收益率的影响
        rebalance_return = results_df.groupby('rebalance_freq')['total_return'].agg(['mean', 'std', 'max'])
        axes[1,1].errorbar(rebalance_return.index, rebalance_return['mean'],
                          yerr=rebalance_return['std'], marker='s', capsize=5)
        axes[1,1].plot(rebalance_return.index, rebalance_return['max'], 'r--', alpha=0.7, label='最大值')
        axes[1,1].set_title('调仓周期 vs 总收益率')
        axes[1,1].set_xlabel('调仓周期 (天)')
        axes[1,1].set_ylabel('总收益率 (%)')
        axes[1,1].legend()
        axes[1,1].grid(True, alpha=0.3)

        plt.tight_layout()

        # 保存图表
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        filename = f"parameter_analysis_{timestamp}.png"
        plt.savefig(filename, dpi=300, bbox_inches='tight')
        print(f"📊 参数分析图已保存至: {filename}")

        plt.show()

# 从之前的脚本导入数据获取功能
from hstech_hshdyi_rotation_strategy import RotationStrategyDataFetcher

def main():
    """主函数"""
    print("🔍 恒生科技指数 vs 恒生高股息指数动量策略参数优化")
    print("="*80)

    # 1. 获取数据
    fetcher = RotationStrategyDataFetcher()
    if not fetcher.get_all_data():
        print("❌ 数据获取失败，程序退出")
        return

    # 2. 运行参数优化
    optimizer = MomentumStrategyOptimizer(fetcher.hstech_data, fetcher.hshdyi_data)
    optimizer.run_optimization()

    # 3. 分析结果
    results_df, best_sharpe, best_return, best_drawdown = optimizer.analyze_results()

    # 4. 绘制结果
    optimizer.plot_optimization_results(results_df)
    optimizer.plot_parameter_analysis(results_df)

    # 5. 输出最终建议
    print("\n" + "="*100)
    print("🎯 最终参数建议")
    print("="*100)
    print(f"💡 综合考虑收益率和风险，推荐参数组合：")
    print(f"   动量回看天数: {best_sharpe['momentum_window']}天")
    print(f"   调仓周期: {best_sharpe['rebalance_freq']}天")
    print(f"   预期年化收益率: {best_sharpe['annual_return']:.2f}%")
    print(f"   预期夏普比率: {best_sharpe['sharpe_ratio']:.3f}")
    print(f"   预期最大回撤: {best_sharpe['max_drawdown']:.2f}%")

    print("\n🎉 参数优化完成！")
    print("📊 请查看生成的图表文件了解详细分析结果")

if __name__ == "__main__":
    main()
