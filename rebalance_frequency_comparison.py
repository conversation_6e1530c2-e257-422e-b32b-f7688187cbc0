#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
调仓频率对比分析：月度调仓 vs 季度调仓
比较毛利润增长加速度因子在不同调仓频率下的表现
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import os

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_backtest_results():
    """加载回测结果"""
    monthly_data = pd.read_csv('gross_profit_quintile_backtest_results/gross_profit_quintile_backtest_data_月度.csv')
    quarterly_data = pd.read_csv('gross_profit_quintile_backtest_results/gross_profit_quintile_backtest_data_季度.csv')
    
    # 转换日期格式
    monthly_data['rebalance_date'] = pd.to_datetime(monthly_data['rebalance_date'])
    quarterly_data['rebalance_date'] = pd.to_datetime(quarterly_data['rebalance_date'])
    
    return monthly_data, quarterly_data

def calculate_performance_metrics(data, freq_name):
    """计算绩效指标"""
    ls_returns = data['long_short_return'].dropna()
    
    if len(ls_returns) == 0:
        return {}
    
    # 基本统计
    metrics = {
        '调仓频率': freq_name,
        '调仓次数': len(ls_returns),
        '平均收益(%)': ls_returns.mean(),
        '累计收益(%)': (1 + ls_returns / 100).prod() - 1,
        '胜率(%)': (ls_returns > 0).mean() * 100,
        '最大单期收益(%)': ls_returns.max(),
        '最大单期亏损(%)': ls_returns.min(),
        '收益标准差(%)': ls_returns.std()
    }
    
    # 年化指标
    if freq_name == '月度':
        periods_per_year = 12
    else:
        periods_per_year = 4
    
    total_periods = len(ls_returns)
    years = total_periods / periods_per_year
    
    cumulative_return = (1 + ls_returns / 100).prod()
    annualized_return = (cumulative_return ** (1/years) - 1) * 100
    annualized_vol = ls_returns.std() * np.sqrt(periods_per_year)
    
    metrics.update({
        '年化收益(%)': annualized_return,
        '年化波动(%)': annualized_vol,
        '夏普比率': annualized_return / annualized_vol if annualized_vol > 0 else 0
    })
    
    return metrics

def analyze_quintile_performance(data, freq_name):
    """分析各分组表现"""
    quintile_stats = {}
    
    for quintile in ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']:
        col = f'{quintile}_return'
        if col in data.columns:
            returns = data[col].dropna()
            quintile_stats[quintile] = {
                '平均收益(%)': returns.mean(),
                '收益标准差(%)': returns.std(),
                '胜率(%)': (returns > 0).mean() * 100
            }
    
    return quintile_stats

def create_comparison_visualizations(monthly_data, quarterly_data):
    """创建对比可视化图表"""
    print("📊 创建对比可视化图表...")
    
    output_dir = "rebalance_frequency_comparison_results"
    os.makedirs(output_dir, exist_ok=True)
    
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    
    # 1. 多空收益累计曲线对比
    ax1 = axes[0, 0]
    
    # 月度调仓累计收益
    monthly_ls = monthly_data['long_short_return'].dropna()
    monthly_cumulative = (1 + monthly_ls / 100).cumprod()
    ax1.plot(monthly_data['rebalance_date'][:len(monthly_cumulative)], 
             monthly_cumulative, linewidth=2, label='月度调仓', color='blue', marker='o', markersize=3)
    
    # 季度调仓累计收益
    quarterly_ls = quarterly_data['long_short_return'].dropna()
    quarterly_cumulative = (1 + quarterly_ls / 100).cumprod()
    ax1.plot(quarterly_data['rebalance_date'][:len(quarterly_cumulative)], 
             quarterly_cumulative, linewidth=2, label='季度调仓', color='red', marker='s', markersize=4)
    
    ax1.set_title('多空组合累计收益对比 (Q1-Q5)')
    ax1.set_xlabel('日期')
    ax1.set_ylabel('累计收益倍数')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 收益分布对比
    ax2 = axes[0, 1]
    
    ax2.hist(monthly_ls, bins=20, alpha=0.6, label='月度调仓', color='blue', density=True)
    ax2.hist(quarterly_ls, bins=15, alpha=0.6, label='季度调仓', color='red', density=True)
    ax2.axvline(x=monthly_ls.mean(), color='blue', linestyle='--', alpha=0.8)
    ax2.axvline(x=quarterly_ls.mean(), color='red', linestyle='--', alpha=0.8)
    
    ax2.set_title('多空收益分布对比')
    ax2.set_xlabel('收益率 (%)')
    ax2.set_ylabel('密度')
    ax2.legend()
    ax2.grid(True, alpha=0.3)
    
    # 3. 各组平均收益对比
    ax3 = axes[1, 0]
    
    quintiles = ['Q1', 'Q2', 'Q3', 'Q4', 'Q5']
    monthly_means = [monthly_data[f'{q}_return'].mean() for q in quintiles]
    quarterly_means = [quarterly_data[f'{q}_return'].mean() for q in quintiles]
    
    x = np.arange(len(quintiles))
    width = 0.35
    
    bars1 = ax3.bar(x - width/2, monthly_means, width, label='月度调仓', alpha=0.7, color='blue')
    bars2 = ax3.bar(x + width/2, quarterly_means, width, label='季度调仓', alpha=0.7, color='red')
    
    # 标注数值
    for bars in [bars1, bars2]:
        for bar in bars:
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height,
                    f'{height:.1f}%', ha='center', va='bottom', fontsize=8)
    
    ax3.set_title('各组平均收益率对比')
    ax3.set_xlabel('分组')
    ax3.set_ylabel('平均收益率 (%)')
    ax3.set_xticks(x)
    ax3.set_xticklabels(quintiles)
    ax3.legend()
    ax3.grid(True, alpha=0.3)
    
    # 4. 滚动夏普比率对比
    ax4 = axes[1, 1]
    
    # 计算滚动夏普比率（12期窗口）
    window = 12
    
    def rolling_sharpe(returns, window, periods_per_year):
        rolling_mean = returns.rolling(window).mean()
        rolling_std = returns.rolling(window).std()
        return (rolling_mean / rolling_std) * np.sqrt(periods_per_year)
    
    monthly_sharpe = rolling_sharpe(monthly_ls, window, 12)
    quarterly_sharpe = rolling_sharpe(quarterly_ls, window//3, 4)  # 调整窗口大小
    
    ax4.plot(monthly_data['rebalance_date'][:len(monthly_sharpe)], 
             monthly_sharpe, linewidth=2, label='月度调仓', color='blue')
    ax4.plot(quarterly_data['rebalance_date'][:len(quarterly_sharpe)], 
             quarterly_sharpe, linewidth=2, label='季度调仓', color='red')
    
    ax4.axhline(y=0, color='black', linestyle='-', alpha=0.3)
    ax4.set_title('滚动夏普比率对比')
    ax4.set_xlabel('日期')
    ax4.set_ylabel('夏普比率')
    ax4.legend()
    ax4.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig(f'{output_dir}/rebalance_frequency_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 对比图表已保存到 {output_dir}/rebalance_frequency_comparison.png")

def main():
    """主函数"""
    print("🚀 开始调仓频率对比分析...")
    print("="*80)
    
    # 加载数据
    monthly_data, quarterly_data = load_backtest_results()
    
    # 计算绩效指标
    monthly_metrics = calculate_performance_metrics(monthly_data, '月度')
    quarterly_metrics = calculate_performance_metrics(quarterly_data, '季度')
    
    # 打印对比结果
    print("📊 调仓频率绩效对比")
    print("="*80)
    
    comparison_df = pd.DataFrame([monthly_metrics, quarterly_metrics])
    print(comparison_df.round(2).to_string(index=False))
    
    # 分析各组表现
    print("\n📊 各分组表现对比")
    print("="*80)
    
    monthly_quintiles = analyze_quintile_performance(monthly_data, '月度')
    quarterly_quintiles = analyze_quintile_performance(quarterly_data, '季度')
    
    print("\n月度调仓各组表现:")
    for quintile, stats in monthly_quintiles.items():
        print(f"  {quintile}组: 平均收益 {stats['平均收益(%)']:.2f}%, "
              f"波动 {stats['收益标准差(%)']:.2f}%, 胜率 {stats['胜率(%)']:.1f}%")
    
    print("\n季度调仓各组表现:")
    for quintile, stats in quarterly_quintiles.items():
        print(f"  {quintile}组: 平均收益 {stats['平均收益(%)']:.2f}%, "
              f"波动 {stats['收益标准差(%)']:.2f}%, 胜率 {stats['胜率(%)']:.1f}%")
    
    # 创建可视化
    create_comparison_visualizations(monthly_data, quarterly_data)
    
    # 保存对比结果
    output_dir = "rebalance_frequency_comparison_results"
    os.makedirs(output_dir, exist_ok=True)
    comparison_df.to_csv(f'{output_dir}/performance_comparison.csv', index=False)
    
    print(f"\n✅ 对比结果已保存到 {output_dir}/performance_comparison.csv")
    print("🎉 调仓频率对比分析完成！")

if __name__ == "__main__":
    main()
