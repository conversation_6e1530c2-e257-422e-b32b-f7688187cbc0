#!/usr/bin/env python
# -*- coding: utf-8 -*-

import pypandoc
import os

def main():
    # 检查输入文件是否存在
    if not os.path.exists('巴菲特致股东信合集.md'):
        print('Error: 巴菲特致股东信合集.md not found')
        return
    
    # 设置转换选项
    extra_args = [
        '--pdf-engine=xelatex',
        '-V', 'mainfont=SimSun',
        '-V', 'geometry:margin=1in'
    ]
    
    # 转换为PDF
    print('Converting Markdown to PDF...')
    pypandoc.convert_file(
        '巴菲特致股东信合集.md',
        'pdf',
        outputfile='巴菲特致股东信合集.pdf',
        extra_args=extra_args
    )
    
    print('PDF file created: 巴菲特致股东信合集.pdf')

if __name__ == '__main__':
    main()
