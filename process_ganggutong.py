#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Process ganggutong.csv to extract code, name, and industry information
"""

import pandas as pd
import sys

def process_ganggutong_csv(input_file, output_file):
    """
    Process the ganggutong.csv file to extract only code, name, and industry columns
    
    Args:
        input_file (str): Path to input CSV file
        output_file (str): Path to output CSV file
    """
    try:
        # Read the CSV file with different encoding options
        print(f"Reading {input_file}...")

        # Try different encodings
        encodings = ['utf-8', 'gbk', 'gb2312', 'utf-16', 'latin-1']
        df = None

        for encoding in encodings:
            try:
                print(f"Trying encoding: {encoding}")
                df = pd.read_csv(input_file, encoding=encoding, sep='\t')
                print(f"Successfully read with encoding: {encoding}")
                break
            except UnicodeDecodeError:
                continue

        if df is None:
            print("Failed to read file with any encoding")
            return False
        
        # Display the columns to understand the structure
        print("Columns in the input file:")
        print(df.columns.tolist())
        
        # Extract the required columns: 代码, 名称, 所属行业
        # Based on the file structure, these should be columns 1, 2, and the last column
        required_columns = ['代码', '名称', '所属行业']
        
        # Check if all required columns exist
        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"Missing columns: {missing_columns}")
            print("Available columns:", df.columns.tolist())
            return False
        
        # Select only the required columns
        result_df = df[required_columns].copy()

        # Remove any rows with missing data in these columns
        result_df = result_df.dropna()

        # Format stock codes - pad with zeros to make them 5 digits
        result_df['代码'] = result_df['代码'].astype(str).str.zfill(5)

        # Sort by code for consistency
        result_df = result_df.sort_values('代码')

        # Reset index
        result_df = result_df.reset_index(drop=True)
        
        print(f"Processed {len(result_df)} records")
        print("Sample of processed data:")
        print(result_df.head(10))
        
        # Save to output file
        result_df.to_csv(output_file, index=False, encoding='utf-8')
        print(f"Results saved to {output_file}")
        
        return True
        
    except Exception as e:
        print(f"Error processing file: {e}")
        return False

def main():
    input_file = "ganggutong.csv"
    output_file = "ganggutong_processed.csv"
    
    success = process_ganggutong_csv(input_file, output_file)
    
    if success:
        print("Processing completed successfully!")
    else:
        print("Processing failed!")
        sys.exit(1)

if __name__ == "__main__":
    main()
