#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
动量因子回测系统
基于标普500成分股价格动量进行因子回测
"""

import pandas as pd
import numpy as np
import yfinance as yf
import os
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import warnings
warnings.filterwarnings('ignore')

class MomentumFactorBacktest:
    """动量因子回测器"""
    
    def __init__(self, data_dir='sp500_price_cache'):
        self.data_dir = data_dir
        self.price_data = {}
        self.momentum_data = {}
        self.factor_returns = {}
        self.benchmark_returns = None
        
    def load_price_data(self):
        """加载所有股票的价格数据"""
        print("📊 加载价格数据...")

        # 从缓存目录加载价格数据
        if os.path.exists(self.data_dir):
            pkl_files = [f for f in os.listdir(self.data_dir) if f.endswith('_price.pkl')]
            print(f"   发现 {len(pkl_files)} 个缓存文件")

            success_count = 0
            for file in pkl_files:  # 加载所有可用文件
                symbol = file.replace('_price.pkl', '')
                file_path = os.path.join(self.data_dir, file)

                try:
                    # 使用pickle加载缓存数据
                    import pickle
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)

                    # 处理不同的数据格式
                    if isinstance(data, dict) and 'price_data' in data:
                        # 新格式：字典包含price_data键
                        price_series = data['price_data']
                        if isinstance(price_series, pd.Series) and len(price_series) > 500:
                            # 确保索引是datetime类型
                            if not isinstance(price_series.index, pd.DatetimeIndex):
                                try:
                                    price_series.index = pd.to_datetime(price_series.index)
                                except:
                                    continue
                            self.price_data[symbol] = price_series.dropna()
                            success_count += 1
                    elif isinstance(data, pd.Series):
                        # 旧格式：直接是Series
                        data = data.dropna()
                        if len(data) > 500:  # 至少需要2年的数据
                            # 确保索引是datetime类型
                            if not isinstance(data.index, pd.DatetimeIndex):
                                try:
                                    data.index = pd.to_datetime(data.index)
                                except:
                                    continue
                            self.price_data[symbol] = data
                            success_count += 1
                    elif isinstance(data, pd.DataFrame):
                        # DataFrame格式
                        if 'Adj Close' in data.columns:
                            series = data['Adj Close'].dropna()
                            if len(series) > 500:
                                try:
                                    if not isinstance(series.index, pd.DatetimeIndex):
                                        series.index = pd.to_datetime(series.index)
                                    self.price_data[symbol] = series
                                    success_count += 1
                                except:
                                    continue
                        elif 'Close' in data.columns:
                            # 如果没有Adj Close，使用Close列
                            series = data['Close'].dropna()
                            if len(series) > 500:
                                try:
                                    if not isinstance(series.index, pd.DatetimeIndex):
                                        series.index = pd.to_datetime(series.index)
                                    self.price_data[symbol] = series
                                    success_count += 1
                                except:
                                    continue

                except Exception as e:
                    print(f"   ⚠️ 加载 {symbol} 失败: {e}")
                    continue

                # 显示进度
                if success_count % 50 == 0 and success_count > 0:
                    print(f"   已加载 {success_count} 只股票...")

        print(f"✅ 成功加载 {len(self.price_data)} 只股票的价格数据")
        
    def get_price_data_yfinance(self, symbols, start_date='2020-01-01', end_date='2025-06-01'):
        """使用yfinance获取股票价格数据"""
        print("📈 使用yfinance获取股票价格数据...")
        
        # 分批获取价格数据，避免API限制
        batch_size = 50
        all_price_data = {}
        
        for i in range(0, len(symbols), batch_size):
            batch_symbols = symbols[i:i+batch_size]
            print(f"   获取第 {i//batch_size + 1} 批: {len(batch_symbols)} 只股票")
            
            try:
                # 使用yfinance批量获取数据
                tickers = ' '.join(batch_symbols)
                data = yf.download(tickers, start=start_date, end=end_date, 
                                 progress=False, group_by='ticker')
                
                if len(batch_symbols) == 1:
                    # 单只股票的情况
                    symbol = batch_symbols[0]
                    if 'Adj Close' in data.columns:
                        all_price_data[symbol] = data['Adj Close']
                else:
                    # 多只股票的情况
                    for symbol in batch_symbols:
                        try:
                            if symbol in data.columns.levels[0]:
                                adj_close = data[symbol]['Adj Close']
                                if not adj_close.empty:
                                    all_price_data[symbol] = adj_close
                        except:
                            continue
                            
            except Exception as e:
                print(f"   ❌ 批次 {i//batch_size + 1} 获取失败: {e}")
                continue
        
        self.price_data = all_price_data
        print(f"✅ 成功获取 {len(self.price_data)} 只股票的价格数据")
        
    def calculate_momentum_factors(self, lookback_periods=[20, 60, 120, 252]):
        """计算动量因子"""
        print("🔄 计算动量因子...")
        
        momentum_data = {}
        
        for symbol, prices in self.price_data.items():
            if len(prices) < max(lookback_periods) + 10:
                continue
                
            symbol_momentum = pd.DataFrame(index=prices.index)
            
            # 计算不同周期的动量因子
            for period in lookback_periods:
                # 价格动量 = (当前价格 - N日前价格) / N日前价格
                momentum_name = f'momentum_{period}d'
                symbol_momentum[momentum_name] = prices.pct_change(period)
                
                # 移动平均动量
                ma_momentum_name = f'ma_momentum_{period}d'
                ma_price = prices.rolling(window=period).mean()
                symbol_momentum[ma_momentum_name] = (prices - ma_price) / ma_price
                
            # 计算复合动量因子 (多周期加权平均)
            # 权重: 短期30%, 中期40%, 长期30%
            if all(f'momentum_{p}d' in symbol_momentum.columns for p in [20, 60, 252]):
                symbol_momentum['composite_momentum'] = (
                    0.3 * symbol_momentum['momentum_20d'] +
                    0.4 * symbol_momentum['momentum_60d'] +
                    0.3 * symbol_momentum['momentum_252d']
                )
            
            momentum_data[symbol] = symbol_momentum
        
        self.momentum_data = momentum_data
        print(f"✅ 成功计算 {len(momentum_data)} 只股票的动量因子")
        
    def get_benchmark_data(self, benchmark='^GSPC', start_date='2020-01-01', end_date='2025-06-01'):
        """获取基准指数数据"""
        print("📊 获取基准指数数据...")
        
        try:
            benchmark_data = yf.download(benchmark, start=start_date, end=end_date, progress=False)
            self.benchmark_returns = benchmark_data['Adj Close'].pct_change().dropna()
            print(f"✅ 成功获取基准指数数据: {len(self.benchmark_returns)} 个数据点")
        except Exception as e:
            print(f"❌ 获取基准指数失败: {e}")
    
    def create_factor_portfolios(self, factor_name='composite_momentum', rebalance_freq='M', n_groups=5):
        """创建因子投资组合"""
        print(f"🔄 创建 {n_groups} 分组动量因子投资组合 (因子: {factor_name}, 重新平衡频率: {rebalance_freq})...")
        
        # 获取所有股票的共同日期范围
        common_symbols = set(self.momentum_data.keys()) & set(self.price_data.keys())
        print(f"   共同股票数量: {len(common_symbols)}")
        
        if len(common_symbols) < 50:
            print("❌ 可用股票数量太少，无法进行有效回测")
            return
        
        # 创建动量因子数据矩阵
        momentum_matrix = pd.DataFrame()
        for symbol in common_symbols:
            if factor_name in self.momentum_data[symbol].columns:
                momentum_matrix[symbol] = self.momentum_data[symbol][factor_name]
        
        # 创建价格数据矩阵
        price_matrix = pd.DataFrame()
        for symbol in common_symbols:
            price_matrix[symbol] = self.price_data[symbol]
        
        # 统一时区处理
        print("   处理时区和日期索引...")

        # 移除时区信息，统一为UTC
        for symbol in list(momentum_matrix.columns):
            if momentum_matrix[symbol].index.tz is not None:
                momentum_matrix[symbol].index = momentum_matrix[symbol].index.tz_convert('UTC').tz_localize(None)

        for symbol in list(price_matrix.columns):
            if price_matrix[symbol].index.tz is not None:
                price_matrix[symbol].index = price_matrix[symbol].index.tz_convert('UTC').tz_localize(None)

        # 重建矩阵
        momentum_matrix = pd.DataFrame(momentum_matrix)
        price_matrix = pd.DataFrame(price_matrix)

        # 对齐时间索引
        common_dates = momentum_matrix.index.intersection(price_matrix.index)
        momentum_matrix = momentum_matrix.loc[common_dates]
        price_matrix = price_matrix.loc[common_dates]

        # 计算收益率
        returns_matrix = price_matrix.pct_change().dropna()

        # 重新平衡日期
        if rebalance_freq == 'M':
            rebalance_dates = momentum_matrix.resample('M').last().index
        elif rebalance_freq == 'Q':
            rebalance_dates = momentum_matrix.resample('Q').last().index
        else:
            rebalance_dates = momentum_matrix.resample('Y').last().index
        
        # 初始化组合收益率
        portfolio_returns = {f'Group_{i+1}': [] for i in range(n_groups)}
        portfolio_returns['Long_Short'] = []
        
        print(f"   重新平衡次数: {len(rebalance_dates)}")
        
        for i, rebal_date in enumerate(rebalance_dates[:-1]):
            next_rebal_date = rebalance_dates[i+1]

            # 获取重新平衡日的动量因子数据 - 使用最近可用日期
            try:
                current_momentum = momentum_matrix.loc[rebal_date].dropna()
            except KeyError:
                # 如果确切日期不存在，找最近的可用日期
                available_dates = momentum_matrix.index[momentum_matrix.index <= rebal_date]
                if len(available_dates) == 0:
                    continue
                nearest_date = available_dates[-1]
                current_momentum = momentum_matrix.loc[nearest_date].dropna()
            
            if len(current_momentum) < n_groups * 5:  # 确保每组至少有5只股票
                continue
            
            # 按动量因子排序并分组 (动量高的排在前面)
            sorted_momentum = current_momentum.sort_values(ascending=False)
            group_size = len(sorted_momentum) // n_groups
            
            groups = {}
            for j in range(n_groups):
                start_idx = j * group_size
                if j == n_groups - 1:  # 最后一组包含剩余所有股票
                    end_idx = len(sorted_momentum)
                else:
                    end_idx = (j + 1) * group_size
                
                groups[f'Group_{j+1}'] = sorted_momentum.iloc[start_idx:end_idx].index.tolist()
            
            # 计算各组合在下一个重新平衡期间的收益率
            period_returns = returns_matrix.loc[rebal_date:next_rebal_date]
            
            for group_name, stocks in groups.items():
                # 等权重投资组合收益率
                available_stocks = [s for s in stocks if s in period_returns.columns]
                if available_stocks:
                    group_returns = period_returns[available_stocks].mean(axis=1)
                    portfolio_returns[group_name].extend(group_returns.tolist())
            
            # 多空组合 (高动量组 - 低动量组)
            high_momentum_stocks = groups['Group_1']
            low_momentum_stocks = groups[f'Group_{n_groups}']
            
            high_available = [s for s in high_momentum_stocks if s in period_returns.columns]
            low_available = [s for s in low_momentum_stocks if s in period_returns.columns]
            
            if high_available and low_available:
                high_returns = period_returns[high_available].mean(axis=1)
                low_returns = period_returns[low_available].mean(axis=1)
                long_short_returns = high_returns - low_returns
                portfolio_returns['Long_Short'].extend(long_short_returns.tolist())
        
        # 转换为DataFrame
        max_length = max(len(returns) for returns in portfolio_returns.values())
        for key in portfolio_returns:
            current_length = len(portfolio_returns[key])
            if current_length < max_length:
                portfolio_returns[key].extend([np.nan] * (max_length - current_length))
        
        self.factor_returns = pd.DataFrame(portfolio_returns)
        print(f"✅ 因子组合创建完成，数据长度: {len(self.factor_returns)}")

    def calculate_performance_metrics(self):
        """计算绩效指标"""
        print("📊 计算绩效指标...")

        metrics = {}

        for col in self.factor_returns.columns:
            returns = self.factor_returns[col].dropna()

            if len(returns) == 0:
                continue

            # 基本统计
            total_return = (1 + returns).prod() - 1
            annual_return = (1 + total_return) ** (252 / len(returns)) - 1
            annual_vol = returns.std() * np.sqrt(252)
            sharpe_ratio = annual_return / annual_vol if annual_vol > 0 else 0

            # 最大回撤
            cumulative = (1 + returns).cumprod()
            rolling_max = cumulative.expanding().max()
            drawdown = (cumulative - rolling_max) / rolling_max
            max_drawdown = drawdown.min()

            # 胜率
            win_rate = (returns > 0).mean()

            metrics[col] = {
                'Total_Return': total_return * 100,
                'Annual_Return': annual_return * 100,
                'Annual_Volatility': annual_vol * 100,
                'Sharpe_Ratio': sharpe_ratio,
                'Max_Drawdown': max_drawdown * 100,
                'Win_Rate': win_rate * 100,
                'Observations': len(returns)
            }

        return pd.DataFrame(metrics).T

    def plot_cumulative_returns(self, save_path='momentum_factor_backtest_results.png'):
        """绘制累积收益率图"""
        print("📈 绘制累积收益率图...")

        fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12))

        # 计算累积收益率
        cumulative_returns = (1 + self.factor_returns.fillna(0)).cumprod()

        # 第一个子图：所有分组
        colors = ['red', 'orange', 'green', 'blue', 'purple']
        for i, col in enumerate([c for c in cumulative_returns.columns if c.startswith('Group_')]):
            ax1.plot(cumulative_returns.index, cumulative_returns[col],
                    color=colors[i % len(colors)], label=col, linewidth=2)

        # 添加基准
        if self.benchmark_returns is not None:
            benchmark_cum = (1 + self.benchmark_returns.fillna(0)).cumprod()
            # 对齐时间索引
            aligned_benchmark = benchmark_cum.reindex(cumulative_returns.index, method='ffill')
            ax1.plot(cumulative_returns.index, aligned_benchmark,
                    color='black', label='S&P 500', linewidth=2, linestyle='--')

        ax1.set_title('Momentum Factor - Group Performance', fontsize=14, fontweight='bold')
        ax1.set_ylabel('Cumulative Return', fontsize=12)
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 第二个子图：多空组合
        if 'Long_Short' in cumulative_returns.columns:
            ax2.plot(cumulative_returns.index, cumulative_returns['Long_Short'],
                    color='darkred', label='Long-Short (High - Low Momentum)', linewidth=2)
            ax2.axhline(y=1, color='gray', linestyle='-', alpha=0.5)

        ax2.set_title('Momentum Factor - Long-Short Performance', fontsize=14, fontweight='bold')
        ax2.set_ylabel('Cumulative Return', fontsize=12)
        ax2.set_xlabel('Date', fontsize=12)
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.show()
        print(f"📊 图表已保存到: {save_path}")

    def generate_backtest_report(self, output_file='momentum_factor_report.txt'):
        """生成回测报告"""
        print("📋 生成回测报告...")

        metrics_df = self.calculate_performance_metrics()

        with open(output_file, 'w', encoding='utf-8') as f:
            f.write("动量因子回测报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"回测期间: 2020年6月 至 2025年6月\n")
            f.write(f"股票池: 标普500成分股\n")
            f.write(f"重新平衡频率: 月度\n")
            f.write(f"分组数量: 5组\n")
            f.write(f"动量因子: 复合动量 (20日30% + 60日40% + 252日30%)\n\n")

            f.write("📊 绩效指标汇总:\n")
            f.write("-" * 80 + "\n")
            f.write(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}\n")
            f.write("-" * 80 + "\n")

            for idx, row in metrics_df.iterrows():
                f.write(f"{idx:>12} "
                       f"{row['Total_Return']:>9.2f}% "
                       f"{row['Annual_Return']:>9.2f}% "
                       f"{row['Annual_Volatility']:>9.2f}% "
                       f"{row['Sharpe_Ratio']:>9.2f} "
                       f"{row['Max_Drawdown']:>9.2f}% "
                       f"{row['Win_Rate']:>7.1f}%\n")

            f.write("\n📈 因子有效性分析:\n")
            f.write("-" * 50 + "\n")

            # 分析因子单调性
            group_returns = [metrics_df.loc[f'Group_{i}', 'Annual_Return'] for i in range(1, 6)
                           if f'Group_{i}' in metrics_df.index]

            if len(group_returns) >= 5:
                monotonic = all(group_returns[i] >= group_returns[i+1] for i in range(len(group_returns)-1))
                f.write(f"单调性检验: {'通过' if monotonic else '未通过'}\n")
                f.write(f"高低分组收益差: {group_returns[0] - group_returns[-1]:.2f}%\n")

            if 'Long_Short' in metrics_df.index:
                ls_return = metrics_df.loc['Long_Short', 'Annual_Return']
                ls_sharpe = metrics_df.loc['Long_Short', 'Sharpe_Ratio']
                f.write(f"多空组合年化收益: {ls_return:.2f}%\n")
                f.write(f"多空组合夏普比率: {ls_sharpe:.2f}\n")

                # 因子显著性
                if ls_return > 5 and ls_sharpe > 0.8:
                    f.write("因子有效性: 强\n")
                elif ls_return > 2 and ls_sharpe > 0.4:
                    f.write("因子有效性: 中等\n")
                else:
                    f.write("因子有效性: 弱\n")

        print(f"📋 回测报告已保存到: {output_file}")
        return metrics_df

def main():
    """主函数"""
    print("🚀 动量因子回测系统")
    print("=" * 60)

    # 创建回测器
    backtest = MomentumFactorBacktest()

    # 1. 加载价格数据
    backtest.load_price_data()

    # 检查数据质量
    print(f"📊 数据质量检查:")
    if len(backtest.price_data) > 0:
        # 显示数据范围信息
        date_ranges = []
        for symbol, prices in list(backtest.price_data.items())[:5]:  # 显示前5只股票的信息
            start_date = prices.index.min().strftime('%Y-%m-%d')
            end_date = prices.index.max().strftime('%Y-%m-%d')
            date_ranges.append(f"{symbol}: {start_date} 至 {end_date} ({len(prices)} 天)")

        for info in date_ranges:
            print(f"   {info}")

    # 如果缓存数据不足，使用yfinance获取
    if len(backtest.price_data) < 50:
        print("⚠️ 缓存数据不足，使用yfinance获取数据...")
        # 标普500成分股示例列表（部分）
        sp500_symbols = [
            'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'NVDA', 'META', 'TSLA', 'BRK-B', 'UNH', 'JNJ',
            'JPM', 'V', 'PG', 'XOM', 'HD', 'CVX', 'MA', 'BAC', 'ABBV', 'PFE',
            'AVGO', 'KO', 'LLY', 'WMT', 'PEP', 'TMO', 'COST', 'DIS', 'ABT', 'DHR',
            'MRK', 'VZ', 'NFLX', 'ADBE', 'NKE', 'CRM', 'ACN', 'TXN', 'LIN', 'RTX',
            'QCOM', 'NEE', 'PM', 'HON', 'UPS', 'T', 'SPGI', 'LOW', 'INTU', 'IBM'
        ]
        backtest.get_price_data_yfinance(sp500_symbols)

    if len(backtest.price_data) < 20:
        print("❌ 价格数据不足，无法进行回测")
        return

    # 2. 计算动量因子
    backtest.calculate_momentum_factors()

    if len(backtest.momentum_data) < 20:
        print("❌ 动量因子计算失败")
        return

    # 3. 获取基准数据
    backtest.get_benchmark_data()

    # 4. 创建因子投资组合
    backtest.create_factor_portfolios()

    if backtest.factor_returns.empty:
        print("❌ 因子组合创建失败")
        return

    # 5. 计算绩效指标
    metrics_df = backtest.calculate_performance_metrics()

    # 6. 显示结果
    print("\n📊 回测结果:")
    print("=" * 80)
    print(f"{'组合':>12} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8}")
    print("-" * 80)

    for idx, row in metrics_df.iterrows():
        print(f"{idx:>12} "
              f"{row['Total_Return']:>9.2f}% "
              f"{row['Annual_Return']:>9.2f}% "
              f"{row['Annual_Volatility']:>9.2f}% "
              f"{row['Sharpe_Ratio']:>9.2f} "
              f"{row['Max_Drawdown']:>9.2f}% "
              f"{row['Win_Rate']:>7.1f}%")

    # 7. 生成图表和报告
    backtest.plot_cumulative_returns()
    backtest.generate_backtest_report()

    print(f"\n🎯 动量因子回测完成!")

if __name__ == "__main__":
    main()
