import yfinance as yf
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
from scipy import signal
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei']
plt.rcParams['axes.unicode_minus'] = False

def get_hsi_data():
    """获取恒生指数数据"""
    print("正在获取恒生指数数据...")

    try:
        # 使用yfinance获取恒生指数历史数据
        # ^HSI 是恒生指数在Yahoo Finance的代码
        hsi_ticker = yf.Ticker("^HSI")
        data = hsi_ticker.history(start="2000-01-01", end=datetime.now().strftime("%Y-%m-%d"))

        if data.empty:
            raise Exception("获取到的数据为空")

        # 处理数据格式 - yfinance返回的列名是英文的
        data = data.rename(columns={
            'Open': 'open',
            'Close': 'close',
            'High': 'high',
            'Low': 'low',
            'Volume': 'volume'
        })

        # 只保留需要的列
        data = data[['open', 'high', 'low', 'close', 'volume']]

        # 删除包含NaN的行
        data = data.dropna()

        print(f"数据获取完成，时间范围：{data.index[0].date()} 到 {data.index[-1].date()}")
        print(f"总数据点数：{len(data)}")

        return data

    except Exception as e:
        print(f"获取数据失败：{e}")
        return None

def hp_filter_detrend(series, lambda_param=1600):
    """HP滤波去趋势"""
    try:
        from scipy.sparse import diags
        from scipy.sparse.linalg import spsolve
    except ImportError:
        print("警告: scipy库未安装，使用简化的去趋势方法")
        return simple_detrend_fallback(series)

    n = len(series)
    if n < 4:
        return series, np.zeros(n)

    # 构建HP滤波的二阶差分矩阵
    # 创建单位矩阵
    I = np.eye(n)

    # 创建二阶差分矩阵D
    D = np.zeros((n-2, n))
    for i in range(n-2):
        D[i, i] = 1
        D[i, i+1] = -2
        D[i, i+2] = 1

    # HP滤波公式: (I + λD'D)τ = y
    # 其中τ是趋势，y是原始序列
    DTD = D.T @ D
    A = I + lambda_param * DTD

    # 求解趋势
    trend = np.linalg.solve(A, series.values)
    cycle = series.values - trend

    return pd.Series(trend, index=series.index), pd.Series(cycle, index=series.index)

def simple_detrend_fallback(series, window=252):
    """备用的简单去趋势方法"""
    trend = series.rolling(window=window, center=True).mean()
    trend = trend.interpolate(method='linear')
    trend = trend.fillna(method='bfill').fillna(method='ffill')
    cycle = series - trend
    return trend, cycle

def calculate_signals(data, lookback_days=20, buy_threshold=30, sell_threshold=80):
    """计算交易信号"""
    print("正在计算去趋势信号...")

    # HP滤波去趋势 - 针对恒生指数调整lambda参数
    print("开始HP滤波去趋势...")
    trend, cycle = hp_filter_detrend(data['close'], lambda_param=14400000)
    print("HP滤波去趋势完成")

    # 计算去趋势后的标准化指标
    cycle_std = cycle.rolling(lookback_days).std()
    cycle_normalized = cycle / cycle_std

    print(f"原始周期指标范围: {cycle.min():.2f} 到 {cycle.max():.2f}")
    print(f"原始周期指标均值: {cycle.mean():.2f}")
    print(f"标准化周期指标范围: {cycle_normalized.min():.2f} 到 {cycle_normalized.max():.2f}")
    print(f"标准化周期指标均值: {cycle_normalized.mean():.2f}")
    print(f"标准化周期指标标准差: {cycle_normalized.std():.2f}")

    # 检查是否有NaN值
    print(f"周期指标中的NaN数量: {cycle.isna().sum()}")
    print(f"标准化周期指标中的NaN数量: {cycle_normalized.isna().sum()}")

    # 计算移动平均作为参考
    ma_20 = data['close'].rolling(20).mean()
    ma_50 = data['close'].rolling(50).mean()
    ma_200 = data['close'].rolling(200).mean()

    # 生成交易信号
    signals = pd.DataFrame(index=data.index)
    signals['price'] = data['close']
    signals['trend'] = trend
    signals['cycle'] = cycle
    signals['cycle_normalized'] = cycle_normalized
    signals['ma_20'] = ma_20
    signals['ma_50'] = ma_50
    signals['ma_200'] = ma_200

    # 交易信号逻辑
    signals['position'] = 0
    signals['signal'] = 0

    for i in range(lookback_days, len(signals)):
        current_cycle = cycle_normalized.iloc[i]
        prev_position = signals['position'].iloc[i-1] if i > 0 else 0

        # 买入信号：去趋势指标低于买入阈值
        if current_cycle < buy_threshold and prev_position == 0:
            signals['position'].iloc[i] = 1
            signals['signal'].iloc[i] = 1  # 买入

        # 卖出信号：去趋势指标高于卖出阈值
        elif current_cycle > sell_threshold and prev_position == 1:
            signals['position'].iloc[i] = 0
            signals['signal'].iloc[i] = -1  # 卖出

        # 保持当前仓位
        else:
            signals['position'].iloc[i] = prev_position
            signals['signal'].iloc[i] = 0  # 无操作

    return signals

def backtest_strategy(signals, backtest_start_date=None):
    """回测策略"""
    print("正在进行策略回测...")

    # 计算收益率
    signals['returns'] = signals['price'].pct_change()

    # 策略收益（持仓时获得收益，空仓时收益为0）
    signals['strategy_returns'] = signals['position'].shift(1) * signals['returns']

    # 基准收益（买入持有）
    signals['benchmark_returns'] = signals['returns']

    # 累计收益
    signals['strategy_cumulative'] = (1 + signals['strategy_returns'].fillna(0)).cumprod()
    signals['benchmark_cumulative'] = (1 + signals['benchmark_returns'].fillna(0)).cumprod()

    # 如果指定了回测开始日期，则只保留该日期之后的数据用于绩效计算
    if backtest_start_date is not None:
        signals['backtest_period'] = signals.index >= backtest_start_date
    else:
        signals['backtest_period'] = True

    return signals

def calculate_performance_metrics(returns, backtest_period=None):
    """计算绩效指标"""
    # 如果指定了回测期间，则只计算该期间的绩效
    if backtest_period is not None:
        returns = returns[backtest_period]

    returns = returns.dropna()

    if len(returns) == 0:
        return {
            '总收益率': '0.00%',
            '年化收益率': '0.00%',
            '年化波动率': '0.00%',
            '夏普比率': '0.00',
            '最大回撤': '0.00%'
        }

    total_return = (1 + returns).prod() - 1
    annual_return = (1 + total_return) ** (252 / len(returns)) - 1
    volatility = returns.std() * np.sqrt(252)
    sharpe_ratio = annual_return / volatility if volatility > 0 else 0

    # 计算最大回撤
    cumulative = (1 + returns).cumprod()
    rolling_max = cumulative.expanding().max()
    drawdown = (cumulative - rolling_max) / rolling_max
    max_drawdown = drawdown.min()

    return {
        '总收益率': f"{total_return:.2%}",
        '年化收益率': f"{annual_return:.2%}",
        '年化波动率': f"{volatility:.2%}",
        '夏普比率': f"{sharpe_ratio:.2f}",
        '最大回撤': f"{max_drawdown:.2%}"
    }

def plot_results(signals):
    """绘制结果图表"""
    fig, axes = plt.subplots(4, 1, figsize=(15, 16))

    # 第一个子图：价格走势和趋势
    ax1 = axes[0]
    ax1.plot(signals.index, signals['price'], label='恒生指数', linewidth=1, alpha=0.8)
    ax1.plot(signals.index, signals['trend'], label='HP滤波趋势', linewidth=2, color='red')
    ax1.plot(signals.index, signals['ma_20'], label='MA20', alpha=0.7, color='orange')
    ax1.plot(signals.index, signals['ma_50'], label='MA50', alpha=0.7, color='green')
    ax1.plot(signals.index, signals['ma_200'], label='MA200', alpha=0.7, color='purple')

    # 标记买卖点
    buy_points = signals[signals['signal'] == 1]
    sell_points = signals[signals['signal'] == -1]

    ax1.scatter(buy_points.index, buy_points['price'], color='green', marker='^',
               s=100, label=f'买入信号({len(buy_points)}次)', zorder=5)
    ax1.scatter(sell_points.index, sell_points['price'], color='red', marker='v',
               s=100, label=f'卖出信号({len(sell_points)}次)', zorder=5)

    ax1.set_title('恒生指数价格走势与交易信号')
    ax1.set_ylabel('价格')
    ax1.legend()
    ax1.grid(True, alpha=0.3)

    # 第二个子图：去趋势周期
    ax2 = axes[1]
    ax2.plot(signals.index, signals['cycle'], label='去趋势周期', color='blue', alpha=0.7)
    ax2.plot(signals.index, signals['cycle_normalized'], label='标准化周期', color='purple', linewidth=2)
    ax2.axhline(y=-0.5, color='green', linestyle='--', alpha=0.7, label='买入阈值(-0.5)')
    ax2.axhline(y=0.5, color='red', linestyle='--', alpha=0.7, label='卖出阈值(0.5)')
    ax2.axhline(y=0, color='black', linestyle='-', alpha=0.3)

    ax2.set_title('去趋势周期指标')
    ax2.set_ylabel('周期值')
    ax2.legend()
    ax2.grid(True, alpha=0.3)

    # 第三个子图：仓位
    ax3 = axes[2]
    ax3.fill_between(signals.index, 0, signals['position'], alpha=0.3, color='blue', label='持仓状态')
    ax3.set_title('持仓状态')
    ax3.set_ylabel('仓位(0=空仓, 1=满仓)')
    ax3.legend()
    ax3.grid(True, alpha=0.3)

    # 第四个子图：累计收益对比
    ax4 = axes[3]
    ax4.plot(signals.index, signals['strategy_cumulative'], label='去趋势策略', linewidth=2, color='blue')
    ax4.plot(signals.index, signals['benchmark_cumulative'], label='买入持有', linewidth=2, color='red')
    ax4.set_title('累计收益对比')
    ax4.set_ylabel('累计收益')
    ax4.legend()
    ax4.grid(True, alpha=0.3)

    plt.tight_layout()
    plt.savefig('hsi_detrend_strategy.png', dpi=300, bbox_inches='tight')
    plt.show()

def optimize_parameters(data, backtest_start_date=None):
    """参数优化函数"""
    print("=== 开始参数优化 ===\n")

    # 定义参数搜索空间（减少组合数量）
    lambda_params = [7200000, 14400000, 28800000]  # HP滤波lambda参数
    threshold_pairs = [
        (-0.5, 0.5), (-0.8, 0.8), (-1.0, 1.0)
    ]  # (买入阈值, 卖出阈值)

    if backtest_start_date:
        print(f"回测期间：{backtest_start_date} 到 {data.index[-1].date()}")
        backtest_period = data.index >= backtest_start_date
    else:
        backtest_period = None

    best_sharpe = -999
    best_params = None
    results = []

    total_combinations = len(lambda_params) * len(threshold_pairs)
    current_combination = 0

    for lambda_param in lambda_params:
        for buy_threshold, sell_threshold in threshold_pairs:
            current_combination += 1
            print(f"测试参数组合 {current_combination}/{total_combinations}: "
                  f"lambda={lambda_param}, 阈值=({buy_threshold}, {sell_threshold})")

            try:
                # 计算信号（不打印调试信息）
                signals = calculate_signals_silent(data, lambda_param, 20, buy_threshold, sell_threshold)

                # 回测策略
                signals = backtest_strategy(signals, backtest_start_date)

                # 计算绩效指标（只计算回测期间）
                if backtest_start_date:
                    backtest_mask = signals.index >= backtest_start_date
                    strategy_returns = signals['strategy_returns'][backtest_mask].dropna()
                else:
                    strategy_returns = signals['strategy_returns'].dropna()

                if len(strategy_returns) == 0 or strategy_returns.std() == 0:
                    continue

                # 计算关键指标
                total_return = (1 + strategy_returns).prod() - 1
                annual_return = (1 + total_return) ** (252 / len(strategy_returns)) - 1
                volatility = strategy_returns.std() * np.sqrt(252)
                sharpe_ratio = annual_return / volatility if volatility > 0 else 0

                # 计算最大回撤
                cumulative = (1 + strategy_returns).cumprod()
                rolling_max = cumulative.expanding().max()
                drawdown = (cumulative - rolling_max) / rolling_max
                max_drawdown = drawdown.min()

                # 计算交易次数
                buy_signals = len(signals[signals['signal'] == 1])
                sell_signals = len(signals[signals['signal'] == -1])

                result = {
                    'lambda': lambda_param,
                    'buy_threshold': buy_threshold,
                    'sell_threshold': sell_threshold,
                    'annual_return': annual_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'total_return': total_return,
                    'buy_signals': buy_signals,
                    'sell_signals': sell_signals
                }

                results.append(result)

                # 更新最佳参数
                if sharpe_ratio > best_sharpe:
                    best_sharpe = sharpe_ratio
                    best_params = result

                print(f"  年化收益率: {annual_return:.2%}, 夏普比率: {sharpe_ratio:.3f}, "
                      f"最大回撤: {max_drawdown:.2%}, 交易次数: {buy_signals}")

            except Exception as e:
                print(f"  参数组合失败: {e}")
                continue

    # 输出优化结果
    print(f"\n=== 参数优化完成 ===")
    print(f"最佳参数组合:")
    print(f"  Lambda参数: {best_params['lambda']:,}")
    print(f"  买入阈值: {best_params['buy_threshold']}")
    print(f"  卖出阈值: {best_params['sell_threshold']}")
    print(f"  年化收益率: {best_params['annual_return']:.2%}")
    print(f"  夏普比率: {best_params['sharpe_ratio']:.3f}")
    print(f"  最大回撤: {best_params['max_drawdown']:.2%}")
    print(f"  交易次数: {best_params['buy_signals']}")

    # 保存优化结果
    results_df = pd.DataFrame(results)
    results_df.to_csv('parameter_optimization_results.csv', index=False, encoding='utf-8-sig')
    print(f"\n详细优化结果已保存至: parameter_optimization_results.csv")

    return best_params

def calculate_signals_silent(data, lambda_param, lookback_days, buy_threshold, sell_threshold):
    """计算交易信号（静默版本，向量化优化）"""
    # HP滤波去趋势
    trend, cycle = hp_filter_detrend(data['close'], lambda_param=lambda_param)

    # 计算去趋势后的标准化指标
    cycle_std = cycle.rolling(lookback_days).std()
    cycle_normalized = cycle / cycle_std

    # 生成交易信号（向量化）
    signals = pd.DataFrame(index=data.index)
    signals['price'] = data['close']
    signals['trend'] = trend
    signals['cycle'] = cycle
    signals['cycle_normalized'] = cycle_normalized

    # 计算移动平均作为参考
    signals['ma_20'] = data['close'].rolling(20).mean()
    signals['ma_50'] = data['close'].rolling(50).mean()
    signals['ma_200'] = data['close'].rolling(200).mean()

    # 向量化计算买卖信号
    buy_condition = cycle_normalized < buy_threshold
    sell_condition = cycle_normalized > sell_threshold

    # 初始化仓位和信号
    signals['position'] = 0
    signals['signal'] = 0

    # 简化的信号逻辑（向量化）
    position = 0
    for i in range(lookback_days, len(signals)):
        if not pd.isna(cycle_normalized.iloc[i]):
            if buy_condition.iloc[i] and position == 0:
                position = 1
                signals.iloc[i, signals.columns.get_loc('signal')] = 1
            elif sell_condition.iloc[i] and position == 1:
                position = 0
                signals.iloc[i, signals.columns.get_loc('signal')] = -1

            signals.iloc[i, signals.columns.get_loc('position')] = position

    return signals

def main():
    """主函数"""
    print("=== 恒生指数去趋势策略回测（最近5年）===\n")

    # 获取数据
    data = get_hsi_data()
    if data is None:
        print("数据获取失败，程序退出")
        return

    # 设置最近5年的回测开始日期
    from datetime import datetime, timedelta
    end_date = data.index[-1]
    backtest_start_date = end_date - timedelta(days=5*365)

    print(f"使用全部历史数据进行信号计算：{data.index[0].date()} 到 {data.index[-1].date()}")
    print(f"但只对最近5年进行回测分析：{backtest_start_date.date()} 到 {data.index[-1].date()}\n")

    # 参数优化（只对最近5年进行优化）
    best_params = optimize_parameters(data, backtest_start_date)

    print(f"\n=== 使用最佳参数进行详细回测 ===")

    # 使用最佳参数计算信号
    signals = calculate_signals_silent(
        data,
        best_params['lambda'],
        20,
        best_params['buy_threshold'],
        best_params['sell_threshold']
    )

    # 回测策略
    signals = backtest_strategy(signals, backtest_start_date)

    # 计算绩效指标（只计算最近5年）
    backtest_mask = signals.index >= backtest_start_date
    strategy_metrics = calculate_performance_metrics(signals['strategy_returns'], backtest_mask)
    benchmark_metrics = calculate_performance_metrics(signals['benchmark_returns'], backtest_mask)

    # 输出结果
    print(f"信号计算期间：{signals.index[0].date()} 到 {signals.index[-1].date()}")
    print(f"回测分析期间：{backtest_start_date.date()} 到 {signals.index[-1].date()}")
    print(f"回测交易天数：{len(signals[backtest_mask])}天")

    print("\n去趋势策略表现：")
    for key, value in strategy_metrics.items():
        print(f"  {key}: {value}")

    print("\n买入持有表现：")
    for key, value in benchmark_metrics.items():
        print(f"  {key}: {value}")

    # 策略统计（只统计最近5年）
    backtest_signals = signals[backtest_mask]
    buy_signals = len(backtest_signals[backtest_signals['signal'] == 1])
    sell_signals = len(backtest_signals[backtest_signals['signal'] == -1])
    holding_days = len(backtest_signals[backtest_signals['position'] == 1])
    holding_ratio = holding_days / len(backtest_signals)

    print(f"\n=== 策略统计（最近5年）===")
    print(f"买入次数：{buy_signals}次")
    print(f"卖出次数：{sell_signals}次")
    print(f"持有天数：{holding_days}天")
    print(f"持有比例：{holding_ratio:.1%}")

    # 绘制图表
    plot_results(signals)

    # 保存结果
    signals.to_csv('hsi_detrend_strategy_optimized_results.csv', encoding='utf-8-sig')
    print(f"\n详细结果已保存至：hsi_detrend_strategy_optimized_results.csv")
    print(f"图表已保存至：hsi_detrend_strategy.png")

if __name__ == "__main__":
    main()
