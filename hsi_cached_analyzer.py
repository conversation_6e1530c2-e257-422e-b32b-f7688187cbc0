#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股相对强度分析器（带数据缓存）
基于S&P 500分析器修改，适用于恒生指数成分股
"""

import pandas as pd
import numpy as np
import akshare as ak
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
from tqdm import tqdm
import pickle
import os
import time
from typing import List, Dict

warnings.filterwarnings('ignore')

class CachedHSIAnalyzer:
    """恒生指数成分股相对强度分析器（带缓存功能）"""
    
    def __init__(self, period: str = "1y", benchmark: str = "^HSI", cache_dir: str = "hsi_cache"):
        self.period = period
        self.benchmark = benchmark
        self.cache_dir = cache_dir
        self.symbols = []
        self.stock_data = {}
        self.benchmark_data = None
        self.relative_strength_data = {}
        self.analysis_results = pd.DataFrame()
        
        # Create cache directory
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
            print(f"📁 Created cache directory: {cache_dir}")
        
        print(f"🚀 恒生指数成分股相对强度分析器已初始化")
        print(f"📊 分析周期: {period}, 基准指数: {benchmark}")
        print(f"💾 缓存目录: {cache_dir}")
    
    def get_cache_filename(self, symbol: str) -> str:
        """Generate cache filename for a symbol"""
        return os.path.join(self.cache_dir, f"{symbol}_{self.period}.pkl")
    
    def get_cache_info_filename(self) -> str:
        """Generate cache info filename"""
        return os.path.join(self.cache_dir, f"cache_info_{self.period}.pkl")
    
    def is_cache_valid(self, cache_file: str, max_age_hours: int = 24) -> bool:
        """Check if cache file is valid (exists and not too old)"""
        if not os.path.exists(cache_file):
            return False
        
        # Check file age
        file_time = os.path.getmtime(cache_file)
        current_time = time.time()
        age_hours = (current_time - file_time) / 3600
        
        return age_hours < max_age_hours
    
    def save_to_cache(self, symbol: str, data: pd.Series) -> None:
        """Save data to cache"""
        try:
            cache_file = self.get_cache_filename(symbol)
            with open(cache_file, 'wb') as f:
                pickle.dump(data, f)
        except Exception as e:
            print(f"⚠️  Failed to save {symbol} to cache: {e}")
    
    def load_from_cache(self, symbol: str) -> pd.Series:
        """Load data from cache"""
        try:
            cache_file = self.get_cache_filename(symbol)
            if self.is_cache_valid(cache_file):
                with open(cache_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            print(f"⚠️  Failed to load {symbol} from cache: {e}")
        return None
    
    def save_cache_info(self, info: dict) -> None:
        """Save cache information"""
        try:
            cache_info_file = self.get_cache_info_filename()
            with open(cache_info_file, 'wb') as f:
                pickle.dump(info, f)
        except Exception as e:
            print(f"⚠️  Failed to save cache info: {e}")
    
    def load_cache_info(self) -> dict:
        """Load cache information"""
        try:
            cache_info_file = self.get_cache_info_filename()
            if os.path.exists(cache_info_file):
                with open(cache_info_file, 'rb') as f:
                    return pickle.load(f)
        except Exception as e:
            print(f"⚠️  Failed to load cache info: {e}")
        return {}
    
    def load_hsi_constituents_from_csv(self, csv_file: str = 'data_files/hsi_constituents.csv') -> List[str]:
        """从CSV文件加载恒生指数成分股"""
        try:
            print(f"📁 从 {csv_file} 加载恒生指数成分股...")

            # 读取CSV文件
            df = pd.read_csv(csv_file, dtype={'代码': str})

            # 格式化股票代码 - akshare使用5位数字格式（保持前导0）
            hsi_stocks = []
            for _, row in df.iterrows():
                code = str(row['代码']).strip()
                if code and code != 'nan':
                    # akshare港股使用5位数字格式，如 00700, 09988
                    code_formatted = str(code).zfill(5)  # 补齐到5位数
                    hsi_stocks.append(code_formatted)

            self.symbols = hsi_stocks
            print(f"✅ 已加载 {len(hsi_stocks)} 只恒生指数成分股")
            return hsi_stocks

        except Exception as e:
            print(f"❌ 加载成分股失败: {e}")
            return self._get_fallback_hsi_stocks()

    def _get_fallback_hsi_stocks(self) -> List[str]:
        """备用恒生指数成分股列表 - akshare格式（5位数）"""
        fallback_stocks = [
            '00700',  # 腾讯控股
            '09988',  # 阿里巴巴-W
            '00941',  # 中国移动
            '01299',  # 友邦保险
            '00939',  # 建设银行
            '01398',  # 工商银行
            '03988',  # 中国银行
            '00005',  # 汇丰控股
            '02318',  # 中国平安
            '03690',  # 美团-W
            '00883',  # 中国海洋石油
            '00386',  # 中国石油化工股份
            '00857',  # 中国石油天然气股份
            '02628',  # 中国人寿
            '01088',  # 中国神华
            '00762',  # 中国联通
            '03968',  # 招商银行
            '09999',  # 网易-S
            '09618',  # 京东集团-SW
            '01810',  # 小米集团-W
            '01024',  # 快手-W
            '09888',  # 百度集团-SW
            '00688',  # 中国海外发展
            '00001',  # 长和
            '01113',  # 长实集团
            '00016',  # 新鸿基地产
            '00012',  # 恒基地产
            '00388',  # 香港交易所
            '00002',  # 中电控股
            '00066'   # 港铁公司
        ]

        self.symbols = fallback_stocks
        print(f"⚠️  使用备用成分股列表: {len(fallback_stocks)} 只港股")
        return fallback_stocks
    

    
    def get_dividend_data(self, symbol: str) -> pd.DataFrame:
        """获取单只股票的股息率数据"""
        try:
            # 尝试获取港股分红数据
            # 注意：akshare可能没有直接的港股分红接口，这里提供一个框架
            # 实际使用时可能需要使用其他数据源或API

            # 方法1：尝试使用股息率接口（如果存在）
            try:
                # 这是一个示例，实际的akshare接口可能不同
                dividend_data = ak.stock_hk_gxl_lg() if symbol == "HSI" else None
                if dividend_data is not None:
                    return dividend_data
            except:
                pass

            # 方法2：模拟股息率数据（用于演示）
            # 在实际应用中，应该使用真实的数据源
            print(f"⚠️  暂时使用模拟股息率数据 for {symbol}")

            # 生成过去一年的模拟数据
            end_date = datetime.now()
            start_date = end_date - timedelta(days=365)
            date_range = pd.date_range(start=start_date, end=end_date, freq='M')

            # 模拟股息率数据（年化，百分比）
            np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
            dividend_yields = np.random.normal(3.5, 1.5, len(date_range))  # 平均3.5%，标准差1.5%
            dividend_yields = np.clip(dividend_yields, 0.5, 8.0)  # 限制在合理范围内

            df = pd.DataFrame({
                'date': date_range,
                'dividend_yield': dividend_yields
            })
            df['date'] = pd.to_datetime(df['date'])
            df.set_index('date', inplace=True)

            return df

        except Exception as e:
            print(f"❌ 获取 {symbol} 股息率数据失败: {e}")
            return pd.DataFrame()

    def download_with_cache(self, force_refresh: bool = False) -> bool:
        """Download data with caching support"""
        try:
            print(f"📥 开始缓存下载 {len(self.symbols)} 只港股...")

            # Load cache info
            cache_info = self.load_cache_info()

            # Download benchmark data (恒生指数)
            print(f"📈 下载恒生指数数据...")
            benchmark_cache_file = self.get_cache_filename("HSI")

            if not force_refresh and self.is_cache_valid(benchmark_cache_file):
                print(f"💾 从缓存加载恒生指数...")
                self.benchmark_data = self.load_from_cache("HSI")
            else:
                print(f"🌐 从akshare下载恒生指数...")
                try:
                    # 使用akshare获取恒生指数数据
                    hsi_data = ak.stock_hk_index_daily_em(symbol="HSI")

                    if not hsi_data.empty:
                        # 过滤日期范围（最近400天）
                        end_date = datetime.now()
                        start_date = end_date - timedelta(days=400)

                        # 重命名列并处理日期
                        hsi_data = hsi_data.rename(columns={
                            '日期': 'Date',
                            '收盘': 'Close'
                        })
                        hsi_data['Date'] = pd.to_datetime(hsi_data['Date'])
                        hsi_data = hsi_data[(hsi_data['Date'] >= start_date) & (hsi_data['Date'] <= end_date)]

                        self.benchmark_data = pd.Series(hsi_data['Close'].values,
                                                      index=hsi_data['Date'])
                        self.save_to_cache("HSI", self.benchmark_data)
                        print(f"✅ 恒生指数数据准备完成: {len(self.benchmark_data)} 个数据点")
                    else:
                        print(f"❌ 无法获取恒生指数数据")
                        return False

                except Exception as e:
                    print(f"❌ 下载恒生指数失败: {e}")
                    print(f"🔄 尝试备用方案...")

                    # 备用方案：使用腾讯控股作为基准（港股龙头）
                    try:
                        print(f"📈 使用腾讯控股(00700)作为基准...")
                        tencent_data = ak.stock_hk_daily(symbol='00700')

                        if tencent_data is not None and not tencent_data.empty:
                            # 过滤日期范围
                            end_date = datetime.now()
                            start_date = end_date - timedelta(days=400)

                            tencent_data['date'] = pd.to_datetime(tencent_data['date'])
                            tencent_data = tencent_data[(tencent_data['date'] >= start_date) & (tencent_data['date'] <= end_date)]

                            self.benchmark_data = pd.Series(tencent_data['close'].values,
                                                          index=tencent_data['date'])
                            self.save_to_cache("HSI", self.benchmark_data)
                            print(f"✅ 备用基准数据准备完成: {len(self.benchmark_data)} 个数据点")
                        else:
                            print(f"❌ 备用方案也失败")
                            return False
                    except Exception as e2:
                        print(f"❌ 备用方案失败: {e2}")
                        return False
            
            print(f"✅ 基准指数数据准备完成: {len(self.benchmark_data)} 个数据点")
            
            # Download stock data with caching
            successful_downloads = 0
            cache_hits = 0
            new_downloads = 0
            failed_downloads = 0
            
            print(f"📊 处理 {len(self.symbols)} 只港股（使用缓存）...")
            
            for symbol in tqdm(self.symbols, desc="处理港股"):
                try:
                    # Check cache first
                    if not force_refresh:
                        cached_data = self.load_from_cache(symbol)
                        if cached_data is not None:
                            self.stock_data[symbol] = cached_data
                            successful_downloads += 1
                            cache_hits += 1
                            continue

                    # Download from akshare
                    try:
                        # 使用akshare获取港股数据（基于成功的实现）
                        stock_data = None

                        # 使用stock_hk_daily接口（不需要日期参数）
                        try:
                            stock_data = ak.stock_hk_daily(symbol=symbol)

                            if stock_data is not None and not stock_data.empty:
                                # 过滤日期范围（最近400天）
                                end_date = datetime.now()
                                start_date = end_date - timedelta(days=400)

                                stock_data['date'] = pd.to_datetime(stock_data['date'])
                                stock_data = stock_data[(stock_data['date'] >= start_date) & (stock_data['date'] <= end_date)]
                        except Exception as e:
                            print(f"⚠️  下载 {symbol} 失败: {e}")
                            pass

                        if stock_data is not None and not stock_data.empty and len(stock_data) > 50:
                            # 处理数据格式（基于成功的实现）
                            try:
                                # akshare的stock_hk_daily返回的数据格式
                                # 包含 'date', 'open', 'high', 'low', 'close', 'volume' 等列
                                if 'close' in stock_data.columns and 'date' in stock_data.columns:
                                    # 创建时间序列
                                    dates = pd.to_datetime(stock_data['date'])
                                    prices = pd.to_numeric(stock_data['close'], errors='coerce')

                                    stock_close = pd.Series(prices.values, index=dates).dropna()

                                    if len(stock_close) > 50:
                                        self.stock_data[symbol] = stock_close
                                        self.save_to_cache(symbol, stock_close)
                                        successful_downloads += 1
                                        new_downloads += 1
                                    else:
                                        failed_downloads += 1
                                else:
                                    failed_downloads += 1
                            except Exception as e:
                                print(f"⚠️  处理 {symbol} 数据失败: {e}")
                                failed_downloads += 1
                        else:
                            failed_downloads += 1

                    except Exception as e:
                        failed_downloads += 1
                        continue

                except Exception as e:
                    failed_downloads += 1
                    continue
            
            # Update cache info
            cache_info.update({
                'last_update': datetime.now().isoformat(),
                'period': self.period,
                'total_symbols': len(self.symbols),
                'successful_downloads': successful_downloads,
                'cache_hits': cache_hits,
                'new_downloads': new_downloads,
                'failed_downloads': failed_downloads
            })
            self.save_cache_info(cache_info)
            
            print(f"\n📊 下载汇总:")
            print(f"   总股票数: {len(self.symbols)}")
            print(f"   成功下载: {successful_downloads}")
            print(f"   缓存命中: {cache_hits}")
            print(f"   新下载: {new_downloads}")
            print(f"   下载失败: {failed_downloads}")
            print(f"   缓存效率: {cache_hits/len(self.symbols)*100:.1f}%")
            
            return successful_downloads > 0

        except Exception as e:
            print(f"❌ 缓存下载失败: {e}")
            return False

    def calculate_relative_strength(self) -> bool:
        """计算相对强度"""
        try:
            print(f"🔢 计算 {len(self.stock_data)} 只港股的相对强度...")

            if self.benchmark_data is None or len(self.stock_data) == 0:
                print(f"❌ 缺少必要数据")
                return False

            for symbol, stock_close in tqdm(self.stock_data.items(), desc="计算相对强度"):
                try:
                    # Handle timezone issues
                    stock_close_tz = stock_close.copy()
                    benchmark_tz = self.benchmark_data.copy()

                    if stock_close_tz.index.tz is not None:
                        stock_close_tz.index = stock_close_tz.index.tz_localize(None)
                    if benchmark_tz.index.tz is not None:
                        benchmark_tz.index = benchmark_tz.index.tz_localize(None)

                    # Use pandas join for alignment
                    stock_df = pd.DataFrame({'stock': stock_close_tz})
                    benchmark_df = pd.DataFrame({'benchmark': benchmark_tz})

                    merged = stock_df.join(benchmark_df, how='inner').dropna()

                    if len(merged) < 30:
                        continue

                    # Calculate relative strength
                    relative_strength = (merged['stock'] / merged['benchmark']) * 100

                    # Normalize to start at 100
                    if len(relative_strength) > 0 and not pd.isna(relative_strength.iloc[0]) and relative_strength.iloc[0] != 0:
                        relative_strength = relative_strength / relative_strength.iloc[0] * 100
                        self.relative_strength_data[symbol] = relative_strength

                except Exception as e:
                    continue

            print(f"✅ 已计算 {len(self.relative_strength_data)} 只港股的相对强度")
            return len(self.relative_strength_data) > 0

        except Exception as e:
            print(f"❌ 相对强度计算失败: {e}")
            return False

    def analyze_trends(self) -> pd.DataFrame:
        """分析相对强度趋势"""
        try:
            print(f"📈 分析 {len(self.relative_strength_data)} 只港股的趋势...")

            results = []

            for symbol, rs_data in tqdm(self.relative_strength_data.items(), desc="趋势分析"):
                try:
                    if len(rs_data) < 30:
                        continue

                    # Basic statistics
                    current_rs = rs_data.iloc[-1]
                    start_rs = rs_data.iloc[0]
                    total_change = current_rs - start_rs
                    total_change_pct = (current_rs / start_rs - 1) * 100

                    # Calculate slopes for different periods
                    periods = {
                        '1M': min(22, len(rs_data)),
                        '3M': min(66, len(rs_data)),
                        '6M': min(132, len(rs_data))
                    }

                    slopes = {}
                    for period_name, period_days in periods.items():
                        if period_days >= 10:
                            period_data = rs_data.iloc[-period_days:]
                            x = np.arange(len(period_data))
                            y = period_data.values
                            slope = np.polyfit(x, y, 1)[0]
                            slopes[f'{period_name}_slope'] = slope

                    # Moving averages
                    rs_ma20 = rs_data.rolling(20).mean()
                    rs_ma50 = rs_data.rolling(50).mean()

                    # Position relative to moving averages
                    above_ma20 = current_rs > rs_ma20.iloc[-1] if not pd.isna(rs_ma20.iloc[-1]) else False
                    above_ma50 = current_rs > rs_ma50.iloc[-1] if not pd.isna(rs_ma50.iloc[-1]) else False

                    # Strength score (0-100)
                    strength_score = 0

                    # Trend scores
                    if slopes.get('1M_slope', 0) > 0:
                        strength_score += 15
                    if slopes.get('3M_slope', 0) > 0:
                        strength_score += 25
                    if slopes.get('6M_slope', 0) > 0:
                        strength_score += 25

                    # Position scores
                    if above_ma20:
                        strength_score += 15
                    if above_ma50:
                        strength_score += 10
                    if current_rs > 100:
                        strength_score += 10

                    result = {
                        'symbol': symbol,
                        'current_rs': current_rs,
                        'total_change': total_change,
                        'total_change_pct': total_change_pct,
                        'above_ma20': above_ma20,
                        'above_ma50': above_ma50,
                        'strength_score': strength_score,
                        **slopes
                    }

                    results.append(result)

                except Exception as e:
                    continue

            self.analysis_results = pd.DataFrame(results)

            if not self.analysis_results.empty:
                self.analysis_results = self.analysis_results.sort_values('strength_score', ascending=False)
                self.analysis_results.reset_index(drop=True, inplace=True)
                print(f"✅ 已分析 {len(self.analysis_results)} 只港股")

            return self.analysis_results

        except Exception as e:
            print(f"❌ 趋势分析失败: {e}")
            return pd.DataFrame()

    def get_rising_stocks(self, min_score: int = 60) -> pd.DataFrame:
        """获取持续上升的港股"""
        if self.analysis_results.empty:
            return pd.DataFrame()

        rising_stocks = self.analysis_results[
            (self.analysis_results.get('1M_slope', 0) > 0) &
            (self.analysis_results.get('3M_slope', 0) > 0) &
            (self.analysis_results.get('6M_slope', 0) > 0) &
            (self.analysis_results['strength_score'] >= min_score)
        ]

        return rising_stocks.sort_values('strength_score', ascending=False)

    def clear_cache(self) -> None:
        """清除所有缓存数据"""
        try:
            import shutil
            if os.path.exists(self.cache_dir):
                shutil.rmtree(self.cache_dir)
                os.makedirs(self.cache_dir)
                print(f"🗑️  缓存已清除: {self.cache_dir}")
        except Exception as e:
            print(f"❌ 清除缓存失败: {e}")

    def get_cache_stats(self) -> dict:
        """获取缓存统计信息"""
        cache_info = self.load_cache_info()

        # Count cache files
        cache_files = [f for f in os.listdir(self.cache_dir) if f.endswith('.pkl')]

        stats = {
            'cache_directory': self.cache_dir,
            'cache_files_count': len(cache_files),
            'last_update': cache_info.get('last_update', 'Never'),
            'cache_hits': cache_info.get('cache_hits', 0),
            'new_downloads': cache_info.get('new_downloads', 0),
            'total_symbols': cache_info.get('total_symbols', 0)
        }

        return stats

    def run_analysis(self, force_refresh: bool = False) -> bool:
        """运行完整的相对强度分析"""
        try:
            print("🚀 开始恒生指数成分股相对强度分析（带缓存）...")
            print("=" * 70)

            # Load symbols
            symbols = self.load_hsi_constituents_from_csv()
            if not symbols:
                return False

            # Download data with caching
            if not self.download_with_cache(force_refresh=force_refresh):
                return False

            # Calculate relative strength
            if not self.calculate_relative_strength():
                return False

            # Analyze trends
            results = self.analyze_trends()
            if results.empty:
                return False

            print("✅ 分析完成!")
            return True

        except Exception as e:
            print(f"❌ 分析失败: {e}")
            return False


def main():
    """主函数"""
    print("🎯 恒生指数成分股相对强度分析器（带缓存）")
    print("=" * 50)

    # Create analyzer with cache
    analyzer = CachedHSIAnalyzer(period="1y", benchmark="^HSI", cache_dir="hsi_cache")

    # Show cache stats
    cache_stats = analyzer.get_cache_stats()
    print(f"\n💾 缓存状态:")
    print(f"   缓存文件数: {cache_stats['cache_files_count']}")
    print(f"   最后更新: {cache_stats['last_update']}")

    # Run analysis (will use cache if available)
    start_time = time.time()

    if not analyzer.run_analysis(force_refresh=False):  # Set to True to force refresh
        print("❌ 分析失败")
        return

    end_time = time.time()
    duration = end_time - start_time

    # Display results
    print(f"\n📊 分析结果 (耗时 {duration:.1f} 秒):")
    print(f"总分析港股数: {len(analyzer.analysis_results)}")

    if not analyzer.analysis_results.empty:
        # Top 10 strongest stocks
        print("\n🏆 前10强势港股:")
        top_10 = analyzer.analysis_results.head(10)
        for i, (_, stock) in enumerate(top_10.iterrows(), 1):
            print(f"{i:2d}. {stock['symbol']:<12} 评分:{stock['strength_score']:5.0f} 相对强度:{stock['current_rs']:6.1f}")

        # Rising stocks
        rising_stocks = analyzer.get_rising_stocks(min_score=60)
        print(f"\n📈 持续上升港股 ({len(rising_stocks)} 只):")
        for _, stock in rising_stocks.head(10).iterrows():
            print(f"   {stock['symbol']:<12} 评分:{stock['strength_score']:5.0f} 相对强度:{stock['current_rs']:6.1f}")

        # Save results
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = f"cached_hsi_results_{timestamp}.csv"
        analyzer.analysis_results.to_csv(results_file, index=False)
        print(f"\n📁 结果已保存至: {results_file}")

        print("\n✅ 分析完成! 数据已缓存，下次运行将更快。")

    else:
        print("❌ 未生成分析结果")


if __name__ == "__main__":
    main()
