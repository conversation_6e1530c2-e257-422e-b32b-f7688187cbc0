#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速回测美股中期择时策略
"""

import pandas as pd
import numpy as np
import yfinance as yf

def main():
    print("="*60)
    print("美股中期择时策略快速回测")
    print("="*60)

    # 加载择时指标
    timing_df = pd.read_csv('data/improved_us_mid_term_timing_indicator.csv', index_col=0)
    timing_df.index = pd.to_datetime(timing_df.index)
    timing_indicator = timing_df['Improved_US_Mid_Term_Timing']

    print(f"择时指标数据: {len(timing_indicator)}个观测值")

    # 下载SPY数据
    spy_data = yf.download('SPY', start='2004-12-01', end='2025-02-01', progress=False)
    spy_monthly = spy_data['Close'].resample('M').last()

    # 对齐数据
    timing_monthly = timing_indicator.copy()
    timing_monthly.index = timing_monthly.index.to_period('M').to_timestamp('M')
    spy_monthly.index = spy_monthly.index.to_period('M').to_timestamp('M')

    # 找共同日期
    common_dates = timing_monthly.index.intersection(spy_monthly.index)
    timing_aligned = timing_monthly.loc[common_dates]
    spy_aligned = spy_monthly.loc[common_dates]

    print(f"对齐后数据点: {len(common_dates)}")

    # 生成信号
    signals = (timing_aligned >= 25).astype(int)

    print(f"持有期数: {signals.sum()}/{len(signals)} ({signals.mean():.1%})")

    # 计算收益率
    spy_returns = spy_aligned.pct_change().dropna()
    strategy_returns = spy_returns * signals.shift(1).loc[spy_returns.index]

    # 累积收益
    spy_cumulative = (1 + spy_returns).cumprod()
    strategy_cumulative = (1 + strategy_returns).cumprod()

    # 计算指标 - 确保转换为标量
    spy_total = (spy_cumulative.iloc[-1] - 1).item()
    strategy_total = (strategy_cumulative.iloc[-1] - 1).item()

    years = len(spy_returns) / 12
    spy_annual = ((spy_cumulative.iloc[-1] ** (1/years)) - 1).item()
    strategy_annual = ((strategy_cumulative.iloc[-1] ** (1/years)) - 1).item()

    spy_vol = (spy_returns.std() * np.sqrt(12)).item()
    strategy_vol = (strategy_returns.std() * np.sqrt(12)).item()

    spy_sharpe = spy_annual / spy_vol
    strategy_sharpe = strategy_annual / strategy_vol

    # 最大回撤
    spy_dd = ((spy_cumulative / spy_cumulative.expanding().max()) - 1).min().item()
    strategy_dd = ((strategy_cumulative / strategy_cumulative.expanding().max()) - 1).min().item()

    # 打印结果
    print(f"\n回测结果 ({common_dates[0].strftime('%Y-%m')} - {common_dates[-1].strftime('%Y-%m')}):")
    print("-" * 60)
    print(f"{'指标':<15} {'SPY买入持有':<15} {'择时策略':<15}")
    print("-" * 60)
    print(f"{'总收益率':<15} {spy_total:.1%:<15} {strategy_total:.1%:<15}")
    print(f"{'年化收益率':<15} {spy_annual:.1%:<15} {strategy_annual:.1%:<15}")
    print(f"{'年化波动率':<15} {spy_vol:.1%:<15} {strategy_vol:.1%:<15}")
    print(f"{'夏普比率':<15} {spy_sharpe:.2f:<15} {strategy_sharpe:.2f:<15}")
    print(f"{'最大回撤':<15} {spy_dd:.1%:<15} {strategy_dd:.1%:<15}")

    print(f"\n最终投资组合价值 (初始$100,000):")
    print(f"SPY买入持有: ${100000 * spy_cumulative.iloc[-1].item():,.0f}")
    print(f"择时策略: ${100000 * strategy_cumulative.iloc[-1].item():,.0f}")

    # 保存简单结果
    results = pd.DataFrame({
        'Date': common_dates,
        'Timing_Indicator': timing_aligned.values,
        'SPY_Price': spy_aligned.values,
        'Signal': signals.values,
        'SPY_Cumulative': spy_cumulative.values,
        'Strategy_Cumulative': strategy_cumulative.values
    })

    results.to_csv('data/quick_backtest_results.csv', index=False)
    print(f"\n结果已保存至: data/quick_backtest_results.csv")

    return results

if __name__ == "__main__":
    results = main()
