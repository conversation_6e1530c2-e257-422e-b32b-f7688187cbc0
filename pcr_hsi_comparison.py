#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PCR与恒生指数对比分析
绘制Put/Call Ratio与恒生指数的对比图表
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import yfinance as yf
import akshare as ak
from datetime import datetime, timedelta
import os
import warnings
warnings.filterwarnings('ignore')

# 设置matplotlib支持中文显示
plt.rcParams['font.sans-serif'] = ['Arial Unicode MS', 'SimHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

# 代理设置已移除

def load_pcr_data():
    """加载PCR数据"""
    try:
        # 读取PCR数据
        pcr_df = pd.read_csv('data/Put_Call_Ratio.csv')

        # 转换日期格式
        pcr_df['Date'] = pd.to_datetime(pcr_df['Date'], format='%d/%m/%Y')
        pcr_df.set_index('Date', inplace=True)
        pcr_df.sort_index(inplace=True)

        print(f"PCR数据加载成功，共{len(pcr_df)}条记录")
        print(f"数据时间范围：{pcr_df.index[0].date()} 到 {pcr_df.index[-1].date()}")

        return pcr_df
    except Exception as e:
        print(f"加载PCR数据失败：{e}")
        return None

def get_hsi_data(start_date, end_date):
    """获取恒生指数数据"""
    print("正在获取恒生指数数据...")

    # 尝试多种数据源
    sources_tried = []

    # 1. 尝试使用akshare获取恒生指数数据
    try:
        sources_tried.append("AKShare")
        print("尝试从AKShare获取数据...")

        hsi_data = ak.stock_hk_index_daily_em(symbol="HSI")
        if not hsi_data.empty:
            # 重命名列
            hsi_data = hsi_data.rename(columns={
                '日期': 'Date',
                '开盘': 'Open',
                '收盘': 'Close',
                '最高': 'High',
                '最低': 'Low',
                '成交量': 'Volume'
            })
            hsi_data['Date'] = pd.to_datetime(hsi_data['Date'])
            hsi_data.set_index('Date', inplace=True)
            hsi_data = hsi_data.sort_index()

            # 筛选日期范围
            hsi_data = hsi_data[(hsi_data.index >= start_date) & (hsi_data.index <= end_date)]

            if len(hsi_data) >= 10:
                print(f"从AKShare获取恒生指数数据成功，时间范围：{hsi_data.index[0].date()} 到 {hsi_data.index[-1].date()}")
                print(f"总数据点数：{len(hsi_data)}")
                return hsi_data
            else:
                print("AKShare数据量不足")
    except Exception as e:
        print(f"AKShare获取数据失败：{e}")

    # 2. 尝试使用yfinance获取恒生指数数据
    try:
        sources_tried.append("Yahoo Finance")
        print("尝试从Yahoo Finance获取数据...")

        hsi_ticker = yf.Ticker("^HSI")
        hsi_data = hsi_ticker.history(start=start_date, end=end_date)

        if not hsi_data.empty and len(hsi_data) >= 10:
            print(f"从Yahoo Finance获取恒生指数数据成功，时间范围：{hsi_data.index[0].date()} 到 {hsi_data.index[-1].date()}")
            print(f"总数据点数：{len(hsi_data)}")
            return hsi_data
        else:
            print("Yahoo Finance数据为空或数据量不足")
    except Exception as e:
        print(f"Yahoo Finance获取数据失败：{e}")

    print(f"所有数据源都失败了，尝试过的源：{', '.join(sources_tried)}")
    return None

def align_data(pcr_df, hsi_data):
    """对齐PCR和HSI数据"""
    # 确保两个数据集的索引都是日期类型，并统一时区
    pcr_df.index = pd.to_datetime(pcr_df.index).tz_localize(None)
    hsi_data.index = pd.to_datetime(hsi_data.index).tz_localize(None)

    # 找到共同的日期范围
    start_date = max(pcr_df.index.min(), hsi_data.index.min())
    end_date = min(pcr_df.index.max(), hsi_data.index.max())

    print(f"共同数据时间范围：{start_date.date()} 到 {end_date.date()}")

    # 筛选共同时间范围的数据
    pcr_aligned = pcr_df[(pcr_df.index >= start_date) & (pcr_df.index <= end_date)]
    hsi_aligned = hsi_data[(hsi_data.index >= start_date) & (hsi_data.index <= end_date)]

    # 合并数据，使用外连接保留所有日期
    combined_df = pd.merge(pcr_aligned[['Put/Call Ratio']],
                          hsi_aligned[['Close']],
                          left_index=True, right_index=True, how='outer')

    # 前向填充缺失值
    combined_df = combined_df.fillna(method='ffill')

    print(f"对齐后的数据点数：{len(combined_df)}")

    return combined_df

def create_comparison_chart(combined_df):
    """创建PCR与恒生指数对比图表"""
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(15, 12), sharex=True)

    # 绘制恒生指数
    ax1.plot(combined_df.index, combined_df['Close'], color='blue', linewidth=1.5, label='恒生指数')
    ax1.set_ylabel('恒生指数点位', fontsize=12, color='blue')
    ax1.tick_params(axis='y', labelcolor='blue')
    ax1.grid(True, alpha=0.3)
    ax1.legend(loc='upper left')
    ax1.set_title('恒生指数走势', fontsize=14, fontweight='bold')

    # 绘制PCR
    ax2.plot(combined_df.index, combined_df['Put/Call Ratio'], color='red', linewidth=1.5, label='Put/Call Ratio')
    ax2.axhline(y=1.0, color='gray', linestyle='--', alpha=0.7, label='PCR=1.0')
    ax2.axhline(y=0.7, color='green', linestyle='--', alpha=0.7, label='看涨信号(0.7)')
    ax2.axhline(y=1.3, color='orange', linestyle='--', alpha=0.7, label='看跌信号(1.3)')
    ax2.set_ylabel('Put/Call Ratio', fontsize=12, color='red')
    ax2.tick_params(axis='y', labelcolor='red')
    ax2.grid(True, alpha=0.3)
    ax2.legend(loc='upper left')
    ax2.set_title('Put/Call Ratio走势', fontsize=14, fontweight='bold')
    ax2.set_xlabel('日期', fontsize=12)

    # 设置x轴格式
    fig.autofmt_xdate()

    plt.tight_layout()
    plt.suptitle('恒生指数与Put/Call Ratio对比分析', fontsize=16, fontweight='bold', y=0.98)

    # 保存图表
    plt.savefig('pcr_hsi_comparison.png', dpi=300, bbox_inches='tight')
    print("图表已保存为 pcr_hsi_comparison.png")

    plt.show()

def calculate_correlation(combined_df):
    """计算相关性分析"""
    # 计算相关系数
    correlation = combined_df['Put/Call Ratio'].corr(combined_df['Close'])

    print(f"\n=== 相关性分析 ===")
    print(f"PCR与恒生指数的相关系数：{correlation:.4f}")

    # 计算滞后相关性
    print(f"\n=== 滞后相关性分析 ===")
    for lag in range(1, 6):
        # PCR领先HSI
        pcr_lead_corr = combined_df['Put/Call Ratio'].corr(combined_df['Close'].shift(lag))
        print(f"PCR领先{lag}天的相关系数：{pcr_lead_corr:.4f}")

        # HSI领先PCR
        hsi_lead_corr = combined_df['Put/Call Ratio'].shift(lag).corr(combined_df['Close'])
        print(f"HSI领先{lag}天的相关系数：{hsi_lead_corr:.4f}")

def analyze_extreme_values(combined_df):
    """分析极值情况"""
    print(f"\n=== 极值分析 ===")

    # PCR极值分析
    pcr_high = combined_df['Put/Call Ratio'] > 1.3  # 高PCR（看跌情绪）
    pcr_low = combined_df['Put/Call Ratio'] < 0.7   # 低PCR（看涨情绪）

    print(f"高PCR(>1.3)出现次数：{pcr_high.sum()}次")
    print(f"低PCR(<0.7)出现次数：{pcr_low.sum()}次")

    # 分析高PCR后的市场表现
    if pcr_high.sum() > 0:
        high_pcr_dates = combined_df[pcr_high].index
        future_returns = []
        for date in high_pcr_dates:
            try:
                current_price = combined_df.loc[date, 'Close']
                future_date = date + timedelta(days=10)
                if future_date in combined_df.index:
                    future_price = combined_df.loc[future_date, 'Close']
                    return_10d = (future_price - current_price) / current_price * 100
                    future_returns.append(return_10d)
            except:
                continue

        if future_returns:
            avg_return = np.mean(future_returns)
            print(f"高PCR后10天平均收益率：{avg_return:.2f}%")

    # 分析低PCR后的市场表现
    if pcr_low.sum() > 0:
        low_pcr_dates = combined_df[pcr_low].index
        future_returns = []
        for date in low_pcr_dates:
            try:
                current_price = combined_df.loc[date, 'Close']
                future_date = date + timedelta(days=10)
                if future_date in combined_df.index:
                    future_price = combined_df.loc[future_date, 'Close']
                    return_10d = (future_price - current_price) / current_price * 100
                    future_returns.append(return_10d)
            except:
                continue

        if future_returns:
            avg_return = np.mean(future_returns)
            print(f"低PCR后10天平均收益率：{avg_return:.2f}%")

def main():
    """主函数"""
    print("=== PCR与恒生指数对比分析 ===")

    # 加载PCR数据
    pcr_df = load_pcr_data()
    if pcr_df is None:
        return

    # 获取恒生指数数据
    start_date = pcr_df.index.min() - timedelta(days=30)  # 提前30天确保数据完整
    end_date = pcr_df.index.max() + timedelta(days=30)    # 延后30天确保数据完整

    hsi_data = get_hsi_data(start_date, end_date)
    if hsi_data is None:
        return

    # 对齐数据
    combined_df = align_data(pcr_df, hsi_data)
    if combined_df.empty:
        print("没有找到重叠的数据")
        return

    # 创建对比图表
    create_comparison_chart(combined_df)

    # 计算相关性
    calculate_correlation(combined_df)

    # 分析极值情况
    analyze_extreme_values(combined_df)

    print("\n分析完成！")

if __name__ == "__main__":
    main()
