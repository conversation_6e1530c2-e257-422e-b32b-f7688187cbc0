import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import os
import sys
from datetime import datetime, timedelta
import yfinance as yf
from pathlib import Path
import time
import requests
from urllib.parse import urlparse
import socket
import ssl
import importlib.util

def load_hsi_constituents():
    """
    Load HSI constituents from CSV file

    Returns:
        pandas.DataFrame: DataFrame with HSI constituent stocks
    """
    print("Loading HSI constituents...")

    # Path to the HSI constituents CSV file
    file_path = "data/hsi_constituents.csv"

    if not os.path.exists(file_path):
        print(f"Error: HSI constituents file not found at {file_path}")
        return None

    # Load the CSV file
    constituents_df = pd.read_csv(file_path)

    print(f"Loaded {len(constituents_df)} HSI constituents")

    return constituents_df

def get_stock_tickers(constituents_df):
    """
    Get Yahoo Finance tickers for HSI constituent stocks

    Args:
        constituents_df (pandas.DataFrame): DataFrame with HSI constituent stocks

    Returns:
        list: List of Yahoo Finance tickers for HSI constituent stocks
    """
    # Extract stock codes from the constituents DataFrame
    stock_codes = constituents_df['code'].astype(str).tolist()

    # Format stock codes as Yahoo Finance tickers (Hong Kong stocks)
    # Yahoo Finance format for Hong Kong stocks is: XXXX.HK
    tickers = [f"{code.zfill(4)}.HK" for code in stock_codes]

    return tickers

def load_config():
    """
    Load configuration from config/hsi_data_config.py

    Returns:
        module: Configuration module
    """
    try:
        # Try to import the config module
        config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'config', 'hsi_data_config.py')

        if not os.path.exists(config_path):
            print(f"Warning: Config file not found at {config_path}")
            return None

        # Load the module from file path
        spec = importlib.util.spec_from_file_location("hsi_data_config", config_path)
        config = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(config)

        return config
    except Exception as e:
        print(f"Error loading config: {str(e)}")
        return None

def setup_proxy():
    """
    Setup proxy for Yahoo Finance API requests

    Returns:
        dict: Proxy configuration for requests
    """
    # Try to load proxy settings from config file
    config = load_config()
    proxies = {}

    if config and hasattr(config, 'PROXY_SETTINGS'):
        proxies = config.PROXY_SETTINGS
        print(f"Loaded proxy settings from config file: {proxies}")
    else:
        # Fallback to environment variables
        http_proxy = os.environ.get('HTTP_PROXY', '')
        https_proxy = os.environ.get('HTTPS_PROXY', '')

        if http_proxy or https_proxy:
            proxies = {
                'http': http_proxy,
                'https': https_proxy
            }
            print(f"Using proxy settings from environment variables")
        else:
            print("No proxy settings found in config or environment variables")
            return {}

    # Set proxies for yfinance
    if proxies:
        print(f"Using proxy: {proxies}")

        # Monkey patch yfinance's session to use our proxy
        session = requests.Session()
        session.proxies.update(proxies)
        yf.base._requests_session = session

        # Also set the proxy for socket connections
        if 'https' in proxies and proxies['https']:
            proxy_url = proxies['https']
            # Handle socks5h:// and other protocols
            if '://' in proxy_url:
                proxy_parts = proxy_url.split('://')
                if len(proxy_parts) == 2 and ':' in proxy_parts[1]:
                    proxy_host_port = proxy_parts[1].split(':')
                    if len(proxy_host_port) == 2:
                        os.environ['HTTP_PROXY'] = proxies.get('http', '')
                        os.environ['HTTPS_PROXY'] = proxies.get('https', '')

    return proxies

def load_stock_data(tickers, start_date, end_date, force_download=False):
    """
    Load historical stock data for HSI constituents, prioritizing local data

    Args:
        tickers (list): List of Yahoo Finance tickers
        start_date (str): Start date in YYYY-MM-DD format
        end_date (str): End date in YYYY-MM-DD format
        force_download (bool): Whether to force download even if local data exists

    Returns:
        pandas.DataFrame: DataFrame with historical stock data
    """
    print(f"Loading historical data for {len(tickers)} stocks from {start_date} to {end_date}...")

    # Create directories to store the data
    data_dir = "data/stock_prices"
    os.makedirs(data_dir, exist_ok=True)

    # Create a directory for individual stock CSV files
    csv_dir = os.path.join(data_dir, "individual")
    os.makedirs(csv_dir, exist_ok=True)

    # Load data for each ticker with error handling
    all_data = {}
    successful_loads = 0
    failed_loads = 0

    for i, ticker in enumerate(tickers):
        csv_file = os.path.join(csv_dir, f"{ticker.replace('.', '_')}.csv")

        # Check if local file exists
        if os.path.exists(csv_file) and not force_download:
            try:
                # Load from CSV - handle the special format
                # First check the file format
                with open(csv_file, 'r') as f:
                    first_line = f.readline().strip()

                if first_line == "Price,Close,High,Low,Open,Volume":
                    # Special format with headers in first row, tickers in second row, "Date" in third row
                    # Skip the first 3 rows and use the 4th row onwards
                    stock_data = pd.read_csv(csv_file, skiprows=3)

                    # First column is the date
                    stock_data.rename(columns={stock_data.columns[0]: 'Date'}, inplace=True)

                    # Convert Date to datetime and set as index
                    stock_data['Date'] = pd.to_datetime(stock_data['Date'])
                    stock_data.set_index('Date', inplace=True)
                else:
                    # Standard CSV format
                    stock_data = pd.read_csv(csv_file)

                    # Convert Date to datetime and set as index
                    if 'Date' in stock_data.columns:
                        stock_data['Date'] = pd.to_datetime(stock_data['Date'])
                        stock_data.set_index('Date', inplace=True)

                # Add to all_data - handle different column formats
                if 'Close' in stock_data.columns:
                    close_column = 'Close'
                elif 'Price' in stock_data.columns:
                    close_column = 'Price'
                else:
                    # Try to find a column that might contain close prices
                    potential_columns = [col for col in stock_data.columns if 'close' in col.lower() or 'price' in col.lower()]
                    if potential_columns:
                        close_column = potential_columns[0]
                    else:
                        # If all else fails, use the second column (assuming first is date)
                        if len(stock_data.columns) > 1:
                            close_column = stock_data.columns[1]
                        else:
                            print(f"Error: No suitable price column found in {csv_file}")
                            print(f"Available columns: {stock_data.columns.tolist()}")
                            failed_loads += 1
                            continue

                # Filter for the requested date range
                if isinstance(stock_data.index, pd.DatetimeIndex):
                    stock_data = stock_data[(stock_data.index >= start_date) & (stock_data.index <= end_date)]

                # Use the identified close column
                all_data[ticker] = stock_data[close_column]
                successful_loads += 1
                print(f"Loaded {ticker} from CSV file using column '{close_column}' ({i+1}/{len(tickers)})")
            except Exception as e:
                print(f"Error loading {ticker} from CSV: {str(e)}")
                failed_loads += 1
        else:
            # File doesn't exist or force_download is True
            print(f"No local data found for {ticker} or force_download is True")
            failed_loads += 1

    print(f"Load summary: {successful_loads} successful, {failed_loads} failed")

    # Combine all stock data into a single DataFrame
    if all_data:
        # Check if we have any valid data
        if len(all_data) > 0:
            # Create DataFrame from dictionary of Series
            try:
                stock_prices = pd.DataFrame(all_data)
                print(f"Created DataFrame with data for {len(all_data)} stocks")
                return stock_prices
            except ValueError as e:
                print(f"Error creating DataFrame: {str(e)}")

                # Debug information
                print("Data shapes:")
                for ticker, series in all_data.items():
                    print(f"{ticker}: {len(series)} data points, index type: {type(series.index)}")

                # Try to align the indices
                print("Attempting to align indices...")
                common_index = pd.DatetimeIndex(sorted(set.intersection(*[set(s.index) for s in all_data.values()])))
                if len(common_index) > 0:
                    aligned_data = {ticker: series.reindex(common_index) for ticker, series in all_data.items()}
                    stock_prices = pd.DataFrame(aligned_data)
                    print(f"Created aligned DataFrame with {len(stock_prices)} rows and {len(aligned_data)} columns")
                    return stock_prices
                else:
                    print("No common dates found across all stocks")
                    return None
        else:
            print("No valid stock data was loaded")
            return None
    else:
        print("No stock data was loaded")
        return None

def calculate_advance_decline_line(stock_prices):
    """
    Calculate the Advance/Decline Line for HSI constituents

    Args:
        stock_prices (pandas.DataFrame): DataFrame with historical stock prices

    Returns:
        pandas.DataFrame: DataFrame with Advance/Decline Line
    """
    print("Calculating Advance/Decline Line...")

    # Calculate daily returns
    daily_returns = stock_prices.pct_change()

    # Count advancing and declining stocks each day
    advancing = (daily_returns > 0).sum(axis=1)
    declining = (daily_returns < 0).sum(axis=1)
    unchanged = (daily_returns == 0).sum(axis=1)

    # Calculate daily advance-decline difference
    daily_ad_diff = advancing - declining

    # Calculate cumulative advance-decline line
    ad_line = daily_ad_diff.cumsum()

    # Create a DataFrame with all the metrics
    ad_data = pd.DataFrame({
        'Advancing': advancing,
        'Declining': declining,
        'Unchanged': unchanged,
        'AD_Diff': daily_ad_diff,
        'AD_Line': ad_line
    })

    return ad_data

def plot_advance_decline_line(ad_data, stock_prices, output_dir="output"):
    """
    Plot the Hang Seng Index and Advance/Decline Line in two separate subplots

    Args:
        ad_data (pandas.DataFrame): DataFrame with Advance/Decline Line data
        stock_prices (pandas.DataFrame): DataFrame with historical stock prices
        output_dir (str): Directory to save the plot
    """
    print("Plotting HSI and Advance/Decline Line in separate subplots...")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Try to load HSI data directly if available
    hsi_file = os.path.join("data", "hsi_data.csv")
    if os.path.exists(hsi_file):
        try:
            print(f"Loading HSI data from {hsi_file}")
            hsi_data = pd.read_csv(hsi_file)
            hsi_data['Date'] = pd.to_datetime(hsi_data['Date'])
            hsi_data.set_index('Date', inplace=True)

            # Filter to match the date range of the AD data
            hsi_data = hsi_data[hsi_data.index.isin(ad_data.index)]

            if not hsi_data.empty:
                hsi_prices = hsi_data['Close']
                print(f"Using actual HSI data for plotting ({len(hsi_prices)} data points)")
            else:
                # Fallback to approximation
                hsi_prices = stock_prices.mean(axis=1)
                print("Using approximated HSI data (mean of constituent stocks)")
        except Exception as e:
            print(f"Error loading HSI data: {str(e)}")
            # Fallback to approximation
            hsi_prices = stock_prices.mean(axis=1)
            print("Using approximated HSI data (mean of constituent stocks)")
    else:
        # Fallback to approximation
        hsi_prices = stock_prices.mean(axis=1)
        print("Using approximated HSI data (mean of constituent stocks)")

    # Create a figure with two separate subplots
    fig, (ax1, ax2) = plt.subplots(2, 1, figsize=(12, 10), sharex=True)

    # Plot HSI on the first subplot
    ax1.plot(hsi_prices.index, hsi_prices, 'b-', linewidth=1.5)
    ax1.set_title('Hang Seng Index (HSI)')
    ax1.set_ylabel('Price')
    ax1.grid(True)

    # Add a secondary y-axis for percentage change
    ax1_twin = ax1.twinx()
    hsi_pct_change = (hsi_prices / hsi_prices.iloc[0] - 1) * 100
    ax1_twin.plot(hsi_prices.index, hsi_pct_change, 'b--', alpha=0.5, linewidth=1)
    ax1_twin.set_ylabel('Percentage Change (%)')

    # Plot AD Line on the second subplot
    ax2.plot(ad_data.index, ad_data['AD_Line'], 'r-', linewidth=1.5)
    ax2.set_title('Advance/Decline Line')
    ax2.set_ylabel('AD Line Value')
    ax2.grid(True)

    # Format x-axis dates
    plt.gcf().autofmt_xdate()

    # Add a date label to the bottom subplot
    ax2.set_xlabel('Date')

    # Adjust layout
    plt.tight_layout()

    # Save the plot
    output_file = os.path.join(output_dir, "hsi_advance_decline_line.png")
    plt.savefig(output_file, dpi=300, bbox_inches='tight')
    print(f"Plot saved to {output_file}")

    # Show the plot
    plt.show()

def save_advance_decline_data(ad_data, output_dir="data"):
    """
    Save Advance/Decline Line data to CSV

    Args:
        ad_data (pandas.DataFrame): DataFrame with Advance/Decline Line data
        output_dir (str): Directory to save the data
    """
    print("Saving Advance/Decline Line data...")

    # Create output directory if it doesn't exist
    os.makedirs(output_dir, exist_ok=True)

    # Save to CSV
    timestamp = datetime.now().strftime("%Y%m%d")
    output_file = os.path.join(output_dir, f"hsi_advance_decline_{timestamp}.csv")
    ad_data.to_csv(output_file)

    # Also save to a standard filename for easy access
    standard_file = os.path.join(output_dir, "hsi_advance_decline.csv")
    ad_data.to_csv(standard_file)

    print(f"Data saved to {output_file} and {standard_file}")

def main():
    try:
        print("=" * 50)
        print("Starting Hang Seng Index Advance/Decline Line calculation")
        print("=" * 50)

        # Load HSI constituents
        constituents_df = load_hsi_constituents()

        if constituents_df is None:
            print("Error: Failed to load HSI constituents")
            return

        # Get stock tickers
        tickers = get_stock_tickers(constituents_df)
        print(f"Prepared {len(tickers)} stock tickers for processing")

        # Define date range (default: 1 year of data)
        end_date = datetime.now().strftime("%Y-%m-%d")

        # Allow customizing the time period via environment variables
        days_back = int(os.environ.get('HSI_DAYS_BACK', '365'))
        start_date = (datetime.now() - timedelta(days=days_back)).strftime("%Y-%m-%d")

        print(f"Using date range: {start_date} to {end_date} ({days_back} days)")

        # Load historical stock data (prioritize local data)
        print("Loading stock price data...")
        stock_prices = load_stock_data(tickers, start_date, end_date, force_download=False)

        if stock_prices is None or stock_prices.empty:
            print("Error: Could not load stock price data")
            return

        print(f"Successfully loaded data for {stock_prices.shape[1]} stocks over {stock_prices.shape[0]} days")
        print(f"Date range in loaded data: {stock_prices.index.min().strftime('%Y-%m-%d')} to {stock_prices.index.max().strftime('%Y-%m-%d')}")

        # Calculate Advance/Decline Line
        print("\nCalculating Advance/Decline Line...")
        ad_data = calculate_advance_decline_line(stock_prices)

        print(f"Calculated Advance/Decline Line with {len(ad_data)} data points")
        print(f"AD Line range: {ad_data['AD_Line'].min():.2f} to {ad_data['AD_Line'].max():.2f}")

        # Save Advance/Decline Line data
        print("\nSaving Advance/Decline Line data...")
        save_advance_decline_data(ad_data)

        # Plot Advance/Decline Line
        print("\nPlotting Advance/Decline Line...")
        plot_advance_decline_line(ad_data, stock_prices)

        print("\n" + "=" * 50)
        print("Advance/Decline Line calculation completed successfully")
        print("=" * 50)

    except Exception as e:
        print(f"\nError in main function: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("Starting HSI Advance/Decline Line calculation...")
    main()
    print("Script execution completed.")
