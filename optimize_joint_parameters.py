#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
联合优化择时策略的平滑窗口和阈值参数

同时优化smooth_window、exit_threshold和enter_threshold三个参数，
找出最佳的参数组合。
"""

import pandas as pd
import numpy as np
import yfinance as yf
import os
import warnings
from scipy import stats
from statsmodels.tsa.filters.hp_filter import hpfilter
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp
import itertools

warnings.filterwarnings('ignore')

# 设置数据目录
DATA_DIR = 'data'

def rolling_smooth_and_detrend(series, window_size=252, smooth_window=18):
    """在滚动窗口中进行去噪和去趋势处理"""
    result = pd.Series(index=series.index, dtype=float)

    for i in range(len(series)):
        start_idx = max(0, i - window_size + 1)
        end_idx = i + 1
        window_data = series.iloc[start_idx:end_idx].dropna()

        if len(window_data) >= 30:
            # 先去噪
            smoothed_window = window_data.rolling(window=smooth_window, center=True).mean()
            smoothed_window = smoothed_window.fillna(method='bfill').fillna(method='ffill')

            # 后去趋势
            try:
                cycle, _ = hpfilter(smoothed_window, lamb=7000000)
                detrended_value = cycle.iloc[-1]
            except Exception:
                x = np.arange(len(smoothed_window))
                coeffs = np.polyfit(x, smoothed_window.values, 1)
                trend_line = np.polyval(coeffs, x)
                detrended = smoothed_window.values - trend_line
                detrended_value = detrended[-1]

            result.iloc[i] = detrended_value
        else:
            if len(window_data) >= smooth_window:
                smoothed_value = window_data.tail(smooth_window).mean()
                result.iloc[i] = smoothed_value - window_data.mean()
            elif len(window_data) > 0:
                result.iloc[i] = window_data.iloc[-1] - window_data.mean()

    return result

def calculate_rolling_percentiles(data, window=252, smooth_window=18):
    """计算滚动分位数"""
    indicators = ['HY_OAS', 'TIPS_5Y', 'EPU']
    percentile_data = pd.DataFrame(index=data.index)

    for indicator in indicators:
        if indicator in data.columns:
            processed_series = rolling_smooth_and_detrend(data[indicator], 
                                                        window_size=window, 
                                                        smooth_window=smooth_window)
            
            def rolling_percentile(series, window_size):
                result = pd.Series(index=series.index, dtype=float)
                for i in range(len(series)):
                    start_idx = max(0, i - window_size + 1)
                    end_idx = i + 1
                    window_data = series.iloc[start_idx:end_idx].dropna()
                    
                    if len(window_data) >= window_size // 2:
                        current_value = window_data.iloc[-1]
                        percentile = stats.percentileofscore(window_data, current_value) / 100.0
                        result.iloc[i] = percentile
                return result

            percentile_data[f'{indicator}_percentile'] = rolling_percentile(processed_series, window)

    return percentile_data

def create_timing_indicator(percentile_data):
    """创建择时指标"""
    timing_data = percentile_data.copy()
    
    timing_data['HY_OAS_adjusted'] = 1 - timing_data['HY_OAS_percentile']
    timing_data['TIPS_5Y_adjusted'] = timing_data['TIPS_5Y_percentile']
    timing_data['EPU_adjusted'] = 1 - timing_data['EPU_percentile']
    
    timing_data['Market_Timing_Indicator'] = (
        timing_data['HY_OAS_adjusted'] +
        timing_data['TIPS_5Y_adjusted'] +
        timing_data['EPU_adjusted']
    ) / 3 * 100
    
    return timing_data.dropna()

def backtest_with_parameters(args):
    """使用指定参数进行回测的函数，用于并行化"""
    smooth_window, exit_threshold, enter_threshold, combined_data, sp500_prices = args
    
    try:
        # 计算择时指标
        percentile_data = calculate_rolling_percentiles(combined_data, smooth_window=smooth_window)
        timing_data = create_timing_indicator(percentile_data)
        timing_indicator = timing_data['Market_Timing_Indicator']
        
        # 对齐数据 - 从2020年开始
        start_date = pd.to_datetime('2020-01-01')
        common_dates = sp500_prices.index.intersection(timing_indicator.index)
        common_dates = common_dates[common_dates >= start_date]
        
        if len(common_dates) < 500:  # 至少需要500个交易日
            return None
        
        indicator_aligned = timing_indicator.reindex(common_dates, method='ffill')
        prices_aligned = sp500_prices.reindex(common_dates, method='ffill')
        
        # 生成交易信号
        signals = pd.Series(index=common_dates, dtype=float)
        current_position = 1.0
        
        for date in common_dates:
            indicator_value = indicator_aligned[date]
            if pd.isna(indicator_value):
                signals[date] = current_position
                continue
                
            if indicator_value < exit_threshold:
                current_position = 0.0
            elif indicator_value > enter_threshold:
                current_position = 1.0
            
            signals[date] = current_position
        
        # 计算收益率
        returns = prices_aligned.pct_change().fillna(0)
        strategy_returns = signals.shift(1).fillna(1.0) * returns
        buy_hold_returns = returns
        
        # 计算绩效指标
        def calc_metrics(ret_series):
            total_days = len(ret_series)
            total_years = total_days / 252
            total_return = (1 + ret_series).prod() - 1
            annual_return = (1 + total_return) ** (1/total_years) - 1
            annual_vol = ret_series.std() * np.sqrt(252)
            sharpe = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0
            
            cumret = (1 + ret_series).cumprod()
            drawdown = (cumret / cumret.expanding().max() - 1)
            max_dd = drawdown.min()
            
            return {
                'annual_return': annual_return,
                'annual_volatility': annual_vol,
                'sharpe_ratio': sharpe,
                'max_drawdown': max_dd,
                'cash_days': (signals == 0).sum(),
                'cash_ratio': (signals == 0).sum() / len(signals)
            }
        
        strategy_metrics = calc_metrics(strategy_returns)
        buy_hold_metrics = calc_metrics(buy_hold_returns)
        
        return {
            'smooth_window': smooth_window,
            'exit_threshold': exit_threshold,
            'enter_threshold': enter_threshold,
            'strategy_annual_return': strategy_metrics['annual_return'],
            'strategy_volatility': strategy_metrics['annual_volatility'],
            'strategy_sharpe': strategy_metrics['sharpe_ratio'],
            'strategy_max_dd': strategy_metrics['max_drawdown'],
            'buy_hold_annual_return': buy_hold_metrics['annual_return'],
            'buy_hold_sharpe': buy_hold_metrics['sharpe_ratio'],
            'excess_return': strategy_metrics['annual_return'] - buy_hold_metrics['annual_return'],
            'excess_sharpe': strategy_metrics['sharpe_ratio'] - buy_hold_metrics['sharpe_ratio'],
            'cash_days': strategy_metrics['cash_days'],
            'cash_ratio': strategy_metrics['cash_ratio']
        }
        
    except Exception as e:
        print(f"参数组合 ({smooth_window}, {exit_threshold}, {enter_threshold}) 测试失败: {e}")
        return None

def optimize_joint_parameters():
    """联合优化平滑窗口和阈值参数"""
    print("开始联合优化平滑窗口和阈值参数...")
    
    # 1. 加载数据
    print("1. 加载数据...")
    
    # 加载原始数据
    hy_oas = pd.read_csv(os.path.join(DATA_DIR, 'hy_oas.csv'), index_col=0, parse_dates=True)
    tips_inflation = pd.read_csv(os.path.join(DATA_DIR, 'tips_5y_inflation.csv'), index_col=0, parse_dates=True)
    epu_index = pd.read_csv(os.path.join(DATA_DIR, 'daily_epu_index.csv'), index_col=0, parse_dates=True)
    
    # 数据预处理
    hy_oas_clean = hy_oas[['HY_OAS']].copy()
    tips_clean = tips_inflation.copy()
    tips_clean.columns = ['TIPS_5Y']
    epu_clean = epu_index.copy()
    epu_clean.columns = ['EPU']
    
    # 合并数据
    start_date = max(hy_oas_clean.index.min(), tips_clean.index.min(), epu_clean.index.min())
    end_date = min(hy_oas_clean.index.max(), tips_clean.index.max(), epu_clean.index.max())
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    combined_data = pd.DataFrame({
        'HY_OAS': hy_oas_clean.reindex(date_range, method='ffill')['HY_OAS'],
        'TIPS_5Y': tips_clean.reindex(date_range, method='ffill')['TIPS_5Y'],
        'EPU': epu_clean.reindex(date_range, method='ffill')['EPU']
    }).dropna()
    
    # 下载标普500数据
    sp500_data = yf.download('^GSPC', start='2000-01-01', progress=False)
    if isinstance(sp500_data.columns, pd.MultiIndex):
        sp500_data.columns = sp500_data.columns.droplevel(1)
    sp500_prices = sp500_data['Adj Close'] if 'Adj Close' in sp500_data.columns else sp500_data['Close']
    
    print(f"   数据加载完成，共 {len(combined_data)} 条记录")
    
    # 2. 设计参数测试范围
    print("\n2. 设计参数测试范围...")
    
    # 基于之前的优化结果，缩小搜索范围
    smooth_windows = [12, 15, 18, 20, 22]  # 重点测试18附近
    exit_thresholds = [5, 8, 10, 12]  # 重点测试8附近
    enter_thresholds = [30, 35, 40, 45]  # 重点测试40附近
    
    # 生成所有有效的参数组合
    param_combinations = []
    for smooth_window, exit_th, enter_th in itertools.product(smooth_windows, exit_thresholds, enter_thresholds):
        if exit_th < enter_th:  # 确保逻辑正确
            param_combinations.append((smooth_window, exit_th, enter_th))
    
    print(f"   将测试 {len(param_combinations)} 个参数组合")
    print(f"   平滑窗口范围: {smooth_windows}")
    print(f"   空仓阈值范围: {exit_thresholds}")
    print(f"   满仓阈值范围: {enter_thresholds}")
    
    # 3. 并行化测试
    print("\n3. 并行化测试参数组合...")
    
    # 准备并行化参数
    args_list = [(sw, exit_th, enter_th, combined_data, sp500_prices) 
                 for sw, exit_th, enter_th in param_combinations]
    
    # 使用进程池并行化
    max_workers = min(mp.cpu_count(), len(param_combinations))
    print(f"   使用 {max_workers} 个进程并行计算...")
    
    results = []
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_params = {executor.submit(backtest_with_parameters, args): args[:3] 
                           for args in args_list}
        
        # 收集结果
        completed = 0
        for future in as_completed(future_to_params):
            sw, exit_th, enter_th = future_to_params[future]
            completed += 1
            
            try:
                result = future.result()
                if result is not None:
                    results.append(result)
                    print(f"   ✓ 参数 ({sw:2d},{exit_th:2d},{enter_th:2d}) ({completed:2d}/{len(param_combinations)}) "
                          f"夏普={result['strategy_sharpe']:.3f}, 超额收益={result['excess_return']:.2%}")
                else:
                    print(f"   ✗ 参数 ({sw:2d},{exit_th:2d},{enter_th:2d}) ({completed:2d}/{len(param_combinations)}) 回测失败")
            except Exception as e:
                print(f"   ✗ 参数 ({sw:2d},{exit_th:2d},{enter_th:2d}) ({completed:2d}/{len(param_combinations)}) 错误: {str(e)[:30]}")
                continue
    
    # 4. 分析结果
    print(f"\n4. 分析结果...")
    
    if not results:
        print("   没有成功的测试结果")
        return None
    
    results_df = pd.DataFrame(results)
    
    # 保存详细结果
    output_file = os.path.join(DATA_DIR, 'joint_optimization_results.csv')
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"   详细结果已保存到: {output_file}")
    
    return results_df

def analyze_joint_results(results_df):
    """分析联合优化结果"""
    print("\n" + "="*80)
    print("联合参数优化结果分析")
    print("="*80)
    
    # 找出各项指标的最优值
    best_sharpe = results_df.loc[results_df['strategy_sharpe'].idxmax()]
    best_excess_return = results_df.loc[results_df['excess_return'].idxmax()]
    min_drawdown = results_df.loc[results_df['strategy_max_dd'].idxmax()]
    
    print(f"最佳夏普比率: 参数=({best_sharpe['smooth_window']:.0f},{best_sharpe['exit_threshold']:.0f},{best_sharpe['enter_threshold']:.0f}), 夏普比率={best_sharpe['strategy_sharpe']:.3f}")
    print(f"最佳超额收益: 参数=({best_excess_return['smooth_window']:.0f},{best_excess_return['exit_threshold']:.0f},{best_excess_return['enter_threshold']:.0f}), 超额收益={best_excess_return['excess_return']:.2%}")
    print(f"最小回撤: 参数=({min_drawdown['smooth_window']:.0f},{min_drawdown['exit_threshold']:.0f},{min_drawdown['enter_threshold']:.0f}), 最大回撤={min_drawdown['strategy_max_dd']:.2%}")
    
    # 综合评分
    optimal_cash_ratio = 0.08
    cash_ratio_score = 1 - abs(results_df['cash_ratio'] - optimal_cash_ratio) / optimal_cash_ratio
    cash_ratio_score = np.clip(cash_ratio_score, 0, 1)
    
    results_df['composite_score'] = (
        0.4 * (results_df['strategy_sharpe'] / results_df['strategy_sharpe'].max()) +
        0.3 * ((results_df['excess_return'] - results_df['excess_return'].min()) / 
               (results_df['excess_return'].max() - results_df['excess_return'].min())) +
        0.2 * ((results_df['strategy_max_dd'] - results_df['strategy_max_dd'].min()) / 
               (results_df['strategy_max_dd'].max() - results_df['strategy_max_dd'].min())) +
        0.1 * cash_ratio_score
    )
    
    best_composite = results_df.loc[results_df['composite_score'].idxmax()]
    
    print(f"\n综合最佳: 参数=({best_composite['smooth_window']:.0f},{best_composite['exit_threshold']:.0f},{best_composite['enter_threshold']:.0f})")
    print(f"  年化收益率: {best_composite['strategy_annual_return']:.2%}")
    print(f"  夏普比率: {best_composite['strategy_sharpe']:.3f}")
    print(f"  超额收益: {best_composite['excess_return']:.2%}")
    print(f"  最大回撤: {best_composite['strategy_max_dd']:.2%}")
    print(f"  空仓比例: {best_composite['cash_ratio']:.1%}")
    print(f"  综合评分: {best_composite['composite_score']:.3f}")
    
    # 显示前10名
    print(f"\n前10名参数组合:")
    top10 = results_df.nlargest(10, 'composite_score')
    print(f"{'排名':<4} {'参数(SW,Exit,Enter)':<18} {'夏普':<6} {'超额收益':<8} {'最大回撤':<8} {'空仓比例':<8} {'综合评分':<8}")
    print("-" * 70)
    for i, (_, row) in enumerate(top10.iterrows(), 1):
        print(f"{i:<4} ({row['smooth_window']:.0f},{row['exit_threshold']:.0f},{row['enter_threshold']:.0f}){'':<10} "
              f"{row['strategy_sharpe']:.3f}  {row['excess_return']:>7.2%} "
              f"{row['strategy_max_dd']:>7.2%} {row['cash_ratio']:>7.1%} "
              f"{row['composite_score']:>7.3f}")
    
    return int(best_composite['smooth_window']), int(best_composite['exit_threshold']), int(best_composite['enter_threshold'])

if __name__ == "__main__":
    # 运行优化
    results_df = optimize_joint_parameters()
    
    if results_df is not None:
        # 分析结果
        best_smooth, best_exit, best_enter = analyze_joint_results(results_df)
        print(f"\n推荐使用参数: smooth_window = {best_smooth}, exit_threshold = {best_exit}, enter_threshold = {best_enter}")
    else:
        print("优化失败")
