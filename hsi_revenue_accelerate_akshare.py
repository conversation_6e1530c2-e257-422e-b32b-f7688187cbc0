#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
从恒生指数成分股中筛选出营收加速增长的公司

使用akshare获取财务数据，分析营收增长趋势，筛选出营收加速增长的公司
"""

import os
import sys
import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from datetime import datetime
import time
import logging
from tqdm import tqdm
import akshare as ak

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("hsi_revenue_accelerate_akshare.log"),
        logging.StreamHandler(sys.stdout)
    ]
)
logger = logging.getLogger(__name__)

# 创建输出目录
OUTPUT_DIR = "output/hsi_revenue_accelerate_akshare"
os.makedirs(OUTPUT_DIR, exist_ok=True)

def load_hsi_constituents(file_path="data_files/hsi_constituents.csv"):
    """从CSV文件加载恒生指数成分股"""
    try:
        # 确保代码列被读取为字符串类型
        df = pd.read_csv(file_path, dtype={'代码': str})

        # 确保代码列的格式正确（5位数字，前导零）
        if '代码' in df.columns:
            df['代码'] = df['代码'].apply(lambda x: str(x).zfill(5))

        logger.info(f"从{file_path}加载了{len(df)}只恒生指数成分股")
        return df
    except Exception as e:
        logger.error(f"加载恒生指数成分股失败: {str(e)}")
        return None

def get_financial_data(stock_code, stock_name):
    """使用akshare获取单只股票的财务数据"""
    try:
        logger.info(f"获取 {stock_code} ({stock_name}) 的财务数据")

        # 对于港股，需要使用特定的接口
        # 使用akshare的接口获取港股财务数据
        # 尝试获取利润表数据
        try:
            # 获取利润表
            income_statement = ak.stock_financial_hk_report_em(
                stock=stock_code,
                symbol="利润表",
                indicator="报告期"
            )

            # 保存原始数据
            income_statement.to_csv(os.path.join(OUTPUT_DIR, f"{stock_code}_income_statement.csv"), encoding='utf-8-sig')
            logger.info(f"成功获取 {stock_code} 的利润表数据，形状: {income_statement.shape}")

            # 处理数据
            return process_income_statement(income_statement, stock_code, stock_name)

        except Exception as e:
            logger.warning(f"获取 {stock_code} 的利润表数据失败: {str(e)}")

            # 尝试获取财务指标数据
            try:
                # 获取财务指标
                financial_indicator = ak.stock_financial_hk_analysis_indicator_em(
                    symbol=stock_code,
                    indicator="报告期"
                )

                # 保存原始数据
                financial_indicator.to_csv(os.path.join(OUTPUT_DIR, f"{stock_code}_financial_indicator.csv"), encoding='utf-8-sig')
                logger.info(f"成功获取 {stock_code} 的财务指标数据，形状: {financial_indicator.shape}")

                # 处理数据
                return process_financial_indicator(financial_indicator, stock_code, stock_name)

            except Exception as e2:
                logger.warning(f"获取 {stock_code} 的财务指标数据失败: {str(e2)}")
                return None

    except Exception as e:
        logger.error(f"获取 {stock_code} 的财务数据时出错: {str(e)}")
        return None

def process_income_statement(income_statement, stock_code, stock_name):
    """处理利润表数据，提取营收信息"""
    try:
        # 检查数据是否为空
        if income_statement is None or income_statement.empty:
            logger.warning(f"{stock_code} 的利润表数据为空")
            return None

        # 打印列名，帮助调试
        logger.debug(f"{stock_code} 利润表列名: {income_statement.columns.tolist()}")

        # 查找营业收入相关的行
        revenue_rows = income_statement[income_statement['报表项目'].str.contains('营业收入|总收入|收入总额|营业总收入', na=False)]

        if revenue_rows.empty:
            logger.warning(f"在 {stock_code} 的利润表中未找到营业收入相关行")
            return None

        # 获取第一个匹配的行（通常是营业收入）
        revenue_row = revenue_rows.iloc[0]

        # 创建结果列表
        result_rows = []

        # 遍历每一列（每一个报告期）
        for col in income_statement.columns:
            # 跳过非日期列
            if col == '报表项目':
                continue

            try:
                # 尝试将列名转换为日期
                report_date = pd.to_datetime(col)

                # 获取该报告期的营收
                revenue = revenue_row[col]

                # 如果是字符串，尝试转换为数值
                if isinstance(revenue, str):
                    # 移除可能的逗号和其他非数字字符
                    revenue = float(revenue.replace(',', '').replace(' ', ''))

                # 创建结果行
                result_row = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'report_date': report_date,
                    'revenue': revenue
                }

                result_rows.append(result_row)

            except Exception as e:
                logger.warning(f"处理 {stock_code} 的 {col} 报告期数据时出错: {str(e)}")
                continue

        # 检查是否成功提取到数据
        if not result_rows:
            logger.warning(f"未能从 {stock_code} 的利润表中提取到有效的营收数据")
            return None

        # 创建DataFrame
        df = pd.DataFrame(result_rows)

        # 添加年份和季度信息
        df['year'] = df['report_date'].dt.year
        df['month'] = df['report_date'].dt.month
        df['quarter'] = df['month'].apply(lambda x: (x-1)//3 + 1)
        df['year_quarter'] = df['year'].astype(str) + '-Q' + df['quarter'].astype(str)

        # 按日期排序
        df = df.sort_values('report_date')

        return df

    except Exception as e:
        logger.error(f"处理 {stock_code} 的利润表数据时出错: {str(e)}")
        return None

def process_financial_indicator(financial_indicator, stock_code, stock_name):
    """处理财务指标数据，提取营收信息"""
    try:
        # 检查数据是否为空
        if financial_indicator is None or financial_indicator.empty:
            logger.warning(f"{stock_code} 的财务指标数据为空")
            return None

        # 打印列名，帮助调试
        logger.debug(f"{stock_code} 财务指标列名: {financial_indicator.columns.tolist()}")

        # 查找包含日期的列和营收相关的列
        date_col = None
        revenue_col = None

        for col in financial_indicator.columns:
            if '日期' in col or 'date' in col.lower():
                date_col = col
            if '营业收入' in col or '总收入' in col or 'revenue' in col.lower():
                revenue_col = col

        if date_col is None or revenue_col is None:
            logger.warning(f"在 {stock_code} 的财务指标中未找到日期列或营收列")
            return None

        # 创建结果列表
        result_rows = []

        # 遍历每一行（每一个报告期）
        for _, row in financial_indicator.iterrows():
            try:
                # 获取报告日期
                report_date = pd.to_datetime(row[date_col])

                # 获取营收
                revenue = row[revenue_col]

                # 如果是字符串，尝试转换为数值
                if isinstance(revenue, str):
                    # 移除可能的逗号和其他非数字字符
                    revenue = float(revenue.replace(',', '').replace(' ', ''))

                # 创建结果行
                result_row = {
                    'stock_code': stock_code,
                    'stock_name': stock_name,
                    'report_date': report_date,
                    'revenue': revenue
                }

                result_rows.append(result_row)

            except Exception as e:
                logger.warning(f"处理 {stock_code} 的财务指标行时出错: {str(e)}")
                continue

        # 检查是否成功提取到数据
        if not result_rows:
            logger.warning(f"未能从 {stock_code} 的财务指标中提取到有效的营收数据")
            return None

        # 创建DataFrame
        df = pd.DataFrame(result_rows)

        # 添加年份和季度信息
        df['year'] = df['report_date'].dt.year
        df['month'] = df['report_date'].dt.month
        df['quarter'] = df['month'].apply(lambda x: (x-1)//3 + 1)
        df['year_quarter'] = df['year'].astype(str) + '-Q' + df['quarter'].astype(str)

        # 按日期排序
        df = df.sort_values('report_date')

        return df

    except Exception as e:
        logger.error(f"处理 {stock_code} 的财务指标数据时出错: {str(e)}")
        return None

def calculate_growth_rates(df):
    """计算同比增长率和加速度"""
    try:
        # 确保数据按日期排序
        df = df.sort_values('report_date')

        # 初始化增长率列
        df['revenue_yoy'] = np.nan
        df['revenue_acceleration'] = np.nan

        # 按季度分组
        for quarter in range(1, 5):
            quarter_data = df[df['quarter'] == quarter].copy()
            quarter_data = quarter_data.sort_values('year')

            # 计算同比增长率
            for i in range(1, len(quarter_data)):
                current_year = quarter_data.iloc[i]['year']
                prev_year = quarter_data.iloc[i-1]['year']

                # 确保是相邻年份
                if current_year == prev_year + 1:
                    # 营收同比增长率
                    current_revenue = quarter_data.iloc[i]['revenue']
                    prev_revenue = quarter_data.iloc[i-1]['revenue']

                    if prev_revenue and prev_revenue != 0:
                        revenue_yoy = (current_revenue - prev_revenue) / prev_revenue * 100
                        df.loc[quarter_data.iloc[i].name, 'revenue_yoy'] = revenue_yoy

        # 计算增长率的加速度（增长率的变化）
        for quarter in range(1, 5):
            quarter_data = df[df['quarter'] == quarter].copy()
            quarter_data = quarter_data.sort_values('year')

            # 需要至少两个增长率数据点才能计算加速度
            if 'revenue_yoy' in quarter_data.columns and len(quarter_data.dropna(subset=['revenue_yoy'])) >= 2:
                growth_rates = quarter_data['revenue_yoy'].dropna()

                for i in range(1, len(growth_rates)):
                    current_growth = growth_rates.iloc[i]
                    prev_growth = growth_rates.iloc[i-1]

                    # 计算加速度（增长率的变化）
                    acceleration = current_growth - prev_growth
                    df.loc[growth_rates.index[i], 'revenue_acceleration'] = acceleration

        return df

    except Exception as e:
        logger.error(f"计算增长率时出错: {str(e)}")
        return df

def is_accelerating(df, consecutive_periods=2):
    """判断是否连续加速增长"""
    try:
        # 确保数据按日期排序
        df = df.sort_values('report_date')

        # 获取加速度数据
        acceleration_data = df['revenue_acceleration'].dropna()

        # 检查是否有足够的数据点
        if len(acceleration_data) < consecutive_periods:
            return False

        # 检查最近几期是否连续为正（增长加速）
        recent_acceleration = acceleration_data.tail(consecutive_periods)
        is_accelerating = all(recent_acceleration > 0)

        return is_accelerating

    except Exception as e:
        logger.error(f"判断是否加速增长时出错: {str(e)}")
        return False

def analyze_stock(stock_code, stock_name):
    """分析单只股票的营收增长情况"""
    try:
        # 获取财务数据
        financial_data = get_financial_data(stock_code, stock_name)

        if financial_data is None or financial_data.empty:
            logger.warning(f"无法获取 {stock_code} 的财务数据")
            return None

        # 计算增长率和加速度
        growth_data = calculate_growth_rates(financial_data)

        # 保存处理后的数据
        growth_data.to_csv(os.path.join(OUTPUT_DIR, f"{stock_code}_growth_data.csv"), encoding='utf-8-sig')

        # 判断是否加速增长
        accelerating = is_accelerating(growth_data)

        # 获取最新的增长率和加速度
        latest_data = growth_data.sort_values('report_date').tail(1)
        latest_growth = latest_data['revenue_yoy'].iloc[0] if 'revenue_yoy' in latest_data.columns and not latest_data['revenue_yoy'].isna().all() else None
        latest_acceleration = latest_data['revenue_acceleration'].iloc[0] if 'revenue_acceleration' in latest_data.columns and not latest_data['revenue_acceleration'].isna().all() else None

        # 返回分析结果
        result = {
            'stock_code': stock_code,
            'stock_name': stock_name,
            'latest_growth': latest_growth,
            'latest_acceleration': latest_acceleration,
            'is_accelerating': accelerating,
            'data': growth_data
        }

        return result

    except Exception as e:
        logger.error(f"分析 {stock_code} 时出错: {str(e)}")
        return None

def main():
    """主函数"""
    logger.info("开始分析恒生指数成分股的营收增长情况")

    # 加载恒生指数成分股
    constituents_df = load_hsi_constituents()
    if constituents_df is None:
        logger.error("加载恒生指数成分股失败，退出程序")
        return

    # 测试单只股票
    test_stock = True
    if test_stock:
        # 测试单只股票 - 腾讯控股
        stock_code = "00700"
        stock_name = "腾讯控股"

        logger.info(f"测试单只股票: {stock_code} {stock_name}")
        result = analyze_stock(stock_code, stock_name)

        if result is not None:
            logger.info(f"分析结果: 最新增长率={result['latest_growth']:.2f}%, 增长加速度={result['latest_acceleration']:.2f}%, 是否加速增长={result['is_accelerating']}")
        else:
            logger.warning(f"无法分析 {stock_code}")

        return

    # 分析所有股票
    results = []

    # 遍历所有股票
    for _, row in tqdm(constituents_df.iterrows(), total=len(constituents_df), desc="分析进度"):
        stock_code = row['代码']
        stock_name = row['名称']

        # 分析股票
        result = analyze_stock(stock_code, stock_name)

        # 添加到结果列表
        if result is not None:
            results.append(result)

        # 添加延迟以避免API限制
        time.sleep(1)

    # 筛选出营收加速增长的公司
    accelerating_companies = [r for r in results if r['is_accelerating']]

    # 按最新增长率排序
    accelerating_companies = sorted(accelerating_companies, key=lambda x: x['latest_growth'] if x['latest_growth'] is not None else -float('inf'), reverse=True)

    # 输出结果
    logger.info(f"共分析了 {len(results)} 只股票，其中 {len(accelerating_companies)} 只股票营收呈现加速增长趋势")

    # 创建结果DataFrame
    result_df = pd.DataFrame([
        {
            '代码': r['stock_code'],
            '名称': r['stock_name'],
            '最新增长率': r['latest_growth'],
            '增长加速度': r['latest_acceleration'],
            '连续加速增长': r['is_accelerating']
        }
        for r in accelerating_companies
    ])

    # 保存结果
    result_file = os.path.join(OUTPUT_DIR, "hsi_accelerating_companies.csv")
    result_df.to_csv(result_file, index=False, encoding='utf-8-sig')
    logger.info(f"已保存结果到 {result_file}")

    # 打印结果
    if not result_df.empty:
        pd.set_option('display.max_rows', None)
        pd.set_option('display.width', 1000)
        pd.set_option('display.float_format', '{:.2f}%'.format)
        print("\n营收加速增长的公司:")
        print(result_df)
    else:
        print("没有找到营收加速增长的公司")

if __name__ == "__main__":
    main()
