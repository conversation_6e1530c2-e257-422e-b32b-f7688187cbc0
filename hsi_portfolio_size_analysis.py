#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股智能股息率因子 - 持仓数量优化分析
分析不同持仓数量对投资组合收益和风险的影响
"""

import os
import json
import numpy as np
import pandas as pd
import matplotlib.pyplot as plt
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class HSIPortfolioSizeAnalyzer:
    """恒生指数股息率因子持仓数量优化分析器"""
    
    def __init__(self, data_dir: str = "hsi_smart_dividend_yields"):
        self.data_dir = data_dir
        self.stock_data = {}
        self.analysis_results = {}
        
        print("🔍 恒生指数股息率因子持仓数量优化分析器")
        print("=" * 60)
    
    def load_stock_data(self) -> bool:
        """加载股票数据"""
        try:
            print("📊 加载股票数据...")
            
            # 找到最新的数据文件
            csv_files = []
            for filename in os.listdir(self.data_dir):
                if filename.endswith('.csv') and '_smart_dividend_yield_' in filename and not filename.startswith('hsi_'):
                    csv_files.append(filename)
            
            # 按时间戳分组，每个股票选择最新的文件
            stock_latest_files = {}
            for filename in csv_files:
                parts = filename.split('_')
                if len(parts) >= 5:
                    stock_code = parts[0]
                    stock_name = parts[1]
                    timestamp = parts[4] + '_' + parts[5].replace('.csv', '')
                    
                    if stock_code not in stock_latest_files or timestamp > stock_latest_files[stock_code]['timestamp']:
                        stock_latest_files[stock_code] = {
                            'filename': filename,
                            'stock_name': stock_name,
                            'timestamp': timestamp
                        }
            
            loaded_count = 0
            for stock_code, file_info in stock_latest_files.items():
                filename = file_info['filename']
                stock_name = file_info['stock_name']
                
                file_path = os.path.join(self.data_dir, filename)
                stock_df = self._load_stock_file(file_path, stock_code, stock_name)
                
                if stock_df is not None and len(stock_df) > 0:
                    self.stock_data[stock_code] = {
                        'name': stock_name,
                        'data': stock_df
                    }
                    loaded_count += 1
            
            print(f"✅ 成功加载 {loaded_count} 只股票的数据")
            return loaded_count > 0
            
        except Exception as e:
            print(f"❌ 加载数据失败: {e}")
            return False
    
    def _load_stock_file(self, file_path: str, stock_code: str, stock_name: str) -> Optional[pd.DataFrame]:
        """加载单个股票文件"""
        try:
            df = pd.read_csv(file_path, encoding='utf-8-sig')
            
            required_cols = ['日期', '股价(港元)', '智能股息率(%)', '智能年度分红(港元)']
            if not all(col in df.columns for col in required_cols):
                return None
            
            df['日期'] = pd.to_datetime(df['日期'])
            df = df.sort_values('日期')
            df.set_index('日期', inplace=True)
            df = df.dropna(subset=['股价(港元)', '智能股息率(%)'])
            
            df['price'] = df['股价(港元)']
            df['dividend_yield'] = df['智能股息率(%)']
            df['annual_dividend'] = df['智能年度分红(港元)']
            
            return df
            
        except Exception as e:
            return None
    
    def calculate_total_return_with_dividends(self, stock_code: str, start_date: datetime, end_date: datetime) -> float:
        """计算包含分红的总收益率"""
        if stock_code not in self.stock_data:
            return 0.0
        
        df = self.stock_data[stock_code]['data']
        period_data = df[(df.index >= start_date) & (df.index <= end_date)]
        
        if len(period_data) < 2:
            return 0.0
        
        start_price = period_data['price'].iloc[0]
        end_price = period_data['price'].iloc[-1]
        
        if pd.isna(start_price) or pd.isna(end_price) or start_price <= 0:
            return 0.0
        
        # 资本利得收益率
        capital_gain_return = (end_price - start_price) / start_price
        
        # 分红收益率
        dividend_return = 0.0
        period_days = (end_date - start_date).days
        
        if period_days > 0:
            mid_date = start_date + timedelta(days=period_days // 2)
            mid_data = period_data[period_data.index <= mid_date]
            if len(mid_data) > 0:
                latest_mid_data = mid_data.iloc[-1]
                annual_dividend = latest_mid_data.get('annual_dividend', 0)
                
                if pd.notna(annual_dividend) and annual_dividend > 0:
                    period_dividend = annual_dividend * (period_days / 365)
                    dividend_return = period_dividend / start_price
        
        return capital_gain_return + dividend_return
    
    def analyze_portfolio_sizes(self, portfolio_sizes: List[int], 
                               start_date: str = "2021-02-12", 
                               end_date: str = "2025-03-31") -> Dict:
        """分析不同持仓数量的投资组合表现"""
        print(f"📈 分析不同持仓数量的投资组合表现...")
        print(f"📅 分析期间: {start_date} 至 {end_date}")
        
        start_dt = pd.to_datetime(start_date)
        end_dt = pd.to_datetime(end_date)
        
        # 获取基准日期的股息率排名
        base_date = start_dt
        factor_scores = self.calculate_factor_scores(base_date)
        
        if factor_scores.empty:
            print("❌ 无法获取因子得分")
            return {}
        
        # 按股息率排序
        sorted_scores = factor_scores.sort_values(ascending=False)
        
        results = {}
        
        for size in portfolio_sizes:
            print(f"🔄 分析持仓 {size} 只股票的组合...")
            
            # 选择前N只高股息率股票
            if size > len(sorted_scores):
                selected_stocks = sorted_scores.index.tolist()
            else:
                selected_stocks = sorted_scores.head(size).index.tolist()
            
            # 计算组合收益
            portfolio_returns = []
            stock_returns = []
            
            for stock_code in selected_stocks:
                stock_return = self.calculate_total_return_with_dividends(
                    stock_code, start_dt, end_dt
                )
                if stock_return != 0.0:
                    stock_returns.append(stock_return)
            
            if stock_returns:
                # 等权重组合收益
                portfolio_return = np.mean(stock_returns)
                portfolio_std = np.std(stock_returns)
                
                # 计算其他指标
                sharpe_ratio = portfolio_return / portfolio_std if portfolio_std > 0 else 0
                
                # 年化收益率（假设持有期约4年）
                holding_years = (end_dt - start_dt).days / 365
                annual_return = (1 + portfolio_return) ** (1/holding_years) - 1
                annual_std = portfolio_std * np.sqrt(252/holding_years)  # 简化年化波动率
                annual_sharpe = annual_return / annual_std if annual_std > 0 else 0
                
                # 平均股息率
                avg_dividend_yield = factor_scores[selected_stocks].mean()
                
                results[size] = {
                    'portfolio_return': portfolio_return,
                    'annual_return': annual_return,
                    'portfolio_std': portfolio_std,
                    'annual_std': annual_std,
                    'sharpe_ratio': annual_sharpe,
                    'avg_dividend_yield': avg_dividend_yield,
                    'stock_count': len(stock_returns),
                    'selected_stocks': selected_stocks,
                    'individual_returns': stock_returns
                }
                
                print(f"   持仓 {size} 只: 年化收益 {annual_return*100:.2f}%, 夏普比率 {annual_sharpe:.3f}, 平均股息率 {avg_dividend_yield:.2f}%")
        
        return results
    
    def calculate_factor_scores(self, date: datetime) -> pd.Series:
        """计算指定日期的股息率因子得分"""
        scores = {}
        
        for stock_code, stock_info in self.stock_data.items():
            df = stock_info['data']
            available_dates = df.index[df.index <= date]
            
            if len(available_dates) > 0:
                latest_date = available_dates[-1]
                dividend_yield = df.loc[latest_date, 'dividend_yield']
                
                if pd.notna(dividend_yield) and dividend_yield >= 0:
                    scores[stock_code] = dividend_yield
        
        return pd.Series(scores)

    def plot_portfolio_size_analysis(self, results: Dict, save_path: str = "hsi_portfolio_size_analysis.png"):
        """绘制持仓数量分析图表"""
        if not results:
            print("❌ 没有分析结果可供绘制")
            return

        print("📊 绘制持仓数量分析图表...")

        # 准备数据
        sizes = sorted(results.keys())
        annual_returns = [results[size]['annual_return'] * 100 for size in sizes]
        sharpe_ratios = [results[size]['sharpe_ratio'] for size in sizes]
        annual_stds = [results[size]['annual_std'] * 100 for size in sizes]
        avg_yields = [results[size]['avg_dividend_yield'] for size in sizes]

        # 创建图表
        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('恒生指数高股息率组合持仓数量优化分析', fontsize=16, fontweight='bold')

        # 子图1: 年化收益率 vs 持仓数量
        ax1 = axes[0, 0]
        ax1.plot(sizes, annual_returns, 'bo-', linewidth=2, markersize=8)
        ax1.set_title('年化收益率 vs 持仓数量')
        ax1.set_xlabel('持仓股票数量')
        ax1.set_ylabel('年化收益率 (%)')
        ax1.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (size, ret) in enumerate(zip(sizes, annual_returns)):
            ax1.annotate(f'{ret:.1f}%', (size, ret), textcoords="offset points",
                        xytext=(0,10), ha='center')

        # 子图2: 夏普比率 vs 持仓数量
        ax2 = axes[0, 1]
        ax2.plot(sizes, sharpe_ratios, 'ro-', linewidth=2, markersize=8)
        ax2.set_title('夏普比率 vs 持仓数量')
        ax2.set_xlabel('持仓股票数量')
        ax2.set_ylabel('夏普比率')
        ax2.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (size, sharpe) in enumerate(zip(sizes, sharpe_ratios)):
            ax2.annotate(f'{sharpe:.3f}', (size, sharpe), textcoords="offset points",
                        xytext=(0,10), ha='center')

        # 子图3: 年化波动率 vs 持仓数量
        ax3 = axes[1, 0]
        ax3.plot(sizes, annual_stds, 'go-', linewidth=2, markersize=8)
        ax3.set_title('年化波动率 vs 持仓数量')
        ax3.set_xlabel('持仓股票数量')
        ax3.set_ylabel('年化波动率 (%)')
        ax3.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (size, std) in enumerate(zip(sizes, annual_stds)):
            ax3.annotate(f'{std:.1f}%', (size, std), textcoords="offset points",
                        xytext=(0,10), ha='center')

        # 子图4: 平均股息率 vs 持仓数量
        ax4 = axes[1, 1]
        ax4.plot(sizes, avg_yields, 'mo-', linewidth=2, markersize=8)
        ax4.set_title('平均股息率 vs 持仓数量')
        ax4.set_xlabel('持仓股票数量')
        ax4.set_ylabel('平均股息率 (%)')
        ax4.grid(True, alpha=0.3)

        # 添加数值标签
        for i, (size, yield_val) in enumerate(zip(sizes, avg_yields)):
            ax4.annotate(f'{yield_val:.2f}%', (size, yield_val), textcoords="offset points",
                        xytext=(0,10), ha='center')

        plt.tight_layout()
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        print(f"📊 图表已保存: {save_path}")

    def generate_recommendation_report(self, results: Dict):
        """生成持仓数量推荐报告"""
        if not results:
            print("❌ 没有分析结果")
            return

        print("\n" + "="*80)
        print("恒生指数高股息率组合持仓数量优化分析报告")
        print("="*80)
        print(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"分析股票池: 恒生指数成分股")
        print(f"策略: 高股息率股票等权重组合")
        print()

        # 详细结果表格
        print("持仓数量分析结果")
        print("-" * 100)
        print(f"{'持仓数量':<8} {'年化收益':<10} {'年化波动率':<10} {'夏普比率':<10} {'平均股息率':<12} {'实际股票数':<10}")
        print("-" * 100)

        sizes = sorted(results.keys())
        best_sharpe_size = max(sizes, key=lambda x: results[x]['sharpe_ratio'])
        best_return_size = max(sizes, key=lambda x: results[x]['annual_return'])

        for size in sizes:
            res = results[size]
            marker = ""
            if size == best_sharpe_size:
                marker += " 🏆最佳夏普"
            if size == best_return_size:
                marker += " 📈最高收益"

            print(f"{size:<8} {res['annual_return']*100:<10.2f}% {res['annual_std']*100:<10.2f}% "
                  f"{res['sharpe_ratio']:<10.3f} {res['avg_dividend_yield']:<12.2f}% "
                  f"{res['stock_count']:<10}{marker}")

        print()

        # 推荐分析
        print("持仓数量推荐分析")
        print("-" * 50)

        # 找到最佳夏普比率
        best_sharpe = results[best_sharpe_size]
        print(f"🏆 最佳风险调整收益: {best_sharpe_size}只股票")
        print(f"   年化收益: {best_sharpe['annual_return']*100:.2f}%")
        print(f"   夏普比率: {best_sharpe['sharpe_ratio']:.3f}")
        print(f"   年化波动率: {best_sharpe['annual_std']*100:.2f}%")
        print()

        # 分析收益递减效应
        returns_by_size = [(size, results[size]['annual_return']) for size in sizes]
        print("📊 边际收益分析:")
        for i in range(1, len(returns_by_size)):
            prev_size, prev_return = returns_by_size[i-1]
            curr_size, curr_return = returns_by_size[i]
            marginal_return = curr_return - prev_return
            print(f"   {prev_size}→{curr_size}只: 边际收益 {marginal_return*100:+.2f}%")
        print()

        # 给出建议
        print("💡 投资建议:")
        if best_sharpe_size <= 10:
            print(f"   ✅ 推荐持仓 {best_sharpe_size} 只股票（最佳风险调整收益）")
            print(f"   ✅ 集中投资高质量高股息率股票")
        elif best_sharpe_size <= 20:
            print(f"   ✅ 推荐持仓 {best_sharpe_size} 只股票（平衡收益与风险）")
            print(f"   ✅ 适度分散化，保持较高收益")
        else:
            print(f"   ⚠️  持仓 {best_sharpe_size} 只股票可能过度分散")
            print(f"   💡 建议考虑减少至15-20只以提高收益")

        print()
        print("🔍 关键洞察:")
        print("   • 持仓数量过少：个股风险高，波动性大")
        print("   • 持仓数量过多：稀释高股息率优势，收益下降")
        print("   • 最优持仓数量：平衡收益与风险的甜蜜点")


def main():
    """主函数"""
    print("🎯 恒生指数高股息率组合持仓数量优化分析")
    print("=" * 60)

    # 创建分析器
    analyzer = HSIPortfolioSizeAnalyzer()

    # 加载数据
    if not analyzer.load_stock_data():
        print("❌ 数据加载失败")
        return

    # 定义要分析的持仓数量
    portfolio_sizes = [3, 5, 8, 10, 12, 15, 20, 25, 30]

    try:
        # 分析不同持仓数量
        results = analyzer.analyze_portfolio_sizes(portfolio_sizes)

        if results:
            # 绘制图表
            analyzer.plot_portfolio_size_analysis(results)

            # 生成报告
            analyzer.generate_recommendation_report(results)

            print(f"\n🎉 持仓数量优化分析完成！")
            print(f"📊 图表已保存: hsi_portfolio_size_analysis.png")
        else:
            print("❌ 分析失败，请检查数据")

    except Exception as e:
        print(f"❌ 分析过程出错: {e}")
        import traceback
        traceback.print_exc()


if __name__ == "__main__":
    main()
