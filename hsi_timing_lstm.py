"""
Hang Seng Index Timing Strategy using LSTM with OECD and Volume-Price Data

This module implements an LSTM-based timing strategy for the Hang Seng Index
using OECD China Leading Indicator data and volume-price data.
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import precision_score, recall_score
import torch
import torch.nn as nn
import torch.optim as optim
from torch.utils.data import Dataset, DataLoader
import os
import logging
from datetime import datetime
import warnings
from openfe import OpenFE
warnings.filterwarnings('ignore')

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Set device for PyTorch
device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
logger.info(f"Using device: {device}")

# Set plotting style
sns.set_theme(style="whitegrid")
plt.rcParams['figure.figsize'] = (14, 8)

class TimeSeriesDataset(Dataset):
    """
    PyTorch Dataset for time series data
    """
    def __init__(self, X, y):
        self.X = torch.tensor(X, dtype=torch.float32)
        self.y = torch.tensor(y, dtype=torch.float32)

    def __len__(self):
        return len(self.X)

    def __getitem__(self, idx):
        return self.X[idx], self.y[idx]


class LSTMModel(nn.Module):
    """
    LSTM model for time series prediction
    """
    def __init__(self, input_size, hidden_size, num_layers, output_size, dropout_rate=0.2):
        super(LSTMModel, self).__init__()
        self.hidden_size = hidden_size
        self.num_layers = num_layers

        # LSTM layers
        self.lstm = nn.LSTM(
            input_size=input_size,
            hidden_size=hidden_size,
            num_layers=num_layers,
            batch_first=True,
            dropout=dropout_rate if num_layers > 1 else 0
        )

        # Fully connected layer
        self.fc = nn.Linear(hidden_size, output_size)

        # Sigmoid activation
        self.sigmoid = nn.Sigmoid()

    def forward(self, x):
        # Initialize hidden state and cell state
        h0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(device)
        c0 = torch.zeros(self.num_layers, x.size(0), self.hidden_size).to(device)

        # Forward propagate LSTM
        out, _ = self.lstm(x, (h0, c0))

        # Get output from the last time step
        out = self.fc(out[:, -1, :])

        # Apply sigmoid activation
        out = self.sigmoid(out)

        return out


class HSITimingLSTM:
    """
    Hang Seng Index Timing Strategy using LSTM with OECD and Volume-Price Data
    """

    def __init__(self, start_date='2010-01-01', end_date=None):
        """
        Initialize the HSI Timing LSTM strategy.

        Args:
            start_date (str): Start date for data collection and analysis
            end_date (str): End date for data collection and analysis
        """
        self.start_date = pd.to_datetime(start_date)
        self.end_date = pd.to_datetime(end_date) if end_date else pd.to_datetime(datetime.now().strftime('%Y-%m-%d'))
        self.hsi_data = None
        self.oecd_data = None
        self.combined_data = None
        self.model = None
        self.scaler = None
        self.X_train = None
        self.y_train = None
        self.X_val = None
        self.y_val = None
        self.X_test = None
        self.y_test = None
        self.feature_list = None

        logger.info(f"Initialized HSI Timing LSTM strategy from {self.start_date.strftime('%Y-%m-%d')} to {self.end_date.strftime('%Y-%m-%d')}")

    def load_hsi_data(self, file_path='data/hsi_combined_data.csv'):
        """
        Load Hang Seng Index data from CSV file.

        Args:
            file_path (str): Path to the CSV file containing HSI data

        Returns:
            bool: True if data loaded successfully, False otherwise
        """
        logger.info(f"Loading HSI data from {file_path}")

        try:
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return False

            # Load data
            df = pd.read_csv(file_path, index_col=0)

            # Convert index to datetime
            df.index = pd.to_datetime(df.index)
            logger.info(f"Data index type: {type(df.index)}, Sample: {df.index[0]}")

            # Filter by date range
            df = df[(df.index >= self.start_date) & (df.index <= self.end_date)]

            # Check if data is empty
            if df.empty:
                logger.error("No data found for the specified date range")
                return False

            # Store data
            self.hsi_data = df

            logger.info(f"Successfully loaded {len(df)} records of HSI data")
            return True

        except Exception as e:
            logger.error(f"Error loading HSI data: {str(e)}")
            return False

    def load_oecd_data(self, file_path='data/hsi_macro_data.csv'):
        """
        Load OECD China Leading Indicator data from CSV file.

        Args:
            file_path (str): Path to the CSV file containing OECD data

        Returns:
            bool: True if data loaded successfully, False otherwise
        """
        logger.info(f"Loading OECD data from {file_path}")

        try:
            if not os.path.exists(file_path):
                logger.error(f"File not found: {file_path}")
                return False

            # Load data
            df = pd.read_csv(file_path, index_col=0)

            # Convert index to datetime
            df.index = pd.to_datetime(df.index)
            logger.info(f"OECD data index type: {type(df.index)}, Sample: {df.index[0]}")

            # Check if OECD CLI data exists
            if 'oecd_cli' not in df.columns:
                logger.error("OECD CLI data column not found")
                logger.info(f"Available columns: {df.columns.tolist()}")
                return False

            # Filter by date range
            df = df[(df.index >= self.start_date) & (df.index <= self.end_date)]

            # Check if data is empty
            if df.empty:
                logger.error("No OECD data found for the specified date range")
                return False

            # Store data
            self.oecd_data = df

            logger.info(f"Successfully loaded {len(df)} records of OECD data")
            return True

        except Exception as e:
            logger.error(f"Error loading OECD data: {str(e)}")
            return False

    def merge_data(self):
        """
        Prepare HSI price data (without OECD data).

        Returns:
            bool: True if data prepared successfully, False otherwise
        """
        logger.info("Preparing HSI data (without OECD data)")

        try:
            # Check if data is loaded
            if self.hsi_data is None:
                logger.error("HSI data not loaded")
                return False

            # Create a copy of HSI data
            combined = self.hsi_data.copy()

            # Store combined data
            self.combined_data = combined

            logger.info(f"Successfully prepared data. Dataset has {len(combined)} records")
            return True

        except Exception as e:
            logger.error(f"Error preparing data: {str(e)}")
            return False

    def calculate_technical_indicators(self):
        """
        Calculate technical indicators for HSI data.

        Returns:
            bool: True if indicators calculated successfully, False otherwise
        """
        logger.info("Calculating technical indicators")

        try:
            # Check if combined data is available
            if self.combined_data is None:
                logger.error("Combined data not available")
                return False

            df = self.combined_data.copy()

            # Ensure we have OHLCV data
            required_cols = ['Open', 'High', 'Low', 'Close', 'Volume']
            available_cols = [col for col in required_cols if col in df.columns]

            if len(available_cols) < 4:  # Need at least OHLC
                logger.error(f"Insufficient price data columns. Available: {available_cols}")
                return False

            # Calculate returns
            df['Returns'] = df['Close'].pct_change()
            df['Log_Returns'] = np.log(df['Close'] / df['Close'].shift(1))

            # Moving Averages
            df['MA_5'] = df['Close'].rolling(window=5).mean()
            df['MA_10'] = df['Close'].rolling(window=10).mean()
            df['MA_20'] = df['Close'].rolling(window=20).mean()
            df['MA_50'] = df['Close'].rolling(window=50).mean()
            df['MA_200'] = df['Close'].rolling(window=200).mean()

            # Relative Strength Index (RSI)
            delta = df['Close'].diff()
            gain = delta.where(delta > 0, 0)
            loss = -delta.where(delta < 0, 0)
            avg_gain = gain.rolling(window=14).mean()
            avg_loss = loss.rolling(window=14).mean()
            rs = avg_gain / avg_loss
            df['RSI_14'] = 100 - (100 / (1 + rs))

            # MACD
            ema_12 = df['Close'].ewm(span=12, adjust=False).mean()
            ema_26 = df['Close'].ewm(span=26, adjust=False).mean()
            df['MACD'] = ema_12 - ema_26
            df['MACD_Signal'] = df['MACD'].ewm(span=9, adjust=False).mean()
            df['MACD_Hist'] = df['MACD'] - df['MACD_Signal']

            # Bollinger Bands
            df['BB_Middle'] = df['Close'].rolling(window=20).mean()
            df['BB_Std'] = df['Close'].rolling(window=20).std()
            df['BB_Upper'] = df['BB_Middle'] + (df['BB_Std'] * 2)
            df['BB_Lower'] = df['BB_Middle'] - (df['BB_Std'] * 2)
            df['BB_Width'] = (df['BB_Upper'] - df['BB_Lower']) / df['BB_Middle']
            df['BB_Position'] = (df['Close'] - df['BB_Lower']) / (df['BB_Upper'] - df['BB_Lower'])

            # Volume indicators
            if 'Volume' in df.columns:
                # Volume relative to moving average
                df['Volume_MA_20'] = df['Volume'].rolling(window=20).mean()
                df['Volume_Ratio'] = df['Volume'] / df['Volume_MA_20']

                # On-Balance Volume (OBV)
                df['OBV'] = (np.sign(df['Close'].diff()) * df['Volume']).fillna(0).cumsum()

            # Momentum indicators
            df['Momentum_10'] = df['Close'] / df['Close'].shift(10) - 1
            df['Momentum_20'] = df['Close'] / df['Close'].shift(20) - 1

            # Volatility
            df['Volatility_20'] = df['Returns'].rolling(window=20).std()

            # Price relative to moving averages
            df['Price_to_MA_50'] = df['Close'] / df['MA_50']
            df['Price_to_MA_200'] = df['Close'] / df['MA_200']

            # Golden Cross / Death Cross indicator
            df['MA_50_200_Cross'] = np.where(df['MA_50'] > df['MA_200'], 1, -1)

            # Distance from 52-week high/low
            df['52W_High'] = df['Close'].rolling(window=252).max()
            df['52W_Low'] = df['Close'].rolling(window=252).min()
            df['Pct_From_52W_High'] = (df['Close'] / df['52W_High']) - 1
            df['Pct_From_52W_Low'] = (df['Close'] / df['52W_Low']) - 1

            # Cyclical features (day of week, month of year)
            df['Day_of_Week'] = df.index.dayofweek
            df['Month'] = df.index.month

            # Sine and cosine transformations for cyclical features
            df['Day_of_Week_Sin'] = np.sin(2 * np.pi * df['Day_of_Week'] / 5)
            df['Day_of_Week_Cos'] = np.cos(2 * np.pi * df['Day_of_Week'] / 5)
            df['Month_Sin'] = np.sin(2 * np.pi * df['Month'] / 12)
            df['Month_Cos'] = np.cos(2 * np.pi * df['Month'] / 12)

            # Update combined data
            self.combined_data = df

            logger.info("Successfully calculated technical indicators")
            return True

        except Exception as e:
            logger.error(f"Error calculating technical indicators: {str(e)}")
            return False

    def generate_openfe_features(self, target_col='Target_5d', max_features=20):
        """
        Generate features using OpenFE.

        Args:
            target_col (str): Name of the target column
            max_features (int): Maximum number of features to generate

        Returns:
            bool: True if features generated successfully, False otherwise
        """
        logger.info(f"Generating features using OpenFE (max_features={max_features})")

        try:
            # Check if combined data is available
            if self.combined_data is None:
                logger.error("Combined data not available")
                return False

            # Create a copy of the data
            df = self.combined_data.copy()

            # Drop rows with NaN values in target column
            df = df.dropna(subset=[target_col])

            # Select numeric columns only
            numeric_cols = df.select_dtypes(include=['number']).columns.tolist()

            # Remove target column from features
            if target_col in numeric_cols:
                numeric_cols.remove(target_col)

            # Remove date-related columns and other non-feature columns
            exclude_patterns = ['Date', 'Unnamed', 'Forward_Returns', 'Target_']
            numeric_cols = [col for col in numeric_cols if not any(pattern in col for pattern in exclude_patterns)]

            logger.info(f"Using {len(numeric_cols)} base features for OpenFE")

            # Initialize OpenFE
            logger.info("Initializing OpenFE...")
            openfe = OpenFE()

            # Print OpenFE version and available methods
            logger.info(f"OpenFE version: {openfe.__class__.__module__}")
            logger.info(f"OpenFE methods: {[method for method in dir(openfe) if not method.startswith('_')]}")

            # Fit OpenFE - use positive n_jobs value and no max_features parameter
            logger.info("Fitting OpenFE (this may take a while)...")
            try:
                openfe.fit(
                    data=df[numeric_cols],
                    label=df[target_col],
                    n_jobs=4  # Use 4 cores instead of -1
                )
                logger.info("OpenFE fit completed successfully")
            except Exception as e:
                logger.error(f"Error during OpenFE fit: {str(e)}")
                # Try alternative API
                try:
                    logger.info("Trying alternative OpenFE API...")
                    openfe.fit(
                        df[numeric_cols],
                        df[target_col],
                        n_jobs=4
                    )
                    logger.info("Alternative OpenFE fit completed successfully")
                except Exception as e2:
                    logger.error(f"Error during alternative OpenFE fit: {str(e2)}")
                    raise e2

            # Get the top features
            if hasattr(openfe, 'new_features_list') and openfe.new_features_list:
                # Limit to max_features if we have more
                features = openfe.new_features_list[:max_features] if len(openfe.new_features_list) > max_features else openfe.new_features_list
                logger.info(f"Generated {len(features)} new features")
            else:
                logger.warning("OpenFE did not generate any features, continuing with base features only")
                return False

            # Transform the data with the new features
            logger.info("Transforming data with new features...")
            from openfe import transform
            try:
                # First try with the standard API
                X_new, _ = transform(df[numeric_cols], None, features, n_jobs=4)  # Use 4 cores instead of -1
            except TypeError:
                # If that fails, try alternative API formats
                try:
                    X_new = transform(df[numeric_cols], features, n_jobs=4)
                except TypeError:
                    # Last resort, try without n_jobs
                    X_new = transform(df[numeric_cols], features)

            # Add new features to the combined data
            new_feature_count = 0
            for col in X_new.columns:
                if col not in numeric_cols:  # Only add new columns
                    self.combined_data[f'openfe_{col}'] = X_new[col].reindex(self.combined_data.index)
                    new_feature_count += 1

            logger.info(f"Added {new_feature_count} new features to the dataset")

            # Try to convert features to formulas for better understanding
            try:
                from openfe import tree_to_formula
                feature_formulas = [tree_to_formula(feature) for feature in features]
                for i, formula in enumerate(feature_formulas):
                    logger.info(f"Feature {i+1}: {formula}")

                # Save feature formulas for later inspection
                feature_df = pd.DataFrame({
                    'Feature': feature_formulas,
                    'Importance': range(len(feature_formulas))
                })
                feature_df.to_csv('openfe_features.csv', index=False)
                logger.info("Features saved to 'openfe_features.csv'")
            except Exception as e:
                logger.warning(f"Could not convert features to formulas: {str(e)}")

            logger.info("Successfully generated OpenFE features")
            return True

        except Exception as e:
            logger.error(f"Error generating OpenFE features: {str(e)}")
            return False

    def create_target_variable(self, forward_days=5):
        """
        Create target variable for prediction.

        Args:
            forward_days (int): Number of days to look ahead for price movement

        Returns:
            bool: True if target created successfully, False otherwise
        """
        logger.info(f"Creating target variable for {forward_days}-day forward returns")

        try:
            # Check if combined data is available
            if self.combined_data is None:
                logger.error("Combined data not available")
                return False

            df = self.combined_data.copy()

            # Calculate forward returns
            df[f'Forward_Returns_{forward_days}d'] = df['Close'].shift(-forward_days) / df['Close'] - 1

            # Create binary target (1 for positive returns, 0 for negative)
            df[f'Target_{forward_days}d'] = (df[f'Forward_Returns_{forward_days}d'] > 0).astype(int)

            # Update combined data
            self.combined_data = df

            logger.info("Successfully created target variable")
            return True

        except Exception as e:
            logger.error(f"Error creating target variable: {str(e)}")
            return False

    def prepare_features(self, target_col='Target_5d', sequence_length=30):
        """
        Prepare features for LSTM model.

        Args:
            target_col (str): Name of the target column
            sequence_length (int): Length of sequence for LSTM input

        Returns:
            bool: True if features prepared successfully, False otherwise
        """
        logger.info(f"Preparing features for LSTM model with sequence length {sequence_length}")

        try:
            # Check if combined data is available
            if self.combined_data is None:
                logger.error("Combined data not available")
                return False

            # Select features for model
            feature_columns = [
                # Price and volume
                'Close', 'Volume', 'Returns', 'Log_Returns',

                # Technical indicators
                'MA_5', 'MA_20', 'MA_50', 'MA_200',
                'RSI_14', 'MACD', 'MACD_Signal', 'MACD_Hist',
                'BB_Width', 'BB_Position',
                'Volume_Ratio', 'OBV',
                'Momentum_10', 'Momentum_20', 'Volatility_20',
                'Price_to_MA_50', 'Price_to_MA_200',
                'MA_50_200_Cross',
                'Pct_From_52W_High', 'Pct_From_52W_Low',

                # Cyclical features
                'Day_of_Week_Sin', 'Day_of_Week_Cos',
                'Month_Sin', 'Month_Cos',

                # OECD data - commented out to exclude
                # 'oecd_cli', 'oecd_cli_mom', 'oecd_cli_3m', 'oecd_cli_6m'
            ]

            # Check which features are available
            available_features = [col for col in feature_columns if col in self.combined_data.columns]

            if len(available_features) < 5:  # Need at least a few features
                logger.error(f"Insufficient features available. Found only: {available_features}")
                return False

            logger.info(f"Using {len(available_features)} features: {available_features}")

            # Store feature list
            self.feature_list = available_features

            # Drop rows with NaN values
            df_clean = self.combined_data.dropna(subset=available_features + [target_col]).copy()

            if df_clean.empty:
                logger.error("No data left after dropping NaN values")
                return False

            # Extract features and target
            X = df_clean[available_features].values
            y = df_clean[target_col].values

            # Scale features
            self.scaler = MinMaxScaler(feature_range=(0, 1))
            X_scaled = self.scaler.fit_transform(X)

            # Create sequences for LSTM
            X_sequences, y_sequences = [], []
            for i in range(len(X_scaled) - sequence_length):
                X_sequences.append(X_scaled[i:i+sequence_length])
                y_sequences.append(y[i+sequence_length])

            X_sequences = np.array(X_sequences)
            y_sequences = np.array(y_sequences)

            # Split data into train, validation, and test sets (80%, 10%, 10%)
            train_size = int(len(X_sequences) * 0.8)
            val_size = int(len(X_sequences) * 0.1)

            self.X_train = X_sequences[:train_size]
            self.y_train = y_sequences[:train_size]

            self.X_val = X_sequences[train_size:train_size+val_size]
            self.y_val = y_sequences[train_size:train_size+val_size]

            self.X_test = X_sequences[train_size+val_size:]
            self.y_test = y_sequences[train_size+val_size:]

            logger.info(f"Data split: Train={len(self.X_train)}, Validation={len(self.X_val)}, Test={len(self.X_test)}")

            return True

        except Exception as e:
            logger.error(f"Error preparing features: {str(e)}")
            return False

    def train_model(self, hidden_size=50, num_layers=2, dropout_rate=0.2,
                   learning_rate=0.001, epochs=50, batch_size=32, patience=10):
        """
        Train LSTM model.

        Args:
            hidden_size (int): Number of hidden units in LSTM
            num_layers (int): Number of LSTM layers
            dropout_rate (float): Dropout rate for regularization
            learning_rate (float): Learning rate for optimizer
            epochs (int): Maximum number of training epochs
            batch_size (int): Batch size for training
            patience (int): Patience for early stopping

        Returns:
            bool: True if model trained successfully, False otherwise
        """
        logger.info("Training LSTM model")

        try:
            # Check if data is prepared
            if self.X_train is None or self.y_train is None:
                logger.error("Data not prepared. Call prepare_features() first.")
                return False

            # Create datasets and data loaders
            train_dataset = TimeSeriesDataset(self.X_train, self.y_train)
            val_dataset = TimeSeriesDataset(self.X_val, self.y_val)

            train_loader = DataLoader(train_dataset, batch_size=batch_size, shuffle=True)
            val_loader = DataLoader(val_dataset, batch_size=batch_size)

            # Build model
            input_size = self.X_train.shape[2]  # Number of features
            output_size = 1  # Binary classification

            self.model = LSTMModel(
                input_size=input_size,
                hidden_size=hidden_size,
                num_layers=num_layers,
                output_size=output_size,
                dropout_rate=dropout_rate
            ).to(device)

            # Define loss function and optimizer
            criterion = nn.BCELoss()
            optimizer = optim.Adam(self.model.parameters(), lr=learning_rate)

            # Variables for early stopping
            best_val_loss = float('inf')
            patience_counter = 0
            best_model_state = None

            # Training history
            train_losses = []
            val_losses = []
            train_accs = []
            val_accs = []

            # Training loop
            logger.info(f"Starting training for {epochs} epochs")
            for epoch in range(epochs):
                # Training mode
                self.model.train()
                train_loss = 0
                train_correct = 0
                train_total = 0

                for inputs, targets in train_loader:
                    inputs, targets = inputs.to(device), targets.to(device).view(-1, 1)

                    # Forward pass
                    outputs = self.model(inputs)
                    loss = criterion(outputs, targets)

                    # Backward pass and optimization
                    optimizer.zero_grad()
                    loss.backward()
                    optimizer.step()

                    train_loss += loss.item()

                    # Calculate accuracy
                    predicted = (outputs > 0.5).float()
                    train_correct += (predicted == targets).sum().item()
                    train_total += targets.size(0)

                # Calculate average training loss and accuracy
                avg_train_loss = train_loss / len(train_loader)
                train_accuracy = train_correct / train_total

                # Validation mode
                self.model.eval()
                val_loss = 0
                val_correct = 0
                val_total = 0

                with torch.no_grad():
                    for inputs, targets in val_loader:
                        inputs, targets = inputs.to(device), targets.to(device).view(-1, 1)

                        # Forward pass
                        outputs = self.model(inputs)
                        loss = criterion(outputs, targets)

                        val_loss += loss.item()

                        # Calculate accuracy
                        predicted = (outputs > 0.5).float()
                        val_correct += (predicted == targets).sum().item()
                        val_total += targets.size(0)

                # Calculate average validation loss and accuracy
                avg_val_loss = val_loss / len(val_loader)
                val_accuracy = val_correct / val_total

                # Save history
                train_losses.append(avg_train_loss)
                val_losses.append(avg_val_loss)
                train_accs.append(train_accuracy)
                val_accs.append(val_accuracy)

                # Print progress
                logger.info(f"Epoch {epoch+1}/{epochs} - "
                           f"Train Loss: {avg_train_loss:.4f}, Train Acc: {train_accuracy:.4f}, "
                           f"Val Loss: {avg_val_loss:.4f}, Val Acc: {val_accuracy:.4f}")

                # Check for early stopping
                if avg_val_loss < best_val_loss:
                    best_val_loss = avg_val_loss
                    patience_counter = 0
                    best_model_state = self.model.state_dict().copy()
                else:
                    patience_counter += 1
                    if patience_counter >= patience:
                        logger.info(f"Early stopping at epoch {epoch+1}")
                        break

            # Load best model
            if best_model_state is not None:
                self.model.load_state_dict(best_model_state)

            # Plot training history
            plt.figure(figsize=(12, 5))

            plt.subplot(1, 2, 1)
            plt.plot(train_losses, label='Train Loss')
            plt.plot(val_losses, label='Validation Loss')
            plt.xlabel('Epoch')
            plt.ylabel('Loss')
            plt.legend()
            plt.title('Training and Validation Loss')

            plt.subplot(1, 2, 2)
            plt.plot(train_accs, label='Train Accuracy')
            plt.plot(val_accs, label='Validation Accuracy')
            plt.xlabel('Epoch')
            plt.ylabel('Accuracy')
            plt.legend()
            plt.title('Training and Validation Accuracy')

            plt.tight_layout()
            plt.savefig('hsi_lstm_training_history.png')

            logger.info("Model training completed")
            return True

        except Exception as e:
            logger.error(f"Error training model: {str(e)}")
            return False

    def evaluate_model(self):
        """
        Evaluate the trained model on test data.

        Returns:
            dict: Dictionary containing evaluation metrics
        """
        logger.info("Evaluating model on test data")

        try:
            # Check if model is trained
            if self.model is None:
                logger.error("Model not trained. Call train_model() first.")
                return None

            # Create test dataset and loader
            test_dataset = TimeSeriesDataset(self.X_test, self.y_test)
            test_loader = DataLoader(test_dataset, batch_size=32)

            # Evaluation mode
            self.model.eval()

            # Variables for metrics
            test_loss = 0
            test_correct = 0
            test_total = 0

            # Predictions and targets for metrics
            all_preds = []
            all_targets = []

            # Loss function
            criterion = nn.BCELoss()

            with torch.no_grad():
                for inputs, targets in test_loader:
                    inputs, targets = inputs.to(device), targets.to(device).view(-1, 1)

                    # Forward pass
                    outputs = self.model(inputs)
                    loss = criterion(outputs, targets)

                    test_loss += loss.item()

                    # Calculate accuracy
                    predicted = (outputs > 0.5).float()
                    test_correct += (predicted == targets).sum().item()
                    test_total += targets.size(0)

                    # Store predictions and targets
                    all_preds.extend(outputs.cpu().numpy())
                    all_targets.extend(targets.cpu().numpy())

            # Calculate metrics
            avg_test_loss = test_loss / len(test_loader)
            test_accuracy = test_correct / test_total

            # Convert to numpy arrays
            all_preds = np.array(all_preds).flatten()
            all_targets = np.array(all_targets).flatten()

            # Calculate precision and recall
            binary_preds = (all_preds > 0.5).astype(int)
            precision = precision_score(all_targets, binary_preds)
            recall = recall_score(all_targets, binary_preds)

            # Print metrics
            logger.info(f"Test Loss: {avg_test_loss:.4f}")
            logger.info(f"Test Accuracy: {test_accuracy:.4f}")
            logger.info(f"Precision: {precision:.4f}")
            logger.info(f"Recall: {recall:.4f}")

            # Return metrics
            metrics = {
                'test_loss': avg_test_loss,
                'test_accuracy': test_accuracy,
                'precision': precision,
                'recall': recall,
                'predictions': all_preds,
                'targets': all_targets
            }

            return metrics

        except Exception as e:
            logger.error(f"Error evaluating model: {str(e)}")
            return None

    def generate_signals(self, threshold=0.6, sequence_length=30, test_only=False):
        """
        Generate trading signals based on model predictions.

        Args:
            threshold (float): Probability threshold for generating buy signals
            sequence_length (int): Length of sequence for LSTM input
            test_only (bool): Whether to generate signals only for the test set

        Returns:
            pandas.DataFrame: DataFrame containing signals
        """
        logger.info(f"Generating trading signals with threshold {threshold}")

        try:
            # Check if model is trained
            if self.model is None:
                logger.error("Model not trained. Call train_model() first.")
                return None

            # Check if combined data is available
            if self.combined_data is None:
                logger.error("Combined data not available")
                return None

            # Get clean data (no NaN values in features)
            df_clean = self.combined_data.dropna(subset=self.feature_list).copy()

            # Create a DataFrame for signals
            signals = pd.DataFrame(index=df_clean.index)
            signals['Close'] = df_clean['Close']
            signals['Signal'] = 0  # Initialize with no position

            # Get feature data
            X = df_clean[self.feature_list].values

            # Scale features
            X_scaled = self.scaler.transform(X)

            # Create sequences for prediction
            X_sequences = []
            for i in range(len(X_scaled) - sequence_length + 1):
                X_sequences.append(X_scaled[i:i+sequence_length])

            X_sequences = np.array(X_sequences)

            # Skip if no sequences
            if len(X_sequences) == 0:
                logger.error("No sequences available for prediction")
                return None

            # Create dataset and loader
            pred_dataset = torch.tensor(X_sequences, dtype=torch.float32)
            pred_loader = DataLoader(pred_dataset, batch_size=32)

            # Prediction mode
            self.model.eval()

            # Generate predictions
            predictions = []
            with torch.no_grad():
                for inputs in pred_loader:
                    inputs = inputs.to(device)
                    outputs = self.model(inputs)
                    predictions.extend(outputs.cpu().numpy())

            # Convert to numpy array
            predictions = np.array(predictions).flatten()

            # Generate signals
            signal_values = np.zeros(len(df_clean))
            signal_values[sequence_length-1:sequence_length-1+len(predictions)] = (predictions > threshold).astype(int)

            # Store signals
            signals['Probability'] = np.nan
            signals['Probability'].iloc[sequence_length-1:sequence_length-1+len(predictions)] = predictions
            signals['Signal'] = signal_values

            # Add returns
            signals['Returns'] = df_clean['Returns']

            # If test_only is True, filter signals to only include test set
            if test_only and self.X_test is not None and len(self.X_test) > 0:
                # Calculate the start index for the test set
                train_val_size = len(self.X_train) + len(self.X_val)
                test_start_idx = train_val_size + sequence_length - 1

                # Get the test set dates from the full dataset
                test_dates = signals.index[test_start_idx:]

                # Filter signals to only include test set dates
                signals = signals.loc[test_dates].copy()
                logger.info(f"Filtered signals to test set only: {len(signals)} days from {signals.index[0].strftime('%Y-%m-%d')} to {signals.index[-1].strftime('%Y-%m-%d')}")

            logger.info(f"Generated {len(signals)} signals")
            return signals

        except Exception as e:
            logger.error(f"Error generating signals: {str(e)}")
            return None

    def backtest_strategy(self, signals=None, threshold=0.6, sequence_length=30, initial_capital=10000):
        """
        Backtest the LSTM trading strategy only on the test set.

        Args:
            signals (pandas.DataFrame): DataFrame containing signals (optional)
            threshold (float): Probability threshold for generating buy signals
            sequence_length (int): Length of sequence for LSTM input
            initial_capital (float): Initial capital for backtesting

        Returns:
            dict: Dictionary containing backtest results
        """
        logger.info("Backtesting LSTM strategy on test set only")

        try:
            # Generate signals if not provided, using test_only=True
            if signals is None:
                signals = self.generate_signals(threshold=threshold, sequence_length=sequence_length, test_only=True)

            if signals is None:
                logger.error("Failed to generate signals")
                return None

            # Create a copy of signals for backtesting
            backtest = signals.copy()

            logger.info(f"Backtesting on test set only: {len(backtest)} days from {backtest.index[0].strftime('%Y-%m-%d')} to {backtest.index[-1].strftime('%Y-%m-%d')}")

            # Calculate strategy returns
            backtest['Strategy_Returns'] = backtest['Signal'].shift(1) * backtest['Returns']

            # Handle first day (no signal)
            backtest['Strategy_Returns'].iloc[0] = 0

            # Calculate cumulative returns
            backtest['Cumulative_Returns'] = (1 + backtest['Returns']).cumprod() - 1
            backtest['Strategy_Cumulative_Returns'] = (1 + backtest['Strategy_Returns']).cumprod() - 1

            # Calculate drawdowns
            backtest['Peak'] = backtest['Strategy_Cumulative_Returns'].cummax()
            backtest['Drawdown'] = (backtest['Strategy_Cumulative_Returns'] - backtest['Peak']) / (1 + backtest['Peak'])

            # Calculate portfolio value
            backtest['Portfolio_Value'] = initial_capital * (1 + backtest['Strategy_Cumulative_Returns'])

            # Calculate performance metrics
            total_days = len(backtest)
            trading_days_per_year = 252
            years = total_days / trading_days_per_year

            # Total return
            total_return = backtest['Strategy_Cumulative_Returns'].iloc[-1]

            # Annualized return
            annualized_return = (1 + total_return) ** (1 / years) - 1 if years > 0 else 0

            # Annualized volatility
            annualized_volatility = backtest['Strategy_Returns'].std() * np.sqrt(trading_days_per_year)

            # Sharpe ratio (assuming risk-free rate of 0)
            sharpe_ratio = annualized_return / annualized_volatility if annualized_volatility > 0 else 0

            # Maximum drawdown
            max_drawdown = backtest['Drawdown'].min()

            # Win rate
            win_rate = (backtest['Strategy_Returns'] > 0).mean()

            # Print performance metrics
            logger.info(f"Backtest Results (Test Set Only):")
            logger.info(f"Total Return: {total_return:.4f} ({total_return*100:.2f}%)")
            logger.info(f"Annualized Return: {annualized_return:.4f} ({annualized_return*100:.2f}%)")
            logger.info(f"Annualized Volatility: {annualized_volatility:.4f} ({annualized_volatility*100:.2f}%)")
            logger.info(f"Sharpe Ratio: {sharpe_ratio:.4f}")
            logger.info(f"Maximum Drawdown: {max_drawdown:.4f} ({max_drawdown*100:.2f}%)")
            logger.info(f"Win Rate: {win_rate:.4f} ({win_rate*100:.2f}%)")

            # Plot backtest results
            self.plot_backtest_results(backtest)

            # Return results
            results = {
                'backtest_data': backtest,
                'total_return': total_return,
                'annualized_return': annualized_return,
                'annualized_volatility': annualized_volatility,
                'sharpe_ratio': sharpe_ratio,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate
            }

            return results

        except Exception as e:
            logger.error(f"Error backtesting strategy: {str(e)}")
            return None

    def plot_backtest_results(self, backtest):
        """
        Plot backtest results.

        Args:
            backtest (pandas.DataFrame): DataFrame containing backtest data
        """
        logger.info("Plotting backtest results")

        try:
            # Create figure
            plt.figure(figsize=(15, 12))

            # Plot cumulative returns
            plt.subplot(3, 1, 1)
            plt.plot(backtest.index, backtest['Cumulative_Returns'], label='Buy & Hold')
            plt.plot(backtest.index, backtest['Strategy_Cumulative_Returns'], label='LSTM Strategy')
            plt.xlabel('Date')
            plt.ylabel('Cumulative Returns')
            plt.title('Cumulative Returns: LSTM Strategy vs. Buy & Hold')
            plt.legend()
            plt.grid(True)

            # Plot drawdowns
            plt.subplot(3, 1, 2)
            plt.fill_between(backtest.index, 0, backtest['Drawdown'], color='red', alpha=0.3)
            plt.xlabel('Date')
            plt.ylabel('Drawdown')
            plt.title('Strategy Drawdown')
            plt.grid(True)

            # Plot positions and price
            plt.subplot(3, 1, 3)
            plt.plot(backtest.index, backtest['Close'], color='blue', alpha=0.5)

            # Plot buy signals
            buy_signals = backtest[backtest['Signal'] == 1]
            plt.scatter(buy_signals.index, buy_signals['Close'], color='green', marker='^', s=100, label='Buy Signal')

            # Plot sell signals (when signal changes from 1 to 0)
            sell_signals = backtest[(backtest['Signal'].shift(1) == 1) & (backtest['Signal'] == 0)]
            plt.scatter(sell_signals.index, sell_signals['Close'], color='red', marker='v', s=100, label='Sell Signal')

            plt.xlabel('Date')
            plt.ylabel('Price')
            plt.title('HSI Price and Trading Signals')
            plt.legend()
            plt.grid(True)

            plt.tight_layout()
            plt.savefig('hsi_lstm_backtest_results.png')

            logger.info("Backtest results plotted and saved to 'hsi_lstm_backtest_results.png'")

        except Exception as e:
            logger.error(f"Error plotting backtest results: {str(e)}")

    def run_pipeline(self, forward_days=5, sequence_length=30, threshold=0.6,
                    hidden_size=50, num_layers=2, dropout_rate=0.2,
                    learning_rate=0.001, epochs=50, batch_size=32, patience=10,
                    use_openfe=False, max_openfe_features=10):
        """
        Run the complete LSTM strategy pipeline.

        Args:
            forward_days (int): Number of days to look ahead for price movement
            sequence_length (int): Length of sequence for LSTM input
            threshold (float): Probability threshold for generating buy signals
            hidden_size (int): Number of hidden units in LSTM
            num_layers (int): Number of LSTM layers
            dropout_rate (float): Dropout rate for regularization
            learning_rate (float): Learning rate for optimizer
            epochs (int): Maximum number of training epochs
            batch_size (int): Batch size for training
            patience (int): Patience for early stopping
            use_openfe (bool): Whether to use OpenFE for feature generation
            max_openfe_features (int): Maximum number of features to generate with OpenFE

        Returns:
            dict: Dictionary containing results
        """
        logger.info("Running LSTM strategy pipeline")

        # 1. Load data
        logger.info("1. Loading data...")
        if not self.load_hsi_data():
            logger.error("Failed to load HSI data")
            return None

        # Skip loading OECD data
        logger.info("Skipping OECD data loading (not using OECD features)")
        self.oecd_data = pd.DataFrame()  # Empty DataFrame

        # 2. Merge data
        logger.info("2. Merging data...")
        if not self.merge_data():
            logger.error("Failed to merge data")
            return None

        # 3. Calculate technical indicators
        logger.info("3. Calculating technical indicators...")
        if not self.calculate_technical_indicators():
            logger.error("Failed to calculate technical indicators")
            return None

        # 4. Create target variable
        logger.info("4. Creating target variable...")
        if not self.create_target_variable(forward_days=forward_days):
            logger.error("Failed to create target variable")
            return None

        # 5. Generate OpenFE features if enabled
        if use_openfe:
            logger.info("5. Generating OpenFE features...")
            if not self.generate_openfe_features(target_col=f'Target_{forward_days}d', max_features=max_openfe_features):
                logger.warning("Failed to generate OpenFE features, continuing with base features only")

        # 6. Prepare features
        logger.info(f"{'6' if use_openfe else '5'}. Preparing features...")
        if not self.prepare_features(target_col=f'Target_{forward_days}d', sequence_length=sequence_length):
            logger.error("Failed to prepare features")
            return None

        # 7. Train model
        logger.info(f"{'7' if use_openfe else '6'}. Training model...")
        if not self.train_model(
            hidden_size=hidden_size,
            num_layers=num_layers,
            dropout_rate=dropout_rate,
            learning_rate=learning_rate,
            epochs=epochs,
            batch_size=batch_size,
            patience=patience
        ):
            logger.error("Failed to train model")
            return None

        # 8. Evaluate model
        logger.info(f"{'8' if use_openfe else '7'}. Evaluating model...")
        metrics = self.evaluate_model()
        if metrics is None:
            logger.error("Failed to evaluate model")
            return None

        # 9. Generate signals and backtest
        logger.info(f"{'9' if use_openfe else '8'}. Backtesting strategy...")
        results = self.backtest_strategy(
            threshold=threshold,
            sequence_length=sequence_length
        )
        if results is None:
            logger.error("Failed to backtest strategy")
            return None

        # 10. Return results
        logger.info("Pipeline completed successfully")
        return {
            'metrics': metrics,
            'backtest_results': results
        }


# Main function to run the strategy
def main():
    """
    Main function to run the HSI LSTM timing strategy.
    """
    import argparse

    # Parse command line arguments
    parser = argparse.ArgumentParser(description='Hang Seng Index LSTM Timing Strategy')

    parser.add_argument('--start-date', type=str, default='2010-01-01',
                        help='Start date for data collection (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default=None,
                        help='End date for data collection (YYYY-MM-DD)')
    parser.add_argument('--forward-days', type=int, default=5,
                        help='Number of days to look ahead for price movement')
    parser.add_argument('--sequence-length', type=int, default=30,
                        help='Length of sequence for LSTM input')
    parser.add_argument('--threshold', type=float, default=0.6,
                        help='Probability threshold for generating buy signals')
    parser.add_argument('--hidden-size', type=int, default=50,
                        help='Number of hidden units in LSTM')
    parser.add_argument('--num-layers', type=int, default=2,
                        help='Number of LSTM layers')
    parser.add_argument('--dropout-rate', type=float, default=0.2,
                        help='Dropout rate for regularization')
    parser.add_argument('--learning-rate', type=float, default=0.001,
                        help='Learning rate for optimizer')
    parser.add_argument('--epochs', type=int, default=50,
                        help='Maximum number of training epochs')
    parser.add_argument('--batch-size', type=int, default=32,
                        help='Batch size for training')
    parser.add_argument('--patience', type=int, default=10,
                        help='Patience for early stopping')
    parser.add_argument('--use-openfe', action='store_true', default=False,
                        help='Use OpenFE for automatic feature generation')
    parser.add_argument('--no-openfe', action='store_false', dest='use_openfe',
                        help='Disable OpenFE feature generation (default)')
    parser.add_argument('--max-openfe-features', type=int, default=20,
                        help='Maximum number of features to generate with OpenFE')

    args = parser.parse_args()

    # Create strategy instance
    strategy = HSITimingLSTM(start_date=args.start_date, end_date=args.end_date)

    # Run pipeline
    results = strategy.run_pipeline(
        forward_days=args.forward_days,
        sequence_length=args.sequence_length,
        threshold=args.threshold,
        hidden_size=args.hidden_size,
        num_layers=args.num_layers,
        dropout_rate=args.dropout_rate,
        learning_rate=args.learning_rate,
        epochs=args.epochs,
        batch_size=args.batch_size,
        patience=args.patience,
        use_openfe=args.use_openfe,
        max_openfe_features=args.max_openfe_features
    )

    if results is not None:
        logger.info("Strategy completed successfully")

        # Print final metrics
        metrics = results['metrics']
        backtest = results['backtest_results']

        print("\n===== Model Evaluation Metrics =====")
        print(f"Test Accuracy: {metrics['test_accuracy']:.4f}")
        print(f"Precision: {metrics['precision']:.4f}")
        print(f"Recall: {metrics['recall']:.4f}")

        print("\n===== Backtest Results =====")
        print(f"Total Return: {backtest['total_return']*100:.2f}%")
        print(f"Annualized Return: {backtest['annualized_return']*100:.2f}%")
        print(f"Sharpe Ratio: {backtest['sharpe_ratio']:.4f}")
        print(f"Maximum Drawdown: {backtest['max_drawdown']*100:.2f}%")
        print(f"Win Rate: {backtest['win_rate']*100:.2f}%")

        print("\nBacktest charts saved to 'hsi_lstm_backtest_results.png'")
        print("Training history saved to 'hsi_lstm_training_history.png'")
    else:
        logger.error("Strategy failed to complete")


if __name__ == "__main__":
    main()
