#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
优化择时策略的进出场阈值参数

测试不同的exit_threshold和enter_threshold组合，
找出最佳的择时阈值。
"""

import pandas as pd
import numpy as np
import yfinance as yf
import os
import warnings
from concurrent.futures import ProcessPoolExecutor, as_completed
import multiprocessing as mp

warnings.filterwarnings('ignore')

# 设置数据目录
DATA_DIR = 'data'

def backtest_with_thresholds(args):
    """使用指定阈值进行回测的函数，用于并行化"""
    exit_threshold, enter_threshold, timing_indicator, sp500_prices = args
    
    try:
        # 对齐数据
        start_date = pd.to_datetime('2003-05-12')
        common_dates = sp500_prices.index.intersection(timing_indicator.index)
        common_dates = common_dates[common_dates >= start_date]
        
        if len(common_dates) == 0:
            return None
        
        indicator_aligned = timing_indicator.reindex(common_dates, method='ffill')
        prices_aligned = sp500_prices.reindex(common_dates, method='ffill')
        
        # 生成交易信号
        signals = pd.Series(index=common_dates, dtype=float)
        current_position = 1.0  # 初始满仓
        
        for date in common_dates:
            indicator_value = indicator_aligned[date]
            if pd.isna(indicator_value):
                signals[date] = current_position
                continue
                
            if indicator_value < exit_threshold:
                # 指标 < exit_threshold，空仓
                current_position = 0.0
            elif indicator_value > enter_threshold:
                # 指标 > enter_threshold，满仓
                current_position = 1.0
            # 其他情况保持当前仓位不变
            
            signals[date] = current_position
        
        # 计算收益率
        returns = prices_aligned.pct_change().fillna(0)
        strategy_returns = signals.shift(1).fillna(1.0) * returns
        buy_hold_returns = returns
        
        # 计算绩效指标
        def calc_metrics(ret_series):
            total_days = len(ret_series)
            total_years = total_days / 252
            total_return = (1 + ret_series).prod() - 1
            annual_return = (1 + total_return) ** (1/total_years) - 1
            annual_vol = ret_series.std() * np.sqrt(252)
            sharpe = (annual_return - 0.02) / annual_vol if annual_vol > 0 else 0
            
            cumret = (1 + ret_series).cumprod()
            drawdown = (cumret / cumret.expanding().max() - 1)
            max_dd = drawdown.min()
            
            return {
                'annual_return': annual_return,
                'annual_volatility': annual_vol,
                'sharpe_ratio': sharpe,
                'max_drawdown': max_dd,
                'cash_days': (signals == 0).sum(),
                'cash_ratio': (signals == 0).sum() / len(signals)
            }
        
        strategy_metrics = calc_metrics(strategy_returns)
        buy_hold_metrics = calc_metrics(buy_hold_returns)
        
        return {
            'exit_threshold': exit_threshold,
            'enter_threshold': enter_threshold,
            'strategy_annual_return': strategy_metrics['annual_return'],
            'strategy_volatility': strategy_metrics['annual_volatility'],
            'strategy_sharpe': strategy_metrics['sharpe_ratio'],
            'strategy_max_dd': strategy_metrics['max_drawdown'],
            'buy_hold_annual_return': buy_hold_metrics['annual_return'],
            'buy_hold_sharpe': buy_hold_metrics['sharpe_ratio'],
            'excess_return': strategy_metrics['annual_return'] - buy_hold_metrics['annual_return'],
            'excess_sharpe': strategy_metrics['sharpe_ratio'] - buy_hold_metrics['sharpe_ratio'],
            'cash_days': strategy_metrics['cash_days'],
            'cash_ratio': strategy_metrics['cash_ratio']
        }
        
    except Exception as e:
        print(f"阈值组合 ({exit_threshold}, {enter_threshold}) 测试失败: {e}")
        return None

def optimize_thresholds():
    """优化择时阈值参数"""
    print("开始优化择时阈值参数...")
    
    # 1. 加载数据
    print("1. 加载择时指标和标普500数据...")
    
    # 加载择时指标
    timing_file = os.path.join(DATA_DIR, 'us_market_timing_indicator.csv')
    timing_data = pd.read_csv(timing_file, index_col=0, parse_dates=True)
    timing_indicator = timing_data['Market_Timing_Indicator']
    
    # 下载标普500数据
    sp500_data = yf.download('^GSPC', start='2000-01-01', progress=False)
    if isinstance(sp500_data.columns, pd.MultiIndex):
        sp500_data.columns = sp500_data.columns.droplevel(1)
    sp500_prices = sp500_data['Adj Close'] if 'Adj Close' in sp500_data.columns else sp500_data['Close']
    
    print(f"   择时指标数据: {len(timing_indicator)} 条记录")
    print(f"   标普500数据: {len(sp500_prices)} 条记录")
    
    # 2. 设计阈值测试范围
    print("\n2. 设计阈值测试范围...")
    
    # 基于指标分布设计测试范围
    print(f"   指标统计: 最小值={timing_indicator.min():.1f}, 最大值={timing_indicator.max():.1f}")
    print(f"   指标分位数: 10%={np.percentile(timing_indicator, 10):.1f}, 25%={np.percentile(timing_indicator, 25):.1f}")
    print(f"   指标分位数: 75%={np.percentile(timing_indicator, 75):.1f}, 90%={np.percentile(timing_indicator, 90):.1f}")
    
    # 设计测试范围
    exit_thresholds = [5, 8, 10, 12, 15, 18, 20]  # 空仓阈值
    enter_thresholds = [20, 25, 30, 35, 40, 45, 50]  # 满仓阈值
    
    # 生成有效的阈值组合（确保 exit_threshold < enter_threshold）
    threshold_combinations = []
    for exit_th in exit_thresholds:
        for enter_th in enter_thresholds:
            if exit_th < enter_th:  # 确保逻辑正确
                threshold_combinations.append((exit_th, enter_th))
    
    print(f"   将测试 {len(threshold_combinations)} 个阈值组合")
    print(f"   空仓阈值范围: {exit_thresholds}")
    print(f"   满仓阈值范围: {enter_thresholds}")
    
    # 3. 并行化测试
    print("\n3. 并行化测试阈值组合...")
    
    # 准备并行化参数
    args_list = [(exit_th, enter_th, timing_indicator, sp500_prices) 
                 for exit_th, enter_th in threshold_combinations]
    
    # 使用进程池并行化
    max_workers = min(mp.cpu_count(), len(threshold_combinations))
    print(f"   使用 {max_workers} 个进程并行计算...")
    
    results = []
    with ProcessPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_thresholds = {executor.submit(backtest_with_thresholds, args): args[:2] 
                               for args in args_list}
        
        # 收集结果
        completed = 0
        for future in as_completed(future_to_thresholds):
            exit_th, enter_th = future_to_thresholds[future]
            completed += 1
            
            try:
                result = future.result()
                if result is not None:
                    results.append(result)
                    print(f"   ✓ 阈值 ({exit_th:2d},{enter_th:2d}) ({completed:2d}/{len(threshold_combinations)}) "
                          f"夏普={result['strategy_sharpe']:.3f}, 超额收益={result['excess_return']:.2%}, "
                          f"空仓={result['cash_ratio']:.1%}")
                else:
                    print(f"   ✗ 阈值 ({exit_th:2d},{enter_th:2d}) ({completed:2d}/{len(threshold_combinations)}) 回测失败")
            except Exception as e:
                print(f"   ✗ 阈值 ({exit_th:2d},{enter_th:2d}) ({completed:2d}/{len(threshold_combinations)}) 错误: {str(e)[:30]}")
                continue
    
    # 4. 分析结果
    print(f"\n4. 分析结果...")
    
    if not results:
        print("   没有成功的测试结果")
        return None
    
    results_df = pd.DataFrame(results)
    
    # 保存详细结果
    output_file = os.path.join(DATA_DIR, 'threshold_optimization_results.csv')
    results_df.to_csv(output_file, index=False, encoding='utf-8-sig')
    print(f"   详细结果已保存到: {output_file}")
    
    return results_df

def analyze_threshold_results(results_df):
    """分析阈值优化结果"""
    print("\n" + "="*80)
    print("择时阈值优化结果分析")
    print("="*80)
    
    # 找出各项指标的最优值
    best_sharpe = results_df.loc[results_df['strategy_sharpe'].idxmax()]
    best_excess_sharpe = results_df.loc[results_df['excess_sharpe'].idxmax()]
    best_return = results_df.loc[results_df['strategy_annual_return'].idxmax()]
    best_excess_return = results_df.loc[results_df['excess_return'].idxmax()]
    min_drawdown = results_df.loc[results_df['strategy_max_dd'].idxmax()]  # 最大回撤是负值
    
    print(f"最佳夏普比率: 阈值=({best_sharpe['exit_threshold']:.0f},{best_sharpe['enter_threshold']:.0f}), 夏普比率={best_sharpe['strategy_sharpe']:.3f}")
    print(f"最佳超额夏普: 阈值=({best_excess_sharpe['exit_threshold']:.0f},{best_excess_sharpe['enter_threshold']:.0f}), 超额夏普={best_excess_sharpe['excess_sharpe']:.3f}")
    print(f"最佳收益率: 阈值=({best_return['exit_threshold']:.0f},{best_return['enter_threshold']:.0f}), 年化收益率={best_return['strategy_annual_return']:.2%}")
    print(f"最佳超额收益: 阈值=({best_excess_return['exit_threshold']:.0f},{best_excess_return['enter_threshold']:.0f}), 超额收益={best_excess_return['excess_return']:.2%}")
    print(f"最小回撤: 阈值=({min_drawdown['exit_threshold']:.0f},{min_drawdown['enter_threshold']:.0f}), 最大回撤={min_drawdown['strategy_max_dd']:.2%}")
    
    # 综合评分（夏普比率40%，超额收益30%，最大回撤20%，空仓比例10%）
    # 空仓比例适中更好（不要太高也不要太低）
    optimal_cash_ratio = 0.08  # 8%左右比较合理
    cash_ratio_score = 1 - abs(results_df['cash_ratio'] - optimal_cash_ratio) / optimal_cash_ratio
    cash_ratio_score = np.clip(cash_ratio_score, 0, 1)
    
    results_df['composite_score'] = (
        0.4 * (results_df['strategy_sharpe'] / results_df['strategy_sharpe'].max()) +
        0.3 * ((results_df['excess_return'] - results_df['excess_return'].min()) / 
               (results_df['excess_return'].max() - results_df['excess_return'].min())) +
        0.2 * ((results_df['strategy_max_dd'] - results_df['strategy_max_dd'].min()) / 
               (results_df['strategy_max_dd'].max() - results_df['strategy_max_dd'].min())) +
        0.1 * cash_ratio_score
    )
    
    best_composite = results_df.loc[results_df['composite_score'].idxmax()]
    
    print(f"\n综合最佳: 阈值=({best_composite['exit_threshold']:.0f},{best_composite['enter_threshold']:.0f})")
    print(f"  年化收益率: {best_composite['strategy_annual_return']:.2%}")
    print(f"  夏普比率: {best_composite['strategy_sharpe']:.3f}")
    print(f"  超额收益: {best_composite['excess_return']:.2%}")
    print(f"  最大回撤: {best_composite['strategy_max_dd']:.2%}")
    print(f"  空仓比例: {best_composite['cash_ratio']:.1%}")
    print(f"  综合评分: {best_composite['composite_score']:.3f}")
    
    # 显示前5名
    print(f"\n前5名阈值组合:")
    top5 = results_df.nlargest(5, 'composite_score')
    print(f"{'排名':<4} {'阈值':<8} {'夏普':<6} {'超额收益':<8} {'最大回撤':<8} {'空仓比例':<8} {'综合评分':<8}")
    print("-" * 60)
    for i, (_, row) in enumerate(top5.iterrows(), 1):
        print(f"{i:<4} ({row['exit_threshold']:.0f},{row['enter_threshold']:.0f}){'':<2} "
              f"{row['strategy_sharpe']:.3f}  {row['excess_return']:>7.2%} "
              f"{row['strategy_max_dd']:>7.2%} {row['cash_ratio']:>7.1%} "
              f"{row['composite_score']:>7.3f}")
    
    return int(best_composite['exit_threshold']), int(best_composite['enter_threshold'])

if __name__ == "__main__":
    # 运行优化
    results_df = optimize_thresholds()
    
    if results_df is not None:
        # 分析结果
        best_exit, best_enter = analyze_threshold_results(results_df)
        print(f"\n推荐使用阈值: exit_threshold = {best_exit}, enter_threshold = {best_enter}")
    else:
        print("优化失败")
