#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股相对强度可视化演示
展示类似SPY的可视化分析功能
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from hsi_visualizer import HSIVisualizer

def create_demo_data():
    """创建演示数据"""
    print("🎯 创建恒生指数成分股演示数据...")
    
    # 创建演示分析结果
    demo_results = [
        # 强势科技股
        {'symbol': '700', 'name': '腾讯控股', 'current_rs': 108.5, 'strength_score': 85, 
         '1M_slope': 2.1, '3M_slope': 1.8, '6M_slope': 1.2, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '9988', 'name': '阿里巴巴-W', 'current_rs': 105.2, 'strength_score': 80, 
         '1M_slope': 1.8, '3M_slope': 1.5, '6M_slope': 0.9, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1024', 'name': '快手-W', 'current_rs': 109.6, 'strength_score': 88, 
         '1M_slope': 2.3, '3M_slope': 2.0, '6M_slope': 1.5, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '3690', 'name': '美团-W', 'current_rs': 103.7, 'strength_score': 75, 
         '1M_slope': 1.2, '3M_slope': 1.0, '6M_slope': 0.8, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1810', 'name': '小米集团-W', 'current_rs': 107.9, 'strength_score': 78, 
         '1M_slope': 1.6, '3M_slope': 1.3, '6M_slope': 0.7, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '9618', 'name': '京东集团-SW', 'current_rs': 104.3, 'strength_score': 72, 
         '1M_slope': 1.1, '3M_slope': 0.9, '6M_slope': 0.5, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '981', 'name': '中芯国际', 'current_rs': 110.2, 'strength_score': 85, 
         '1M_slope': 2.0, '3M_slope': 1.7, '6M_slope': 1.3, 'above_ma20': True, 'above_ma50': True},
        
        # 强势金融股
        {'symbol': '1299', 'name': '友邦保险', 'current_rs': 112.3, 'strength_score': 90, 
         '1M_slope': 2.5, '3M_slope': 2.1, '6M_slope': 1.8, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '388', 'name': '香港交易所', 'current_rs': 106.8, 'strength_score': 82, 
         '1M_slope': 1.9, '3M_slope': 1.6, '6M_slope': 1.1, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '2318', 'name': '中国平安', 'current_rs': 101.2, 'strength_score': 65, 
         '1M_slope': 0.8, '3M_slope': 0.6, '6M_slope': 0.2, 'above_ma20': True, 'above_ma50': True},
        
        # 中等表现股票
        {'symbol': '939', 'name': '建设银行', 'current_rs': 98.5, 'strength_score': 55, 
         '1M_slope': 0.5, '3M_slope': 0.3, '6M_slope': -0.2, 'above_ma20': False, 'above_ma50': True},
        
        {'symbol': '1398', 'name': '工商银行', 'current_rs': 96.2, 'strength_score': 45, 
         '1M_slope': -0.2, '3M_slope': -0.1, '6M_slope': -0.5, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '883', 'name': '中国海洋石油', 'current_rs': 99.7, 'strength_score': 60, 
         '1M_slope': 0.3, '3M_slope': 0.1, '6M_slope': -0.1, 'above_ma20': True, 'above_ma50': False},
        
        # 弱势股票
        {'symbol': '5', 'name': '汇丰控股', 'current_rs': 89.3, 'strength_score': 25, 
         '1M_slope': -1.2, '3M_slope': -1.5, '6M_slope': -2.1, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '857', 'name': '中国石油股份', 'current_rs': 87.6, 'strength_score': 20, 
         '1M_slope': -1.8, '3M_slope': -2.1, '6M_slope': -2.8, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '386', 'name': '中国石油化工股份', 'current_rs': 91.4, 'strength_score': 30, 
         '1M_slope': -0.8, '3M_slope': -1.1, '6M_slope': -1.6, 'above_ma20': False, 'above_ma50': False},
        
        # 更多股票
        {'symbol': '1088', 'name': '中国神华', 'current_rs': 93.2, 'strength_score': 35, 
         '1M_slope': -0.3, '3M_slope': -0.6, '6M_slope': -1.2, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '2628', 'name': '中国人寿', 'current_rs': 95.1, 'strength_score': 50, 
         '1M_slope': 0.1, '3M_slope': -0.2, '6M_slope': -0.8, 'above_ma20': False, 'above_ma50': False},
        
        {'symbol': '1093', 'name': '石药集团', 'current_rs': 102.4, 'strength_score': 68, 
         '1M_slope': 1.0, '3M_slope': 0.8, '6M_slope': 0.4, 'above_ma20': True, 'above_ma50': True},
        
        {'symbol': '1177', 'name': '中国生物制药', 'current_rs': 100.8, 'strength_score': 62, 
         '1M_slope': 0.6, '3M_slope': 0.4, '6M_slope': 0.1, 'above_ma20': True, 'above_ma50': False},
    ]
    
    # 转换为DataFrame并排序
    analysis_results = pd.DataFrame(demo_results)
    analysis_results = analysis_results.sort_values('strength_score', ascending=False)
    
    return analysis_results

def create_demo_relative_strength_data(analysis_results):
    """创建演示相对强度时间序列数据"""
    print("📈 创建相对强度时间序列数据...")
    
    # 创建日期范围（过去一年）
    end_date = datetime.now()
    start_date = end_date - timedelta(days=365)
    date_range = pd.date_range(start=start_date, end=end_date, freq='D')
    
    relative_strength_data = {}
    
    for _, stock in analysis_results.iterrows():
        symbol = stock['symbol']
        current_rs = stock['current_rs']
        
        # 生成模拟的相对强度数据
        np.random.seed(hash(symbol) % 2**32)  # 确保每个股票的数据一致
        
        # 基于当前相对强度和趋势生成历史数据
        n_points = len(date_range)
        
        # 生成趋势
        if stock['6M_slope'] > 0:
            # 上升趋势
            trend = np.linspace(current_rs - 15, current_rs, n_points)
        else:
            # 下降趋势
            trend = np.linspace(current_rs + 10, current_rs, n_points)
        
        # 添加随机波动
        noise = np.random.normal(0, 3, n_points)
        
        # 添加周期性波动
        cycle = 2 * np.sin(np.linspace(0, 4*np.pi, n_points))
        
        # 组合生成最终数据
        rs_values = trend + noise + cycle
        
        # 确保数据合理性
        rs_values = np.clip(rs_values, 70, 130)
        
        # 创建时间序列
        rs_series = pd.Series(rs_values, index=date_range)
        relative_strength_data[symbol] = rs_series
    
    return relative_strength_data

def main():
    """主函数 - 演示恒生指数可视化功能"""
    print("🎨 恒生指数成分股相对强度可视化演示")
    print("=" * 60)
    
    # 1. 创建演示数据
    analysis_results = create_demo_data()
    relative_strength_data = create_demo_relative_strength_data(analysis_results)
    
    print(f"✅ 演示数据创建完成:")
    print(f"   - 分析股票数: {len(analysis_results)}")
    print(f"   - 时间序列数据: {len(relative_strength_data)} 只股票")
    
    # 2. 初始化可视化器
    visualizer = HSIVisualizer(analysis_results, relative_strength_data)
    
    # 3. 生成各种可视化图表
    print(f"\n📊 开始生成可视化图表...")
    
    # 3.1 前15强势股综合分析
    print(f"\n1️⃣ 生成前15强势港股综合分析图表...")
    visualizer.plot_top_relative_strength_stocks(top_n=15)
    
    # 3.2 个股详细分析（前6只强势股）
    print(f"\n2️⃣ 生成个股详细分析图表...")
    top_6_symbols = analysis_results.head(6)['symbol'].tolist()
    visualizer.plot_individual_stock_analysis(top_6_symbols)
    
    # 3.3 行业分析
    print(f"\n3️⃣ 生成行业分析图表...")
    visualizer.plot_sector_analysis()
    
    # 3.4 综合性能仪表板
    print(f"\n4️⃣ 生成综合性能仪表板...")
    visualizer.plot_performance_dashboard()
    
    # 4. 生成分析报告
    print(f"\n📝 生成分析摘要报告...")
    
    total_stocks = len(analysis_results)
    strong_stocks = len(analysis_results[analysis_results['strength_score'] >= 70])
    avg_score = analysis_results['strength_score'].mean()
    avg_rs = analysis_results['current_rs'].mean()
    
    rising_stocks = analysis_results[
        (analysis_results['1M_slope'] > 0) &
        (analysis_results['3M_slope'] > 0) &
        (analysis_results['6M_slope'] > 0) &
        (analysis_results['strength_score'] >= 70)
    ]
    
    print(f"\n📊 恒生指数成分股相对强度分析摘要:")
    print(f"=" * 50)
    print(f"📈 总分析股票数: {total_stocks}")
    print(f"🏆 强势股数量: {strong_stocks} ({strong_stocks/total_stocks*100:.1f}%)")
    print(f"📊 平均强度评分: {avg_score:.1f}")
    print(f"📈 平均相对强度: {avg_rs:.1f}")
    print(f"🚀 持续上升强势股: {len(rising_stocks)} 只")
    
    print(f"\n🏆 前5强势港股:")
    for i, (_, stock) in enumerate(analysis_results.head(5).iterrows(), 1):
        print(f"   {i}. {stock['symbol']} ({stock['name']}) - 评分: {stock['strength_score']:.0f}")
    
    print(f"\n🚀 持续上升强势股:")
    for _, stock in rising_stocks.iterrows():
        print(f"   • {stock['symbol']} ({stock['name']}) - 评分: {stock['strength_score']:.0f}")
    
    # 5. 保存结果
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    results_file = f"hsi_visualization_demo_results_{timestamp}.csv"
    analysis_results.to_csv(results_file, index=False, encoding='utf-8-sig')
    
    print(f"\n📁 演示结果已保存至: {results_file}")
    print(f"🎨 所有可视化图表已生成并保存")
    
    print(f"\n✅ 恒生指数成分股可视化演示完成!")
    print(f"   • 成功展示了类似SPY的可视化分析功能")
    print(f"   • 包含综合分析、个股分析、行业分析和性能仪表板")
    print(f"   • 所有图表均已保存为高分辨率PNG文件")

if __name__ == "__main__":
    main()
