import json
import pandas as pd
import os
from datetime import datetime
import glob

def parse_hsi_json():
    """
    Parse the saved HSI constituents JSON file and extract constituent stocks data
    """
    print("Parsing HSI constituents JSON file...")
    
    # Find the most recent formatted JSON file
    data_dir = "data"
    json_files = glob.glob(os.path.join(data_dir, "hsi_constituents_formatted_*.json"))
    
    if not json_files:
        print("No JSON files found. Please run save_hsi_json.py first.")
        return None
    
    # Sort files by modification time (newest first)
    latest_file = max(json_files, key=os.path.getmtime)
    print(f"Using latest JSON file: {latest_file}")
    
    try:
        # Load the JSON data
        with open(latest_file, 'r', encoding='utf-8') as f:
            data = json.load(f)
        
        # Navigate through the JSON structure to find the constituents
        constituents = []
        
        if 'indexSeriesList' in data:
            # Look for Hang Seng Index in the index series list
            for series in data['indexSeriesList']:
                if series.get('seriesName') == "Hang Seng Index and Sub-indexes" or series.get('seriesCode') == "hsi":
                    # Found the HSI series, now look for the main index
                    for index in series.get('indexList', []):
                        if index.get('indexName') == "Hang Seng Index":
                            # Found the main HSI index
                            # Check if constituents are directly in this index
                            if 'constituentContent' in index and isinstance(index['constituentContent'], list):
                                constituents = index['constituentContent']
                                print(f"Found {len(constituents)} constituents in the main HSI index")
                            
                            # If not found, check sub-indexes
                            if not constituents:
                                for sub_index in index.get('subIndexList', []):
                                    if 'constituentContent' in sub_index and isinstance(sub_index['constituentContent'], list):
                                        constituents.extend(sub_index['constituentContent'])
                                        print(f"Found {len(sub_index['constituentContent'])} constituents in sub-index: {sub_index.get('indexName')}")
        
        if not constituents:
            print("Could not find constituents data in the JSON file.")
            return None
        
        # Convert to DataFrame
        df = pd.DataFrame(constituents)
        
        # Clean up column names
        df.columns = [col.strip() for col in df.columns]
        
        # Save to CSV
        timestamp = datetime.now().strftime("%Y%m%d")
        output_file = os.path.join(data_dir, f"hsi_constituents_{timestamp}.csv")
        df.to_csv(output_file, index=False)
        
        # Also save to a standard filename for easy access
        standard_file = os.path.join(data_dir, "hsi_constituents.csv")
        df.to_csv(standard_file, index=False)
        
        print(f"Successfully extracted {len(df)} HSI constituent stocks")
        print(f"Data saved to {output_file} and {standard_file}")
        
        return df
    
    except Exception as e:
        print(f"Error parsing HSI constituents JSON: {str(e)}")
        return None

def main():
    # Parse HSI constituents JSON
    constituents_df = parse_hsi_json()
    
    if constituents_df is not None:
        # Display the first few rows
        print("\nHSI Constituents Preview:")
        print(constituents_df.head())
        
        # Display column names
        print("\nColumns in the DataFrame:")
        print(constituents_df.columns.tolist())
        
        # Display summary
        print(f"\nTotal number of constituents: {len(constituents_df)}")

if __name__ == "__main__":
    print("Starting HSI JSON parser...")
    main()
    print("Script execution completed.")
