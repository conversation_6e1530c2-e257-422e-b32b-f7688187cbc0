#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股相对强度可视化分析器
基于S&P 500可视化功能，适用于恒生指数成分股
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import matplotlib.dates as mdates
from datetime import datetime, timedelta
import warnings
from typing import List, Dict
import seaborn as sns

warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans', 'WenQuanYi Micro Hei']
plt.rcParams['axes.unicode_minus'] = False

class HSIVisualizer:
    """恒生指数成分股相对强度可视化器"""
    
    def __init__(self, analysis_results: pd.DataFrame, relative_strength_data: Dict):
        """
        初始化可视化器
        
        Args:
            analysis_results: 分析结果DataFrame
            relative_strength_data: 相对强度数据字典
        """
        self.analysis_results = analysis_results
        self.relative_strength_data = relative_strength_data
        
        # 设置图表样式
        plt.style.use('seaborn-v0_8-whitegrid')
        
        print("🎨 恒生指数成分股可视化器已初始化")
    
    def plot_top_relative_strength_stocks(self, top_n: int = 20, save_path: str = None) -> None:
        """
        绘制相对强度最强的港股图表
        
        Args:
            top_n: 显示前N只股票
            save_path: 保存路径
        """
        try:
            if self.analysis_results.empty:
                print("❌ 没有分析结果可供绘制")
                return
            
            print(f"📊 正在绘制前 {top_n} 只相对强度最强的港股...")
            
            # 获取前N只股票
            top_stocks = self.analysis_results.head(top_n)
            
            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(20, 16))
            fig.suptitle(f'恒生指数成分股相对强度分析 - 前{top_n}只强势港股', fontsize=16, fontweight='bold')
            
            # 1. 相对强度线图
            ax1 = axes[0, 0]
            colors = plt.cm.tab20(np.linspace(0, 1, min(top_n, 20)))
            
            for i, (_, stock) in enumerate(top_stocks.iterrows()):
                symbol = stock['symbol']
                if symbol in self.relative_strength_data:
                    rs_data = self.relative_strength_data[symbol]
                    ax1.plot(rs_data.index, rs_data.values,
                            label=f"{symbol} ({stock['strength_score']:.0f}分)",
                            color=colors[i % len(colors)], linewidth=1.5, alpha=0.8)
            
            ax1.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='基准线(100)')
            ax1.set_title('相对强度线图 (相对于恒生指数)', fontsize=14, fontweight='bold')
            ax1.set_xlabel('日期')
            ax1.set_ylabel('相对强度')
            ax1.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=8)
            ax1.grid(True, alpha=0.3)
            
            # 2. 强度评分柱状图
            ax2 = axes[0, 1]
            bars = ax2.bar(range(len(top_stocks)), top_stocks['strength_score'],
                          color=plt.cm.RdYlGn(top_stocks['strength_score']/100))
            ax2.set_title('强度评分排行榜', fontsize=14, fontweight='bold')
            ax2.set_xlabel('港股排名')
            ax2.set_ylabel('强度评分')
            ax2.set_xticks(range(len(top_stocks)))
            ax2.set_xticklabels(top_stocks['symbol'], rotation=45, ha='right')
            
            # 添加数值标签
            for i, bar in enumerate(bars):
                height = bar.get_height()
                ax2.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{height:.0f}', ha='center', va='bottom', fontsize=8)
            ax2.grid(True, alpha=0.3)
            
            # 3. 当前相对强度分布
            ax3 = axes[1, 0]
            current_rs_values = top_stocks['current_rs']
            
            # 创建颜色映射
            colors_scatter = plt.cm.RdYlGn(top_stocks['strength_score']/100)
            scatter = ax3.scatter(range(len(top_stocks)), current_rs_values,
                                c=top_stocks['strength_score'], cmap='RdYlGn',
                                s=100, alpha=0.7, edgecolors='black')
            
            # 添加股票标签
            for i, (_, stock) in enumerate(top_stocks.iterrows()):
                ax3.annotate(stock['symbol'], (i, stock['current_rs']),
                           xytext=(0, 10), textcoords='offset points',
                           ha='center', fontsize=8, alpha=0.8)
            
            ax3.axhline(y=100, color='red', linestyle='--', alpha=0.5, label='基准线(100)')
            ax3.set_title('当前相对强度分布', fontsize=14, fontweight='bold')
            ax3.set_xlabel('港股排名')
            ax3.set_ylabel('当前相对强度')
            ax3.set_xticks(range(len(top_stocks)))
            ax3.set_xticklabels(top_stocks['symbol'], rotation=45, ha='right')
            
            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax3)
            cbar.set_label('强度评分')
            ax3.grid(True, alpha=0.3)
            ax3.legend()
            
            # 4. 趋势分析热力图
            ax4 = axes[1, 1]
            
            # 准备热力图数据
            trend_data = []
            trend_labels = []
            
            for _, stock in top_stocks.iterrows():
                trends = []
                if '1M_slope' in stock:
                    trends.append(stock['1M_slope'])
                if '3M_slope' in stock:
                    trends.append(stock['3M_slope'])
                if '6M_slope' in stock:
                    trends.append(stock['6M_slope'])
                
                if trends:
                    trend_data.append(trends)
                    trend_labels.append(stock['symbol'])
            
            if trend_data:
                trend_matrix = np.array(trend_data)
                
                # 创建热力图
                im = ax4.imshow(trend_matrix, cmap='RdYlGn', aspect='auto')
                
                # 设置标签
                ax4.set_xticks(range(len(['1个月', '3个月', '6个月'])))
                ax4.set_xticklabels(['1个月', '3个月', '6个月'])
                ax4.set_yticks(range(len(trend_labels)))
                ax4.set_yticklabels(trend_labels)
                
                # 添加数值标签
                for i in range(len(trend_labels)):
                    for j in range(trend_matrix.shape[1]):
                        text = ax4.text(j, i, f'{trend_matrix[i, j]:.1f}',
                                      ha="center", va="center", color="black", fontsize=8)
                
                ax4.set_title('趋势斜率热力图', fontsize=14, fontweight='bold')
                
                # 添加颜色条
                cbar2 = plt.colorbar(im, ax=ax4)
                cbar2.set_label('趋势斜率')
            
            plt.tight_layout()
            
            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"📁 图表已保存到: {save_path}")
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                default_path = f"hsi_relative_strength_analysis_{timestamp}.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                print(f"📁 图表已保存到: {default_path}")
            
            plt.show()
            
        except Exception as e:
            print(f"❌ 绘图失败: {e}")
    
    def plot_individual_stock_analysis(self, symbols: List[str], save_path: str = None) -> None:
        """
        绘制个股详细分析图表
        
        Args:
            symbols: 股票代码列表
            save_path: 保存路径
        """
        try:
            print(f"📊 正在绘制个股详细分析...")
            
            n_stocks = len(symbols)
            if n_stocks == 0:
                print("❌ 没有指定股票")
                return
            
            # 计算子图布局
            cols = min(3, n_stocks)
            rows = (n_stocks + cols - 1) // cols
            
            fig, axes = plt.subplots(rows, cols, figsize=(6*cols, 5*rows))
            if n_stocks == 1:
                axes = [axes]
            elif rows == 1:
                axes = axes.reshape(1, -1)
            
            fig.suptitle('恒生指数成分股个股相对强度详细分析', fontsize=16, fontweight='bold')
            
            for i, symbol in enumerate(symbols):
                row = i // cols
                col = i % cols
                ax = axes[row, col] if rows > 1 else axes[col]
                
                if symbol in self.relative_strength_data:
                    rs_data = self.relative_strength_data[symbol]
                    
                    # 绘制相对强度线
                    ax.plot(rs_data.index, rs_data.values, 'b-', linewidth=2, label='相对强度')
                    
                    # 添加移动平均线
                    if len(rs_data) >= 20:
                        ma20 = rs_data.rolling(20).mean()
                        ax.plot(ma20.index, ma20.values, 'orange', linewidth=1, alpha=0.8, label='MA20')
                    
                    if len(rs_data) >= 50:
                        ma50 = rs_data.rolling(50).mean()
                        ax.plot(ma50.index, ma50.values, 'red', linewidth=1, alpha=0.8, label='MA50')
                    
                    # 添加基准线
                    ax.axhline(y=100, color='gray', linestyle='--', alpha=0.7, label='基准线')
                    
                    # 获取股票信息
                    stock_info = self.analysis_results[self.analysis_results['symbol'] == symbol]
                    if not stock_info.empty:
                        stock = stock_info.iloc[0]
                        title = f"{symbol}\n评分: {stock['strength_score']:.0f} | 相对强度: {stock['current_rs']:.1f}"
                    else:
                        title = f"{symbol}"
                    
                    ax.set_title(title, fontsize=12, fontweight='bold')
                    ax.set_xlabel('日期')
                    ax.set_ylabel('相对强度')
                    ax.legend(fontsize=8)
                    ax.grid(True, alpha=0.3)
                    
                    # 格式化x轴日期
                    ax.xaxis.set_major_formatter(mdates.DateFormatter('%m/%d'))
                    ax.xaxis.set_major_locator(mdates.MonthLocator(interval=2))
                    plt.setp(ax.xaxis.get_majorticklabels(), rotation=45)
                
                else:
                    ax.text(0.5, 0.5, f'无数据\n{symbol}', ha='center', va='center',
                           transform=ax.transAxes, fontsize=12)
                    ax.set_title(symbol, fontsize=12, fontweight='bold')
            
            # 隐藏多余的子图
            for i in range(n_stocks, rows * cols):
                row = i // cols
                col = i % cols
                if rows > 1:
                    axes[row, col].set_visible(False)
                else:
                    axes[col].set_visible(False)
            
            plt.tight_layout()
            
            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"📁 个股分析图表已保存到: {save_path}")
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                default_path = f"hsi_individual_analysis_{timestamp}.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                print(f"📁 个股分析图表已保存到: {default_path}")
            
            plt.show()
            
        except Exception as e:
            print(f"❌ 个股分析绘图失败: {e}")

    def plot_sector_analysis(self, save_path: str = None) -> None:
        """
        绘制行业分析图表

        Args:
            save_path: 保存路径
        """
        try:
            print(f"📊 正在绘制行业分析图表...")

            # 定义行业分类
            sector_mapping = {
                # 科技股
                '700': '科技', '9988': '科技', '3690': '科技', '1810': '科技',
                '9618': '科技', '1024': '科技', '9888': '科技', '981': '科技',
                '6618': '科技', '9999': '科技',

                # 金融股
                '939': '金融', '1398': '金融', '3988': '金融', '2318': '金融',
                '1299': '金融', '5': '金融', '388': '金融', '2388': '金融',
                '11': '金融',

                # 能源股
                '883': '能源', '857': '能源', '386': '能源', '1088': '能源',

                # 地产股
                '688': '地产', '1': '地产', '1113': '地产', '16': '地产',
                '12': '地产', '101': '地产', '1997': '地产', '1109': '地产',

                # 消费股
                '1929': '消费', '322': '消费', '2020': '消费', '2331': '消费',
                '1876': '消费', '288': '消费',

                # 医药股
                '1093': '医药', '1177': '医药', '3692': '医药', '1099': '医药',
                '2359': '医药', '2269': '医药', '241': '医药',

                # 工业股
                '175': '工业', '1211': '工业', '669': '工业', '2': '工业',
                '66': '工业', '3': '工业', '6': '工业'
            }

            # 为分析结果添加行业信息
            results_with_sector = self.analysis_results.copy()
            results_with_sector['sector'] = results_with_sector['symbol'].map(sector_mapping)
            results_with_sector['sector'] = results_with_sector['sector'].fillna('其他')

            # 计算行业统计
            sector_stats = results_with_sector.groupby('sector').agg({
                'strength_score': ['mean', 'count'],
                'current_rs': 'mean'
            }).round(2)

            sector_stats.columns = ['平均评分', '股票数量', '平均相对强度']
            sector_stats = sector_stats.sort_values('平均评分', ascending=False)

            # 创建图表
            fig, axes = plt.subplots(2, 2, figsize=(16, 12))
            fig.suptitle('恒生指数成分股行业分析', fontsize=16, fontweight='bold')

            # 1. 行业平均评分柱状图
            ax1 = axes[0, 0]
            bars1 = ax1.bar(sector_stats.index, sector_stats['平均评分'],
                           color=plt.cm.viridis(np.linspace(0, 1, len(sector_stats))))
            ax1.set_title('各行业平均强度评分', fontsize=14, fontweight='bold')
            ax1.set_ylabel('平均强度评分')
            ax1.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar in bars1:
                height = bar.get_height()
                ax1.text(bar.get_x() + bar.get_width()/2., height + 1,
                        f'{height:.1f}', ha='center', va='bottom')
            ax1.grid(True, alpha=0.3)

            # 2. 行业股票数量饼图
            ax2 = axes[0, 1]
            colors = plt.cm.Set3(np.linspace(0, 1, len(sector_stats)))
            wedges, texts, autotexts = ax2.pie(sector_stats['股票数量'],
                                              labels=sector_stats.index,
                                              autopct='%1.1f%%',
                                              colors=colors,
                                              startangle=90)
            ax2.set_title('各行业股票数量分布', fontsize=14, fontweight='bold')

            # 3. 行业相对强度散点图
            ax3 = axes[1, 0]
            scatter = ax3.scatter(sector_stats['平均评分'], sector_stats['平均相对强度'],
                                s=sector_stats['股票数量']*20, alpha=0.7,
                                c=range(len(sector_stats)), cmap='viridis')

            # 添加行业标签
            for i, sector in enumerate(sector_stats.index):
                ax3.annotate(sector,
                           (sector_stats.loc[sector, '平均评分'],
                            sector_stats.loc[sector, '平均相对强度']),
                           xytext=(5, 5), textcoords='offset points', fontsize=10)

            ax3.axhline(y=100, color='red', linestyle='--', alpha=0.5, label='基准线(100)')
            ax3.set_xlabel('平均强度评分')
            ax3.set_ylabel('平均相对强度')
            ax3.set_title('行业评分 vs 相对强度', fontsize=14, fontweight='bold')
            ax3.legend()
            ax3.grid(True, alpha=0.3)

            # 4. 各行业强势股数量
            ax4 = axes[1, 1]
            strong_stocks_by_sector = results_with_sector[results_with_sector['strength_score'] >= 70].groupby('sector').size()

            # 确保所有行业都有数据
            for sector in sector_stats.index:
                if sector not in strong_stocks_by_sector:
                    strong_stocks_by_sector[sector] = 0

            strong_stocks_by_sector = strong_stocks_by_sector.reindex(sector_stats.index)

            bars4 = ax4.bar(strong_stocks_by_sector.index, strong_stocks_by_sector.values,
                           color=plt.cm.plasma(np.linspace(0, 1, len(strong_stocks_by_sector))))
            ax4.set_title('各行业强势股数量 (评分≥70)', fontsize=14, fontweight='bold')
            ax4.set_ylabel('强势股数量')
            ax4.tick_params(axis='x', rotation=45)

            # 添加数值标签
            for bar in bars4:
                height = bar.get_height()
                if height > 0:
                    ax4.text(bar.get_x() + bar.get_width()/2., height + 0.1,
                            f'{int(height)}', ha='center', va='bottom')
            ax4.grid(True, alpha=0.3)

            plt.tight_layout()

            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"📁 行业分析图表已保存到: {save_path}")
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                default_path = f"hsi_sector_analysis_{timestamp}.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                print(f"📁 行业分析图表已保存到: {default_path}")

            plt.show()

            # 打印行业统计
            print(f"\n📊 行业分析统计:")
            print(sector_stats)

        except Exception as e:
            print(f"❌ 行业分析绘图失败: {e}")

    def plot_performance_dashboard(self, save_path: str = None) -> None:
        """
        绘制综合性能仪表板

        Args:
            save_path: 保存路径
        """
        try:
            print(f"📊 正在绘制综合性能仪表板...")

            # 创建图表
            fig = plt.figure(figsize=(20, 12))
            gs = fig.add_gridspec(3, 4, hspace=0.3, wspace=0.3)

            fig.suptitle('恒生指数成分股相对强度综合仪表板', fontsize=18, fontweight='bold')

            # 1. 强度评分分布直方图
            ax1 = fig.add_subplot(gs[0, 0])
            ax1.hist(self.analysis_results['strength_score'], bins=20, alpha=0.7, color='skyblue', edgecolor='black')
            ax1.set_title('强度评分分布', fontweight='bold')
            ax1.set_xlabel('强度评分')
            ax1.set_ylabel('股票数量')
            ax1.grid(True, alpha=0.3)

            # 2. 相对强度分布
            ax2 = fig.add_subplot(gs[0, 1])
            ax2.hist(self.analysis_results['current_rs'], bins=20, alpha=0.7, color='lightgreen', edgecolor='black')
            ax2.axvline(x=100, color='red', linestyle='--', alpha=0.7, label='基准线(100)')
            ax2.set_title('当前相对强度分布', fontweight='bold')
            ax2.set_xlabel('相对强度')
            ax2.set_ylabel('股票数量')
            ax2.legend()
            ax2.grid(True, alpha=0.3)

            # 3. 强势股比例饼图
            ax3 = fig.add_subplot(gs[0, 2])
            strong_count = len(self.analysis_results[self.analysis_results['strength_score'] >= 70])
            weak_count = len(self.analysis_results) - strong_count

            sizes = [strong_count, weak_count]
            labels = [f'强势股 ({strong_count})', f'其他 ({weak_count})']
            colors = ['#ff9999', '#66b3ff']

            ax3.pie(sizes, labels=labels, colors=colors, autopct='%1.1f%%', startangle=90)
            ax3.set_title('强势股比例 (评分≥70)', fontweight='bold')

            # 4. 趋势方向统计
            ax4 = fig.add_subplot(gs[0, 3])
            if '1M_slope' in self.analysis_results.columns:
                up_trend = len(self.analysis_results[self.analysis_results['1M_slope'] > 0])
                down_trend = len(self.analysis_results[self.analysis_results['1M_slope'] <= 0])

                ax4.bar(['上升趋势', '下降趋势'], [up_trend, down_trend],
                       color=['green', 'red'], alpha=0.7)
                ax4.set_title('1个月趋势方向', fontweight='bold')
                ax4.set_ylabel('股票数量')

                # 添加数值标签
                for i, v in enumerate([up_trend, down_trend]):
                    ax4.text(i, v + 0.5, str(v), ha='center', va='bottom')
            ax4.grid(True, alpha=0.3)

            # 5. 前10强势股相对强度线图
            ax5 = fig.add_subplot(gs[1, :])
            top_10 = self.analysis_results.head(10)

            for i, (_, stock) in enumerate(top_10.iterrows()):
                symbol = stock['symbol']
                if symbol in self.relative_strength_data:
                    rs_data = self.relative_strength_data[symbol]
                    ax5.plot(rs_data.index, rs_data.values,
                            label=f"{symbol} ({stock['strength_score']:.0f})",
                            linewidth=2, alpha=0.8)

            ax5.axhline(y=100, color='red', linestyle='--', alpha=0.7, label='基准线(100)')
            ax5.set_title('前10强势港股相对强度走势', fontsize=14, fontweight='bold')
            ax5.set_xlabel('日期')
            ax5.set_ylabel('相对强度')
            ax5.legend(bbox_to_anchor=(1.05, 1), loc='upper left', fontsize=10)
            ax5.grid(True, alpha=0.3)

            # 6. 评分vs相对强度散点图
            ax6 = fig.add_subplot(gs[2, 0:2])
            scatter = ax6.scatter(self.analysis_results['strength_score'],
                                self.analysis_results['current_rs'],
                                c=self.analysis_results['strength_score'],
                                cmap='RdYlGn', s=60, alpha=0.7, edgecolors='black')

            ax6.axhline(y=100, color='red', linestyle='--', alpha=0.5)
            ax6.axvline(x=70, color='blue', linestyle='--', alpha=0.5, label='强势线(70)')
            ax6.set_xlabel('强度评分')
            ax6.set_ylabel('当前相对强度')
            ax6.set_title('强度评分 vs 当前相对强度', fontweight='bold')
            ax6.legend()
            ax6.grid(True, alpha=0.3)

            # 添加颜色条
            cbar = plt.colorbar(scatter, ax=ax6)
            cbar.set_label('强度评分')

            # 7. 关键统计指标
            ax7 = fig.add_subplot(gs[2, 2:])
            ax7.axis('off')

            # 计算关键指标
            total_stocks = len(self.analysis_results)
            avg_score = self.analysis_results['strength_score'].mean()
            avg_rs = self.analysis_results['current_rs'].mean()
            strong_stocks = len(self.analysis_results[self.analysis_results['strength_score'] >= 70])
            above_benchmark = len(self.analysis_results[self.analysis_results['current_rs'] > 100])

            stats_text = f"""
关键统计指标

总股票数: {total_stocks}
平均强度评分: {avg_score:.1f}
平均相对强度: {avg_rs:.1f}

强势股数量: {strong_stocks} ({strong_stocks/total_stocks*100:.1f}%)
超越基准股数: {above_benchmark} ({above_benchmark/total_stocks*100:.1f}%)

分析日期: {datetime.now().strftime('%Y-%m-%d')}
            """

            ax7.text(0.1, 0.9, stats_text, transform=ax7.transAxes, fontsize=12,
                    verticalalignment='top', bbox=dict(boxstyle='round', facecolor='lightblue', alpha=0.8))

            # 保存图表
            if save_path:
                plt.savefig(save_path, dpi=300, bbox_inches='tight')
                print(f"📁 综合仪表板已保存到: {save_path}")
            else:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                default_path = f"hsi_performance_dashboard_{timestamp}.png"
                plt.savefig(default_path, dpi=300, bbox_inches='tight')
                print(f"📁 综合仪表板已保存到: {default_path}")

            plt.show()

        except Exception as e:
            print(f"❌ 综合仪表板绘图失败: {e}")
