#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
恒生指数成分股分红数据快速爬虫

分批处理，快速获取所有恒生指数成分股的分红数据
"""

import urllib.request
import urllib.parse
import json
import re
import csv
import time
from datetime import datetime

def parse_dividend_amount(plan_explain):
    """解析分红金额"""
    if not plan_explain or plan_explain == "未派发或宣派股息":
        return 0.0

    plan_text = str(plan_explain)

    # 港币金额模式
    hkd_patterns = [
        r'港币(\d+\.?\d*)元',
        r'相当于港币(\d+\.?\d+)元',
        r'港币(\d+\.?\d+)',
        r'每股派港币(\d+\.?\d*)元',
        r'派港币(\d+\.?\d*)元',
        r'派息港币(\d+\.?\d*)元',
    ]

    for pattern in hkd_patterns:
        match = re.search(pattern, plan_text)
        if match:
            try:
                return float(match.group(1))
            except:
                continue

    # 其他格式
    other_patterns = [
        r'每股派(\d+\.?\d*)港币',
        r'派(\d+\.?\d*)港币',
        r'每股(\d+\.?\d*)港币',
        r'每股派(\d+\.?\d*)元',
        r'派(\d+\.?\d*)元',
        r'每股(\d+\.?\d*)仙',
        r'派(\d+\.?\d*)仙',
        r'(\d+\.?\d*)港币',
    ]

    for pattern in other_patterns:
        match = re.search(pattern, plan_text)
        if match:
            try:
                amount = float(match.group(1))
                if '仙' in pattern:
                    amount = amount / 100
                return amount
            except:
                continue

    # 括号中的港币等值
    bracket_pattern = r'\(相当于港币(\d+\.?\d+)元'
    match = re.search(bracket_pattern, plan_text)
    if match:
        try:
            return float(match.group(1))
        except:
            pass

    return 0.0

def get_mock_price(stock_code):
    """生成模拟股价"""
    import hashlib
    hash_value = int(hashlib.md5(stock_code.encode()).hexdigest()[:8], 16)
    return float((hash_value % 400) + 100)

def fetch_single_stock(stock_code, stock_name):
    """获取单只股票数据"""
    base_url = "https://datacenter.eastmoney.com/securities/api/data/v1/get"
    headers = {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36',
        'Referer': 'https://datacenter.eastmoney.com/',
        'Accept': 'application/json, text/plain, */*',
    }

    params = {
        'reportName': 'RPT_HKF10_MAIN_DIVBASIC',
        'columns': 'SECURITY_CODE,UPDATE_DATE,REPORT_TYPE,EX_DIVIDEND_DATE,DIVIDEND_DATE,TRANSFER_END_DATE,YEAR,PLAN_EXPLAIN,IS_BFP',
        'quoteColumns': '',
        'filter': f'(SECURITY_CODE="{stock_code}")',
        'pageNumber': 1,
        'pageSize': 50,  # 增加获取记录数以获取更多历史数据
        'sortTypes': '-1,-1',
        'sortColumns': 'NOTICE_DATE,EX_DIVIDEND_DATE',
        'source': 'F10',
        'client': 'PC',
        'v': str(int(time.time() * 1000))
    }

    try:
        query_string = urllib.parse.urlencode(params)
        full_url = f"{base_url}?{query_string}"
        req = urllib.request.Request(full_url, headers=headers)

        with urllib.request.urlopen(req, timeout=10) as response:
            if response.status == 200:
                content = response.read().decode('utf-8')
                data = json.loads(content)

                if 'result' in data and 'data' in data['result']:
                    raw_records = data['result']['data']

                    # 解析分红记录 - 保存完整的历史数据
                    dividend_history = []
                    for record in raw_records:
                        plan_explain = record.get('PLAN_EXPLAIN', '')
                        amount = parse_dividend_amount(plan_explain)

                        if amount > 0:
                            dividend_history.append({
                                'year': record.get('YEAR'),
                                'ex_date': record.get('EX_DIVIDEND_DATE'),
                                'dividend_date': record.get('DIVIDEND_DATE'),
                                'amount': amount,
                                'plan': plan_explain,
                                'report_type': record.get('REPORT_TYPE')
                            })

                    # 按年份排序（最新的在前）
                    dividend_history.sort(key=lambda x: x['year'] if x['year'] else 0, reverse=True)

                    # 计算汇总指标
                    latest_dividend = dividend_history[0]['amount'] if dividend_history else 0
                    dividend_years = len(dividend_history)
                    mock_price = get_mock_price(stock_code)
                    dividend_yield = (latest_dividend / mock_price) * 100 if latest_dividend > 0 else 0

                    # 计算平均分红（近5年）
                    recent_dividends = [d['amount'] for d in dividend_history[:5]]
                    avg_dividend_5y = sum(recent_dividends) / len(recent_dividends) if recent_dividends else 0
                    avg_yield_5y = (avg_dividend_5y / mock_price) * 100 if avg_dividend_5y > 0 else 0

                    return {
                        'code': stock_code,
                        'name': stock_name,
                        'dividend_history': dividend_history,  # 完整的分红历史
                        'latest_dividend': latest_dividend,
                        'avg_dividend_5y': avg_dividend_5y,
                        'dividend_years': dividend_years,
                        'mock_price': mock_price,
                        'dividend_yield': dividend_yield,
                        'avg_yield_5y': avg_yield_5y,
                        'status': 'success'
                    }
                else:
                    return {'code': stock_code, 'name': stock_name, 'status': 'no_data'}
            else:
                return {'code': stock_code, 'name': stock_name, 'status': f'http_{response.status}'}

    except Exception as e:
        return {'code': stock_code, 'name': stock_name, 'status': 'error', 'error': str(e)[:50]}

def load_hsi_stocks():
    """加载恒生指数成分股"""
    stocks = []
    try:
        with open('data_files/hsi_constituents.csv', 'r', encoding='utf-8') as f:
            reader = csv.DictReader(f)
            for row in reader:
                code = str(row['代码']).strip()
                name = str(row['名称']).strip()
                if code and code != 'nan' and len(code) > 0:
                    stocks.append({
                        'code': code.zfill(5),
                        'name': name
                    })
        return stocks
    except Exception as e:
        print(f"❌ 加载股票列表失败: {e}")
        return []

def process_batch(stocks, batch_size=10):
    """分批处理股票"""
    results = []
    total = len(stocks)
    
    print(f"🚀 开始处理 {total} 只股票，分 {(total + batch_size - 1) // batch_size} 批...")
    
    for i in range(0, total, batch_size):
        batch = stocks[i:i + batch_size]
        batch_num = i // batch_size + 1
        total_batches = (total + batch_size - 1) // batch_size
        
        print(f"\n📦 批次 {batch_num}/{total_batches} (股票 {i+1}-{min(i+batch_size, total)}):")
        
        batch_results = []
        for j, stock in enumerate(batch):
            print(f"  [{j+1}/{len(batch)}] {stock['code']} ({stock['name'][:15]:<15})", end=" ")

            result = fetch_single_stock(stock['code'], stock['name'])
            batch_results.append(result)

            if result['status'] == 'success':
                dividend = result['latest_dividend']
                yield_rate = result['dividend_yield']
                years = result['dividend_years']
                print(f"✅ {dividend:.3f}港元 ({yield_rate:.2f}%) {years}年")
            else:
                print(f"❌ {result['status']}")

            time.sleep(0.8)  # 减少延时
        
        results.extend(batch_results)
        
        # 批次间稍长延时
        if batch_num < total_batches:
            print(f"⏳ 批次完成，等待 3 秒...")
            time.sleep(3)
    
    return results

def generate_summary(results):
    """生成结果汇总"""
    successful = [r for r in results if r['status'] == 'success']
    dividend_stocks = [r for r in successful if r['latest_dividend'] > 0]
    
    print(f"\n📊 爬取结果汇总")
    print("=" * 60)
    print(f"总股票数: {len(results)}")
    print(f"成功获取: {len(successful)}")
    print(f"有分红记录: {len(dividend_stocks)}")
    print(f"获取失败: {len(results) - len(successful)}")
    
    if dividend_stocks:
        # 按股息率排序
        dividend_stocks.sort(key=lambda x: x['dividend_yield'], reverse=True)
        
        print(f"\n🏆 股息率前15名:")
        print("-" * 85)
        print(f"{'代码':<8} {'名称':<18} {'最新分红':<10} {'5年均分红':<10} {'股息率':<8} {'5年均率':<8} {'年数':<6}")
        print("-" * 85)

        for i, stock in enumerate(dividend_stocks[:15]):
            name = stock['name'][:16] + '..' if len(stock['name']) > 16 else stock['name']
            print(f"{stock['code']:<8} {name:<18} {stock['latest_dividend']:<10.3f} {stock['avg_dividend_5y']:<10.3f} {stock['dividend_yield']:<8.2f}% {stock['avg_yield_5y']:<8.2f}% {stock['dividend_years']:<6}")

        # 统计
        dividends = [s['latest_dividend'] for s in dividend_stocks]
        yields = [s['dividend_yield'] for s in dividend_stocks]
        avg_dividends_5y = [s['avg_dividend_5y'] for s in dividend_stocks]
        avg_yields_5y = [s['avg_yield_5y'] for s in dividend_stocks]

        print(f"\n📈 统计信息:")
        print(f"平均分红(最新): {sum(dividends)/len(dividends):.3f} 港元")
        print(f"平均分红(5年): {sum(avg_dividends_5y)/len(avg_dividends_5y):.3f} 港元")
        print(f"最高分红: {max(dividends):.3f} 港元")
        print(f"平均股息率(最新): {sum(yields)/len(yields):.2f}%")
        print(f"平均股息率(5年): {sum(avg_yields_5y)/len(avg_yields_5y):.2f}%")
        print(f"最高股息率: {max(yields):.2f}%")

def save_individual_stock_data(result, timestamp):
    """保存单只股票的分红数据到独立文件"""
    if result['status'] != 'success':
        return None

    try:
        # 创建股票数据目录
        import os
        stock_data_dir = f"hsi_dividend_data_{timestamp}"
        if not os.path.exists(stock_data_dir):
            os.makedirs(stock_data_dir)

        # 单只股票的数据文件
        stock_filename = f"{stock_data_dir}/{result['code']}_{result['name'].replace('/', '_')}.json"

        stock_data = {
            'stock_code': result['code'],
            'stock_name': result['name'],
            'scrape_time': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            'summary': {
                'latest_dividend': result['latest_dividend'],
                'avg_dividend_5y': result['avg_dividend_5y'],
                'dividend_years': result['dividend_years'],
                'mock_price': result['mock_price'],
                'dividend_yield': result['dividend_yield'],
                'avg_yield_5y': result['avg_yield_5y']
            },
            'dividend_history': result['dividend_history']
        }

        with open(stock_filename, 'w', encoding='utf-8') as f:
            json.dump(stock_data, f, ensure_ascii=False, indent=2)

        return stock_filename

    except Exception as e:
        print(f"   ❌ 保存 {result['code']} 数据失败: {e}")
        return None

def save_results(results):
    """保存结果"""
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')

    # 1. 保存汇总CSV
    summary_filename = f"hsi_dividend_summary_{timestamp}.csv"
    try:
        with open(summary_filename, 'w', newline='', encoding='utf-8') as f:
            writer = csv.writer(f)
            writer.writerow(['股票代码', '股票名称', '最新分红(港元)', '5年平均分红(港元)', '股息率(%)', '5年平均股息率(%)', '分红年数', '模拟股价(港元)', '状态'])

            for result in results:
                if result['status'] == 'success':
                    writer.writerow([
                        result['code'],
                        result['name'],
                        f"{result['latest_dividend']:.3f}",
                        f"{result['avg_dividend_5y']:.3f}",
                        f"{result['dividend_yield']:.2f}",
                        f"{result['avg_yield_5y']:.2f}",
                        result['dividend_years'],
                        f"{result['mock_price']:.2f}",
                        result['status']
                    ])
                else:
                    writer.writerow([
                        result['code'],
                        result['name'],
                        'N/A', 'N/A', 'N/A', 'N/A', 'N/A', 'N/A',
                        result['status']
                    ])

        print(f"💾 汇总数据已保存到: {summary_filename}")

    except Exception as e:
        print(f"❌ 保存汇总数据失败: {e}")
        summary_filename = None

    # 2. 为每只股票保存独立的分红数据文件
    print(f"\n📁 开始保存每只股票的独立数据文件...")
    successful_files = []
    failed_count = 0

    for result in results:
        if result['status'] == 'success':
            stock_file = save_individual_stock_data(result, timestamp)
            if stock_file:
                successful_files.append(stock_file)
            else:
                failed_count += 1

    stock_data_dir = f"hsi_dividend_data_{timestamp}"
    print(f"💾 成功保存 {len(successful_files)} 只股票的独立数据文件")
    if failed_count > 0:
        print(f"❌ {failed_count} 只股票的数据文件保存失败")
    print(f"📂 股票数据目录: {stock_data_dir}")

    return summary_filename, stock_data_dir

def main():
    """主函数"""
    print("🎯 恒生指数成分股分红数据快速爬虫")
    print("📡 数据来源: 东方财富网API")
    print("=" * 50)
    
    start_time = time.time()
    
    try:
        # 1. 加载股票列表
        print("📁 加载恒生指数成分股...")
        stocks = load_hsi_stocks()
        
        if not stocks:
            print("❌ 无法加载股票列表")
            return
        
        print(f"✅ 加载了 {len(stocks)} 只股票")
        
        # 2. 分批处理
        results = process_batch(stocks, batch_size=8)
        
        # 3. 生成汇总
        generate_summary(results)
        
        # 4. 保存结果
        summary_file, stock_data_dir = save_results(results)

        # 5. 完成信息
        elapsed = time.time() - start_time
        print(f"\n✅ 快速爬取完成！")
        print(f"⏱️  总耗时: {elapsed/60:.1f} 分钟")
        print(f"📁 输出文件:")
        if summary_file:
            print(f"   📊 汇总数据: {summary_file}")
        if stock_data_dir:
            print(f"   📂 股票数据目录: {stock_data_dir}")
            print(f"   💡 每只股票的分红历史数据都单独存储在该目录中")
        
    except KeyboardInterrupt:
        print("\n⚠️  用户中断程序")
    except Exception as e:
        print(f"\n❌ 程序出错: {e}")
        import traceback
        traceback.print_exc()

def test_single_stock(stock_code, stock_name):
    """测试单只股票的数据获取"""
    print(f"🧪 测试股票: {stock_code} ({stock_name})")
    result = fetch_single_stock(stock_code, stock_name)

    if result['status'] == 'success':
        print(f"✅ 成功获取数据")
        print(f"📊 分红年数: {result['dividend_years']}")
        print(f"💰 最新分红: {result['latest_dividend']} 港元")
        print(f"📈 股息率: {result['dividend_yield']:.2f}%")
        print(f"\n📋 分红历史:")
        for i, div in enumerate(result['dividend_history']):
            print(f"  {i+1:2d}. {div['year']} 年: {div['amount']:.2f} 港元 (除息日: {div['ex_date']})")
    else:
        print(f"❌ 获取失败: {result['status']}")

    return result

if __name__ == "__main__":
    # 如果想测试单只股票，取消下面的注释
    test_single_stock("00700", "腾讯控股")
    # main()
