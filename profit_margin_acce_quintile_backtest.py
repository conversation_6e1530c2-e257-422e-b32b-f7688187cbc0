#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
净利润率增长加速度因子5分组季度调仓回测
回测时间：最近10年（2015-2024）
注：使用净利润率（net_margin）作为盈利能力指标
"""

import pandas as pd
import numpy as np
import sqlite3
from datetime import datetime, timedelta
from typing import Dict, List, Tuple
import warnings
import matplotlib.pyplot as plt
import seaborn as sns
import os
try:
    from scipy.stats import spearmanr
except ImportError:
    print("⚠️ scipy未安装，将跳过秩相关系数计算")
    spearmanr = None
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ProfitMarginQuintileBacktest:
    """净利润率增长加速度5分组回测分析类"""
    
    def __init__(self, 
                 financial_db_path: str = "ganggutong_financial_data.db",
                 price_db_path: str = "ganggutong_10year_data.db"):
        self.financial_db_path = financial_db_path
        self.price_db_path = price_db_path
        self.financial_data = None
        self.price_data = {}
        self.factor_data = None
        
    def load_data(self):
        """加载数据"""
        print("📊 加载数据...")
        
        # 加载财务数据
        conn = sqlite3.connect(self.financial_db_path)
        query = """
        SELECT stock_code, report_date, period_type, net_margin
        FROM financial_data 
        WHERE net_margin IS NOT NULL 
        ORDER BY stock_code, report_date
        """
        self.financial_data = pd.read_sql_query(query, conn)
        conn.close()
        
        self.financial_data['report_date'] = pd.to_datetime(self.financial_data['report_date'])
        self.financial_data['net_margin'] = pd.to_numeric(self.financial_data['net_margin'], errors='coerce')
        
        # 加载价格数据并转换为字典结构
        conn = sqlite3.connect(self.price_db_path)
        query = """
        SELECT stock_code, date, close
        FROM stock_prices 
        WHERE close IS NOT NULL 
        ORDER BY stock_code, date
        """
        price_df = pd.read_sql_query(query, conn)
        conn.close()
        
        price_df['date'] = pd.to_datetime(price_df['date'])
        price_df['close'] = pd.to_numeric(price_df['close'], errors='coerce')
        
        # 转换为字典结构以提高查询效率
        print("🔄 优化价格数据结构...")
        for stock_code in price_df['stock_code'].unique():
            stock_prices = price_df[price_df['stock_code'] == stock_code].copy()
            stock_prices = stock_prices.set_index('date').sort_index()
            self.price_data[stock_code] = stock_prices['close']
        
        print(f"✅ 财务数据: {len(self.financial_data)} 条")
        print(f"✅ 价格数据: {len(self.price_data)} 只股票")
        
    def calculate_factors(self):
        """计算净利润率增长加速度因子"""
        print("🧮 计算净利润率增长加速度因子...")
        
        factor_data = []
        
        for stock_code in self.financial_data['stock_code'].unique():
            stock_data = self.financial_data[
                self.financial_data['stock_code'] == stock_code
            ].copy().sort_values('report_date')
            
            # 分别处理季度和年度数据
            for period_type in ['quarterly', 'annual']:
                period_data = stock_data[
                    stock_data['period_type'] == period_type
                ].copy()
                
                if len(period_data) < 6:
                    continue
                
                # 计算净利润率变化
                period_data['net_margin_change'] = period_data['net_margin'].diff() * 100
                
                # 计算净利润率增长加速度
                period_data['net_margin_acceleration'] = period_data['net_margin_change'].diff()
                
                factor_data.append(period_data)
        
        if factor_data:
            self.factor_data = pd.concat(factor_data, ignore_index=True)
            self.factor_data = self.factor_data.dropna(subset=['net_margin_acceleration'])
            print(f"✅ 因子数据: {len(self.factor_data)} 条")
        else:
            raise ValueError("无法计算因子数据")


def main():
    """主函数"""
    print("🚀 开始净利润率增长加速度因子季度调仓回测分析...")
    print("📝 注意：Q1组为净利润率增长加速度最高组，Q5组为最低组")
    print("📝 说明：使用净利润率变化的加速度作为因子")
    print("="*80)

    # 检查数据可用性
    import sqlite3
    conn = sqlite3.connect("ganggutong_financial_data.db")
    cursor = conn.cursor()
    cursor.execute("SELECT COUNT(*) FROM financial_data WHERE net_margin IS NOT NULL")
    count = cursor.fetchone()[0]
    conn.close()
    
    print(f"📊 净利润率数据记录数: {count}")
    
    if count < 100:
        print("❌ 净利润率数据不足，无法进行有效回测")
        print("💡 建议使用毛利润增长加速度因子（已验证有效）")
        return None
    
    # 运行回测分析
    backtest = ProfitMarginQuintileBacktest()
    try:
        results = backtest.run_full_analysis(rebalance_freq='Q')
        print("\n" + "="*80)
        print("🎉 分析完成！")
        print("📁 结果文件保存在 profit_margin_quintile_backtest_results/ 目录下")
        return results
    except Exception as e:
        print(f"❌ 回测失败: {e}")
        return None


if __name__ == "__main__":
    main()
