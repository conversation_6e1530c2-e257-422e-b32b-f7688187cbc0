#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
港股通数据查询工具
用于查询和分析已下载的港股通成分股数据
"""

import sqlite3
import pandas as pd
from datetime import datetime, timedelta
import matplotlib.pyplot as plt
import seaborn as sns

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

DATABASE_NAME = 'ganggutong_10year_data.db'

class GangguTongDataQuery:
    def __init__(self, db_name=DATABASE_NAME):
        self.db_name = db_name
    
    def get_connection(self):
        """获取数据库连接"""
        return sqlite3.connect(self.db_name)
    
    def get_stock_list(self):
        """获取所有股票列表"""
        query = """
        SELECT stock_code, stock_name, industry,
               (SELECT COUNT(*) FROM stock_prices WHERE stock_code = si.stock_code) as data_count,
               (SELECT MIN(date) FROM stock_prices WHERE stock_code = si.stock_code) as start_date,
               (SELECT MAX(date) FROM stock_prices WHERE stock_code = si.stock_code) as end_date
        FROM stock_info si
        ORDER BY stock_code
        """
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn)
        
        return df
    
    def get_stock_data(self, stock_code, start_date=None, end_date=None):
        """获取指定股票的价格数据"""
        query = """
        SELECT sp.*, si.stock_name, si.industry
        FROM stock_prices sp
        JOIN stock_info si ON sp.stock_code = si.stock_code
        WHERE sp.stock_code = ?
        """
        params = [stock_code]
        
        if start_date:
            query += " AND sp.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND sp.date <= ?"
            params.append(end_date)
        
        query += " ORDER BY sp.date"
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=params)
        
        if not df.empty:
            df['date'] = pd.to_datetime(df['date'])
        
        return df
    
    def get_industry_summary(self):
        """获取行业汇总信息"""
        query = """
        SELECT si.industry,
               COUNT(DISTINCT si.stock_code) as stock_count,
               AVG(data_counts.data_count) as avg_data_count
        FROM stock_info si
        LEFT JOIN (
            SELECT stock_code, COUNT(*) as data_count
            FROM stock_prices
            GROUP BY stock_code
        ) data_counts ON si.stock_code = data_counts.stock_code
        GROUP BY si.industry
        ORDER BY stock_count DESC
        """
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn)
        
        return df
    
    def get_data_summary(self):
        """获取数据库整体统计信息"""
        with self.get_connection() as conn:
            # 股票总数
            stock_count = pd.read_sql_query("SELECT COUNT(*) as count FROM stock_info", conn).iloc[0]['count']
            
            # 价格记录总数
            price_count = pd.read_sql_query("SELECT COUNT(*) as count FROM stock_prices", conn).iloc[0]['count']
            
            # 日期范围
            date_range = pd.read_sql_query("""
                SELECT MIN(date) as min_date, MAX(date) as max_date 
                FROM stock_prices
            """, conn).iloc[0]
            
            # 有数据的股票数量
            stocks_with_data = pd.read_sql_query("""
                SELECT COUNT(DISTINCT stock_code) as count FROM stock_prices
            """, conn).iloc[0]['count']
        
        return {
            'total_stocks': stock_count,
            'stocks_with_data': stocks_with_data,
            'total_price_records': price_count,
            'date_range': (date_range['min_date'], date_range['max_date']),
            'avg_records_per_stock': price_count / stocks_with_data if stocks_with_data > 0 else 0
        }
    
    def search_stocks(self, keyword):
        """搜索股票（按代码或名称）"""
        query = """
        SELECT stock_code, stock_name, industry,
               (SELECT COUNT(*) FROM stock_prices WHERE stock_code = si.stock_code) as data_count
        FROM stock_info si
        WHERE stock_code LIKE ? OR stock_name LIKE ?
        ORDER BY stock_code
        """
        
        search_pattern = f"%{keyword}%"
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=[search_pattern, search_pattern])
        
        return df
    
    def get_top_stocks_by_volume(self, top_n=20, start_date=None, end_date=None):
        """获取成交量最大的股票"""
        query = """
        SELECT sp.stock_code, si.stock_name, si.industry,
               AVG(sp.volume) as avg_volume,
               SUM(sp.volume) as total_volume,
               COUNT(*) as trading_days
        FROM stock_prices sp
        JOIN stock_info si ON sp.stock_code = si.stock_code
        WHERE sp.volume IS NOT NULL
        """
        params = []
        
        if start_date:
            query += " AND sp.date >= ?"
            params.append(start_date)
        
        if end_date:
            query += " AND sp.date <= ?"
            params.append(end_date)
        
        query += """
        GROUP BY sp.stock_code, si.stock_name, si.industry
        ORDER BY avg_volume DESC
        LIMIT ?
        """
        params.append(top_n)
        
        with self.get_connection() as conn:
            df = pd.read_sql_query(query, conn, params=params)
        
        return df
    
    def plot_stock_price(self, stock_code, start_date=None, end_date=None):
        """绘制股票价格走势图"""
        df = self.get_stock_data(stock_code, start_date, end_date)
        
        if df.empty:
            print(f"没有找到股票 {stock_code} 的数据")
            return
        
        stock_name = df.iloc[0]['stock_name']
        
        plt.figure(figsize=(12, 8))
        
        # 绘制价格走势
        plt.subplot(2, 1, 1)
        plt.plot(df['date'], df['close'], label='收盘价', linewidth=1)
        plt.plot(df['date'], df['high'], alpha=0.3, label='最高价', linewidth=0.5)
        plt.plot(df['date'], df['low'], alpha=0.3, label='最低价', linewidth=0.5)
        plt.title(f'{stock_code} ({stock_name}) 价格走势')
        plt.ylabel('价格')
        plt.legend()
        plt.grid(True, alpha=0.3)
        
        # 绘制成交量
        plt.subplot(2, 1, 2)
        plt.bar(df['date'], df['volume'], alpha=0.7, width=1)
        plt.title('成交量')
        plt.ylabel('成交量')
        plt.xlabel('日期')
        plt.grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.show()
    
    def export_stock_data(self, stock_code, filename=None):
        """导出股票数据到CSV文件"""
        df = self.get_stock_data(stock_code)
        
        if df.empty:
            print(f"没有找到股票 {stock_code} 的数据")
            return
        
        if filename is None:
            stock_name = df.iloc[0]['stock_name']
            filename = f"{stock_code}_{stock_name}_data.csv"
        
        df.to_csv(filename, index=False, encoding='utf-8-sig')
        print(f"数据已导出到: {filename}")

def main():
    """主函数 - 提供交互式查询界面"""
    query_tool = GangguTongDataQuery()
    
    print("=" * 60)
    print("港股通数据查询工具")
    print("=" * 60)
    
    try:
        # 显示数据库概况
        summary = query_tool.get_data_summary()
        print(f"\n数据库概况:")
        print(f"  总股票数: {summary['total_stocks']}")
        print(f"  有数据股票数: {summary['stocks_with_data']}")
        print(f"  总价格记录数: {summary['total_price_records']:,}")
        print(f"  数据日期范围: {summary['date_range'][0]} 到 {summary['date_range'][1]}")
        print(f"  平均每股记录数: {summary['avg_records_per_stock']:.0f}")
        
        while True:
            print("\n" + "=" * 60)
            print("请选择操作:")
            print("1. 查看所有股票列表")
            print("2. 搜索股票")
            print("3. 查看股票详细数据")
            print("4. 查看行业汇总")
            print("5. 查看成交量排行")
            print("6. 绘制股票价格图")
            print("7. 导出股票数据")
            print("0. 退出")
            
            choice = input("\n请输入选择 (0-7): ").strip()
            
            if choice == '0':
                print("再见！")
                break
            elif choice == '1':
                df = query_tool.get_stock_list()
                print(f"\n股票列表 (共 {len(df)} 只):")
                print(df.to_string(index=False))
            elif choice == '2':
                keyword = input("请输入搜索关键词 (股票代码或名称): ").strip()
                if keyword:
                    df = query_tool.search_stocks(keyword)
                    if not df.empty:
                        print(f"\n搜索结果:")
                        print(df.to_string(index=False))
                    else:
                        print("没有找到匹配的股票")
            elif choice == '3':
                stock_code = input("请输入股票代码: ").strip()
                if stock_code:
                    df = query_tool.get_stock_data(stock_code)
                    if not df.empty:
                        print(f"\n{stock_code} 数据 (共 {len(df)} 条记录):")
                        print(df.head(10).to_string(index=False))
                        if len(df) > 10:
                            print("... (仅显示前10条)")
                    else:
                        print("没有找到该股票的数据")
            elif choice == '4':
                df = query_tool.get_industry_summary()
                print(f"\n行业汇总:")
                print(df.to_string(index=False))
            elif choice == '5':
                try:
                    top_n = int(input("请输入要显示的股票数量 (默认20): ").strip() or "20")
                    df = query_tool.get_top_stocks_by_volume(top_n)
                    print(f"\n成交量排行 (前 {top_n} 名):")
                    print(df.to_string(index=False))
                except ValueError:
                    print("请输入有效的数字")
            elif choice == '6':
                stock_code = input("请输入股票代码: ").strip()
                if stock_code:
                    query_tool.plot_stock_price(stock_code)
            elif choice == '7':
                stock_code = input("请输入股票代码: ").strip()
                if stock_code:
                    filename = input("请输入文件名 (留空使用默认名称): ").strip() or None
                    query_tool.export_stock_data(stock_code, filename)
            else:
                print("无效的选择，请重新输入")
    
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n发生错误: {e}")

if __name__ == "__main__":
    main()
