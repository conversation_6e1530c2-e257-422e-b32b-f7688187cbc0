"""
多指数月末交易策略批量回测脚本

该脚本将在以下指数上运行月末交易策略：
- 中证300 (CSI300)
- 中证500 (CSI500)
- 中证1000 (CSI1000)
- 中证2000 (CSI2000)
- 恒生指数 (HSI)
- 恒生科技指数 (HSTECH)

策略描述：
- 买入时机：每月倒数第2个交易日买入指定指数
- 卖出时机：下个月持有5个交易日后卖出
- 其余时间持有现金

作者：AI Assistant
创建时间：2025年1月
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from datetime import datetime
import warnings
warnings.filterwarnings('ignore')

# 导入我们的策略类
from csi300_monthly_strategy import UniversalMonthlyStrategy, generate_comparison_report

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def run_comprehensive_backtest():
    """运行全面的多指数回测"""
    
    # 要回测的指数列表
    indices = ['CSI300', 'CSI500', 'CSI1000', 'CSI2000', 'HSI', 'HSTECH']
    
    print("🚀 多指数月末交易策略批量回测")
    print("="*80)
    print("策略：月末倒数第2个交易日买入，下月持有5个交易日后卖出")
    print("="*80)
    print("将在以下指数上运行策略：")
    
    # 显示指数信息
    for idx in indices:
        strategy_temp = UniversalMonthlyStrategy(index_symbol=idx)
        config = strategy_temp.index_config[idx]
        print(f"  - {idx}: {config['name']}")
    print("="*80)
    
    # 存储所有回测结果
    all_results = []
    successful_backtests = 0
    failed_backtests = 0
    
    # 逐个运行回测
    for i, index_symbol in enumerate(indices, 1):
        print(f"\n📈 [{i}/{len(indices)}] 开始回测 {index_symbol}")
        print("-" * 60)
        
        try:
            # 创建策略实例
            strategy = UniversalMonthlyStrategy(
                index_symbol=index_symbol, 
                start_date="2005-01-01"
            )
            
            config = strategy.index_config[index_symbol]
            print(f"正在回测: {config['name']}")
            
            # 获取数据
            if not strategy.get_index_data():
                print(f"❌ {index_symbol} 数据获取失败，跳过")
                failed_backtests += 1
                continue

            # 检查是否使用了模拟数据，如果是则跳过
            if hasattr(strategy, '_used_demo_data') and strategy._used_demo_data:
                print(f"⚠️ {index_symbol} 只能获取模拟数据，跳过真实回测")
                failed_backtests += 1
                continue
            
            print(f"✅ 数据获取成功，时间范围：{strategy.data.index[0].date()} 到 {strategy.data.index[-1].date()}")
            print(f"   总数据点数：{len(strategy.data)}")
            
            # 生成交易信号
            if not strategy.generate_signals():
                print(f"❌ {index_symbol} 信号生成失败，跳过")
                failed_backtests += 1
                continue
            
            # 执行回测
            results = strategy.backtest()
            if results is None:
                print(f"❌ {index_symbol} 回测执行失败，跳过")
                failed_backtests += 1
                continue
            
            # 保存结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            plot_filename = f"{index_symbol.lower()}_monthly_strategy_{timestamp}.png"
            
            # 绘制图表
            strategy.plot_results(save_path=plot_filename)
            
            # 保存详细结果
            strategy.save_results()
            
            # 存储结果用于对比
            all_results.append({
                'index': index_symbol,
                'strategy': strategy,
                'results': results,
                'plot_file': plot_filename
            })
            
            successful_backtests += 1
            print(f"✅ {index_symbol} 回测完成")
            
            # 打印简要结果
            print(f"   策略总收益: {results['strategy_total_return']:.2%}")
            print(f"   基准总收益: {results['benchmark_total_return']:.2%}")
            print(f"   策略夏普比率: {results['strategy_sharpe']:.3f}")
            print(f"   胜率: {results['win_rate']:.2%}")
            
        except Exception as e:
            print(f"❌ {index_symbol} 回测过程中发生错误: {e}")
            failed_backtests += 1
            continue
    
    # 生成汇总报告
    print("\n" + "="*80)
    print("📊 回测汇总")
    print("="*80)
    print(f"总指数数量: {len(indices)}")
    print(f"成功回测: {successful_backtests}")
    print(f"失败回测: {failed_backtests}")
    
    if all_results:
        print(f"\n正在生成对比报告...")
        comparison_df = generate_comparison_report(all_results)
        
        # 生成可视化对比图
        create_comparison_charts(all_results)
        
        print(f"\n🎉 批量回测完成！")
        print(f"📊 成功回测了 {successful_backtests} 个指数")
        print(f"📋 详细结果和图表已保存")
        
        return all_results, comparison_df
    else:
        print("❌ 没有成功的回测结果")
        return None, None

def create_comparison_charts(all_results):
    """创建对比图表"""
    if not all_results:
        return
    
    # 创建对比图表
    fig, axes = plt.subplots(2, 2, figsize=(16, 12))
    fig.suptitle('多指数月末交易策略回测结果对比', fontsize=16, fontweight='bold')
    
    # 提取数据
    index_names = []
    strategy_returns = []
    benchmark_returns = []
    sharpe_ratios = []
    max_drawdowns = []
    win_rates = []
    
    for result in all_results:
        strategy = result['strategy']
        results = result['results']
        config = strategy.index_config[result['index']]
        
        index_names.append(config['name'])
        strategy_returns.append(results['strategy_total_return'] * 100)
        benchmark_returns.append(results['benchmark_total_return'] * 100)
        sharpe_ratios.append(results['strategy_sharpe'])
        max_drawdowns.append(abs(results['strategy_max_drawdown']) * 100)
        win_rates.append(results['win_rate'] * 100)
    
    # 1. 总收益对比
    ax1 = axes[0, 0]
    x = np.arange(len(index_names))
    width = 0.35
    
    ax1.bar(x - width/2, strategy_returns, width, label='策略收益', alpha=0.8, color='red')
    ax1.bar(x + width/2, benchmark_returns, width, label='基准收益', alpha=0.8, color='blue')
    ax1.set_title('总收益对比 (%)')
    ax1.set_ylabel('收益率 (%)')
    ax1.set_xticks(x)
    ax1.set_xticklabels(index_names, rotation=45, ha='right')
    ax1.legend()
    ax1.grid(True, alpha=0.3)
    
    # 2. 夏普比率对比
    ax2 = axes[0, 1]
    bars = ax2.bar(index_names, sharpe_ratios, alpha=0.8, color='green')
    ax2.set_title('夏普比率对比')
    ax2.set_ylabel('夏普比率')
    ax2.set_xticklabels(index_names, rotation=45, ha='right')
    ax2.grid(True, alpha=0.3)
    
    # 在柱子上显示数值
    for bar, value in zip(bars, sharpe_ratios):
        ax2.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 0.01,
                f'{value:.2f}', ha='center', va='bottom')
    
    # 3. 最大回撤对比
    ax3 = axes[1, 0]
    bars = ax3.bar(index_names, max_drawdowns, alpha=0.8, color='orange')
    ax3.set_title('最大回撤对比 (%)')
    ax3.set_ylabel('最大回撤 (%)')
    ax3.set_xticklabels(index_names, rotation=45, ha='right')
    ax3.grid(True, alpha=0.3)
    
    # 4. 胜率对比
    ax4 = axes[1, 1]
    bars = ax4.bar(index_names, win_rates, alpha=0.8, color='purple')
    ax4.set_title('胜率对比 (%)')
    ax4.set_ylabel('胜率 (%)')
    ax4.set_xticklabels(index_names, rotation=45, ha='right')
    ax4.grid(True, alpha=0.3)
    
    # 在柱子上显示数值
    for bar, value in zip(bars, win_rates):
        ax4.text(bar.get_x() + bar.get_width()/2, bar.get_height() + 1,
                f'{value:.1f}%', ha='center', va='bottom')
    
    plt.tight_layout()
    
    # 保存对比图表
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    comparison_chart_filename = f"multi_index_strategy_comparison_{timestamp}.png"
    plt.savefig(comparison_chart_filename, dpi=300, bbox_inches='tight')
    print(f"📊 对比图表已保存至: {comparison_chart_filename}")
    
    plt.show()

def main():
    """主函数"""
    print("开始多指数月末交易策略批量回测...")
    
    # 运行批量回测
    results, comparison_df = run_comprehensive_backtest()
    
    if results:
        print("\n" + "="*80)
        print("🎯 回测总结")
        print("="*80)
        print("所有指数的月末交易策略回测已完成。")
        print("请查看生成的图表和CSV文件了解详细结果。")
        print("="*80)
    else:
        print("❌ 批量回测失败，请检查数据源和网络连接。")

if __name__ == "__main__":
    main()
