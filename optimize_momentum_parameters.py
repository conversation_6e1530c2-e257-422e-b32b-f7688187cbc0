#!/usr/bin/env python3
"""
优化基于动量因子的4ETF轮动策略参数
测试不同的m_days参数值，找到最佳动量窗口
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
from strategies.momentum_etf_rotation_strategy import MomentumETFRotationStrategy
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def optimize_momentum_parameters():
    """优化动量参数"""

    print("="*80)
    print("基于动量因子的4ETF轮动策略 - 参数优化")
    print("="*80)
    print("测试目标: 找到最佳的动量计算窗口 (m_days)")
    print("测试范围: 5天到120天，步长5天")
    print("评估指标: 年化收益率、夏普比率、最大回撤、交易次数")
    print("="*80)

    # 定义测试参数范围
    m_days_range = list(range(5, 121, 5))  # 5, 10, 15, ..., 120

    # 存储结果
    results = []

    # 固定其他参数
    start_date = '2019-01-01'
    end_date = None
    initial_capital = 100000

    print(f"开始测试 {len(m_days_range)} 个参数组合...")

    for i, m_days in enumerate(m_days_range):
        print(f"\n进度: {i+1}/{len(m_days_range)} - 测试 m_days={m_days}")

        try:
            # 创建策略实例
            strategy = MomentumETFRotationStrategy(
                start_date=start_date,
                end_date=end_date,
                initial_capital=initial_capital,
                m_days=m_days
            )

            # 获取数据（只在第一次获取）
            if i == 0:
                print("  获取ETF数据...")
                strategy.fetch_data()
            else:
                # 复用第一次获取的数据
                first_strategy = MomentumETFRotationStrategy(
                    start_date=start_date,
                    end_date=end_date,
                    initial_capital=initial_capital,
                    m_days=5  # 临时值
                )
                first_strategy.fetch_data()
                strategy.price_data = first_strategy.price_data

            # 运行回测
            print("  运行回测...")
            backtest_results = strategy.run_backtest()

            if backtest_results is not None and len(backtest_results) > 0:
                # 计算性能指标
                metrics = strategy.calculate_performance_metrics()

                # 提取数值
                total_return = float(metrics['总收益率'].strip('%')) / 100
                annual_return = float(metrics['年化收益率'].strip('%')) / 100
                volatility = float(metrics['年化波动率'].strip('%')) / 100
                sharpe_ratio = float(metrics['夏普比率'])
                max_drawdown = float(metrics['最大回撤'].strip('%')) / 100
                win_rate = float(metrics['胜率'].strip('%')) / 100
                trade_count = int(metrics['交易次数'])

                # 计算综合评分 (可以根据需要调整权重)
                # 综合评分 = 年化收益率 * 0.4 + 夏普比率 * 0.3 - |最大回撤| * 0.2 - 交易次数/1000 * 0.1
                composite_score = (annual_return * 0.4 +
                                 sharpe_ratio * 0.3 -
                                 abs(max_drawdown) * 0.2 -
                                 trade_count / 1000 * 0.1)

                result = {
                    'm_days': m_days,
                    'total_return': total_return,
                    'annual_return': annual_return,
                    'volatility': volatility,
                    'sharpe_ratio': sharpe_ratio,
                    'max_drawdown': max_drawdown,
                    'win_rate': win_rate,
                    'trade_count': trade_count,
                    'composite_score': composite_score
                }

                results.append(result)

                print(f"  年化收益: {annual_return:.2%}, 夏普比率: {sharpe_ratio:.2f}, 最大回撤: {max_drawdown:.2%}")

            else:
                print("  回测失败，跳过此参数")

        except Exception as e:
            print(f"  测试 m_days={m_days} 时出错: {str(e)}")
            continue

    # 转换为DataFrame
    results_df = pd.DataFrame(results)

    if len(results_df) == 0:
        print("错误：没有成功的测试结果")
        return None

    # 保存详细结果
    results_df.to_csv('momentum_parameter_optimization_results.csv', index=False)
    print(f"\n详细结果已保存到: momentum_parameter_optimization_results.csv")

    return results_df

def analyze_results(results_df):
    """分析优化结果"""

    print("\n" + "="*80)
    print("参数优化结果分析")
    print("="*80)

    # 找到各项指标的最佳参数
    best_return = results_df.loc[results_df['annual_return'].idxmax()]
    best_sharpe = results_df.loc[results_df['sharpe_ratio'].idxmax()]
    best_drawdown = results_df.loc[results_df['max_drawdown'].idxmax()]  # 最大回撤最小（最接近0）
    best_composite = results_df.loc[results_df['composite_score'].idxmax()]

    print("🏆 各项指标最佳参数:")
    print(f"最高年化收益率: m_days={best_return['m_days']}, 收益率={best_return['annual_return']:.2%}")
    print(f"最高夏普比率: m_days={best_sharpe['m_days']}, 夏普比率={best_sharpe['sharpe_ratio']:.2f}")
    print(f"最小回撤: m_days={best_drawdown['m_days']}, 最大回撤={best_drawdown['max_drawdown']:.2%}")
    print(f"最佳综合评分: m_days={best_composite['m_days']}, 综合评分={best_composite['composite_score']:.3f}")

    # 显示前5名综合评分
    print(f"\n📊 综合评分前5名:")
    top5 = results_df.nlargest(5, 'composite_score')
    for i, row in top5.iterrows():
        print(f"{i+1}. m_days={int(row['m_days']):3d} | "
              f"年化收益={row['annual_return']:6.2%} | "
              f"夏普比率={row['sharpe_ratio']:5.2f} | "
              f"最大回撤={row['max_drawdown']:6.2%} | "
              f"交易次数={int(row['trade_count']):3d} | "
              f"综合评分={row['composite_score']:6.3f}")

    return best_composite

def plot_optimization_results(results_df):
    """绘制优化结果图表"""

    fig, axes = plt.subplots(2, 2, figsize=(15, 12))

    # 年化收益率
    ax1 = axes[0, 0]
    ax1.plot(results_df['m_days'], results_df['annual_return'] * 100, 'b-', linewidth=2, marker='o', markersize=4)
    ax1.set_title('年化收益率 vs 动量窗口', fontsize=12, fontweight='bold')
    ax1.set_xlabel('动量窗口 (天)', fontsize=10)
    ax1.set_ylabel('年化收益率 (%)', fontsize=10)
    ax1.grid(True, alpha=0.3)

    # 夏普比率
    ax2 = axes[0, 1]
    ax2.plot(results_df['m_days'], results_df['sharpe_ratio'], 'g-', linewidth=2, marker='o', markersize=4)
    ax2.set_title('夏普比率 vs 动量窗口', fontsize=12, fontweight='bold')
    ax2.set_xlabel('动量窗口 (天)', fontsize=10)
    ax2.set_ylabel('夏普比率', fontsize=10)
    ax2.grid(True, alpha=0.3)

    # 最大回撤
    ax3 = axes[1, 0]
    ax3.plot(results_df['m_days'], results_df['max_drawdown'] * 100, 'r-', linewidth=2, marker='o', markersize=4)
    ax3.set_title('最大回撤 vs 动量窗口', fontsize=12, fontweight='bold')
    ax3.set_xlabel('动量窗口 (天)', fontsize=10)
    ax3.set_ylabel('最大回撤 (%)', fontsize=10)
    ax3.grid(True, alpha=0.3)

    # 综合评分
    ax4 = axes[1, 1]
    ax4.plot(results_df['m_days'], results_df['composite_score'], 'purple', linewidth=2, marker='o', markersize=4)
    ax4.set_title('综合评分 vs 动量窗口', fontsize=12, fontweight='bold')
    ax4.set_xlabel('动量窗口 (天)', fontsize=10)
    ax4.set_ylabel('综合评分', fontsize=10)
    ax4.grid(True, alpha=0.3)

    # 标记最佳点
    best_idx = results_df['composite_score'].idxmax()
    best_m_days = results_df.loc[best_idx, 'm_days']

    for ax, metric in zip([ax1, ax2, ax3, ax4],
                         ['annual_return', 'sharpe_ratio', 'max_drawdown', 'composite_score']):
        best_value = results_df.loc[best_idx, metric]
        if metric == 'annual_return' or metric == 'max_drawdown':
            best_value *= 100
        ax.scatter([best_m_days], [best_value], color='red', s=100, zorder=5)
        ax.annotate(f'最佳: {best_m_days}天',
                   xy=(best_m_days, best_value),
                   xytext=(10, 10),
                   textcoords='offset points',
                   bbox=dict(boxstyle='round,pad=0.3', facecolor='yellow', alpha=0.7),
                   fontsize=9)

    plt.tight_layout()

    # 保存图表
    filename = 'momentum_parameter_optimization_charts.png'
    plt.savefig(filename, dpi=300, bbox_inches='tight')
    print(f"优化结果图表已保存为: {filename}")

    plt.show()
    return fig

def main():
    """主函数"""

    # 运行参数优化
    results_df = optimize_momentum_parameters()

    if results_df is not None:
        # 分析结果
        best_params = analyze_results(results_df)

        # 绘制图表
        plot_optimization_results(results_df)

        # 推荐最佳参数
        print(f"\n🎯 推荐参数:")
        print(f"最佳动量窗口: {best_params['m_days']} 天")
        print(f"预期年化收益率: {best_params['annual_return']:.2%}")
        print(f"预期夏普比率: {best_params['sharpe_ratio']:.2f}")
        print(f"预期最大回撤: {best_params['max_drawdown']:.2%}")

        return best_params

    return None

if __name__ == "__main__":
    best_params = main()
