"""
获取中证2000指数真实历史数据

使用多个数据源（baostock, akshare, yfinance）获取中证2000指数的真实历史数据
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import os

def install_and_import_baostock():
    """安装并导入baostock"""
    try:
        import baostock as bs
        return bs
    except ImportError:
        print("📦 正在安装baostock...")
        import subprocess
        import sys
        subprocess.check_call([sys.executable, "-m", "pip", "install", "baostock"])
        import baostock as bs
        return bs

def fetch_csi2000_from_baostock(start_date='2023-08-01', end_date=None):
    """
    使用baostock获取中证2000指数数据
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        pandas.DataFrame: 指数数据
    """
    print("🌐 尝试使用baostock获取中证2000数据...")
    
    try:
        bs = install_and_import_baostock()
        
        # 登录baostock系统
        lg = bs.login()
        print(f"登录baostock: {lg.error_msg}")
        
        if lg.error_code != '0':
            print("❌ baostock登录失败")
            return None
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 尝试不同的中证2000代码
        codes = [
            'sh.000932',  # 中证2000指数
            'sz.399932',  # 深证中证2000
            'sh.932000',  # 另一种可能的代码
        ]
        
        for code in codes:
            try:
                print(f"  尝试代码: {code}")
                
                # 获取指数K线数据
                rs = bs.query_history_k_data_plus(
                    code, 
                    "date,code,open,high,low,close,volume,amount",
                    start_date=start_date, 
                    end_date=end_date,
                    frequency="d"
                )
                
                if rs.error_code == '0':
                    data_list = []
                    while (rs.error_code == '0') & rs.next():
                        data_list.append(rs.get_row_data())
                    
                    if data_list:
                        df = pd.DataFrame(data_list, columns=rs.fields)
                        
                        # 数据清理和转换
                        df['date'] = pd.to_datetime(df['date'])
                        df = df.set_index('date')
                        
                        # 转换数据类型
                        numeric_cols = ['open', 'high', 'low', 'close', 'volume', 'amount']
                        for col in numeric_cols:
                            if col in df.columns:
                                df[col] = pd.to_numeric(df[col], errors='coerce')
                        
                        # 删除无效数据
                        df = df.dropna()
                        
                        if len(df) > 0:
                            print(f"✅ baostock获取成功 ({code})")
                            print(f"📅 数据范围: {df.index.min().strftime('%Y-%m-%d')} 至 {df.index.max().strftime('%Y-%m-%d')}")
                            print(f"📊 数据点数: {len(df):,}")
                            
                            # 登出baostock系统
                            bs.logout()
                            
                            return df[['open', 'high', 'low', 'close', 'volume']]
                        else:
                            print(f"  ❌ {code} 处理后数据为空")
                    else:
                        print(f"  ❌ {code} 返回空数据")
                else:
                    print(f"  ❌ {code} 查询失败: {rs.error_msg}")
                    
            except Exception as e:
                print(f"  ❌ {code} 异常: {e}")
                continue
        
        # 登出baostock系统
        bs.logout()
        print("❌ 所有baostock代码都获取失败")
        return None
        
    except Exception as e:
        print(f"❌ baostock获取失败: {e}")
        return None

def fetch_csi2000_from_akshare(start_date='2023-08-01', end_date=None):
    """
    使用akshare获取中证2000指数数据
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        pandas.DataFrame: 指数数据
    """
    print("🌐 尝试使用akshare获取中证2000数据...")
    
    try:
        import akshare as ak
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 尝试不同的akshare方法和代码
        methods = [
            ('index_zh_a_hist', '932000'),
            ('stock_zh_index_daily_em', 'sh932000'),
            ('stock_zh_index_daily', 'sh932000'),
        ]
        
        for method_name, symbol in methods:
            try:
                print(f"  尝试方法: {method_name} with symbol: {symbol}")
                
                if method_name == 'index_zh_a_hist':
                    api_start_date = start_date.replace('-', '')
                    api_end_date = end_date.replace('-', '')
                    data = ak.index_zh_a_hist(symbol=symbol, period="daily",
                                            start_date=api_start_date, end_date=api_end_date)
                elif method_name == 'stock_zh_index_daily_em':
                    data = ak.stock_zh_index_daily_em(symbol=symbol)
                elif method_name == 'stock_zh_index_daily':
                    data = ak.stock_zh_index_daily(symbol=symbol)
                
                if data is not None and not data.empty:
                    # 标准化列名
                    column_mapping = {
                        '开盘': 'open', '最高': 'high', '最低': 'low', '收盘': 'close', '成交量': 'volume',
                        'open': 'open', 'high': 'high', 'low': 'low', 'close': 'close', 'volume': 'volume'
                    }
                    
                    data = data.rename(columns=column_mapping)
                    
                    # 确保索引是日期类型
                    if not isinstance(data.index, pd.DatetimeIndex):
                        if '日期' in data.columns:
                            data['date'] = pd.to_datetime(data['日期'])
                            data = data.set_index('date')
                        elif 'date' in data.columns:
                            data['date'] = pd.to_datetime(data['date'])
                            data = data.set_index('date')
                        else:
                            data.index = pd.to_datetime(data.index)
                    
                    # 筛选日期范围
                    start_dt = pd.to_datetime(start_date)
                    end_dt = pd.to_datetime(end_date)
                    data = data[(data.index >= start_dt) & (data.index <= end_dt)]
                    
                    # 删除包含NaN的行
                    data = data.dropna()
                    
                    if len(data) > 0:
                        print(f"✅ akshare获取成功 ({method_name})")
                        print(f"📅 数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                        print(f"📊 数据点数: {len(data):,}")
                        return data[['open', 'high', 'low', 'close', 'volume']]
                    else:
                        print(f"  ❌ {method_name} 处理后数据为空")
                else:
                    print(f"  ❌ {method_name} 返回空数据")
                    
            except Exception as e:
                print(f"  ❌ {method_name} 失败: {e}")
                continue
        
        print("❌ 所有akshare方法都获取失败")
        return None
        
    except Exception as e:
        print(f"❌ akshare获取失败: {e}")
        return None

def fetch_csi2000_from_yfinance(start_date='2023-08-01', end_date=None):
    """
    使用yfinance获取中证2000指数数据
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
    
    Returns:
        pandas.DataFrame: 指数数据
    """
    print("🌐 尝试使用yfinance获取中证2000数据...")
    
    try:
        import yfinance as yf
        
        if end_date is None:
            end_date = datetime.now().strftime('%Y-%m-%d')
        
        # 尝试不同的yfinance代码
        symbols = ['932000.SS', '000932.SS', 'CSI2000.SS']
        
        for symbol in symbols:
            try:
                print(f"  尝试代码: {symbol}")
                
                ticker = yf.Ticker(symbol)
                data = ticker.history(start=start_date, end=end_date)
                
                if not data.empty:
                    # 标准化列名
                    data.columns = data.columns.str.lower()
                    
                    # 删除包含NaN的行
                    data = data.dropna()
                    
                    if len(data) > 0:
                        print(f"✅ yfinance获取成功 ({symbol})")
                        print(f"📅 数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                        print(f"📊 数据点数: {len(data):,}")
                        return data[['open', 'high', 'low', 'close', 'volume']]
                    else:
                        print(f"  ❌ {symbol} 处理后数据为空")
                else:
                    print(f"  ❌ {symbol} 返回空数据")
                    
            except Exception as e:
                print(f"  ❌ {symbol} 失败: {e}")
                continue
        
        print("❌ 所有yfinance代码都获取失败")
        return None
        
    except Exception as e:
        print(f"❌ yfinance获取失败: {e}")
        return None

def get_csi2000_real_data(start_date='2023-08-01', end_date=None, save_file=True):
    """
    获取中证2000指数真实历史数据
    
    Args:
        start_date: 开始日期 (YYYY-MM-DD)
        end_date: 结束日期 (YYYY-MM-DD)
        save_file: 是否保存到文件
    
    Returns:
        pandas.DataFrame: 指数数据
    """
    print("🚀 获取中证2000指数真实历史数据")
    print("="*60)
    
    if end_date is None:
        end_date = datetime.now().strftime('%Y-%m-%d')
    
    print(f"📅 目标日期范围: {start_date} 至 {end_date}")
    print()
    
    # 按优先级尝试不同数据源
    data_sources = [
        ('baostock', fetch_csi2000_from_baostock),
        ('akshare', fetch_csi2000_from_akshare),
        ('yfinance', fetch_csi2000_from_yfinance),
    ]
    
    for source_name, fetch_func in data_sources:
        print(f"{'='*20} {source_name.upper()} {'='*20}")
        
        try:
            data = fetch_func(start_date, end_date)
            
            if data is not None and not data.empty:
                print(f"🎉 成功从{source_name}获取中证2000真实数据！")
                
                # 数据质量检查
                print(f"\n📊 数据质量检查:")
                print(f"   数据点数: {len(data):,}")
                print(f"   数据范围: {data.index.min().strftime('%Y-%m-%d')} 至 {data.index.max().strftime('%Y-%m-%d')}")
                print(f"   价格范围: {data['close'].min():.2f} - {data['close'].max():.2f}")
                
                # 计算基本统计
                total_return = (data['close'].iloc[-1] / data['close'].iloc[0] - 1) * 100
                daily_returns = data['close'].pct_change().dropna()
                volatility = daily_returns.std() * (252**0.5) * 100
                
                print(f"   总收益率: {total_return:.2f}%")
                print(f"   年化波动率: {volatility:.2f}%")
                
                # 保存数据
                if save_file:
                    filename = f"csi2000_real_data_{source_name}_{datetime.now().strftime('%Y%m%d_%H%M%S')}.csv"
                    data.to_csv(filename)
                    print(f"💾 数据已保存到: {filename}")
                
                return data
            else:
                print(f"❌ {source_name}获取失败或返回空数据")
                
        except Exception as e:
            print(f"❌ {source_name}获取异常: {e}")
        
        print()
    
    print("❌ 所有数据源都获取失败")
    return None

def get_csi2000_full_history(save_file=True):
    """
    获取中证2000指数从上市日期开始的完整历史数据

    Args:
        save_file: 是否保存到文件

    Returns:
        pandas.DataFrame: 指数数据
    """
    # 中证2000指数上市日期
    launch_date = '2023-08-11'

    print("🚀 获取中证2000指数完整历史数据（从上市日期开始）")
    print("="*60)
    print(f"📅 中证2000指数上市日期: {launch_date}")
    print(f"📊 获取从上市日期至今的所有交易数据")
    print()

    # 获取完整历史数据
    data = get_csi2000_real_data(start_date=launch_date, end_date=None, save_file=save_file)

    if data is not None:
        # 计算更详细的统计信息
        print("\n📊 完整历史数据统计:")
        print("="*40)

        # 基本信息
        total_days = len(data)
        start_date = data.index.min().strftime('%Y-%m-%d')
        end_date = data.index.max().strftime('%Y-%m-%d')

        print(f"📅 数据期间: {start_date} 至 {end_date}")
        print(f"📊 总交易日数: {total_days:,}")

        # 价格统计
        start_price = data['close'].iloc[0]
        end_price = data['close'].iloc[-1]
        max_price = data['close'].max()
        min_price = data['close'].min()

        print(f"💰 上市首日收盘价: {start_price:,.2f}")
        print(f"💰 最新收盘价: {end_price:,.2f}")
        print(f"📈 历史最高价: {max_price:,.2f}")
        print(f"📉 历史最低价: {min_price:,.2f}")

        # 收益率计算
        total_return = (end_price / start_price - 1) * 100

        # 计算年化收益率
        days_elapsed = (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days
        years_elapsed = days_elapsed / 365.25
        annualized_return = ((end_price / start_price) ** (1/years_elapsed) - 1) * 100 if years_elapsed > 0 else 0

        print(f"📈 总收益率: {total_return:.2f}%")
        print(f"📈 年化收益率: {annualized_return:.2f}%")

        # 风险指标
        daily_returns = data['close'].pct_change().dropna()
        volatility = daily_returns.std() * (252**0.5) * 100

        # 最大回撤
        peak = data['close'].expanding().max()
        drawdown = (data['close'] - peak) / peak
        max_drawdown = drawdown.min() * 100

        print(f"📊 年化波动率: {volatility:.2f}%")
        print(f"⚠️ 最大回撤: {max_drawdown:.2f}%")

        # 夏普比率（假设无风险利率为3%）
        risk_free_rate = 0.03
        sharpe_ratio = (annualized_return/100 - risk_free_rate) / (volatility/100) if volatility > 0 else 0
        print(f"📊 夏普比率: {sharpe_ratio:.3f}")

        # 胜率统计
        positive_days = (daily_returns > 0).sum()
        total_trading_days = len(daily_returns)
        win_rate = positive_days / total_trading_days * 100

        print(f"📈 上涨交易日: {positive_days}/{total_trading_days} ({win_rate:.1f}%)")

        # 成交量统计
        avg_volume = data['volume'].mean() / 1e8
        max_volume = data['volume'].max() / 1e8

        print(f"📊 平均日成交量: {avg_volume:.1f}亿股")
        print(f"📊 最大日成交量: {max_volume:.1f}亿股")

    return data

def main():
    """主函数"""
    # 获取中证2000完整历史数据（从上市日期开始）
    data = get_csi2000_full_history(save_file=True)

    if data is not None:
        print("\n🎯 中证2000完整历史数据获取成功！")
        print("\n💡 后续可以使用这些真实数据进行:")
        print("   - 技术分析和指标计算")
        print("   - 投资策略回测")
        print("   - 风险分析")
        print("   - 与其他指数对比分析")
        print("   - 因子分析和量化研究")
    else:
        print("\n❌ 无法获取中证2000真实数据")

if __name__ == "__main__":
    main()
