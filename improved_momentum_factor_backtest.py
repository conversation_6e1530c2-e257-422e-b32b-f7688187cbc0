#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
改进版动量因子回测系统
针对原版动量因子的问题进行多方面改进
"""

import pandas as pd
import numpy as np
import matplotlib.pyplot as plt
import seaborn as sns
import yfinance as yf
import os
import pickle
from datetime import datetime, timedelta
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Arial Unicode MS', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class ImprovedMomentumFactorBacktest:
    def __init__(self, data_dir='sp500_price_cache'):
        self.data_dir = data_dir
        self.price_data = {}
        self.momentum_factors = {}
        
    def load_price_data(self):
        """加载所有股票的价格数据"""
        print("📊 加载价格数据...")

        if os.path.exists(self.data_dir):
            pkl_files = [f for f in os.listdir(self.data_dir) if f.endswith('_price.pkl')]
            print(f"   发现 {len(pkl_files)} 个缓存文件")

            success_count = 0
            for file in pkl_files:
                symbol = file.replace('_price.pkl', '')
                file_path = os.path.join(self.data_dir, file)

                try:
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)

                    if isinstance(data, dict) and 'price_data' in data:
                        price_series = data['price_data']
                        if isinstance(price_series, pd.Series) and len(price_series) > 500:
                            if not isinstance(price_series.index, pd.DatetimeIndex):
                                try:
                                    price_series.index = pd.to_datetime(price_series.index)
                                except:
                                    continue
                            if price_series.index.tz is not None:
                                price_series.index = price_series.index.tz_convert('UTC').tz_localize(None)
                            self.price_data[symbol] = price_series.dropna()
                            success_count += 1

                except Exception as e:
                    continue

                if success_count % 50 == 0 and success_count > 0:
                    print(f"   已加载 {success_count} 只股票...")

        print(f"✅ 成功加载 {len(self.price_data)} 只股票的价格数据")

    def calculate_improved_momentum_factors(self):
        """计算改进版动量因子"""
        print("🔄 计算改进版动量因子...")
        
        momentum_data = {}
        
        for symbol, prices in self.price_data.items():
            try:
                # 1. 跳过最近1个月的收益率（避免短期反转效应）
                skip_period = 21  # 跳过最近21个交易日
                
                # 2. 计算不同周期的动量，但都跳过最近1个月
                momentum_2_12 = prices.pct_change(63).shift(skip_period)  # 2-12月动量
                momentum_3_12 = prices.pct_change(252).shift(skip_period)  # 3-12月动量
                momentum_6_12 = prices.pct_change(126).shift(skip_period)  # 6-12月动量
                
                # 3. 计算风险调整动量（动量/波动率）
                returns = prices.pct_change()
                volatility_60d = returns.rolling(60).std()
                risk_adj_momentum = momentum_6_12 / volatility_60d
                
                # 4. 计算趋势强度（线性回归斜率的R²）
                def calculate_trend_strength(price_series, window=60):
                    trend_strength = pd.Series(index=price_series.index, dtype=float)
                    for i in range(window, len(price_series)):
                        y = np.log(price_series.iloc[i-window:i].values)
                        x = np.arange(len(y))
                        if len(y) > 10:
                            try:
                                slope, intercept = np.polyfit(x, y, 1)
                                y_pred = slope * x + intercept
                                ss_res = np.sum((y - y_pred) ** 2)
                                ss_tot = np.sum((y - np.mean(y)) ** 2)
                                r_squared = 1 - (ss_res / ss_tot) if ss_tot != 0 else 0
                                trend_strength.iloc[i] = max(0, r_squared)  # 确保非负
                            except:
                                trend_strength.iloc[i] = 0
                    return trend_strength
                
                trend_strength = calculate_trend_strength(prices)
                
                # 5. 计算动量质量因子（考虑交易量，如果有的话）
                # 这里简化为价格动量的一致性
                momentum_consistency = momentum_6_12.rolling(60).apply(
                    lambda x: (x > 0).sum() / len(x) if len(x) > 0 else 0
                )
                
                # 6. 复合改进动量因子
                # 使用多个维度的加权组合，并加入趋势强度调整
                composite_momentum = (
                    momentum_2_12 * 0.2 +  # 短期动量权重降低
                    momentum_6_12 * 0.4 +  # 中期动量为主
                    momentum_3_12 * 0.2 +  # 长期动量
                    risk_adj_momentum * 0.2  # 风险调整动量
                ) * trend_strength * momentum_consistency  # 乘以趋势强度和一致性
                
                # 7. 行业中性化处理（简化版）
                # 这里使用滚动Z-score标准化
                normalized_momentum = (composite_momentum - composite_momentum.rolling(252).mean()) / composite_momentum.rolling(252).std()
                
                momentum_data[symbol] = {
                    'momentum_2_12': momentum_2_12,
                    'momentum_6_12': momentum_6_12,
                    'momentum_3_12': momentum_3_12,
                    'risk_adj_momentum': risk_adj_momentum,
                    'trend_strength': trend_strength,
                    'momentum_consistency': momentum_consistency,
                    'composite_momentum': composite_momentum,
                    'normalized_momentum': normalized_momentum
                }
                
            except Exception as e:
                print(f"   ⚠️ 计算 {symbol} 动量因子失败: {e}")
                continue
        
        self.momentum_factors = momentum_data
        print(f"✅ 成功计算 {len(self.momentum_factors)} 只股票的改进动量因子")

    def create_improved_factor_portfolios(self, factor_name='normalized_momentum', 
                                        rebalance_freq='M', n_groups=5):
        """创建改进版因子投资组合"""
        print(f"🔄 创建改进版 {n_groups} 分组动量因子投资组合...")
        
        # 构建因子矩阵
        factor_matrix = pd.DataFrame()
        price_matrix = pd.DataFrame()
        
        for symbol in self.momentum_factors.keys():
            if symbol in self.price_data:
                factor_matrix[symbol] = self.momentum_factors[symbol][factor_name]
                price_matrix[symbol] = self.price_data[symbol]
        
        print(f"   共同股票数量: {len(factor_matrix.columns)}")
        
        # 重建矩阵
        factor_matrix = pd.DataFrame(factor_matrix)
        price_matrix = pd.DataFrame(price_matrix)
        
        # 对齐时间索引
        common_dates = factor_matrix.index.intersection(price_matrix.index)
        factor_matrix = factor_matrix.loc[common_dates]
        price_matrix = price_matrix.loc[common_dates]
        
        # 计算收益率
        returns_matrix = price_matrix.pct_change().dropna()
        
        # 重新平衡日期
        if rebalance_freq == 'M':
            rebalance_dates = factor_matrix.resample('M').last().index
        elif rebalance_freq == 'Q':
            rebalance_dates = factor_matrix.resample('Q').last().index
        else:
            rebalance_dates = factor_matrix.resample('Y').last().index
        
        print(f"   重新平衡次数: {len(rebalance_dates)}")
        
        # 初始化组合收益率
        portfolio_returns = pd.DataFrame(index=returns_matrix.index)
        
        for i in range(n_groups):
            portfolio_returns[f'Group_{i+1}'] = 0.0
        
        # 多空组合
        portfolio_returns['Long_Short'] = 0.0
        
        # 按重新平衡日期构建投资组合
        for i, rebal_date in enumerate(rebalance_dates[:-1]):
            next_rebal_date = rebalance_dates[i+1]
            
            # 获取重新平衡日的动量因子数据
            try:
                current_factors = factor_matrix.loc[rebal_date].dropna()
            except KeyError:
                available_dates = factor_matrix.index[factor_matrix.index <= rebal_date]
                if len(available_dates) == 0:
                    continue
                nearest_date = available_dates[-1]
                current_factors = factor_matrix.loc[nearest_date].dropna()
            
            if len(current_factors) < 30:  # 提高最小股票数要求
                continue
            
            # 过滤异常值（使用3倍标准差）
            mean_factor = current_factors.mean()
            std_factor = current_factors.std()
            filtered_factors = current_factors[
                (current_factors >= mean_factor - 3*std_factor) & 
                (current_factors <= mean_factor + 3*std_factor)
            ]
            
            if len(filtered_factors) < 20:
                continue
            
            # 按因子值排序
            sorted_stocks = filtered_factors.sort_values(ascending=False)
            
            # 改进分组方法：使用分位数分组而非等数量分组
            quantiles = [0, 0.2, 0.4, 0.6, 0.8, 1.0]
            
            # 获取持有期间的收益率
            period_returns = returns_matrix.loc[rebal_date:next_rebal_date].iloc[1:]
            
            if len(period_returns) == 0:
                continue
            
            # 计算各组合收益率
            for group in range(n_groups):
                start_quantile = quantiles[group]
                end_quantile = quantiles[group + 1]
                
                start_idx = int(len(sorted_stocks) * start_quantile)
                end_idx = int(len(sorted_stocks) * end_quantile)
                
                if end_idx <= start_idx:
                    continue
                    
                group_stocks = sorted_stocks.iloc[start_idx:end_idx].index
                
                # 等权重投资组合
                if len(group_stocks) > 0:
                    group_returns = period_returns[group_stocks].mean(axis=1)
                    portfolio_returns.loc[period_returns.index, f'Group_{group+1}'] = group_returns
            
            # 多空组合：做多前20%，做空后20%
            top_20_pct = int(len(sorted_stocks) * 0.2)
            bottom_20_pct = int(len(sorted_stocks) * 0.8)
            
            long_stocks = sorted_stocks.iloc[:top_20_pct].index
            short_stocks = sorted_stocks.iloc[bottom_20_pct:].index
            
            if len(long_stocks) > 0 and len(short_stocks) > 0:
                long_returns = period_returns[long_stocks].mean(axis=1)
                short_returns = period_returns[short_stocks].mean(axis=1)
                ls_returns = long_returns - short_returns
                portfolio_returns.loc[period_returns.index, 'Long_Short'] = ls_returns
        
        print(f"✅ 改进版因子组合创建完成，数据长度: {len(portfolio_returns)}")
        return portfolio_returns

    def calculate_performance_metrics(self, returns):
        """计算绩效指标"""
        print("📊 计算绩效指标...")
        
        metrics = {}
        
        for col in returns.columns:
            col_returns = returns[col].dropna()
            if len(col_returns) == 0:
                continue
                
            # 累积收益率
            cum_returns = (1 + col_returns).cumprod() - 1
            total_return = cum_returns.iloc[-1] if len(cum_returns) > 0 else 0
            
            # 年化收益率
            years = len(col_returns) / 252
            annual_return = (1 + total_return) ** (1/years) - 1 if years > 0 else 0
            
            # 年化波动率
            annual_vol = col_returns.std() * np.sqrt(252)
            
            # 夏普比率
            sharpe = annual_return / annual_vol if annual_vol > 0 else 0
            
            # 最大回撤
            cum_returns_series = (1 + col_returns).cumprod()
            rolling_max = cum_returns_series.expanding().max()
            drawdown = (cum_returns_series - rolling_max) / rolling_max
            max_drawdown = drawdown.min()
            
            # 胜率
            win_rate = (col_returns > 0).mean()
            
            # 信息比率（相对于等权重基准）
            if col != 'Long_Short':
                benchmark_returns = returns.mean(axis=1)  # 简化的等权重基准
                excess_returns = col_returns - benchmark_returns[:len(col_returns)]
                tracking_error = excess_returns.std() * np.sqrt(252)
                info_ratio = excess_returns.mean() * 252 / tracking_error if tracking_error > 0 else 0
            else:
                info_ratio = 0
            
            metrics[col] = {
                'total_return': total_return,
                'annual_return': annual_return,
                'annual_vol': annual_vol,
                'sharpe_ratio': sharpe,
                'max_drawdown': max_drawdown,
                'win_rate': win_rate,
                'info_ratio': info_ratio
            }
        
        return metrics

    def plot_results(self, portfolio_returns, save_path='improved_momentum_backtest_results.png'):
        """绘制回测结果"""
        print("📈 绘制累积收益率图...")
        
        # 计算累积收益率
        cum_returns = (1 + portfolio_returns).cumprod()
        
        # 创建图表
        plt.figure(figsize=(15, 10))
        
        # 绘制累积收益率
        for col in cum_returns.columns:
            if col == 'Long_Short':
                plt.plot(cum_returns.index, cum_returns[col], 
                        linewidth=3, label=f'{col} (多空组合)', linestyle='--')
            else:
                plt.plot(cum_returns.index, cum_returns[col], 
                        linewidth=2, label=col)
        
        plt.title('改进版动量因子投资组合累积收益率', fontsize=16, fontweight='bold')
        plt.xlabel('日期', fontsize=12)
        plt.ylabel('累积收益率', fontsize=12)
        plt.legend(bbox_to_anchor=(1.05, 1), loc='upper left')
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # 保存图表
        plt.savefig(save_path, dpi=300, bbox_inches='tight')
        plt.close()
        
        print(f"📊 图表已保存到: {save_path}")

    def generate_report(self, portfolio_returns, metrics, save_path='improved_momentum_report.txt'):
        """生成回测报告"""
        print("📋 生成回测报告...")
        
        with open(save_path, 'w', encoding='utf-8') as f:
            f.write("改进版动量因子回测报告\n")
            f.write("=" * 80 + "\n")
            f.write(f"回测时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            
            # 获取数据时间范围
            start_date = portfolio_returns.index.min().strftime('%Y年%m月')
            end_date = portfolio_returns.index.max().strftime('%Y年%m月')
            f.write(f"回测期间: {start_date} 至 {end_date}\n")
            f.write(f"股票池: 标普500成分股\n")
            f.write(f"重新平衡频率: 月度\n")
            f.write(f"分组数量: 5组\n")
            f.write(f"动量因子: 改进版复合动量（跳过近期+风险调整+趋势强度+标准化）\n\n")
            
            f.write("📊 绩效指标汇总:\n")
            f.write("-" * 90 + "\n")
            f.write(f"{'组合':>10} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8} {'信息比率':>10}\n")
            f.write("-" * 90 + "\n")
            
            for portfolio, metric in metrics.items():
                f.write(f"{portfolio:>10} "
                       f"{metric['total_return']:>9.2%} "
                       f"{metric['annual_return']:>9.2%} "
                       f"{metric['annual_vol']:>9.2%} "
                       f"{metric['sharpe_ratio']:>9.2f} "
                       f"{metric['max_drawdown']:>9.2%} "
                       f"{metric['win_rate']:>7.1%} "
                       f"{metric['info_ratio']:>9.2f}\n")
            
            # 因子有效性分析
            f.write(f"\n📈 因子有效性分析:\n")
            f.write("-" * 50 + "\n")
            
            # 检查单调性
            group_returns = [metrics[f'Group_{i}']['annual_return'] for i in range(1, 6)]
            is_monotonic = all(group_returns[i] >= group_returns[i+1] for i in range(4))
            f.write(f"单调性检验: {'通过' if is_monotonic else '未通过'}\n")
            
            # 高低分组收益差
            spread = metrics['Group_1']['annual_return'] - metrics['Group_5']['annual_return']
            f.write(f"高低分组收益差: {spread:.2%}\n")
            
            # 多空组合表现
            if 'Long_Short' in metrics:
                f.write(f"多空组合年化收益: {metrics['Long_Short']['annual_return']:.2%}\n")
                f.write(f"多空组合夏普比率: {metrics['Long_Short']['sharpe_ratio']:.2f}\n")
            
            # 改进效果评估
            f.write(f"\n🔧 改进措施:\n")
            f.write("-" * 50 + "\n")
            f.write("1. 跳过最近1个月收益率，避免短期反转效应\n")
            f.write("2. 加入风险调整动量（动量/波动率）\n")
            f.write("3. 考虑趋势强度（线性回归R²）\n")
            f.write("4. 动量一致性检验\n")
            f.write("5. 滚动标准化处理\n")
            f.write("6. 异常值过滤\n")
            f.write("7. 分位数分组替代等数量分组\n")
        
        print(f"📋 回测报告已保存到: {save_path}")


def main():
    """主函数"""
    print("🚀 改进版动量因子回测系统")
    print("=" * 60)
    
    # 初始化回测系统
    backtest = ImprovedMomentumFactorBacktest()
    
    # 1. 加载价格数据
    backtest.load_price_data()

    if len(backtest.price_data) < 20:
        print("❌ 价格数据不足，无法进行回测")
        return

    # 2. 计算改进版动量因子
    backtest.calculate_improved_momentum_factors()

    # 3. 创建改进版因子投资组合
    portfolio_returns = backtest.create_improved_factor_portfolios()

    # 4. 计算绩效指标
    metrics = backtest.calculate_performance_metrics(portfolio_returns)

    # 5. 显示结果
    print("\n📊 改进版回测结果:")
    print("=" * 90)
    print(f"{'组合':>10} {'总收益':>10} {'年化收益':>10} {'年化波动':>10} {'夏普比率':>10} {'最大回撤':>10} {'胜率':>8} {'信息比率':>10}")
    print("-" * 90)

    for portfolio, metric in metrics.items():
        print(f"{portfolio:>10} "
              f"{metric['total_return']:>9.2%} "
              f"{metric['annual_return']:>9.2%} "
              f"{metric['annual_vol']:>9.2%} "
              f"{metric['sharpe_ratio']:>9.2f} "
              f"{metric['max_drawdown']:>9.2%} "
              f"{metric['win_rate']:>7.1%} "
              f"{metric['info_ratio']:>9.2f}")

    # 6. 绘制图表
    backtest.plot_results(portfolio_returns)

    # 7. 生成报告
    backtest.generate_report(portfolio_returns, metrics)

    print("\n🎯 改进版动量因子回测完成!")


if __name__ == "__main__":
    main()
